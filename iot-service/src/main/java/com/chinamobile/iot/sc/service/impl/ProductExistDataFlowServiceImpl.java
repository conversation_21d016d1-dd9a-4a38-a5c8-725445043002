package com.chinamobile.iot.sc.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.config.CommonConstant;
import com.chinamobile.iot.sc.constant.productflow.ProductFlowAttachmentTypeEnum;
import com.chinamobile.iot.sc.constant.productflow.ProductFlowHandleStatusEnum;
import com.chinamobile.iot.sc.constant.productflow.ProductFlowTypeEnum;
import com.chinamobile.iot.sc.constant.productflow.ProductOperateTypeEnum;
import com.chinamobile.iot.sc.constant.productflow.ProductStandardEnum;
import com.chinamobile.iot.sc.constant.productflow.ProductTypeEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.ProductExistDataFlowMapperExt;
import com.chinamobile.iot.sc.dao.ext.StandardServiceMapperExt;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.excel.ExcelUsedProductExistDataImportHandler;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceAtomImportDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSkuAtomPriceImportDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSkuImportDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSpuImportDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSpuSkuAtomCodeDTO;
import com.chinamobile.iot.sc.pojo.dto.UsedProductExistDataDTO;
import com.chinamobile.iot.sc.pojo.mapper.StandardServiceDO;
import com.chinamobile.iot.sc.service.IStorageService;
import com.chinamobile.iot.sc.service.ProductExistDataFlowService;
import com.chinamobile.iot.sc.service.ProductFlowInstanceService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.FileUtils;
import com.chinamobile.iot.sc.util.ProductFlowUtil;
import com.chinamobile.iot.sc.util.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.MessageDigest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27
 * @description 已存在产品数据流程service实现类
 */
@Service
@Slf4j
public class ProductExistDataFlowServiceImpl implements ProductExistDataFlowService {

    //redis中保存spu默认附件的hash_key，filed为fileUrl和fileKey
    public static final String DEFAULT_ATTACHMENT_KEY = "DEFAULT_ATTACHMENT_KEY";

    @Resource
    private ProductExistDataFlowMapperExt productExistDataFlowMapperExt;

    @Resource
    private ExcelUsedProductExistDataImportHandler excelUsedProductExistDataImportHandler;

    @Resource
    private FileUtils fileUtils;

    @Resource
    private ProductFlowMapper productFlowMapper;

    @Resource
    private ProductFlowUtil productFlowUtil;

    @Resource
    private ProductFlowInstanceMapper productFlowInstanceMapper;

    @Resource
    private ProductFlowInstanceAtomMapper productFlowInstanceAtomMapper;

    @Resource
    private ProductFlowInstanceConfigMapper productFlowInstanceConfigMapper;

    @Resource
    private ProductFlowInstanceSpuMapper productFlowInstanceSpuMapper;

    @Resource
    private ProductFlowInstanceDirectoryMapper productFlowInstanceDirectoryMapper;

    @Resource
    private NavigationInfoMapper navigationInfoMapper;

    @Resource
    private ProductFlowInstanceSkuMapper productFlowInstanceSkuMapper;

    @Resource
    private ProductFlowInstanceTaskMapper productFlowInstanceTaskMapper;

    @Resource
    private ProductFlowInstanceAttachmentMapper productFlowInstanceAttachmentMapper;

    @Resource
    private ProductFlowStepMapper productFlowStepMapper;

    @Autowired
    private ProductFlowInstanceService productFlowInstanceService;

    @Autowired
    private AreaDataConfig areaDataConfig;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private CategoryInfoMapper categoryInfoMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private IStorageService storageService;

    @Resource
    private StandardServiceMapperExt standardServiceMapperExt;

    @Value("${iot.ftp.name}")
    private String sftpUserName;
    @Value("${iot.ftp.password}")
    private String sftpPassword;
    @Value("${iot.ftp.host}")
    private String sftpHost;
    @Value("${iot.ftp.port}")
    private Integer sftpPort;
    @Value("${iot.ftp.productFlowAttachmentPath}")
    private String sftpProductFlowAttachmentPath;
    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void exportUsedProductExistData(HttpServletResponse response) throws IOException {
        List<UsedProductExistDataDTO> existDataDTOList = productExistDataFlowMapperExt.listUsedProductExistData();
        if (CollectionUtil.isNotEmpty(existDataDTOList)) {
            String fileName = "导出在售的存量商品信息";
            ExportParams exportParams = new ExportParams(fileName,
                    fileName, ExcelType.XSSF);
            // 可以在导入时使用当前模板，因此不用title占一行
            exportParams.setTitle(null);
            ExcelUtils.exportExcel(existDataDTOList, UsedProductExistDataDTO.class,
                    fileName, exportParams, response);
        } else {
            throw new BusinessException(BaseErrorConstant.INVALID_DATA_TO_EXPORT);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer importUsedProductExistData(MultipartFile file,
                                                 MultipartFile iotFile, HttpServletRequest request,
                                                 HttpServletResponse response) throws Exception {
        response.setHeader("content-type", "application/octet-stream");
        if (!ExcelUtils.suffixCheck(iotFile.getOriginalFilename())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"文件格式错误，只能是xlsx,xls类型");
        }
        if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"文件格式错误，只能是xlsx,xls类型");
        }
        //保存错误信息
        List<String> errMsg = new ArrayList<>();
        //步骤
        /*
        * 1.读取excel的每个sheet，所有的规格放一起，所有的原子放一起
        * 注意:需要从数据库查询规格的spu，原子的spu和sku
        * 2.按照sku维度组织数据  spu-sku-原子集合
        * 3.根据柳青的文件，使用spu_sku编码找到产品标准、产品类别(如果割接的文件在柳青的文件里找不到，或者在数据库找不到，都要报错)
        * 4.保存流程spu,sku,原子,审核 信息
        * 注意：合同履约类的原子信息 销售单价，销售价格需要自动计算出来
        * 5.找到附件，spu的附件需要为下面的所有sku对应的流程实例保存
        * 注意:存在自己的对象存储时，增加文件夹前缀
        * 6.批量入库
        *
        * */


        //key- spuCode, value - 导入excel中的spu信息
        Map<String, ProductFlowInstanceSpuImportDTO> spuDataMap = new HashMap<>();
        //key- skuCode, value - 导入excel中的sku信息(包含查询数据库填充的spu编码)
        Map<String, ProductFlowInstanceSkuImportDTO> skuDataMap = new HashMap<>();
        //key- atomCode, value - 导入excel中的原子信息(包含查询数据库填充的spu,sku编码)
        Map<String, ProductFlowInstanceAtomImportDTO> atomDataMap = new HashMap<>();
        //key - skuCode_atomCode, value - 原子流程信息 （因为相同的原子编码，在不同的sku下价格和数量信息不一定相同，所以需要带上sku编码）
        Map<String, ProductFlowInstanceAtomImportDTO> skuAtomCodeDataMap = new HashMap<>();
        //key- skuCode, value - 对应的上架流程实例id
        Map<String, String> skuFlowInstanceIdMap = new HashMap<>();
        //key- spuCode, value - 对应的sku编码集合
        Map<String, List<String>> spuSkuCodeMap = new HashMap<>();
        List<String> softAtomCodeList = new ArrayList<>();
        List<String> hardAtomCodeList = new ArrayList<>();

        //1.读取excel的每个sheet，所有的规格放一起，所有的原子放一起
        List<ProductFlowInstanceSkuImportDTO> skuImportDTOList = new ArrayList<>();
        List<ProductFlowInstanceAtomImportDTO> atomImportDTOList = new ArrayList<>();
        //保存原子编码是硬件还是软件
        Map<String,String> atomCodeSoftOrHard = new HashMap<>();
        ExcelReader readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceSpuImportDTO> spuImportDTOList = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(0)
                .head(ProductFlowInstanceSpuImportDTO.class).doReadSync();
        //多个sheet读取，每个sheet需要重新read
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceSkuImportDTO> skuImportDTOListDICT = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(1)
                .head(ProductFlowInstanceSkuImportDTO.class).doReadSync();
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceSkuImportDTO> skuImportDTOListCooperate = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(3)
                .head(ProductFlowInstanceSkuImportDTO.class).doReadSync();
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceSkuImportDTO> skuImportDTOListContract = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(6)
                .head(ProductFlowInstanceSkuImportDTO.class).doReadSync();
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceAtomImportDTO> atomImportDTOListDICT = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(2)
                .head(ProductFlowInstanceAtomImportDTO.class).doReadSync();
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceAtomImportDTO> atomImportDTOListCooperateSoft = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(4)
                .head(ProductFlowInstanceAtomImportDTO.class).doReadSync();
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceAtomImportDTO> atomImportDTOListCooperateHard = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(5)
                .head(ProductFlowInstanceAtomImportDTO.class).doReadSync();
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceAtomImportDTO> atomImportDTOListContractSoft = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(7)
                .head(ProductFlowInstanceAtomImportDTO.class).doReadSync();
        readerBuilder = EasyExcel.read(iotFile.getInputStream()).build();
        List<ProductFlowInstanceAtomImportDTO> atomImportDTOListContractHard = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(8)
                .head(ProductFlowInstanceAtomImportDTO.class).doReadSync();
        skuImportDTOList.addAll(skuImportDTOListDICT);
        skuImportDTOList.addAll(skuImportDTOListCooperate);
        skuImportDTOList.addAll(skuImportDTOListContract);
        atomImportDTOList.addAll(atomImportDTOListDICT);
        atomImportDTOList.addAll(atomImportDTOListCooperateSoft);
        atomImportDTOList.addAll(atomImportDTOListCooperateHard);
        atomImportDTOList.addAll(atomImportDTOListContractSoft);
        atomImportDTOList.addAll(atomImportDTOListContractHard);
        atomImportDTOListCooperateHard.forEach(a -> {
            atomCodeSoftOrHard.put(a.getAtomCode(), AtomOfferingClassEnum.H.getAtomOfferingClass());
        });
        atomImportDTOListContractHard.forEach(a -> {
            atomCodeSoftOrHard.put(a.getAtomCode(), AtomOfferingClassEnum.H.getAtomOfferingClass());
        });
        atomImportDTOListCooperateSoft.forEach(a -> {
            atomCodeSoftOrHard.put(a.getAtomCode(), AtomOfferingClassEnum.S.getAtomOfferingClass());
        });
        atomImportDTOListContractSoft.forEach(a -> {
            atomCodeSoftOrHard.put(a.getAtomCode(), AtomOfferingClassEnum.S.getAtomOfferingClass());
        });

        List<String> contractSoftAtomCode = atomImportDTOListContractSoft.stream().map(a -> {
            return a.getAtomCode();
        }).collect(Collectors.toList());
        List<String> cooperateSoftAtomCode = atomImportDTOListCooperateSoft.stream().map(a -> {
            return a.getAtomCode();
        }).collect(Collectors.toList());
        softAtomCodeList.addAll(contractSoftAtomCode);
        softAtomCodeList.addAll(cooperateSoftAtomCode);
        List<String> contractHardAtomCode = atomImportDTOListContractHard.stream().map(a -> {
            return a.getAtomCode();
        }).collect(Collectors.toList());
        List<String> cooperateHardAtomCode = atomImportDTOListCooperateHard.stream().map(a -> {
            return a.getAtomCode();
        }).collect(Collectors.toList());
        hardAtomCodeList.addAll(contractHardAtomCode);
        hardAtomCodeList.addAll(cooperateHardAtomCode);
        log.info("割接流程,文件读取完毕");
        if(CollectionUtils.isNotEmpty(atomImportDTOList)){
            atomImportDTOList.forEach(s -> {
                atomDataMap.put(s.getAtomCode(),s);
            });
        }else {
            errMsg.add("无有效原子数据");
        }
        //设置spu的导航目录和类目
        for (ProductFlowInstanceSpuImportDTO spuImportDTO : spuImportDTOList) {
            try {
                spuImportDTO.setShelfCatagoryId(productFlowInstanceService.getShelfCmiotCostIdByName(spuImportDTO.getShelfCatagoryId(), "-1"));
            } catch (Exception e) {
                errMsg.add("spu编码"+spuImportDTO.getSpuCode()+"的上架类目"+spuImportDTO.getShelfCatagoryId()+"错误");
            }
            try {
                spuImportDTO.setFirstDirectoryId(productFlowInstanceService.getNavigationIdByName(spuImportDTO.getFirstDirectoryId(),"-1"));
            } catch (Exception e) {
                errMsg.add("spu编码"+spuImportDTO.getSpuCode()+"的一级导航目录"+spuImportDTO.getFirstDirectoryId()+"错误");
                spuImportDTO.setFirstDirectoryId(null);
            }
            if(spuImportDTO.getFirstDirectoryId() != null){
                try {
                    spuImportDTO.setSecondDirectoryId(productFlowInstanceService.getNavigationIdByName(spuImportDTO.getSecondDirectoryId(),spuImportDTO.getFirstDirectoryId()));
                } catch (Exception e) {
                    errMsg.add("spu编码"+spuImportDTO.getSpuCode()+"的二级导航目录"+spuImportDTO.getSecondDirectoryId()+"错误");
                }
            }
        }
        log.info("割接流程,spu类目和导航目录设置完毕");
        //整理sku数据，处理由于原子商品价格导致的合并单元格的情况,把多行sku整理为单行数据
        List<ProductFlowInstanceSkuImportDTO> skuRowDataList = new ArrayList<>();
        ProductFlowInstanceSkuImportDTO skuRowData = null;
        for (ProductFlowInstanceSkuImportDTO skuImportDTO : skuImportDTOList) {
            String skuCode = skuImportDTO.getSkuCode();
            Long saleSinglePrice = skuImportDTO.getSaleSinglePrice();
            Long saleSingleMinPrice = skuImportDTO.getSaleSingleMinPrice();
            Long saleSingleMaxPrice = skuImportDTO.getSaleSingleMaxPrice();
            Integer atomCount = skuImportDTO.getAtomCount();
            String atomCode = skuImportDTO.getAtomCode();
            Boolean noPrice = saleSinglePrice == null && StringUtils.isEmpty(atomCode);
            //销售价格，是所有规格共有的，以此为判断标准。上限价格和底价不是共用的
            Boolean havePrice = saleSinglePrice != null && StringUtils.isNotEmpty(atomCode) ;
            if((!noPrice) && (!havePrice)){
                errMsg.add("规格"+skuCode+"的原子商品编码和价格信息，必须同时为空或者同时不为空");
                continue;
            }
            if(StringUtils.isNotEmpty(skuCode)){
                //当前行包含sku信息(sku的第一行)
                skuRowData = skuImportDTO;
                if(havePrice){
                    //当前行包括价格信息
                    List<ProductFlowInstanceSkuAtomPriceImportDTO> atomPriceImportDTOList = new ArrayList<>();
                    ProductFlowInstanceSkuAtomPriceImportDTO dto = new ProductFlowInstanceSkuAtomPriceImportDTO();
                    dto.setAtomCode(atomCode);
                    dto.setSalePrice(saleSinglePrice);
                    if(saleSingleMaxPrice != null){
                        dto.setSaleMaxPrice(saleSingleMaxPrice);
                    }
                    if(saleSingleMinPrice != null){
                        dto.setSaleMinPrice(saleSingleMinPrice);
                    }
                    if(atomCount != null){
                        if(hardAtomCodeList.contains(atomCode)){
                            dto.setAtomQuantity(atomCount);
                        }else if(softAtomCodeList.contains(atomCode)){
                            dto.setSoftQuantity(atomCount);
                        }
                    }
                    atomPriceImportDTOList.add(dto);
                    skuRowData.setAtomPriceImportDTOList(atomPriceImportDTOList);
                }
                skuRowDataList.add(skuRowData);
            }else if(StringUtils.isEmpty(skuCode) && havePrice){
                //当前行没有sku信息，有价格信息（sku的后面几行）
                if(skuRowData == null){
                    continue;
                }
                List<ProductFlowInstanceSkuAtomPriceImportDTO> atomPriceImportDTOList = skuRowData.getAtomPriceImportDTOList();
                ProductFlowInstanceSkuAtomPriceImportDTO dto = new ProductFlowInstanceSkuAtomPriceImportDTO();
                dto.setAtomCode(atomCode);
                dto.setSalePrice(saleSinglePrice);
                if(saleSingleMaxPrice != null){
                    dto.setSaleMaxPrice(saleSingleMaxPrice);
                }
                if(saleSingleMinPrice != null){
                    dto.setSaleMinPrice(saleSingleMinPrice);
                }
                if(atomCount != null){
                    if(hardAtomCodeList.contains(atomCode)){
                        dto.setAtomQuantity(atomCount);
                    }else if(softAtomCodeList.contains(atomCode)){
                        dto.setSoftQuantity(atomCount);
                    }
                }
                atomPriceImportDTOList.add(dto);
            }
        }

        //为sku设置spu编码，合伙人信息
        if(CollectionUtils.isNotEmpty(skuRowDataList)){
            for (ProductFlowInstanceSkuImportDTO skuImportDTO : skuRowDataList) {
                String skuCode = skuImportDTO.getSkuCode();
                SkuOfferingInfoExample skuExample = new SkuOfferingInfoExample().createCriteria()
                        .andOfferingCodeEqualTo(skuCode)
                        .andOfferingStatusEqualTo("1")
                        .andDeleteTimeIsNull()
                        .example();
                List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(skuExample);
                if(CollectionUtils.isEmpty(skuOfferingInfos)){
                    errMsg.add("有效的sku编码:"+skuCode+"在数据库不存在");
                }else {
                    SkuOfferingInfo skuOfferingInfo = skuOfferingInfos.get(0);
                    String cooperatorId = skuOfferingInfo.getCooperatorId();
                    if(StringUtils.isNotEmpty(cooperatorId) && !"-1".equals(cooperatorId)){
                        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
                        if(data4UserBaseAnswer == null || !"00000".equals(data4UserBaseAnswer.getStateCode())){
                            errMsg.add("规格编码"+skuCode+"查询合伙人信息失败");
                        }else {
                            Data4User data = data4UserBaseAnswer.getData();
                            if(data == null){
                                errMsg.add("规格编码"+skuCode+"查询合伙人信息为空");
                            }else {
                                skuImportDTO.setCooperateCompany(data.getPartnerName());
                                Boolean isPrimary = data.getIsPrimary();
                                if(isPrimary != null && isPrimary){
                                    //当前是主合伙人，未配置从
                                    skuImportDTO.setOrderMasterHandler(data.getPhone());
                                }else {
                                    //当前是从合伙人，查询主
                                    BaseAnswer<Data4User> primaryAnswer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
                                    if(primaryAnswer == null || !"00000".equals(primaryAnswer.getStateCode())){
                                        errMsg.add("规格编码"+skuCode+"查询主合伙人信息失败");
                                    }else {
                                        Data4User primaryAnswerData = primaryAnswer.getData();
                                        if(primaryAnswerData == null){
                                            errMsg.add("规格编码"+skuCode+"查询主合伙人信息为空");
                                        }else {
                                            skuImportDTO.setOrderMasterHandler(primaryAnswerData.getPhone());
                                            skuImportDTO.setOrderSlaveHandler(data.getPhone());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    skuImportDTO.setSpuCode(skuOfferingInfo.getSpuCode());
                }
            }
        }

        log.info("割接流程,sku数据整理完毕");

        //只处理包含在规格中的原子，这里可以不需要再为原子sheet单独查询spu和sku编码
        //为原子设置spu,sku编码(一个导入的原子可能对应多个数据库的原子)
        /*List<ProductFlowInstanceAtomImportDTO> atomImportDTOListWithSpuSku = new ArrayList<>();
        if(CollectionUtils.isEmpty(atomImportDTOList)){
            for (ProductFlowInstanceAtomImportDTO atomImportDTO : atomImportDTOList) {
                String atomCode = atomImportDTO.getAtomCode();
                AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample().createCriteria()
                        .andOfferingCodeEqualTo(atomCode)
                        .andDeleteTimeIsNull()
                        .example();
                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomExample);
                if(CollectionUtils.isEmpty(atomOfferingInfos)){
                    errMsg.add("有效的atom编码:"+atomCode+"在数据库不存在");
                }
                Map<String, List<AtomOfferingInfo>> atomCodeDataMap = atomOfferingInfos.stream().collect(Collectors.groupingBy(AtomOfferingInfo::getOfferingCode));
                for (Map.Entry<String, List<AtomOfferingInfo>> entry : atomCodeDataMap.entrySet()) {
                    String code = entry.getKey();
                    List<AtomOfferingInfo> atomOfferingInfoList = entry.getValue();
                    ProductFlowInstanceAtomImportDTO originalAtomImportDTO = atomDataMap.get(code);
                    List<ProductFlowInstanceAtomImportDTO> newAtomImportDTOList = atomOfferingInfoList.stream().map(a -> {
                        ProductFlowInstanceAtomImportDTO newAtomImportDTO = new ProductFlowInstanceAtomImportDTO();
                        BeanUtils.copyProperties(originalAtomImportDTO, newAtomImportDTO);
                        newAtomImportDTO.setSpuCode(a.getSpuCode());
                        newAtomImportDTO.setSkuCode(a.getSkuCode());
                        return newAtomImportDTO;
                    }).collect(Collectors.toList());
                    atomImportDTOListWithSpuSku.addAll(newAtomImportDTOList);
                }
            }
        }*/
        //为原子商品设置价格信息及数量信息,cmiot费用项id
        List<ProductFlowInstanceAtomImportDTO> atomImportDTOWithPrice = new ArrayList<>();
        Map<String,String> atomSetCmiotMap = new HashMap<>();
        skuRowDataList.forEach(sku -> {
            String skuCode = sku.getSkuCode();
            String spuCode = sku.getSpuCode();
            if(StringUtils.isNotEmpty(skuCode) && StringUtils.isNotEmpty(spuCode)){
                List<ProductFlowInstanceSkuAtomPriceImportDTO> atomPriceImportDTOList = sku.getAtomPriceImportDTOList();
                List<ProductFlowInstanceAtomImportDTO> atomDataWithPrice = atomPriceImportDTOList.stream().map(atom -> {
                    String s = atomSetCmiotMap.get(atom.getAtomCode());
                    ProductFlowInstanceAtomImportDTO atomImportDTO = atomDataMap.get(atom.getAtomCode());
                    //这里避免多个sku下相同的原子商品设置了相同的sku编码，所以建一个新的
                    ProductFlowInstanceAtomImportDTO newAtomImportDTO = new ProductFlowInstanceAtomImportDTO();
                    BeanUtils.copyProperties(atomImportDTO,newAtomImportDTO);
                    newAtomImportDTO.setSalePrice(atom.getSalePrice());
                    newAtomImportDTO.setSaleMaxPrice(atom.getSaleMaxPrice());
                    newAtomImportDTO.setSaleMinPrice(atom.getSaleMinPrice());
                    newAtomImportDTO.setAtomQuantity(atom.getAtomQuantity());
                    newAtomImportDTO.setSoftQuantity(atom.getSoftQuantity());
                    if(newAtomImportDTO == null || atom == null){
                        log.error("割接流程，数据出错,spu:{},sku:{}",spuCode,skuCode);
                    }
                    String cmiotCostProjectName = newAtomImportDTO.getCmiotCostProjectName();
                    if(StringUtils.isNotEmpty(cmiotCostProjectName)){
                        String shelfCmiotCostId = null;
                        try {
                            shelfCmiotCostId = productFlowInstanceService.getShelfCmiotCostIdByName(cmiotCostProjectName, null);
                            newAtomImportDTO.setCmiotCostProjectId(shelfCmiotCostId);
                            atomSetCmiotMap.put(atom.getAtomCode(),"1");
                        } catch (Exception e) {
                            errMsg.add("原子商品编码["+newAtomImportDTO.getAtomCode()+"]的cmiot费用项:["+cmiotCostProjectName+"]不存在");
                        }
                    }
                    newAtomImportDTO.setSpuCode(spuCode);
                    newAtomImportDTO.setSkuCode(skuCode);
                    return newAtomImportDTO;
                }).collect(Collectors.toList());
                atomImportDTOWithPrice.addAll(atomDataWithPrice);
            }
        });
        log.info("割接流程,原子商品价格信息设置完毕");

        if(CollectionUtils.isNotEmpty(spuImportDTOList)){
            spuImportDTOList.forEach(s -> {
                SpuOfferingInfoExample spuExample = new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeEqualTo(s.getSpuCode())
                        .andDeleteTimeIsNull()
                        .andOfferingStatusEqualTo("1")
                        .example();
                List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(spuExample);
                if(CollectionUtils.isEmpty(spuOfferingInfos)){
                    errMsg.add("有效的spu编码"+s.getSpuCode()+"在数据库不存在");
                }else {
                    SpuOfferingInfo spuOfferingInfo = spuOfferingInfos.get(0);
                    s.setSpuId(spuOfferingInfo.getId());
                    s.setUrl(spuOfferingInfo.getUrl());
                    spuDataMap.put(s.getSpuCode(),s);
                }
            });
        }else {
            errMsg.add("无有效spu数据");
        }
        if(CollectionUtils.isNotEmpty(skuRowDataList)){
            skuRowDataList.forEach(s -> {
                skuDataMap.put(s.getSkuCode(),s);
            });
            Map<String, List<ProductFlowInstanceSkuImportDTO>> spuSkuDataMap = skuRowDataList.stream().filter(s -> {return StringUtils.isNotEmpty(s.getSpuCode());}).collect(Collectors.groupingBy(ProductFlowInstanceSkuImportDTO::getSpuCode));
            for (Map.Entry<String, List<ProductFlowInstanceSkuImportDTO>> entry : spuSkuDataMap.entrySet()) {
                String spuCode = entry.getKey();
                List<ProductFlowInstanceSkuImportDTO> skuDataList = entry.getValue();
                List<String> skuCodeList = skuDataList.stream().map(s -> {
                    return s.getSkuCode();
                }).collect(Collectors.toList());
                spuSkuCodeMap.put(spuCode,skuCodeList);
            }
        }else {
            errMsg.add("无有效规格数据");
        }
        if(CollectionUtils.isNotEmpty(atomImportDTOWithPrice)){
            atomImportDTOWithPrice.forEach(a -> {
                String skuCode = a.getSkuCode();
                String spuCode = a.getSpuCode();
                String atomCode = a.getAtomCode();
                AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample().createCriteria()
                        .andSpuCodeEqualTo(spuCode)
                        .andSkuCodeEqualTo(skuCode)
                        .andOfferingCodeEqualTo(atomCode)
                        .andDeleteTimeIsNull()
                        .example();
                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomExample);
                if(CollectionUtils.isEmpty(atomOfferingInfos)){
                    errMsg.add("有效的原子商品在数据库不存在,spuCode:"+spuCode+",skuCode:"+skuCode+",atomCode:"+atomCode);
                }else {
                    skuAtomCodeDataMap.put(skuCode +"_"+ atomCode,a);
                }
            });
        }else {
            errMsg.add("无有效原子数据");
        }

        //2.按照sku维度组织数据  spu-sku-原子集合
        List<ProductFlowInstanceSpuSkuAtomCodeDTO> spuSkuAtomCodeDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuRowDataList)){
            spuSkuAtomCodeDTOList = skuRowDataList.stream().map(s -> {
                ProductFlowInstanceSpuSkuAtomCodeDTO productFlowInstanceSpuSkuAtomCodeDTO = new ProductFlowInstanceSpuSkuAtomCodeDTO();
                productFlowInstanceSpuSkuAtomCodeDTO.setSpuCode(s.getSpuCode());
                productFlowInstanceSpuSkuAtomCodeDTO.setSkuCode(s.getSkuCode());
                return productFlowInstanceSpuSkuAtomCodeDTO;
            }).collect(Collectors.toList());
        }
        //设置原子编码
        Map<String, List<ProductFlowInstanceAtomImportDTO>> skuAtomDataMap = atomImportDTOWithPrice.stream().filter(atom -> {return StringUtils.isNotEmpty(atom.getSkuCode());}).collect(Collectors.groupingBy(ProductFlowInstanceAtomImportDTO::getSkuCode));
        spuSkuAtomCodeDTOList.forEach(s -> {
            List<ProductFlowInstanceAtomImportDTO> productFlowInstanceAtomImportDTOS = skuAtomDataMap.get(s.getSkuCode());
            if(CollectionUtils.isNotEmpty(productFlowInstanceAtomImportDTOS)){
                List<String> atomCodeList = productFlowInstanceAtomImportDTOS.stream().map(p -> {
                    return p.getAtomCode();
                }).collect(Collectors.toList());
                s.setAtomCodeList(atomCodeList);
            }
        });
        log.info("割接流程,按照sku维度组织数据完毕");


        //3.根据柳青的文件，使用spu编码找到产品标准、产品类别(如果割接的文件在柳青的文件里找不到，或者在数据库找不到，都要报错)
        List<UsedProductExistDataDTO> list = EasyExcel.read(file.getInputStream(), UsedProductExistDataDTO.class, null)
                .sheet(0).headRowNumber(1).doReadSync();
        if(CollectionUtils.isEmpty(list)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"产品文件无有效数据");
        }
        Map<String,UsedProductExistDataDTO> spuProductInfoMap = new HashMap<>();
        list.forEach(s -> {
            spuProductInfoMap.put(s.getSpuCode(),s);
        });
        spuSkuAtomCodeDTOList.forEach(s -> {
            UsedProductExistDataDTO productInfo = spuProductInfoMap.get(s.getSpuCode());
            if(productInfo == null){
                errMsg.add("spu:"+s.getSpuCode()+",sku:"+s.getSkuCode()+"在OS产品文件中不存在");
            }else {
                s.setProductStandard(productInfo.getProductStandard());
                s.setProductType(productInfo.getProductType());
            }
        });
        log.info("割接流程,产品标准和产品类别设置完毕");

        //有异常的情况不插入数据，直接返回所有异常,节约接口响应时间
        if(CollectionUtils.isNotEmpty(errMsg)){
            log.error("割接出错:{}",String.join(System.getProperty("line.separator"),errMsg));
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,String.join(System.getProperty("line.separator"),errMsg));
        }

        // 获取流程信息
        ProductFlowExample productFlowExample = new ProductFlowExample();
        productFlowExample.createCriteria()
                .andStatusEqualTo(0)
                .andFlowTypeEqualTo(ProductFlowTypeEnum.PRODUCT_SHELF.code);
        List<ProductFlow> productFlowList = productFlowMapper.selectByExample(productFlowExample);
        if (CollectionUtils.isEmpty(productFlowList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"未配置有效的上架流程信息");
        }

        List<ProductFlow> provinceFlowList = productFlowList.stream().filter(p -> {
            return ProductOperateTypeEnum.PROVINCE.code.intValue() == p.getOperateType().intValue();
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(provinceFlowList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"未配置有效的分省运营类上架流程信息");
        }
        if(provinceFlowList.size() != 1){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"分省运营类上架流程信息大于一条");
        }
        List<ProductFlow> unificationFlowList = productFlowList.stream().filter(p -> {
            return ProductOperateTypeEnum.UNIFICATION.code.intValue() == p.getOperateType().intValue();
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(unificationFlowList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"未配置有效的统一运营类上架流程信息");
        }
        if(unificationFlowList.size() != 1){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"统一运营类上架流程信息大于一条");
        }
        ProductFlow provinceFlow = provinceFlowList.get(0);
        ProductFlow unificationFlow = unificationFlowList.get(0);

        //割接时间 2025-01-04 00:00:00
        Date dayBegin = DateTimeUtil.getFormatDate("2025-01-04 00:00:00", DateTimeUtil.DEFAULT_DATE_DEFAULT);
        List<ProductFlowInstance> productFlowInstanceList = new ArrayList<>();
        List<ProductFlowInstanceAtom> atomList = new ArrayList<>();
        List<ProductFlowInstanceSpu> spuList = new ArrayList<>();
        List<ProductFlowInstanceDirectory> directoryList = new ArrayList<>();
        List<NavigationInfo> navigationInfoList = new ArrayList<>();
        List<ProductFlowInstanceSku> skuList = new ArrayList<>();
        List<ProductFlowInstanceTask> taskList = new ArrayList<>();
        List<ProductFlowInstanceConfig> configList = new ArrayList<>();
        ProductFlow productFlow = null;

        for (ProductFlowInstanceSpuSkuAtomCodeDTO spuSkuAtomCodeDTO : spuSkuAtomCodeDTOList) {
            String spuCode = spuSkuAtomCodeDTO.getSpuCode();
            String skuCode = spuSkuAtomCodeDTO.getSkuCode();
            List<String> atomCodeList = spuSkuAtomCodeDTO.getAtomCodeList();
            if(CollectionUtils.isEmpty(atomCodeList)){
                log.error("无效的信息:spu:{},sku:{}",spuCode,skuCode);
                atomCodeList = new ArrayList<>();
            }
            String productStandardName = spuSkuAtomCodeDTO.getProductStandard();
            String productTypeName = spuSkuAtomCodeDTO.getProductType();
            if (StringUtils.isEmpty(spuCode)
                    || StringUtils.isEmpty(skuCode)
                    || CollectionUtils.isEmpty(atomCodeList)
                    || StringUtils.isEmpty(productStandardName)
                    || StringUtils.isEmpty(productTypeName)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"商品spu编码"+spuCode+"、商品规格编码"+skuCode+"、原子商品编码列表:"+String.join(",",atomCodeList)+"、产品标准:"+productStandardName+"和产品类别"+productTypeName+" 任意信息不能为空");
            }
            if (!ProductStandardEnum.containsName(productStandardName)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"产品标准:"+productStandardName+"不存在;");
            }
            if (!ProductTypeEnum.containsName(productTypeName)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"产品类别:"+productTypeName+"不存在;");
            }
            ProductFlowInstanceSpuImportDTO productFlowInstanceSpuImportDTO = spuDataMap.get(spuCode);
            if(productFlowInstanceSpuImportDTO == null){
                errMsg.add("规格商品sku:"+skuCode+"对应的spu:"+spuCode+"在spu的sheet不存在");
                continue;
            }
            //校验spu必填字段
            if(
                    StringUtils.isEmpty(productFlowInstanceSpuImportDTO.getShelfCatagoryId())||
                    StringUtils.isEmpty(productFlowInstanceSpuImportDTO.getFirstDirectoryId())||
                    StringUtils.isEmpty(productFlowInstanceSpuImportDTO.getSecondDirectoryId())||
                    StringUtils.isEmpty(productFlowInstanceSpuImportDTO.getSpuCode())||
                    StringUtils.isEmpty(productFlowInstanceSpuImportDTO.getSpuName())
            ){
                errMsg.add("spu编码"+spuCode+"在割接文件中缺少必填信息：上架类目"+productFlowInstanceSpuImportDTO.getShelfCatagoryId()+",导航一级目录"+productFlowInstanceSpuImportDTO.getFirstDirectoryId()+",导航二级目录"+productFlowInstanceSpuImportDTO.getSecondDirectoryId()+",商品编码"+productFlowInstanceSpuImportDTO.getSpuCode()+",商品名称"+productFlowInstanceSpuImportDTO.getSpuName()+" 都不能为空");
                continue;
            }
            List<CategoryInfo> categoryInfos = categoryInfoMapper.selectByExample(new CategoryInfoExample().createCriteria().andSpuIdEqualTo(productFlowInstanceSpuImportDTO.getSpuId()).example());
            if(CollectionUtils.isEmpty(categoryInfos)){
                errMsg.add("spuCode"+spuCode+"对应的类目信息不存在");
                continue;
            }
            String spuOfferingClass = categoryInfos.get(0).getOfferingClass();
            if(
                SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(spuOfferingClass)
            ){
                //根据产品要求，除了卡+x，软件服务 范式，其他都要割接
                continue;
            }
            Integer productStandard = ProductStandardEnum.getCode(productStandardName);
            Integer productType = ProductTypeEnum.getCode(spuSkuAtomCodeDTO.getProductType());
            Integer operateType = productFlowInstanceService.getOperateType(productType);
            productFlow = ProductOperateTypeEnum.PROVINCE.code.intValue() == operateType.intValue() ? provinceFlow : unificationFlow;

            //根据产品标准和产品类型，校验必填字段
           /* if(ProductStandardEnum.PLAN.code.intValue() == productStandard){
                //方案类分为 省框/省内，DICT
                if(ProductTypeEnum.PROVINCE_RANGE.code.intValue() == productType || ProductTypeEnum.PROVINCE.code.intValue() == productType){

                }else if(ProductTypeEnum.DICT.code.intValue() == productType){

                }

            }else if(ProductStandardEnum.STANDARD.code.intValue() == productStandard){
                //标准类分为 省框/省内，DICT，联合销售，合同履约
                if(ProductTypeEnum.PROVINCE_RANGE.code.intValue() == productType || ProductTypeEnum.PROVINCE.code.intValue() == productType){

                }else if(ProductTypeEnum.DICT.code.intValue() == productType){

                }else if(ProductTypeEnum.COOPERATE_SALE.code.intValue() == productType){

                }else if(ProductTypeEnum.CONTRACT.code.intValue() == productType){

                }
            }*/
            // 设置流程实例信息
            ProductFlowInstance productFlowInstance = setProductFlowInstance(productFlow, dayBegin);
            productFlowInstanceList.add(productFlowInstance);

            // 设置流程spu信息
            if(ProductTypeEnum.COOPERATE_SALE.code.intValue() == productType){
                //联合销售特有的字段，设置为"无"
                productFlowInstanceSpuImportDTO.setManageDepartment("无");
            }
            productFlowInstanceSpuImportDTO.setProductStandard(productStandard);
            productFlowInstanceSpuImportDTO.setProductType(productType);
            ProductFlowInstanceSpu productFlowInstanceSpu
                    = setProductFlowInstanceSpu(productFlow, productFlowInstance, productFlowInstanceSpuImportDTO, spuSkuAtomCodeDTO, dayBegin);
            spuList.add(productFlowInstanceSpu);
            //设置流程一二级导航信息,spu目录信息
            ProductFlowInstanceDirectory productFlowInstanceDirectory = setProductFlowInstanceDirectory(productFlowInstance.getId(),productFlowInstanceSpuImportDTO,dayBegin,navigationInfoList);
            directoryList.add(productFlowInstanceDirectory);

            // 设置流程sku信息
            ProductFlowInstanceSkuImportDTO skuImportDTO = skuDataMap.get(skuCode);
            //校验sku必填字段,根据产品类型
            if(
                    ProductTypeEnum.PROVINCE_RANGE.code.intValue() == productType ||
                    ProductTypeEnum.PROVINCE.code.intValue() == productType ||
                    ProductTypeEnum.DICT.code.intValue() == productType
            ){
                if(
                        StringUtils.isEmpty(skuImportDTO.getSkuCode())||
                        StringUtils.isEmpty(skuImportDTO.getSkuName())||
                        (StringUtils.isEmpty(skuImportDTO.getSaleBeId())&& StringUtils.isEmpty(skuImportDTO.getSaleLocationId()))||
                        StringUtils.isEmpty(skuImportDTO.getSlideImages())||
                        skuImportDTO.getSaleSinglePrice() == null
                ){
                    errMsg.add("DICT/省框/省内 的sku编码"+skuCode+"在割接文件中缺少必填信息：规格编码,规格商品名称,发布范围,规格多媒体信息,销售价格 都不能为空");
                    continue;
                }
            }else if(
                    ProductTypeEnum.COOPERATE_SALE.code.intValue() == productType
            ){
                if(
                        StringUtils.isEmpty(skuImportDTO.getSkuCode())||
                        StringUtils.isEmpty(skuImportDTO.getSkuName())||
                        (StringUtils.isEmpty(skuImportDTO.getSaleBeId())&& StringUtils.isEmpty(skuImportDTO.getSaleLocationId()))||
                        StringUtils.isEmpty(skuImportDTO.getSlideImages())||
                        skuImportDTO.getSaleSinglePrice() == null
                ){
                    errMsg.add("联合销售 的sku编码"+skuCode+"在割接文件中缺少必填信息：规格编码,规格商品名称,发布范围,规格多媒体信息,销售价格 都不能为空");
                    continue;
                }
            }else if(
                    ProductTypeEnum.CONTRACT.code.intValue() == productType
            ){
                if(
                        StringUtils.isEmpty(skuImportDTO.getSkuCode())||
                        StringUtils.isEmpty(skuImportDTO.getSkuName())||
                        (StringUtils.isEmpty(skuImportDTO.getSaleBeId())&& StringUtils.isEmpty(skuImportDTO.getSaleLocationId()))||
                        StringUtils.isEmpty(skuImportDTO.getSlideImages())||
                        skuImportDTO.getDirectoryPrice() == null
                ){
                    errMsg.add("合同履约 的sku编码"+skuCode+"在割接文件中缺少必填信息：规格编码,规格商品名称,发布范围,规格多媒体信息,目录价格 都不能为空");
                    continue;
                }
            }
            //不要求必填的价格字段，默认为0
            if(skuImportDTO.getSaleSinglePrice() == null){
                skuImportDTO.setSaleSinglePrice(0L);
            }
            if(skuImportDTO.getDirectoryPrice() == null){
                skuImportDTO.setDirectoryPrice(0L);
            }
            // 查询标准服务信息
            List<StandardServiceDO> byAtomCodeList = standardServiceMapperExt.findByAtomCodeList(skuCode, atomCodeList);
            if(CollectionUtils.isNotEmpty(byAtomCodeList)){
                List<StandardServiceDO> stdServiceList = new ArrayList<>();
                //去重复，每个字段都一样才算重复
                for (StandardServiceDO standardServiceDO : byAtomCodeList) {
                    if(!stdServiceList.contains(standardServiceDO)){
                        stdServiceList.add(standardServiceDO);
                    }
                }
                //逗号分隔
                List<String> nameList = stdServiceList.stream().map(s -> {
                    return s.getName();
                }).collect(Collectors.toList());
                List<String> departmentList = stdServiceList.stream().map(s -> {
                    return s.getDepartment();
                }).collect(Collectors.toList());
                List<String> propertyList = stdServiceList.stream().map(s -> {
                    return s.getProperty();
                }).collect(Collectors.toList());
                List<String> realProductList = stdServiceList.stream().map(s -> {
                    return s.getRealProductName();
                }).collect(Collectors.toList());
                StandardServiceDO standardServiceDO = new StandardServiceDO();
                standardServiceDO.setName(String.join(",",nameList));
                standardServiceDO.setDepartment(String.join(",",departmentList));
                standardServiceDO.setProperty(String.join(",",propertyList));
                standardServiceDO.setRealProductName(String.join(",",realProductList));

                if(ProductTypeEnum.DICT.code.intValue() == productType.intValue() && ProductStandardEnum.STANDARD.code == productStandard.intValue()){
                    skuImportDTO.setKeyCompomentName(standardServiceDO.getRealProductName());
                    skuImportDTO.setKeyComponentServiceInfo("无");
                    skuImportDTO.setStandardProductName(standardServiceDO.getName());
                    skuImportDTO.setStandardProductAttribute(standardServiceDO.getProperty());
                    skuImportDTO.setManageDepartment(standardServiceDO.getDepartment());
                    skuImportDTO.setStandardProductManager("无");
                    skuImportDTO.setSendContactPerson("无");
                    //设置规格的 销售价格，低价，上限价格。从原子上取，
                    setSkuPriceInfo(errMsg, skuCode, skuImportDTO,ProductTypeEnum.DICT.name);
                }
                if((ProductTypeEnum.PROVINCE_RANGE.code.intValue() == productType.intValue() || ProductTypeEnum.PROVINCE.code.intValue() == productType.intValue()) && ProductStandardEnum.STANDARD.code == productStandard.intValue() ){
                    skuImportDTO.setKeyCompomentName(standardServiceDO.getRealProductName());
                    skuImportDTO.setKeyComponentServiceInfo("无");
                    skuImportDTO.setStandardProductName(standardServiceDO.getName());
                    skuImportDTO.setStandardProductAttribute(standardServiceDO.getProperty());
                    skuImportDTO.setManageDepartment(standardServiceDO.getDepartment());
                    skuImportDTO.setStandardProductManager("无");
                    //设置规格的 销售价格，低价，上限价格。从原子上取，多个原子的价格信息不同就报错
                    setSkuPriceInfo(errMsg, skuCode, skuImportDTO,ProductTypeEnum.PROVINCE_RANGE.name);
                }
                //方案类(实际上只有DICT/省内/省框)
                if(ProductStandardEnum.PLAN.code.intValue() == productStandard.intValue()){
                    skuImportDTO.setKeyCompomentName(standardServiceDO.getRealProductName());
                    skuImportDTO.setKeyComponentServiceInfo("无");
                    skuImportDTO.setStandardProductName(standardServiceDO.getName());
                    skuImportDTO.setStandardProductAttribute(standardServiceDO.getProperty());
                    skuImportDTO.setManageDepartment(standardServiceDO.getDepartment());
                    skuImportDTO.setStandardProductManager("无");
                    skuImportDTO.setSendContactPerson("无");
                }

                if(ProductTypeEnum.COOPERATE_SALE.code.intValue() == productType.intValue()){
                    skuImportDTO.setKeyCompomentName(standardServiceDO.getRealProductName());
                    skuImportDTO.setKeyComponentServiceInfo("无");
                    skuImportDTO.setHasRemuneration("无");
                    skuImportDTO.setRemunerationPercent("无");
                    skuImportDTO.setStandardProductName(standardServiceDO.getName());
                    skuImportDTO.setStandardProductAttribute(standardServiceDO.getProperty());
                    skuImportDTO.setManageDepartment(standardServiceDO.getDepartment());//合作厂商名,商城订单处理人（主）,商城订单处理人（次） 已经在前面设置了
                    //文件的目录价格是 商品规格销售价
                    skuImportDTO.setSalePrice(skuImportDTO.getDirectoryPrice());
                    //为各环节接口人信息和商品套餐信息设置默认值
                    ProductFlowInstanceConfig productFlowInstanceConfig = new ProductFlowInstanceConfig();
                    productFlowInstanceConfig.setId(BaseServiceUtils.getId());
                    productFlowInstanceConfig.setCreateTime(dayBegin);
                    productFlowInstanceConfig.setUpdateTime(dayBegin);
                    productFlowInstanceConfig.setFlowId(productFlow.getId());
                    productFlowInstanceConfig.setFlowInstanceId(productFlowInstance.getId());
                    productFlowInstanceConfig.setBeforeSaleManager("无");
                    productFlowInstanceConfig.setSendContactPerson("无");
                    productFlowInstanceConfig.setInstallContactPerson("无");
                    productFlowInstanceConfig.setIotPackageContactPerson("无");
                    productFlowInstanceConfig.setSoftAuthContactPerson("无");
                    productFlowInstanceConfig.setAfterSaleContactPerson("无");
                    productFlowInstanceConfig.setAfterMarketRule("无");
                    productFlowInstanceConfig.setRepairContactInfo("无");
                    productFlowInstanceConfig.setReturnContactInfo("无");
                    productFlowInstanceConfig.setProductCompanyInfo("无");
                    productFlowInstanceConfig.setProductCommunicationMethod("无");
                    productFlowInstanceConfig.setIotPackageInfo("无");
                    productFlowInstanceConfig.setHardwareSendList("无");
                    productFlowInstanceConfig.setProductParamInfo("无");
                    productFlowInstanceConfig.setProductSendAddress("无");
                    productFlowInstanceConfig.setHardwareExpress("无");
                    productFlowInstanceConfig.setProductSendTimeInfo("无");
                    productFlowInstanceConfig.setProductUseCondition("无");
                    productFlowInstanceConfig.setSoftPlatformInfo("无");
                    productFlowInstanceConfig.setSoftPlatformDownloadInfo("无");
                    productFlowInstanceConfig.setAppInfo("无");
                    productFlowInstanceConfig.setAppDownloadInfo("无");
                    productFlowInstanceConfig.setInstallInfo("无");
                    configList.add(productFlowInstanceConfig);
                }
                if(ProductTypeEnum.CONTRACT.code.intValue() == productType.intValue()){
                    skuImportDTO.setKeyCompomentName(standardServiceDO.getRealProductName());
                    skuImportDTO.setKeyComponentServiceInfo("无");
                    //文件的目录价格是 省公司价格
                    skuImportDTO.setProvincePrice(skuImportDTO.getDirectoryPrice());

                    ProductFlowInstanceConfig productFlowInstanceConfig = new ProductFlowInstanceConfig();
                    productFlowInstanceConfig.setId(BaseServiceUtils.getId());
                    productFlowInstanceConfig.setCreateTime(dayBegin);
                    productFlowInstanceConfig.setUpdateTime(dayBegin);
                    productFlowInstanceConfig.setFlowId(productFlow.getId());
                    productFlowInstanceConfig.setFlowInstanceId(productFlowInstance.getId());
                    productFlowInstanceConfig.setStandardServiceName(standardServiceDO.getName());
                    productFlowInstanceConfig.setRealProductName(standardServiceDO.getRealProductName());
                    productFlowInstanceConfig.setProductProperty(standardServiceDO.getProperty());
                    productFlowInstanceConfig.setProductDepartment(standardServiceDO.getDepartment());
                    productFlowInstanceConfig.setServiceProviderName(skuImportDTO.getCooperateCompany());
                    productFlowInstanceConfig.setOrderPartnerMasterAccount(skuImportDTO.getOrderMasterHandler());
                    productFlowInstanceConfig.setOrderPartnerSlaveAccount(skuImportDTO.getOrderSlaveHandler());
                    configList.add(productFlowInstanceConfig);
                }

            }
            ProductFlowInstanceSku productFlowInstanceSku
                    = setProductFlowInstanceSku(productFlow, productFlowInstance, skuImportDTO, dayBegin);
            skuList.add(productFlowInstanceSku);
            skuFlowInstanceIdMap.put(skuCode,productFlowInstance.getId());

            // 设置流程原子信息
            if(CollectionUtils.isNotEmpty(atomCodeList)){
                if(ProductStandardEnum.PLAN.name.equals(productStandardName)){
                    //方案类是一个流程实例可对应多个流程原子信息；
                    for (String atomCode : atomCodeList) {
                        ProductFlowInstanceAtomImportDTO atomImportDTO = skuAtomCodeDataMap.get(skuCode + "_" + atomCode);
                        atomImportDTO.setServicePackageName("无");
                        atomImportDTO.setServiceContract("无");
                        atomImportDTO.setIotPurchaseContract("无");
                        atomImportDTO.setMaterialNum("无");
                        //销售价格，底价，最高价 已经在atomImportDTO中，这里无需设置
                        String saleOutOfPriceRange = skuDataMap.get(skuCode).getSaleOutOfPriceRange();
                        atomImportDTO.setSaleOutOfPriceRange(saleOutOfPriceRange);
                        //方案类：cmiot账目项必填
                        if(StringUtils.isEmpty(atomImportDTO.getCmiotCostProjectId())){
                            errMsg.add("原子商品编码:"+atomCode+"缺少CMIOT账目项");
                            continue;
                        }

                        ProductFlowInstanceAtom productFlowInstanceAtom = setProductFlowInstanceAtom(productFlow, productFlowInstance, atomImportDTO,dayBegin,productType, productFlowInstanceSku.getProvincePrice());
                        atomList.add(productFlowInstanceAtom);
                    }
                }else if(ProductStandardEnum.STANDARD.name.equals(productStandardName)){
                    //标准类是一个流程实例对应一个原子信息，如果有多个原子（应该是软硬件原子）需要合并为一个
                    List<ProductFlowInstanceAtomImportDTO> atomListFromSku = skuAtomDataMap.get(skuCode);
                    Long softTotalPrice = null;
                    Long hardTotalPrice = null;
                    Long hardSinglePrice = null;
                    Long softSinglePrice = null;
                    for (ProductFlowInstanceAtomImportDTO atomImportDTO : atomListFromSku) {
                        String atomCode = atomImportDTO.getAtomCode();
                        String softOrHard = atomCodeSoftOrHard.get(atomCode);
                        if(AtomOfferingClassEnum.S.getAtomOfferingClass().equals(softOrHard)){
                            //计算软件销售价,销售单价
                            ProductFlowInstanceAtomImportDTO softAtom = skuAtomCodeDataMap.get(skuCode + "_" + atomCode);

                            softSinglePrice = softAtom.getSalePrice();
                            softTotalPrice = softSinglePrice * softAtom.getSoftQuantity();
                        }else if(AtomOfferingClassEnum.H.getAtomOfferingClass().equals(softOrHard)){
                            //计算硬件销售价,销售单价
                            ProductFlowInstanceAtomImportDTO hardAtom = skuAtomCodeDataMap.get(skuCode + "_" + atomCode);
                            hardSinglePrice = hardAtom.getSalePrice();
                            hardTotalPrice = hardSinglePrice * hardAtom.getAtomQuantity();
                        }
                    }

                   /* if(
                            (productType.intValue() != ProductTypeEnum.CONTRACT.code.intValue()) &&
                            (productType.intValue() != ProductTypeEnum.COOPERATE_SALE.code.intValue()) &&
                            atomListFromSku.size() > 1
                    ){
                            errMsg.add("标准类sku编码"+skuCode+"不是联合销售/合同履约，只允许有一个原子商品信息");
                            continue;
                    }*/
                    ProductFlowInstanceAtomImportDTO mergedAtomDTO = new ProductFlowInstanceAtomImportDTO();
                    if((productType.intValue() != ProductTypeEnum.CONTRACT.code.intValue()) &&
                        (productType.intValue() != ProductTypeEnum.COOPERATE_SALE.code.intValue())){
                        mergedAtomDTO = atomListFromSku.get(0);
                    }else {
                        for (ProductFlowInstanceAtomImportDTO a : atomListFromSku) {
                            String atomCode = a.getAtomCode();
                            String softOrHard = atomCodeSoftOrHard.get(atomCode);
                            if(productType.intValue() == ProductTypeEnum.CONTRACT.code.intValue()){
                                //合同履约的软硬件字段设置
                                if(AtomOfferingClassEnum.S.getAtomOfferingClass().equals(softOrHard)){
                                    mergedAtomDTO.setSpuCode(a.getSpuCode());
                                    mergedAtomDTO.setSkuCode(a.getSkuCode());
                                    mergedAtomDTO.setSoftAtomName(a.getSoftAtomName());
                                    mergedAtomDTO.setSoftSettlePrice(a.getSoftSettlePrice());
                                    mergedAtomDTO.setSoftUnit(a.getSoftUnit());
                                    mergedAtomDTO.setSoftProductCode(a.getSoftProductCode());
                                    mergedAtomDTO.setSoftQuantity(a.getSoftQuantity());
                                    mergedAtomDTO.setCmiotCostProjectId(a.getCmiotCostProjectId());
                                    mergedAtomDTO.setSoftServiceContent(a.getSoftServiceContent());
                                    mergedAtomDTO.setNoSettlement(a.getNoSettlement());
                                    mergedAtomDTO.setSettlementDetailName(a.getSettlementDetailName());
                                    mergedAtomDTO.setDeliverPeriod(a.getDeliverPeriod());
                                }else if(AtomOfferingClassEnum.H.getAtomOfferingClass().equals(softOrHard)){
                                    mergedAtomDTO.setSpuCode(a.getSpuCode());
                                    mergedAtomDTO.setSkuCode(a.getSkuCode());
                                    mergedAtomDTO.setAtomName(a.getAtomName());
                                    mergedAtomDTO.setSettlePrice(a.getSettlePrice());
                                    mergedAtomDTO.setUnit(a.getUnit());
                                    mergedAtomDTO.setAtomQuantity(a.getAtomQuantity());
                                    mergedAtomDTO.setColor(a.getColor());
                                    mergedAtomDTO.setModel(a.getModel());
                                    mergedAtomDTO.setMaterialNum(a.getMaterialNum());
                                    mergedAtomDTO.setServiceContent(a.getServiceContent());
                                }
                            }
                            if(productType.intValue() == ProductTypeEnum.COOPERATE_SALE.code.intValue()){
                                //联合销售的软硬件字段设置
                                if(AtomOfferingClassEnum.S.getAtomOfferingClass().equals(softOrHard)){
                                    mergedAtomDTO.setSpuCode(a.getSpuCode());
                                    mergedAtomDTO.setSkuCode(a.getSkuCode());
                                    mergedAtomDTO.setSoftAtomName(a.getSoftAtomName());
                                }else if(AtomOfferingClassEnum.H.getAtomOfferingClass().equals(softOrHard)){
                                    mergedAtomDTO.setSpuCode(a.getSpuCode());
                                    mergedAtomDTO.setSkuCode(a.getSkuCode());
                                    mergedAtomDTO.setAtomName(a.getAtomName());
                                    mergedAtomDTO.setSettlePrice(a.getSettlePrice());
                                    mergedAtomDTO.setUnit(a.getUnit());
                                    mergedAtomDTO.setColor(a.getColor());
                                    mergedAtomDTO.setModel(a.getModel());
                                    mergedAtomDTO.setMaterialNum(a.getMaterialNum());
                                }
                            }
                        }
                    }

                    //标准类：合同履约，省框/省内，DICT 的cmiot账目项必填
                    if(
                            (
                                    ProductTypeEnum.CONTRACT.code.intValue() == productType ||
                                    ProductTypeEnum.PROVINCE.code.intValue() == productType ||
                                    ProductTypeEnum.PROVINCE_RANGE.code.intValue() == productType ||
                                    ProductTypeEnum.DICT.code.intValue() == productType
                            )&&StringUtils.isEmpty(mergedAtomDTO.getCmiotCostProjectId())
                    ){
                        //这里不好区分软硬件，直接报错规格
                        errMsg.add("规格商品编码"+skuCode+"的原子商品缺少CMIOT账目项");
                        continue;
                    }
                    if(ProductTypeEnum.DICT.code.intValue() == productType){
                        mergedAtomDTO.setServicePackageName("无");
                        mergedAtomDTO.setServiceContract("无");
                        mergedAtomDTO.setIotPurchaseContract("无");
                        mergedAtomDTO.setMaterialNum("无");
                    }
                    if(ProductTypeEnum.PROVINCE_RANGE.code.intValue() == productType || ProductTypeEnum.PROVINCE.code.intValue() == productType){
                        mergedAtomDTO.setSettlePriceCheck(0L);
                        mergedAtomDTO.setProvincePurchaseContract("无");
                        mergedAtomDTO.setIotPurchaseContract("无");
                        mergedAtomDTO.setMaterialNum("无");
                    }
                    if(ProductTypeEnum.COOPERATE_SALE.code.intValue() == productType){
                        mergedAtomDTO.setSoftTotalPrice(softTotalPrice);
                        mergedAtomDTO.setHardwarePrice(hardSinglePrice);
                    }
                    if(ProductTypeEnum.CONTRACT.code.intValue() == productType){
                        mergedAtomDTO.setSoftTotalPrice(softTotalPrice);
                        mergedAtomDTO.setSoftPrice(softSinglePrice);
                        mergedAtomDTO.setSalePrice(hardTotalPrice);
                    }

                    ProductFlowInstanceAtom productFlowInstanceAtom = setProductFlowInstanceAtom(productFlow, productFlowInstance,mergedAtomDTO,dayBegin,productType,productFlowInstanceSku.getProvincePrice());
                    atomList.add(productFlowInstanceAtom);
                }
            }

            //设置流程审核信息
            ProductFlowStepExample stepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlow.getId()).example();
            stepExample.orderBy("step_index ASC");
            List<ProductFlowStep> stepList = productFlowStepMapper.selectByExample(stepExample);
            if(CollectionUtils.isNotEmpty(stepList)){
                for (ProductFlowStep productFlowStep : stepList) {
                    ProductFlowInstanceTask task = setProductFlowInstanceTask(productFlow,productFlowInstance,productFlowStep,dayBegin);
                    taskList.add(task);
                }
                //流程已经结束，手动添加结束步骤
                ProductFlowInstanceTask flowInstanceTask = new ProductFlowInstanceTask();
                flowInstanceTask.setId(BaseServiceUtils.getId());
                flowInstanceTask.setFlowId(productFlowInstance.getFlowId());
                flowInstanceTask.setFlowInstanceId(productFlowInstance.getId());
                flowInstanceTask.setStepId("-1");
                flowInstanceTask.setStepName("结束");
                flowInstanceTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
                flowInstanceTask.setCreateTime(dayBegin);
                flowInstanceTask.setUpdateTime(dayBegin);
                taskList.add(flowInstanceTask);
            }
        }
        //有错误，抛出全部错误信息，不入库
        if(CollectionUtils.isNotEmpty(errMsg)){
            log.error("割接出错:{}",String.join(System.getProperty("line.separator"),errMsg));
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,String.join(System.getProperty("line.separator"),errMsg));
        }
        log.info("割接流程,准备入库");
        //分批次入库
        try {
            if (CollectionUtils.isNotEmpty(productFlowInstanceList)){
                scrollBatchInsert(productFlowInstanceMapper.getClass().getSimpleName(),productFlowInstanceList);
            }
            log.info("割接流程,productFlowInstance入库完毕");
            if (CollectionUtils.isNotEmpty(spuList)){
                scrollBatchInsert(productFlowInstanceSpuMapper.getClass().getSimpleName(),spuList);
            }
            log.info("割接流程,productFlowInstanceSpu入库完毕");
            if (CollectionUtils.isNotEmpty(directoryList)){
                scrollBatchInsert(productFlowInstanceDirectoryMapper.getClass().getSimpleName(),directoryList);
            }
            log.info("割接流程,productFlowInstanceDirectoryList入库完毕");
            if (CollectionUtils.isNotEmpty(navigationInfoList)){
                scrollBatchInsert(navigationInfoMapper.getClass().getSimpleName(),navigationInfoList);
            }
            log.info("割接流程,directoryList入库完毕");
            if (CollectionUtils.isNotEmpty(skuList)){
                scrollBatchInsert(productFlowInstanceSkuMapper.getClass().getSimpleName(),skuList);
            }
            log.info("割接流程,productFlowInstanceSku入库完毕");
            if (CollectionUtils.isNotEmpty(atomList)){
                scrollBatchInsert(productFlowInstanceAtomMapper.getClass().getSimpleName(),atomList);
            }
            log.info("割接流程,productFlowInstanceAtom入库完毕");
            if (CollectionUtils.isNotEmpty(taskList)){
                scrollBatchInsert(productFlowInstanceTaskMapper.getClass().getSimpleName(),taskList);
            }
            log.info("割接流程,productFlowInstanceTask入库完毕");
            if (CollectionUtils.isNotEmpty(configList)){
                scrollBatchInsert(productFlowInstanceConfigMapper.getClass().getSimpleName(),configList);
            }
            log.info("割接流程,productFlowInstanceConfig入库完毕");
        } catch (Exception e) {
            log.error("割接出错",e);
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,e.getMessage());
        }
        log.info("割接流程,流程信息入库完毕");
        log.info("割接流程,开始设置附件信息");
        List<ProductFlowInstanceAttachment> attachmentList = new ArrayList<>();
        //入库附件信息
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        //保存文件名和对应url的map
        Map<String,String> fileNameUrlMap = new HashMap<>();
        //保存文件名和对应key的map
        Map<String,String> fileNameKeyMap = new HashMap<>();
        FileWriter fileWriter = null;
        try {
            log.info("割接流程,下载sftp地址，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpProductFlowAttachmentPath);
            if (sftpUtil.login()) {
                log.info("割接流程,sftp连接成功");
                Map<String,String> tempFileAndRealFileMap = new HashMap<>();
                for (Map.Entry<String, List<String>> entry : spuSkuCodeMap.entrySet()) {
                    String spuCode = entry.getKey();
                    List<String> skuCodeList = entry.getValue();
                    ProductFlowInstanceSpuImportDTO spuImportDTO = spuDataMap.get(spuCode);
                    //spu sheet中的附件，需要为下面的所有sku保存
                    String productDetailFiles = spuImportDTO.getProductDetailFiles();
                    if(StringUtils.isNotEmpty(productDetailFiles)){
                        //同时保存 商品详情页素材（移动端） 和 实质性产品图片（移动端）
                        Integer fileType = ProductFlowAttachmentTypeEnum.PRODUCT_DETAIL_MATERIAL.code;
                        String[] fileArr = productDetailFiles.split(",");
                        uploadAttachment(tempFileAndRealFileMap,fileNameKeyMap,fileNameUrlMap,sftpUtil,errMsg,skuFlowInstanceIdMap, dayBegin, attachmentList, fileType, fileArr,skuCodeList.toArray(new String[skuCodeList.size()]));

                        Integer fileType2 = ProductFlowAttachmentTypeEnum.REAL_PRODUCT_IMAGE.code;
                        String[] fileArr2 = productDetailFiles.split(",");
                        uploadAttachment(tempFileAndRealFileMap,fileNameKeyMap,fileNameUrlMap,sftpUtil,errMsg,skuFlowInstanceIdMap, dayBegin, attachmentList, fileType2, fileArr2,skuCodeList.toArray(new String[skuCodeList.size()]));
                    }else {
                        //经过产品周文静确定，割接文件中spu附件为空的都是测试商品，设置为默认的白板图片
                        setDefaultAttachment(tempFileAndRealFileMap,ProductFlowAttachmentTypeEnum.PRODUCT_DETAIL_MATERIAL.code,attachmentList,skuFlowInstanceIdMap, dayBegin, skuCodeList);
                        setDefaultAttachment(tempFileAndRealFileMap,ProductFlowAttachmentTypeEnum.REAL_PRODUCT_IMAGE.code,attachmentList,skuFlowInstanceIdMap, dayBegin, skuCodeList);
                    }
                    String aftermarketRuleFiles = spuImportDTO.getAftermarketRuleFiles();
                    if(StringUtils.isNotEmpty(aftermarketRuleFiles)){
                        Integer fileType = ProductFlowAttachmentTypeEnum.AFTER_MARKET_IMAGE.code;
                        String[] fileArr = aftermarketRuleFiles.split(",");
                        uploadAttachment(tempFileAndRealFileMap,fileNameKeyMap,fileNameUrlMap,sftpUtil,errMsg,skuFlowInstanceIdMap, dayBegin, attachmentList, fileType, fileArr,skuCodeList.toArray(new String[skuCodeList.size()]));
                    }else {
                        setDefaultAttachment(tempFileAndRealFileMap, ProductFlowAttachmentTypeEnum.AFTER_MARKET_IMAGE.code,attachmentList,skuFlowInstanceIdMap, dayBegin, skuCodeList);
                    }
                    //多媒体资源没有空的，不需要设置默认图片
                    String headerImages = spuImportDTO.getHeaderImages();
                    if(StringUtils.isNotEmpty(headerImages)){
                        Integer fileType = ProductFlowAttachmentTypeEnum.HEAD_IMAGE.code;
                        String[] fileArr = headerImages.split(",");
                        uploadAttachment(tempFileAndRealFileMap,fileNameKeyMap,fileNameUrlMap,sftpUtil,errMsg,skuFlowInstanceIdMap, dayBegin, attachmentList, fileType, fileArr,skuCodeList.toArray(new String[skuCodeList.size()]));
                    }

                    //sku的附件， 需要为各自的sku保存
                    for (String skuCode : skuCodeList) {
                        ProductFlowInstanceSkuImportDTO skuImportDTO = skuDataMap.get(skuCode);
                        String slideImages = skuImportDTO.getSlideImages();
                        if(StringUtils.isNotEmpty(slideImages)){
                            Integer fileType = ProductFlowAttachmentTypeEnum.SLIDE_IMAGE.code;
                            String[] fileArr = slideImages.split(",");
                            uploadAttachment(tempFileAndRealFileMap,fileNameKeyMap,fileNameUrlMap,sftpUtil,errMsg,skuFlowInstanceIdMap, dayBegin, attachmentList, fileType, fileArr,skuCode);
                        }
                    }
                }
                //将虚拟文件名和真实文件名的映射，存入本地文件
                File tempNameAndRealName = new File("tempNameAndRealName.txt");
                fileWriter = new FileWriter(tempNameAndRealName);
                for (Map.Entry<String, String> entry : tempFileAndRealFileMap.entrySet()) {
                    String tempName = entry.getKey();
                    String realName = entry.getValue();
                    fileWriter.write(tempName+","+realName);
                    fileWriter.write(System.getProperty("line.separator"));
                }
                fileWriter.close();

            }
        }catch (Exception e){
            errMsg.add("割接流程,下载附件过程中发生异常:"+e.getMessage());
            log.error("割接流程,下载附件过程中发生异常",e);
        }finally {
            sftpUtil.logout();
            if(fileWriter != null){
                fileWriter.close();
            }
        }

        if(CollectionUtils.isNotEmpty(errMsg)){
            log.error("割接出错:{}",String.join(System.getProperty("line.separator"),errMsg));
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,String.join(System.getProperty("line.separator"),errMsg));
        }
        log.info("割接流程,附件准备入库");
        if(CollectionUtils.isNotEmpty(attachmentList)){
            scrollBatchInsert(productFlowInstanceAttachmentMapper.getClass().getSimpleName(),attachmentList);
        }
        log.info("割接流程,附件入库完毕，全部结束");

        return BaseAnswer.success(null);
    }

    private ProductFlowInstanceDirectory setProductFlowInstanceDirectory(String flowInstanceId, ProductFlowInstanceSpuImportDTO productFlowInstanceSpuImportDTO, Date dayBegin, List<NavigationInfo> navigationInfoList) {
        ProductFlowInstanceDirectory productFlowInstanceDirectory = new ProductFlowInstanceDirectory();
        productFlowInstanceDirectory.setCreateTime(dayBegin);
        productFlowInstanceDirectory.setUpdateTime(dayBegin);
        productFlowInstanceDirectory.setId(BaseServiceUtils.getId());
        productFlowInstanceDirectory.setFlowInstanceId(flowInstanceId);
        productFlowInstanceDirectory.setFirstDirectoryId(productFlowInstanceSpuImportDTO.getFirstDirectoryId());
        productFlowInstanceDirectory.setSecondDirectoryId(productFlowInstanceSpuImportDTO.getSecondDirectoryId());

        NavigationInfo navigationInfo = new NavigationInfo();
        navigationInfo.setId(BaseServiceUtils.getId());
        navigationInfo.setSpuOfferingCode(productFlowInstanceSpuImportDTO.getSpuCode());
        navigationInfo.setLevel1NavigationCode(productFlowInstanceSpuImportDTO.getFirstDirectoryId());
        navigationInfo.setLevel2NavigationCode(productFlowInstanceSpuImportDTO.getSecondDirectoryId());
        navigationInfoList.add(navigationInfo);
        return productFlowInstanceDirectory;
    }

  /*  public static void main(String[] args) throws IOException {
        Map<String,String> tempFileAndRealFileMap = new HashMap<>();
        String fileName = "file1";
        tempFileAndRealFileMap.put("temp_"+md5(fileName),fileName);

        String fileName2 = "file1";
        tempFileAndRealFileMap.put("temp_"+md5(fileName2),fileName2);

        String fileName3 = "file3.txt";
        tempFileAndRealFileMap.put("temp_"+md5(fileName3),fileName3);

        File tempNameAndRealName = new File("temptempNameAndRealName.txt");
        FileWriter fileWriter = new FileWriter(tempNameAndRealName);
        for (Map.Entry<String, String> entry : tempFileAndRealFileMap.entrySet()) {
            String tempName = entry.getKey();
            String realName = entry.getValue();
            fileWriter.write(tempName+","+realName);
            fileWriter.write(System.getProperty("line.separator"));
        }
        fileWriter.close();
    }*/

    private void setDefaultAttachment(Map<String, String> tempFileAndRealFileMap, Integer fileType, List<ProductFlowInstanceAttachment> attachmentList, Map<String, String> skuFlowInstanceIdMap, Date dayBegin, List<String> skuCodeList) throws Exception {
        String fileName = "spu默认附件.png";
        String tempFileName = "temp_"+md5(fileName);
        tempFileAndRealFileMap.put(tempFileName,fileName);

        /*Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(DEFAULT_ATTACHMENT_KEY);
        if(entries == null || entries.isEmpty()){
            //默认图片未上传，需要上传到对象存储
            ClassPathResource classPathResource = new ClassPathResource("productflow/spu默认附件.png");
            InputStream inputStream = classPathResource.getInputStream();
            byte[] bytes = inputStream2ByteArray(inputStream);

            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setBytes(bytes);
            byteArrayUpload.setCover(true);
            byteArrayUpload.setFileName(CommonConstant.PRODUCT_FLOW_ATTACHMENT_PATH+"spu默认附件.png");
            BaseAnswer<UpResult> upResultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            if (upResultBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
                stringRedisTemplate.opsForHash().put(DEFAULT_ATTACHMENT_KEY,"fileKey",upResultBaseAnswer.getData().getKey());
                stringRedisTemplate.opsForHash().put(DEFAULT_ATTACHMENT_KEY,"fileUrl",upResultBaseAnswer.getData().getOuterUrl());
            } else {
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"上传默认SPU图片结果失败:"+upResultBaseAnswer.getMessage());
            }
        }
        entries = stringRedisTemplate.opsForHash().entries(DEFAULT_ATTACHMENT_KEY);
        String fileKey = (String) entries.get("fileKey");
        String fileUrl = (String) entries.get("fileUrl");*/
        for (String skuCode : skuCodeList) {
            ProductFlowInstanceAttachment flowInstanceAttachment = new ProductFlowInstanceAttachment();
            flowInstanceAttachment.setId(BaseServiceUtils.getId());
            flowInstanceAttachment.setFileName(tempFileName);
            flowInstanceAttachment.setType(fileType);
            String flowInstanceId = skuFlowInstanceIdMap.get(skuCode);
            flowInstanceAttachment.setFlowInstanceId(flowInstanceId);
            flowInstanceAttachment.setCreateTime(dayBegin);
            flowInstanceAttachment.setUpdateTime(dayBegin);
/*            flowInstanceAttachment.setFileUrl(fileUrl);
            flowInstanceAttachment.setFileKey(fileKey);*/
            attachmentList.add(flowInstanceAttachment);
        }
    }

    private void setSkuPriceInfo(List<String> errMsg, String skuCode, ProductFlowInstanceSkuImportDTO skuImportDTO, String productTypeName) {
        List<ProductFlowInstanceSkuAtomPriceImportDTO> atomPriceImportDTOList = skuImportDTO.getAtomPriceImportDTOList();
        if(CollectionUtils.isNotEmpty(atomPriceImportDTOList)){
            if(atomPriceImportDTOList.size() == 1){
                ProductFlowInstanceSkuAtomPriceImportDTO dto = atomPriceImportDTOList.get(0);
                skuImportDTO.setSalePrice(dto.getSalePrice());
                skuImportDTO.setSaleMinPrice(dto.getSaleMinPrice());
                skuImportDTO.setSaleMaxPrice(dto.getSaleMaxPrice());
            }else {
                //多个原子的价格信息不同就报错
                Set<String> atomPriceSet = new HashSet<>();
                for (ProductFlowInstanceSkuAtomPriceImportDTO dto : atomPriceImportDTOList) {
                    atomPriceSet.add(dto.getSalePrice()+"_"+dto.getSaleMaxPrice()+"_"+dto.getSaleMinPrice());
                }
                if(atomPriceSet.size() > 1){
                    errMsg.add(productTypeName+"规格:"+skuCode+"对应的原子销售价格，底价，上限价格信息大于1条，无法为规格取值");
                }else {
                    String atomPrice = new ArrayList<>(atomPriceSet).get(0);
                    String[] priceArr = atomPrice.split("_");
                    skuImportDTO.setSalePrice(Long.valueOf(priceArr[0]));
                    skuImportDTO.setSaleMaxPrice(Long.valueOf(priceArr[1]));
                    skuImportDTO.setSaleMinPrice(Long.valueOf(priceArr[2]));
                }
            }
        }
    }

    public static String md5(String str){
        // 加密后的16进制字符串
        String hexStr = "";
        try {
            // 此 MessageDigest 类为应用程序提供信息摘要算法的功能
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            // 转换为MD5码
            byte[] digest = md5.digest(str.getBytes("utf-8"));
            hexStr = ByteUtils.toHexString(digest);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hexStr;
    }

    /**
     * 从商城的FTP下载图片，存储到OS对象存储，并添加到附件集合
     */
    private void uploadAttachment(Map<String,String> tempFileAndRealFileMap,Map<String,String> fileNameKeyMap,Map<String,String> fileNameUrlMap,SFTPUtil sftpUtil,List<String> errMsg,Map<String, String> skuFlowInstanceIdMap, Date dayBegin, List<ProductFlowInstanceAttachment> attachmentList, Integer fileType, String[] fileArr, String... skuCodeList) {
        for (String fileName : fileArr) {
            for (String skuCode : skuCodeList) {
                //避免附件下载失败，这里使用虚拟附件信息，并且将虚拟附件名称和实际附件名称映射起来，保存到文件中,后续由文杰下载实际附件并根据映射fileName,fileKey,fileUrl 更新到数据库
                String tempFileName = "temp_"+md5(fileName);
                tempFileAndRealFileMap.put(tempFileName,fileName);

                ProductFlowInstanceAttachment flowInstanceAttachment = new ProductFlowInstanceAttachment();
                flowInstanceAttachment.setId(BaseServiceUtils.getId());
                flowInstanceAttachment.setFileName(tempFileName);
                flowInstanceAttachment.setType(fileType);
                String flowInstanceId = skuFlowInstanceIdMap.get(skuCode);
                flowInstanceAttachment.setFlowInstanceId(flowInstanceId);
                flowInstanceAttachment.setCreateTime(dayBegin);
                flowInstanceAttachment.setUpdateTime(dayBegin);
                attachmentList.add(flowInstanceAttachment);

               /* try {


                    String fileUrl = fileNameUrlMap.get(fileName);
                    String fileKey = fileNameKeyMap.get(fileName);
                    if(fileUrl != null && fileKey != null){
                        //已经下载和上传过的文件，不再下载和上传
                        flowInstanceAttachment.setFileUrl(fileUrl);
                        flowInstanceAttachment.setFileKey(fileKey);
                        attachmentList.add(flowInstanceAttachment);
                    }else {
                        log.info("割接流程，开始下载文件:{}",fileName);
                        byte[] bytes = sftpUtil.download(sftpProductFlowAttachmentPath,fileName);
                        log.info("割接流程，完成下载文件:{}",fileName);
                        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
                        byteArrayUpload.setBytes(bytes);
                        byteArrayUpload.setCover(true);
                        byteArrayUpload.setFileName(CommonConstant.PRODUCT_FLOW_ATTACHMENT_PATH+skuCode+"/"+fileName);
                        BaseAnswer<UpResult> upResultBaseAnswer = storageService.uploadByte(byteArrayUpload);
                        log.info("割接流程，上传文件结果:{}", JSON.toJSONString(upResultBaseAnswer));
                        if (upResultBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
                            flowInstanceAttachment.setFileUrl(upResultBaseAnswer.getData().getOuterUrl());
                            flowInstanceAttachment.setFileKey(upResultBaseAnswer.getData().getKey());
                            fileNameUrlMap.put(fileName,upResultBaseAnswer.getData().getOuterUrl());
                            fileNameKeyMap.put(fileName,upResultBaseAnswer.getData().getKey());
                            attachmentList.add(flowInstanceAttachment);
                        } else {
                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"上传图片结果失败:"+upResultBaseAnswer.getMessage());
                        }
                    }
                } catch (Exception e) {
                    errMsg.add("文件:{"+fileName+"}下载失败:"+e.getMessage());
                }*/
            }
        }
    }

    /**
     * 分批次插入
     */
    private void scrollBatchInsert(Object mapperName,List<?> data){
        int start = 0;
        int size = 500;
        while (true){
            int end = (start+size) <= data.size() ? (start+size) : data.size();
            List<?> subList = data.subList(start, end);
            if(CollectionUtils.isNotEmpty(subList)){
                if(mapperName.equals(productFlowInstanceMapper.getClass().getSimpleName())){
                    List<ProductFlowInstance> list = (List<ProductFlowInstance>)subList;
                    productFlowInstanceMapper.batchInsert(list);
                }else if(mapperName.equals(productFlowInstanceSpuMapper.getClass().getSimpleName())){
                    List<ProductFlowInstanceSpu> list = (List<ProductFlowInstanceSpu>)subList;
                    productFlowInstanceSpuMapper.batchInsert(list);
                }else if(mapperName.equals(productFlowInstanceDirectoryMapper.getClass().getSimpleName())){
                    List<ProductFlowInstanceDirectory> list = (List<ProductFlowInstanceDirectory>)subList;
                    productFlowInstanceDirectoryMapper.batchInsert(list);
                }else if(mapperName.equals(navigationInfoMapper.getClass().getSimpleName())){
                    List<NavigationInfo> list = (List<NavigationInfo>)subList;
                    navigationInfoMapper.batchInsert(list);
                }else if(mapperName.equals(productFlowInstanceSkuMapper.getClass().getSimpleName())){
                    List<ProductFlowInstanceSku> list = (List<ProductFlowInstanceSku>)subList;
                    productFlowInstanceSkuMapper.batchInsert(list);
                }else if(mapperName.equals(productFlowInstanceAtomMapper.getClass().getSimpleName())){
                    List<ProductFlowInstanceAtom> list = (List<ProductFlowInstanceAtom>)subList;
                    productFlowInstanceAtomMapper.batchInsert(list);
                }else if(mapperName.equals(productFlowInstanceTaskMapper.getClass().getSimpleName())){
                    List<ProductFlowInstanceTask> list = (List<ProductFlowInstanceTask>)subList;
                    productFlowInstanceTaskMapper.batchInsert(list);
                }else if(mapperName.equals(productFlowInstanceAttachmentMapper.getClass().getSimpleName())){
                    List<ProductFlowInstanceAttachment> list = (List<ProductFlowInstanceAttachment>)subList;
                    productFlowInstanceAttachmentMapper.batchInsert(list);
                }else if(mapperName.equals(productFlowInstanceConfigMapper.getClass().getSimpleName())){
                    List<ProductFlowInstanceConfig> list = (List<ProductFlowInstanceConfig>)subList;
                    productFlowInstanceConfigMapper.batchInsert(list);
                }
                else {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"不支持的mapper类型:"+ mapperName);
                }
            }
            if(subList.size() < size){
                break;
            }
            start += size;
            log.info("割接流程，流程信息分批入库中");
        }
    }


    private ProductFlowInstanceTask setProductFlowInstanceTask(ProductFlow productFlow,
                                                               ProductFlowInstance productFlowInstance,
                                                               ProductFlowStep productFlowStep,
                                                               Date now) {
        ProductFlowInstanceTask task = new ProductFlowInstanceTask();
        task.setId(BaseServiceUtils.getId());
        task.setFlowInstanceId(productFlowInstance.getId());
        task.setFlowId(productFlow.getId());
        task.setStepId(productFlowStep.getId());
        task.setStepName(productFlowStep.getStepName());
        task.setAssigneeName("存量导入");
        task.setOptions("存量导入");
        task.setHandleStatus(ProductFlowHandleStatusEnum.PASS.code);
        task.setHandleTime(now);
        task.setCreateTime(now);
        task.setUpdateTime(now);
        return task;
    }

    /**
     * 设置产品流程实例信息
     * @param productFlow
     * @param date
     * @return
     */
    private ProductFlowInstance setProductFlowInstance(ProductFlow productFlow,Date date){
        String flowInstanceNumber = productFlowUtil.generateFlowInstanceNumber(1);

        ProductFlowInstance productFlowInstance = new ProductFlowInstance();
        productFlowInstance.setId(BaseServiceUtils.getId());
        productFlowInstance.setFlowId(productFlow.getId());
        productFlowInstance.setFlowInstanceNumber(flowInstanceNumber);
        productFlowInstance.setCreatorName("存量导入");
        productFlowInstance.setCurrentStepId("-1");
        productFlowInstance.setStatus(1);
        productFlowInstance.setCanEdit(false);
        productFlowInstance.setCanCancel(false);
        productFlowInstance.setCanAudit(false);
        productFlowInstance.setCanConfig(false);
        productFlowInstance.setCreateTime(date);
        productFlowInstance.setUpdateTime(date);
        return productFlowInstance;
    }

    /**
     * 设置商品流程原子实例
     */
    private ProductFlowInstanceAtom setProductFlowInstanceAtom(ProductFlow productFlow,
                                                               ProductFlowInstance productFlowInstance,
                                                               ProductFlowInstanceAtomImportDTO atomImportDTO,
                                                               Date date, Integer productType, Long provincePrice){
        ProductFlowInstanceAtom productFlowInstanceAtom = new ProductFlowInstanceAtom();
        productFlowInstanceAtom.setId(BaseServiceUtils.getId());
        productFlowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
        productFlowInstanceAtom.setFlowId(productFlow.getId());
        BeanUtils.copyProperties(atomImportDTO,productFlowInstanceAtom);
        //自动计算销售单价和销售价格(合同履约类)
        if(ProductTypeEnum.CONTRACT.code.intValue() == productType){
            if (provincePrice != null && atomImportDTO.getSoftQuantity() != null) {
                //销售单价算出来不足1分钱的部分算1分钱
                BigDecimal decimal = new BigDecimal((double)provincePrice/1000 / atomImportDTO.getSoftQuantity() + "");
                long softPrice = (long)(decimal.setScale(2, RoundingMode.CEILING).doubleValue() * 1000L);
                productFlowInstanceAtom.setSoftPrice(softPrice);
                productFlowInstanceAtom.setSoftTotalPrice(provincePrice);
            }
        }
        productFlowInstanceAtom.setCreateTime(date);
        productFlowInstanceAtom.setUpdateTime(date);
        return productFlowInstanceAtom;
    }

    /**
     * 设置商品流程spu信息
     */
    private ProductFlowInstanceSpu setProductFlowInstanceSpu(ProductFlow productFlow,
                                                             ProductFlowInstance productFlowInstance,
                                                             ProductFlowInstanceSpuImportDTO spuImportDTO,
                                                             ProductFlowInstanceSpuSkuAtomCodeDTO spuSkuAtomCodeDTO,
                                                             Date date){
        ProductFlowInstanceSpu productFlowInstanceSpu = new ProductFlowInstanceSpu();
        productFlowInstanceSpu.setId(BaseServiceUtils.getId());
        productFlowInstanceSpu.setFlowInstanceId(productFlowInstance.getId());
        productFlowInstanceSpu.setFlowId(productFlow.getId());
        BeanUtils.copyProperties(spuImportDTO,productFlowInstanceSpu);
        productFlowInstanceSpu.setCreateTime(date);
        productFlowInstanceSpu.setUpdateTime(date);
        return productFlowInstanceSpu;
    }

    /**
     * 设置商品流程sku信息
     */
    private ProductFlowInstanceSku setProductFlowInstanceSku(ProductFlow productFlow,
                                                             ProductFlowInstance productFlowInstance,
                                                             ProductFlowInstanceSkuImportDTO skuImportDTO,
                                                             Date date){
        ProductFlowInstanceSku productFlowInstanceSku = new ProductFlowInstanceSku();
        productFlowInstanceSku.setId(BaseServiceUtils.getId());
        productFlowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
        productFlowInstanceSku.setFlowId(productFlow.getId());
        BeanUtils.copyProperties(skuImportDTO,productFlowInstanceSku);
        //解析省市
        String saleBeId = skuImportDTO.getSaleBeId();
        String saleLocationId = skuImportDTO.getSaleLocationId();
        if(StringUtils.isEmpty(saleBeId) && StringUtils.isNotEmpty(saleLocationId)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"有发布市信息，则必须有发布省信息");
        }
        if(StringUtils.isNotEmpty(saleBeId)){
            List<String> provinceCityList = new ArrayList<>();
            String[] beIdArray = saleBeId.split(",");
            List<String> beIdList = Arrays.asList(beIdArray);
            if(StringUtils.isNotEmpty(saleLocationId)){
                //如果有市编码，说明商品发布范围不是某个全省
                String[] locationIdArray = saleLocationId.split(",");
                List<String> locationIdList = Arrays.asList(locationIdArray);
                Set<String> notAllProvinceList = locationIdList.stream().map(locationId -> {
                    Object beIdObject = areaDataConfig.getLocationIdBeIdMap().get(locationId);
                    return (String) beIdObject;
                }).filter(s -> {return StringUtils.isNotEmpty(s);}).collect(Collectors.toSet());
                ArrayList<String> notAllProvinceBeIdList = new ArrayList<>(notAllProvinceList);
                beIdList = beIdList.stream().filter(d -> {return !notAllProvinceBeIdList.contains(d);}).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(beIdList)){
                    //这部分是全省或全国
                    setReleaseProvince(provinceCityList, beIdList);
                }
                //设置省-市
                locationIdList.forEach(locationId -> {
                    String beId = (String)areaDataConfig.getLocationIdBeIdMap().get(locationId);
                    String provinceName = (String)areaDataConfig.getProvinceCodeNameMap().get(beId);
                    if(CommonConstant.NEIMENG.equals(provinceName)){
                        provinceName = CommonConstant.NEIMENGGU;
                    }
                    if(CommonConstant.ALL_PLATFORM.equals(provinceName)){
                        provinceName = CommonConstant.NATIONWIDE;
                    }
                    String cityName = (String)areaDataConfig.getLocationCodeNameMap().get(locationId);
                    provinceCityList.add(provinceName+"-"+cityName);
                });
            }else {
                //没有市编码，说明全部是全省或全国
                setReleaseProvince(provinceCityList, beIdList);
            }
            productFlowInstanceSku.setSaleProvinceCity(String.join(",",provinceCityList));
        }
        productFlowInstanceSku.setCreateTime(date);
        productFlowInstanceSku.setUpdateTime(date);
        return productFlowInstanceSku;
    }

    private void setReleaseProvince(List<String> provinceCityList, List<String> beIdList) {
        List<String> allProvinceList = beIdList.stream().map(beId -> {
            String provinceName = (String) areaDataConfig.getProvinceCodeNameMap().get(beId);
            if (CommonConstant.NEIMENG.equals(provinceName)) {
                provinceName = CommonConstant.NEIMENGGU;
            }
            if (CommonConstant.ALL_PLATFORM.equals(provinceName)) {
                provinceName = CommonConstant.NATIONWIDE;
            }
            return provinceName;
        }).collect(Collectors.toList());
        provinceCityList.addAll(allProvinceList);
    }

    private byte[] inputStream2ByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        outputStream.close();
        return outputStream.toByteArray();
    }

}
