package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

/**
 * @Author: dgj
 * @Description:售后订单预约信息
 */
@Data
public class AppointmentInfoRequestDTO {
    /**
     * 提交类型
     * 1：客户自主填入
     * 2：超时系统自动提交
     */
    private Integer submitType;
    /**
     * 操作类型
     * 1：新增
     * 2：修改
     */
    private Integer oprType;
    /**
     * 预约人姓名
     * 加密传输;
     * 加密方式参考4.3 加密方式。
     * V256为解密后长度；
     * 新增时必填，修改时非必填；
     */
    private String name;
    /**
     * 预约人电话
     * 加密传输;
     * 加密方式参考4.3 加密方式。
     * V128为解密后长度；
     * 新增时必填，修改时非必填；
     */
    private String phone;
    /**
     * 预约地址
     * 加密传输;
     * 加密方式参考4.3 加密方式。
     * V2048为解密后长度；
     * 新增时必填，修改时非必填；
     */
  //  private String address;

    /**
     * 预约地址 新增时必填，修改时非必填；
     */
    private AddressInfoDTO addressInfo;

    /**
     * 预约时间
     * 格式：yyyyMMddhh，精确至小时；
     * 超时系统自动提交时无预约时间，需服务人员线下确认；
     * 修改时非必填；
     */
    private String time;
}
