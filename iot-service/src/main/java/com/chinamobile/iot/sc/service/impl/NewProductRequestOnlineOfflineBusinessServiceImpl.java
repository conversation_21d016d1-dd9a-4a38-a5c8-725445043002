package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineBusinessMapper;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineBusiness;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineBusinessExample;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineBusinessService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架商务相关service实现类
 */
@Service
public class NewProductRequestOnlineOfflineBusinessServiceImpl implements NewProductRequestOnlineOfflineBusinessService {

    @Resource
    private NewProductRequestOnlineOfflineBusinessMapper newProductRequestOnlineOfflineBusinessMapper;

    @Override
    public void batchSaveBusiness(List<NewProductRequestOnlineOfflineBusiness> businessList) {
        newProductRequestOnlineOfflineBusinessMapper.batchInsert(businessList);
    }

    @Override
    public void deleteBusinessByRequestIdAndComboInfoId(String comboInfoId, String requestId) {
        NewProductRequestOnlineOfflineBusinessExample example = new NewProductRequestOnlineOfflineBusinessExample();
        example.createCriteria()
                .andComboInfoIdEqualTo(comboInfoId)
                .andNewProductRequestIdEqualTo(requestId);
        newProductRequestOnlineOfflineBusinessMapper.deleteByExample(example);
    }

    @Override
    public void batchUpdateBusinessByRequestIdAndComboInfoIdAndFileType(List<NewProductRequestOnlineOfflineBusiness> businessList) {
        businessList.forEach(business -> {
            NewProductRequestOnlineOfflineBusinessExample example = new NewProductRequestOnlineOfflineBusinessExample();
            example.createCriteria()
                    .andComboInfoIdEqualTo(business.getComboInfoId())
                    .andNewProductRequestIdEqualTo(business.getNewProductRequestId())
                    .andFileTypeEqualTo(business.getFileType());
            newProductRequestOnlineOfflineBusinessMapper.updateByExampleSelective(business,example);
        });
    }

    @Override
    public List<NewProductRequestOnlineOfflineBusiness> getBusinessByRequestIdAndComboInfoId(String newProductRequestId, String comboInfoId) {
        NewProductRequestOnlineOfflineBusinessExample example = new NewProductRequestOnlineOfflineBusinessExample();
        example.createCriteria()
                .andNewProductRequestIdEqualTo(newProductRequestId)
                .andComboInfoIdEqualTo(comboInfoId);
        return newProductRequestOnlineOfflineBusinessMapper.selectByExample(example);
    }
}
