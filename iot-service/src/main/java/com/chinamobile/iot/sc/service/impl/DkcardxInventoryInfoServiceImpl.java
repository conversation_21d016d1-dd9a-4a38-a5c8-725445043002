package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.DkcardxInventoryInfoMapper;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfoExample;
import com.chinamobile.iot.sc.service.DkcardxInventoryInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/10
 * @description 代客下单卡+X库存信息表service实现类
 */
@Service
public class DkcardxInventoryInfoServiceImpl implements DkcardxInventoryInfoService {

    @Resource
    private DkcardxInventoryInfoMapper dkcardxInventoryInfoMapper;

    @Override
    public List<DkcardxInventoryInfo> getDkcardxInventoryInfoByNeed(DkcardxInventoryInfoExample example) {
        return dkcardxInventoryInfoMapper.selectByExample(example);
    }

    @Override
    public void updateDkcardxInventoryInfoById(DkcardxInventoryInfo dkcardxInventoryInfo) {
        dkcardxInventoryInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryInfo);
    }

    @Override
    public void addDkcardxInventoryInfo(DkcardxInventoryInfo dkcardxInventoryInfo) {
        dkcardxInventoryInfoMapper.insert(dkcardxInventoryInfo);
    }

    @Override
    public void batchAddDkcardxInventoryInfo(List<DkcardxInventoryInfo> dkcardxInventoryInfoList) {
        dkcardxInventoryInfoMapper.batchInsert(dkcardxInventoryInfoList);
    }

    @Override
    public void updateDkcardxInventoryInfoByNeed(DkcardxInventoryInfo dkcardxInventoryInfo,
                                                 DkcardxInventoryInfoExample example) {
        dkcardxInventoryInfoMapper.updateByExampleSelective(dkcardxInventoryInfo,example);
    }
}
