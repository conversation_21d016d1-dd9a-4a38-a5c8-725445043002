package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.FinancingConfig;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.Order2cFinancingRepaymentMapperExt;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.user.EditFinancingInfoRequest;
import com.chinamobile.iot.sc.enums.FinancingOrderStatusEnum;
import com.chinamobile.iot.sc.enums.log.FinancingManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.UserCenterOperateEnum;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.TradeOrderDetailDTO;
import com.chinamobile.iot.sc.pojo.entity.OrderBaoli;
import com.chinamobile.iot.sc.pojo.entity.OrderBaoliExample;
import com.chinamobile.iot.sc.pojo.mapper.FinancingRepaymentListDO;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.service.BaseSmsService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.OrderFinancingService;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.SFTPUtil;
import com.chinamobile.iot.sc.util.SmsValidUtil;
import com.chinamobile.iot.sc.util.financing.ECDSAUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2023/7/6 10:07
 */
@Service
@Slf4j
public class OrderFinancingServiceImpl implements OrderFinancingService {

    @Autowired
    private FinancingConfig financingConfig;

    @Resource
    private Order2cFinancingRepaymentHistoryMapper order2cFinancingRepaymentHistoryMapper;

    @Resource
    private Order2cFinancingRepaymentMapperExt order2cFinancingRepaymentMapperExt;

    @Resource
    private Order2cFinancingRepaymentMapper order2cFinancingRepaymentMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private OrderBaoliMapper orderBaoliMapper;

    @Resource
    private TradeOrderInfoMapper tradeOrderInfoMapper;

    @Resource
    private UserFeignClient userFeignClient;

    @Autowired
    private BaseSmsService baseSmsService;

    //保理融资发起申请的验证码短信
    @Value("${sms.startFinancingTempId:106966}")
    private String startFinancingTempId;

    //保理融资省专已拒绝的短信通知
    @Value("${sms.provinceRejectedTempId:106962}")
    private String provinceRejectedTempId;

    //保理融资方案待确认的短信通知
    @Value("${sms.planConfirmingTempId:106964}")
    private String planConfirmingTempId;

    //保理融资银行放款的短信通知
    @Value("${sms.bankPayTempId:106965}")
    private String bankPayTempId;

    //保理认证通过
    @Value("${sms.authFinancingPassTempId:106958}")
    private String authFinancingPassTempId;

    //保理认证退回
    @Value("${sms.authFinancingBackTempId:106959}")
    private String authFinancingBackTempId;

    //保理认证拒绝
    @Value("${sms.authFinancingRejectTempId:106960}")
    private String authFinancingRejectTempId;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Autowired
    private LogService logService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TradeOrderInfoServiceImpl tradeOrderInfoService;

    @Autowired
    private OrderFinancingServiceImpl orderFinancingService;
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));


    @Override
    public BaseAnswer<String> getUrl(QueryFinancingUrlParam param) {
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(param.getUserId());
        OrderFinancingUrlParam orderFinancingUrlParam = new OrderFinancingUrlParam();
        if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
            Data4User data4User = data4UserBaseAnswer.getData();
            orderFinancingUrlParam.setCompanyName(data4User.getPartnerName());
            orderFinancingUrlParam.setCertNo(data4User.getUnifiedCode());
            orderFinancingUrlParam.setPersonName(data4User.getName());
            orderFinancingUrlParam.setPersonPhone(data4User.getPhone());
        }
        orderFinancingUrlParam.setBusinessType(param.getBusinessType());
        if(param.getTradeOrderId() != null){
            TradeOrderInfo tradeOrderInfo = tradeOrderInfoMapper.selectByPrimaryKey(param.getTradeOrderId());
            if(tradeOrderInfo == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单不存在");
            }
            orderFinancingUrlParam.setFinanceCode(tradeOrderInfo.getFinanceCode());
        }

        //请求跳转url
        String sign = ECDSAUtil.signObject(financingConfig.getPrivateKey(), orderFinancingUrlParam);
        orderFinancingUrlParam.setSign(sign);
        ResponseEntity<FinancingUrlResponseVO> response = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(orderFinancingUrlParam), headers);

            log.info("getUrl 获取跳转URL request:{}",JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
             response = restTemplateHttps.postForEntity(financingConfig.getJumpUrl(), requestEntity, FinancingUrlResponseVO.class);
            log.info("getUrl 产金返回response:{}",JSON.toJSONString(response));

        } catch (Exception e) {
            log.error("getUrl 获取跳转url发生异常",e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"请求产金平台发生异常");
        }
        FinancingUrlResponseVO responseVO = response.getBody();
        if(!"200".equals(responseVO.getCode())){
            log.error("getUrl 获取跳转url失败:{}",responseVO.getMessage());
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,responseVO.getMessage());
        }
        return BaseAnswer.success(responseVO.getUrl());
    }

    @Override
    public FinancingResponseVO authenticationAndActivationStatus(AuthenticationAndActivationStatusParam param) {
        log.info("authenticationAndActivationStatus 企业认证激活状态推送参数:{}",JSON.toJSONString(param));
        FinancingResponseVO vo = new FinancingResponseVO();
        try {
            //验签
            if(!ECDSAUtil.verifyObject(financingConfig.getFinancingPublicKey(),param,param.getSign())){
                logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.AUTHENTICATION.code,"-",LogResultEnum.LOG_FAIL.code,"验签失败");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验签失败");
            }
            String userId = checkPartner(param);
            EditFinancingInfoRequest editFinancingInfoRequest = new EditFinancingInfoRequest();
            editFinancingInfoRequest.setStatus(param.getStatus());
            editFinancingInfoRequest.setAdvice(param.getAdvice());
            editFinancingInfoRequest.setUserId(userId);
            userFeignClient.editFinancingInfo(editFinancingInfoRequest);

            Msg4Request request = new Msg4Request();
            List<String> mobiles = new ArrayList<>();
            mobiles.add(param.getPersonPhone());
            // 四种情况记录日志
            String logContent = "";
            if(param.getStatus().equals("init")){
                logContent = "【申请注册】";
            }else if (param.getStatus().equals("checking")){
                logContent = "【申请认证】";
            }else if (param.getStatus().equals("back")){
                //退回
                logContent = "【重新认证】";
                request.setTemplateId(authFinancingBackTempId);
            }else if (param.getStatus().equals("activated")){
                logContent = "【账号激活】";
            }else if(param.getStatus().equals("pass")){
                //通过
                request.setTemplateId(authFinancingPassTempId);
            }else if (param.getStatus().equals("reject")){
                //拒绝
                request.setTemplateId(authFinancingRejectTempId);
            }else{}
            if(StringUtils.isNotEmpty(logContent)){
                logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.AUTHENTICATION.code,logContent,userId, 0,LogResultEnum.LOG_SUCESS.code,null);
            }
            if(StringUtils.isNotEmpty(request.getTemplateId())){
                request.setMobiles(mobiles);
                BaseAnswer<Void> messageAnswer = smsFeignClient.asySendMessage(request);
                log.info("authFinancingSms 保理认证短信发送:{}", JSON.toJSONString(messageAnswer));
            }
            return vo;
        } catch (Exception e) {
            String msg = e.getMessage();
            if(e instanceof BusinessException){
                msg = ((BusinessException) e).getStatus().getMessage();
            }
            vo.setCode(BaseErrorConstant.INTERNAL_ERROR.getStateCode());
            vo.setMessage(msg);
            return vo;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> startFinancing(StartFinancingParam param, String userId,String ip) {
        String phone = param.getPhone();
        String maskCode = param.getMaskCode();
        //校验验证码
        SmsValidUtil.checkSmsValid(false,maskCode,phone,redisTemplate);
        Date now = new Date();
        setPublicParam(param,userId);
        TradeOrderInfo tradeOrderInfo = tradeOrderInfoMapper.selectByPrimaryKey(param.getTradeOrderId());
        if(tradeOrderInfo == null){

            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"贸易订单不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单不存在");
        }
        BigDecimal maxFinancingPrice = tradeOrderInfo.getMaxFinancingPrice();
        String applyAmount = param.getApplyAmount();
        //转换为厘
        double amount = Double.parseDouble(applyAmount) * 1000;


        if(amount <= 0){
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"申请金额必须大于0");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"申请金额必须大于0");
        }
        if(maxFinancingPrice == null){
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"最高可融资金额为空");
            });
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"最高可融资金额为空");
        }
        if(maxFinancingPrice.doubleValue() < amount){
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"申请融资金额不能大于最高可融资金额");
            });
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"申请融资金额不能大于最高可融资金额");
        }
        Integer baoliStatus = tradeOrderInfo.getBaoliStatus();
        if(baoliStatus.intValue() != FinancingOrderStatusEnum.PREPARE_APPLY.code && baoliStatus.intValue() != FinancingOrderStatusEnum.PROVINCE_REJECTED.code){
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"只有[待申请]和[省专已拒绝]状态才能申请保理");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有[待申请]和[省专已拒绝]状态才能申请保理");
        }
        boolean firstApply = true;
        if(baoliStatus.intValue() == FinancingOrderStatusEnum.PROVINCE_REJECTED.code){
            //区分 重新申请，用于日志记录
            firstApply = false;
        }
        String tradeNo = tradeOrderInfo.getTradeNo();
        //上传保理订单明细文件
        String fileName = "orderList_"+tradeNo+".xlsx";
        String filePath = financingConfig.getFtpWorkPath()+"/"+fileName;
        List<TradeOrderDetailDTO> tradeOrderDetailDTOList = tradeOrderInfoService.getTradeOrderDetailDTOS(tradeNo);
        if(CollectionUtils.isEmpty(tradeOrderDetailDTOList)){
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"贸易订单无对应的原始订单明细");
            });
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"贸易订单无对应的原始订单明细");
        }
        Workbook workbook = ExcelUtils.exportTradeOrderDetailFast("订单明细", TradeOrderDetailDTO.class, tradeOrderDetailDTOList, true);

        // 封网期间注释掉上传文件
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ByteArrayInputStream inputStream = null;
        try {
            workbook.write(outputStream);
            workbook.close();
            inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            uploadOrderExcel(inputStream,fileName);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                outputStream.close();
                if(inputStream != null){
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        String targetBank = param.getTargetBank();
        String estimateReceiptTime = param.getEstimateReceiptTime();
        String invoiceNo = param.getInvoiceNo();
        String invoiceIncludeTaxAmount = param.getInvoiceIncludeTaxAmount();
        ArrayList<StartFinancingParam.FinancingItem> orderList = new ArrayList<>();
        StartFinancingParam.FinancingItem financingItem = new StartFinancingParam.FinancingItem();
        financingItem.setFilePath(filePath);
        financingItem.setOrderCode(tradeNo);
        financingItem.setProvinceContractNo(tradeOrderInfo.getContractNum());
        // 移动省专公司先写死
        financingItem.setProvinceName("中移物联网有限公司");
//        financingItem.setProvinceName(tradeOrderInfo.getSellerName());
        financingItem.setTotalAmount( String.valueOf(((double)tradeOrderInfo.getTradePrice())/1000));
        financingItem.setApplyAmount(applyAmount);
        financingItem.setTargetBank(targetBank);
        financingItem.setEstimateReceiptTime(estimateReceiptTime);
        financingItem.setInvoiceNo(invoiceNo);
        financingItem.setInvoiceIncludeTaxAmount(invoiceIncludeTaxAmount);
        orderList.add(financingItem);
        param.setOrderList(orderList);

        //将产金不需要的参数设置为Null,避免影响签名
        param.setTradeOrderId(null);
        param.setMaskCode(null);
        param.setPhone(null);
        param.setApplyAmount(null);
        param.setTargetBank(null);
        param.setEstimateReceiptTime(null);
        param.setInvoiceNo(null);
        param.setInvoiceIncludeTaxAmount(null);
        String sign = ECDSAUtil.signObject(financingConfig.getPrivateKey(), param);
        param.setSign(sign);
        //先更新数据库，再请求产金，避免产金请求成功后，数据库更新的过程发生异常回滚而无法回滚产金
        Integer status = FinancingOrderStatusEnum.PROVINCE_AUDITING.code;
        //更新贸易订单状态和申请信息
        tradeOrderInfo.setBaoliStatus(status);
        tradeOrderInfo.setUpdateTime(now);
        tradeOrderInfo.setRequestFinancingPrice((long) (amount));
        tradeOrderInfo.setErwartetesWareneingangsdatum(estimateReceiptTime);
        tradeOrderInfo.setInvoiceNum(invoiceNo);
        tradeOrderInfo.setInvoicePrice((long)(Double.valueOf(invoiceIncludeTaxAmount)*1000));
        tradeOrderInfo.setCanUseBank(targetBank);
        tradeOrderInfoMapper.updateByPrimaryKeySelective(tradeOrderInfo);
        //更新保理订单状态
        OrderBaoli orderBaoli = new OrderBaoli();
        orderBaoli.setBaoliStatus(status);
        orderBaoli.setUpdateTime(now);
        orderBaoliMapper.updateByExampleSelective(orderBaoli,new OrderBaoliExample().createCriteria().andTradeNoEqualTo(tradeNo).example());

        ResponseEntity<FinancingResponseVO> response = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(param), headers);
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            response = restTemplateHttps.postForEntity(financingConfig.getStartFinancingUrl(), requestEntity, FinancingResponseVO.class);
        } catch (Exception e) {
            log.error("startFinancing发生异常",e);
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"请求产金平台发生异常");
            });
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"请求产金平台发生异常");
        }
        if(response == null){
            log.error("startFinancing无响应");
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"请求产金平台无响应");
            });
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"请求产金平台无响应");
        }
        FinancingResponseVO responseVO = response.getBody();
        log.info("startFinancing收到产金平台响应内容:{}",JSON.toJSONString(responseVO));
        if(!"200".equals(responseVO.getCode())){
            log.error("startFinancing返回失败:{}",responseVO.getMessage());
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"-", userId, ip, LogResultEnum.LOG_FAIL.code,"请求产金平台返回失败:"+responseVO.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"请求产金平台返回失败:"+responseVO.getMessage());
        }

        String logContent = (firstApply ? "【申请保理】" : "【重新申请】")+"贸易订单编号:"+tradeNo;
        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,logContent,userId,0,LogResultEnum.LOG_SUCESS.code,null);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> cancelFinancing(CancelFinancingParam param, String userId,String ip) {
        String financingOrderId = param.getFinancingOrderId();
        TradeOrderInfo tradeOrderInfo = tradeOrderInfoMapper.selectByPrimaryKey(financingOrderId);
        if(tradeOrderInfo == null){
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.FINANCING.code,"【撤销】", userId, ip, LogResultEnum.LOG_FAIL.code,"贸易订单不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单不存在");
        }
        //只有省专已拒绝 和 省专审核中 才能撤销
        // 待申请也可以撤销了
        Integer status = tradeOrderInfo.getBaoliStatus();
        if(status.intValue() != FinancingOrderStatusEnum.PROVINCE_AUDITING.code.intValue() && status.intValue() != FinancingOrderStatusEnum.PROVINCE_REJECTED.code.intValue()&&status.intValue() != FinancingOrderStatusEnum.PREPARE_APPLY.code.intValue()){
            executorService.execute(() -> {

                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.FINANCING.code,"【撤销】贸易订单编号"+tradeOrderInfo.getTradeNo(), userId, ip, LogResultEnum.LOG_FAIL.code,"只有[省专已拒绝]或[省专审核中]或[待申请]的贸易订单才能撤销");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有[省专已拒绝]或[省专审核中]或[待申请]的贸易订单才能撤销");
        }
        setPublicParam(param, userId);
        param.setOrderCode(tradeOrderInfo.getTradeNo());
        //去掉产金不需要的参数，避免影响签名
        param.setFinancingOrderId(null);
        //请求产金撤销融资
        String sign = ECDSAUtil.signObject(financingConfig.getPrivateKey(), param);
        param.setSign(sign);

        Date now = new Date();
        //将贸易订单状态设置为撤销
        tradeOrderInfo.setBaoliStatus(FinancingOrderStatusEnum.REVOKE.code);
        tradeOrderInfo.setUpdateTime(now);
        tradeOrderInfoMapper.updateByPrimaryKeySelective(tradeOrderInfo);
        //将保理订单的融资状态释放
        OrderBaoliExample baoliExample = new OrderBaoliExample().createCriteria().andTradeNoEqualTo(tradeOrderInfo.getTradeNo()).example();
        OrderBaoli orderBaoli = new OrderBaoli();
        orderBaoli.setUpdateTime(new Date());
        orderBaoli.setBaoliStatus(FinancingOrderStatusEnum.REVOKE.code);
        orderBaoliMapper.updateByExampleSelective(orderBaoli,baoliExample);

        // 待申请直接撤销不请求产金
        if(status.intValue() != FinancingOrderStatusEnum.PREPARE_APPLY.code.intValue()){
            ResponseEntity<FinancingResponseVO> response = null;
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(param), headers);
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                response = restTemplateHttps.postForEntity(financingConfig.getCancelFinancingUrl(), requestEntity, FinancingResponseVO.class);
            } catch (Exception e) {
                log.error("cancelFinancing发生异常",e);
                executorService.execute(() -> {

                    logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.FINANCING.code,"【撤销】贸易订单编号"+tradeOrderInfo.getTradeNo(), userId, ip, LogResultEnum.LOG_FAIL.code,"请求产金平台发生异常");
                });
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"请求产金平台发生异常");
            }
            if(response == null){
                log.error("cancelFinancing无响应");
                executorService.execute(() -> {

                    logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.FINANCING.code,"【撤销】贸易订单编号"+tradeOrderInfo.getTradeNo(), userId, ip, LogResultEnum.LOG_FAIL.code,"请求产金平台无响应");
                });
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"请求产金平台无响应");
            }
            FinancingResponseVO responseVO = response.getBody();
            log.info("cancelFinancing收到产金平台响应内容:{}",JSON.toJSONString(responseVO));
            if(!"200".equals(responseVO.getCode())){
                log.error("cancelFinancing返回失败:{}",responseVO.getMessage());
                executorService.execute(() -> {

                    logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.FINANCING.code,"【撤销】贸易订单编号"+tradeOrderInfo.getTradeNo(), userId, ip, LogResultEnum.LOG_FAIL.code,"请求产金平台返回失败:"+responseVO.getMessage());
                });
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"请求产金平台返回失败:"+responseVO.getMessage());
            }
        }

        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.FINANCING.code,"【撤销】贸易订单编号"+tradeOrderInfo.getTradeNo(),userId,0,LogResultEnum.LOG_SUCESS.code,null);
        return BaseAnswer.success(null);
    }

    /**
     * 设置请求产金平台的公共参数
     */
    private void setPublicParam(BaseFinancingParam param, String userId) {
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.partnerInfoById(userId);
        Data4User data = data4UserBaseAnswer.getData();
        if(data4UserBaseAnswer == null || data == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"合伙人用户不存在");
        }
        param.setCompanyName(data.getPartnerName());
        param.setCertNo(data.getUnifiedCode());
        param.setPersonName(data.getName());
        param.setPersonPhone(data.getPhone());
    }

    @Override
    public FinancingResponseVO orderConfirmStatus(OrderConfirmStatusParam param) {
        log.info("orderConfirmStatus 入参:{}",JSON.toJSONString(param));
        Date now = new Date();
        FinancingResponseVO vo = new FinancingResponseVO();
        try {
            //内部方法需要支持回滚，只能在外部捕获异常
            return orderFinancingService.innerOrderConfirmStatus(param, now, vo);
        } catch (Exception e) {
            log.error("orderConfirmStatus 发生异常",e);
            String msg = e.getMessage();
            if(e instanceof BusinessException){
                msg = ((BusinessException) e).getStatus().getMessage();
            }
            vo.setCode(BaseErrorConstant.INTERNAL_ERROR.getStateCode());
            vo.setMessage(msg);
            return vo;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public FinancingResponseVO innerOrderConfirmStatus(OrderConfirmStatusParam param, Date now, FinancingResponseVO vo) {
        //验签
        if(!ECDSAUtil.verifyObject(financingConfig.getFinancingPublicKey(),param,param.getSign())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验签失败");
        }
        //校验用户
        String cooperatorId = checkPartner(param);
        List<OrderConfirmStatusParam.OrderConfirmItem> tradeOrderList = param.getOrderList();
        List<String> orderCodeList = tradeOrderList.stream().map(o -> {
            return o.getOrderCode();
        }).collect(Collectors.toList());
        //校验贸易订单的合伙人和参数传过来的用户是否匹配
        checkOrderCode(orderCodeList,cooperatorId);

        String originalStatus = param.getStatus();
        Integer baoliStatus = null;
        if("reject".equals(originalStatus)){
            baoliStatus = FinancingOrderStatusEnum.PROVINCE_REJECTED.code;
        }
        if("pass".equals(originalStatus)){
            baoliStatus = FinancingOrderStatusEnum.PLAN_MAKING.code;
        }
        String advice = param.getAdvice();
        //更新贸易订单的状态
        List<String> tradeOrderCodeList = tradeOrderList.stream().map(t -> {
            return t.getOrderCode();
        }).collect(Collectors.toList());
        TradeOrderInfo tradeOrderInfo = new TradeOrderInfo();
        tradeOrderInfo.setBaoliStatus(baoliStatus);
        tradeOrderInfo.setAdvice(advice);
        tradeOrderInfo.setUpdateTime(now);
        tradeOrderInfoMapper.updateByExampleSelective(tradeOrderInfo,new TradeOrderInfoExample().createCriteria().andTradeNoIn(tradeOrderCodeList).example());
        //更新保理订单的状态
        OrderBaoli orderBaoli = new OrderBaoli();
        orderBaoli.setUpdateTime(now);
        orderBaoli.setBaoliStatus(baoliStatus);
        orderBaoliMapper.updateByExampleSelective(orderBaoli,new OrderBaoliExample().createCriteria().andTradeNoIn(tradeOrderCodeList).example());
        if("reject".equals(originalStatus)){
            //省专拒绝，发送短信通知
            baseSmsService.sendOneParamMsg(param.getPersonPhone(),provinceRejectedTempId,financingConfig.getSmsTradeNoParamName(),String.join(",",tradeOrderCodeList));
        }
        return vo;
    }

    /**
     * 校验推送过来的贸易订单和用户Id是否匹配
     */
    private void checkOrderCode(List<String> orderCodeList, String baoliCooperatorId) {
        TradeOrderInfoExample example = new TradeOrderInfoExample().createCriteria().andTradeNoIn(orderCodeList).example();
        List<TradeOrderInfo> tradeOrderInfos = tradeOrderInfoMapper.selectByExample(example);
        if(tradeOrderInfos.isEmpty()){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单不存在");
        }
        List<String> existedTradeNoList = tradeOrderInfos.stream().map(t -> {
            return t.getTradeNo();
        }).collect(Collectors.toList());
        List<String> notExistedTradeNoList = orderCodeList.stream().filter(o -> {
            return !existedTradeNoList.contains(o);
        }).collect(Collectors.toList());
        if(!notExistedTradeNoList.isEmpty()){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单"+String.join(",",notExistedTradeNoList)+"不存在");
        }
//        // 根据公司比对，因为贸易订单的cooperatorId是合作伙伴id而非保理合作伙伴id
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.partnerInfoById(baoliCooperatorId);
        Data4User data = data4UserBaseAnswer.getData();
        if(data4UserBaseAnswer == null || data == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"保理合伙人用户不存在");
        }
        String baoliPartnerName = data.getPartnerName();
        Map<String,Data4User> tradeNoPartnerInfo = new HashMap<>();
        for (TradeOrderInfo tradeOrderInfo : tradeOrderInfos) {
            String cooperatorId = tradeOrderInfo.getCooperatorId();
            Data4User partnerInfo = tradeNoPartnerInfo.get(cooperatorId);
            if(partnerInfo == null){
                BaseAnswer<Data4User> userBaseAnswer = userFeignClient.partnerInfoById(cooperatorId);
                if(userBaseAnswer == null || userBaseAnswer.getData() == null){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单"+tradeOrderInfo.getTradeNo()+"对应合伙人账号信息不存在");
                }
                tradeNoPartnerInfo.put(cooperatorId,userBaseAnswer.getData());
                partnerInfo = userBaseAnswer.getData();
            }
            String partnerName = partnerInfo.getPartnerName();
            if(!baoliPartnerName.equals(partnerName)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单"+tradeOrderInfo.getTradeNo()+"与合伙人"+baoliCooperatorId+"不匹配");
            }
        }
//        //过滤出合伙人不匹配的贸易订单
////        List<TradeOrderInfo> collect = tradeOrderInfos.stream().filter(t -> {
////            return !t.getCooperatorId().equals(baoliCooperatorId);
////        }).collect(Collectors.toList());
//        List<TradeOrderInfo> collect = tradeOrderInfos.stream().filter(t -> {
//            !t.getCooperatorId().equals(company);
//        }).collect(Collectors.toList());
//        if(!collect.isEmpty()){
//            List<String> notMatchTradeNo = collect.stream().map(c -> {
//                return c.getTradeNo();
//            }).collect(Collectors.toList());
//            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单["+String.join(",",notMatchTradeNo)+"]与合伙人["+baoliCooperatorId+"]不匹配");
//        }
    }

    private String checkPartner(BaseFinancingParam param) {
        String companyName = param.getCompanyName();
        String certNo = param.getCertNo();
        String personName = param.getPersonName();
        String personPhone = param.getPersonPhone();
        BaseAnswer<List<Data4User>> listBaseAnswer = userFeignClient.partnerInfoByPhone(personPhone);
        if(listBaseAnswer == null || CollectionUtils.isEmpty(listBaseAnswer.getData())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"手机号"+personPhone+"对应合伙人账号信息不存在");
        }
        //同手机号可能既有合伙人主从账号，也可能有保理账号
        List<Data4User> data = listBaseAnswer.getData().stream().filter(item -> item.getRoleId().equals(financingConfig.getRoleId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(data)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"手机号"+personPhone+"对应保理账号信息不存在");
        }
        Data4User user = data.get(0);
        if(!companyName.equals(user.getPartnerName())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"手机号"+personPhone+"对应保理账号的公司名称["+companyName+"]不匹配");
        }
        if(!certNo.equals(user.getUnifiedCode())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"手机号"+personPhone+"对应保理账号的统一社会信用代码["+certNo+"]不匹配");
        }
        if(!personName.equals(user.getName())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"手机号"+personPhone+"对应保理账号的用户名["+personName+"]不匹配");
        }
        return user.getUserId();

    }

    @Override
    public FinancingResponseVO orderFinancingStatus(OrderFinancingStatusParam param) {
        log.info("orderFinancingStatus 入参:{}",JSON.toJSONString(param));
        Date now = new Date();
        FinancingResponseVO vo = new FinancingResponseVO();
        try {
            return orderFinancingService.innerOrderFinancingStatus(param, now, vo);
        } catch (Exception e) {
            log.error("orderFinancingStatus 发生异常",e);
            String msg = e.getMessage();
            if(e instanceof BusinessException){
                msg = ((BusinessException) e).getStatus().getMessage();
            }
            vo.setCode(BaseErrorConstant.INTERNAL_ERROR.getStateCode());
            vo.setMessage(msg);
            return vo;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public FinancingResponseVO innerOrderFinancingStatus(OrderFinancingStatusParam param, Date now, FinancingResponseVO vo) {
        //验签
        if(!ECDSAUtil.verifyObject(financingConfig.getFinancingPublicKey(),param,param.getSign())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验签失败");
        }
        List<OrderFinancingStatusParam.OrderFinancingItem> tradeOrderList = param.getOrderList();
        List<String> orderList = tradeOrderList.stream().map(o -> {
            return o.getOrderCode();
        }).collect(Collectors.toList());
        //校验用户
        String cooperatorId = checkPartner(param);
        //校验贸易订单的合伙人和参数传过来的用户是否匹配
        checkOrderCode(orderList,cooperatorId);
        /**
         * 审核状态
         * 待确认方案：toBeConfirmed
         * 供应商(合作伙伴)退回：back
         * 供应商（合作伙伴）拒绝：reject
         * 财司审核中:cmccChecking
         * 财司审核拒绝：cmccReject
         * 银行拒绝：bankReject
         * 银行审批中：bankChecking
         * 已放款:  pass
         */
        String originalStatus = param.getStatus();
        Integer baoliStatus = null;

        switch (originalStatus){
            case "toBeConfirmed":
                baoliStatus = FinancingOrderStatusEnum.PLAN_CONFIRMING.code;
                break;
            case "back":
                baoliStatus = FinancingOrderStatusEnum.PLAN_RETURN.code;
                break;
            case "reject":
                baoliStatus = FinancingOrderStatusEnum.PLAN_REJECTED.code;
                break;
            case "cmccChecking":
                baoliStatus = FinancingOrderStatusEnum.PLAN_AUDITING.code;
                break;
            case "cmccReject":
                baoliStatus = FinancingOrderStatusEnum.PLAN_REJECTED.code;
                break;
            case "bankReject":
                baoliStatus = FinancingOrderStatusEnum.BANK_REJECTED.code;
                break;
            case "bankChecking":
                baoliStatus = FinancingOrderStatusEnum.BANK_AUDITING.code;
                break;
            case "pass":
                baoliStatus = FinancingOrderStatusEnum.BANK_PAYED.code;
                break;
            default:
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"融资方案状态值错误:"+originalStatus);
        }

        //更新贸易订单的状态,融资编码,放款信息
        for (OrderFinancingStatusParam.OrderFinancingItem item : tradeOrderList) {
            TradeOrderInfoExample example = new TradeOrderInfoExample().createCriteria().andTradeNoEqualTo(item.getOrderCode()).example();
            TradeOrderInfo tradeOrderInfo = new TradeOrderInfo();
            tradeOrderInfo.setAdvice(param.getAdvice());
            tradeOrderInfo.setBaoliStatus(baoliStatus);
            tradeOrderInfo.setFinanceCode(item.getFinanceCode());
            if(StringUtils.isNotEmpty(item.getPayAmount())){
                tradeOrderInfo.setPayAmount( (long)((Double.valueOf(item.getPayAmount())) * 1000L) );
            }
            tradeOrderInfo.setPayBank(item.getPayBank());
            tradeOrderInfo.setPayDate(item.getPayDate());
            tradeOrderInfo.setDueDate(item.getDueDate());
            tradeOrderInfo.setUpdateTime(now);
            tradeOrderInfoMapper.updateByExampleSelective(tradeOrderInfo,example);
        }
        //更新保理订单状态
        OrderBaoli orderBaoli = new OrderBaoli();
        orderBaoli.setBaoliStatus(baoliStatus);
        orderBaoli.setUpdateTime(now);
        orderBaoliMapper.updateByExampleSelective(orderBaoli,new OrderBaoliExample().createCriteria().andTradeNoIn(orderList).example());

        //发送提醒短信
        if("toBeConfirmed".equals(originalStatus)){
            baseSmsService.sendOneParamMsg(param.getPersonPhone(),planConfirmingTempId,financingConfig.getSmsTradeNoParamName(),String.join(",",orderList));
        }
        if("pass".equals(originalStatus)){
            baseSmsService.sendOneParamMsg(param.getPersonPhone(),bankPayTempId,financingConfig.getSmsTradeNoParamName(),String.join(",",orderList));
        }
        if("cmccChecking".equals(originalStatus)){
            //记录日志
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(cooperatorId);
            Data4User data = data4UserBaseAnswer.getData();
            logService.recordPushOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.FINANCING.code,"【确认方案】贸易订单编号:"+String.join(",",orderList),data.getName(),data.getPhone(),data.getRoleName());
        }
        return vo;
    }

    @Override
    public FinancingResponseVO repayment(FinancingRepaymentParam param) {
        log.info("repayment 入参:{}",JSON.toJSONString(param));
        FinancingResponseVO vo = new FinancingResponseVO();
        Date now = new Date();
        try {
            return orderFinancingService.innerRepayment(param, vo, now);
        } catch (Exception e) {
            log.error("repayment 发生异常",e);
            String msg = e.getMessage();
            if(e instanceof BusinessException){
                msg = ((BusinessException) e).getStatus().getMessage();
            }
            vo.setCode(BaseErrorConstant.INTERNAL_ERROR.getStateCode());
            vo.setMessage(msg);
            return vo;
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public FinancingResponseVO innerRepayment(FinancingRepaymentParam param, FinancingResponseVO vo, Date now) {
        //验签
        if(!ECDSAUtil.verifyObject(financingConfig.getFinancingPublicKey(),param,param.getSign())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验签失败");
        }
        //校验用户
        String cooperatorId = checkPartner(param);
        List<FinancingRepaymentParam.RepaymentItem> orderList = param.getOrderList();
        List<String> orderCodeList = orderList.stream().map(o -> {
            return o.getOrderCode();
        }).collect(Collectors.toList());
        //校验贸易订单的合伙人和参数传过来的用户是否匹配
        checkOrderCode(orderCodeList,cooperatorId);

        for (FinancingRepaymentParam.RepaymentItem item : orderList) {
            String orderCode = item.getOrderCode();
            //产金平台同步过来的数据是总还款金额，需要减掉之前所有已还款的总额才是当次还款金额
            long totalRepaymentAmount = (long)(Double.valueOf(item.getRepayAmount()) * 1000);
            if(totalRepaymentAmount == 0L){
                continue;
            }
            List<Order2cFinancingRepayment> repaymentList = order2cFinancingRepaymentMapper.selectByExample(new Order2cFinancingRepaymentExample().createCriteria().andOrderCodeEqualTo(orderCode).example());
            Order2cFinancingRepayment repayment = new Order2cFinancingRepayment();
            //本次实际还款金额
            Long currentRepayAmount = null;
            if(repaymentList.isEmpty()){
                //贸易订单首次还款
                repayment.setId(BaseServiceUtils.getId());
                repayment.setOrderCode(orderCode);
                repayment.setRepayAmount(totalRepaymentAmount);
                repayment.setOutstandingAmount((long)(Double.valueOf(item.getOutStandindAmount())*1000));
                repayment.setCreateTime(now);
                repayment.setUpdateTime(now);
                order2cFinancingRepaymentMapper.insertSelective(repayment);
                currentRepayAmount = totalRepaymentAmount;
            }else {
                repayment = repaymentList.get(0);
                Long existedRepayAmount = repayment.getRepayAmount();
                //更新还款金额
                repayment.setRepayAmount(totalRepaymentAmount);
                repayment.setOutstandingAmount((long)(Double.valueOf(item.getOutStandindAmount())*1000));
                repayment.setUpdateTime(now);
                order2cFinancingRepaymentMapper.updateByPrimaryKeySelective(repayment);
                currentRepayAmount = totalRepaymentAmount - existedRepayAmount;
            }
            //添加还款历史
            Order2cFinancingRepaymentHistory repaymentHistory = new Order2cFinancingRepaymentHistory();
            repaymentHistory.setId(BaseServiceUtils.getId());
            repaymentHistory.setOrderCode(orderCode);
            repaymentHistory.setRepayAmount(currentRepayAmount);
            repaymentHistory.setRepayDate(item.getRepayDate());
            repaymentHistory.setCreateTime(now);
            order2cFinancingRepaymentHistoryMapper.insertSelective(repaymentHistory);
            //更新保理订单还款额度（平均分配）
            OrderBaoliExample baoliExample = new OrderBaoliExample().createCriteria().andTradeNoEqualTo(orderCode).example();
            Long baoliOrderCount = orderBaoliMapper.countByExample(baoliExample);
            if(baoliOrderCount != 0){
                long singleRepayment = totalRepaymentAmount / baoliOrderCount;
                OrderBaoli orderBaoli = new OrderBaoli();
                orderBaoli.setUpdateTime(now);
                orderBaoli.setReturnPrice(singleRepayment);
                orderBaoliMapper.updateByExampleSelective(orderBaoli,baoliExample);
            }
        }
        return vo;
    }

    @Override
    public BaseAnswer<PageData<FinancingRepaymentListVO>> repaymentList(FinancingRepaymentListParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if(pageNum <= 0 || pageSize <= 0){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"页数和每页数量必须大于0");
        }
        //判断用户数据权限
        String roleId = loginIfo4Redis.getRoleId();
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + roleId);
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_FINANCING_REPAYMENT_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_FINANCING_REPAYMENT_COMPANY)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }
        //
        List<String> partnerUserIdList = new ArrayList<>();
        if(loginIfo4Redis.getIsAdmin() == null || !loginIfo4Redis.getIsAdmin()){
            //合作伙伴主账号 或者 合作伙伴保理账号，都是查看单位数据
            QueryPartnerListParam queryPartnerListParam = new QueryPartnerListParam();
            queryPartnerListParam.setPartnerName(loginIfo4Redis.getPartnerName());
            BaseAnswer<List<Data4User>> partnerList = userFeignClient.getPartnerList(queryPartnerListParam);
            if(partnerList!=null){
                List<Data4User> data = partnerList.getData();
                if(data!=null){
                    partnerUserIdList = data.stream().map(data4User -> {return data4User.getUserId();}).collect(Collectors.toList());
                }
            }
        }

        PageData<FinancingRepaymentListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        String orderCode = param.getOrderCode();
        String provinceCompanyName = param.getProvinceCompanyName();
        String provinceCompanyContract = param.getProvinceCompanyContract();
        String partnerName = param.getPartnerName();
        PageHelper.startPage(pageNum,pageSize);
        List<FinancingRepaymentListDO> doList = null;
        if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_FINANCING_REPAYMENT_SYSTEM)){
            doList = order2cFinancingRepaymentMapperExt.repaymentList(null,orderCode,provinceCompanyName,provinceCompanyContract,partnerName);
        }
        if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_FINANCING_REPAYMENT_COMPANY)){
            doList = order2cFinancingRepaymentMapperExt.repaymentList(partnerUserIdList,orderCode,provinceCompanyName,provinceCompanyContract,partnerName);
        }
        PageInfo<FinancingRepaymentListDO> pageInfo = new PageInfo<>(doList);
        if(doList.isEmpty()){
            return BaseAnswer.success(pageData);
        }
        List<FinancingRepaymentListVO> list = doList.stream().map(d -> {
            FinancingRepaymentListVO vo = new FinancingRepaymentListVO();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        pageData.setData(list);
        pageData.setCount(pageInfo.getTotal());
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer<FinancingRepaymentBaseVO> repaymentBaseInfo(String orderCode) {
        TradeOrderInfoExample tradeOrderInfoExample = new TradeOrderInfoExample().createCriteria().andTradeNoEqualTo(orderCode).example();
        List<TradeOrderInfo> tradeOrderInfos = tradeOrderInfoMapper.selectByExample(tradeOrderInfoExample);
        if(tradeOrderInfos.isEmpty()){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"保理融资贸易订单不存在");
        }
        FinancingRepaymentBaseVO vo = new FinancingRepaymentBaseVO();
        TradeOrderInfo tradeOrderInfo = tradeOrderInfos.get(0);
        vo.setOrderCode(tradeOrderInfo.getTradeNo());
        vo.setProvinceCompanyName(tradeOrderInfo.getSellerName());
        vo.setProvinceCompanyContract(tradeOrderInfo.getContractNum());
        vo.setPayAmount(tradeOrderInfo.getPayAmount());
        vo.setPayBank(tradeOrderInfo.getPayBank());
        vo.setDueDate(tradeOrderInfo.getDueDate());

        Order2cFinancingRepaymentExample example = new Order2cFinancingRepaymentExample().createCriteria().andOrderCodeEqualTo(orderCode).example();
        List<Order2cFinancingRepayment> list = order2cFinancingRepaymentMapper.selectByExample(example);
        if(!list.isEmpty()){
            Order2cFinancingRepayment repayment = list.get(0);
            BeanUtils.copyProperties(repayment,vo);
        }else {
            //未还款
            vo.setRepayAmount(0L);
            vo.setOutstandingAmount(vo.getPayAmount());
        }
        return BaseAnswer.success(vo);
    }

    @Override
    public BaseAnswer<PageData<FinancingRepaymentHistoryVO>> repaymentHistory(String orderCode, BasePageQuery pageQuery) {
        Integer pageNum = pageQuery.getPageNum();
        Integer pageSize = pageQuery.getPageSize();
        if(pageNum <= 0 || pageSize <= 0){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"页数和每页数量必须大于0");
        }
        PageData<FinancingRepaymentHistoryVO> pageData = new PageData<>();
        pageData.setPage(pageNum);

        PageHelper.startPage(pageNum,pageSize);
        Order2cFinancingRepaymentHistoryExample example = new Order2cFinancingRepaymentHistoryExample().createCriteria().andOrderCodeEqualTo(orderCode).example();
        example.setOrderByClause("create_time desc");
        List<Order2cFinancingRepaymentHistory> repaymentHistoryList = order2cFinancingRepaymentHistoryMapper.selectByExample(example);
        PageInfo<Order2cFinancingRepaymentHistory> pageInfo = new PageInfo<>(repaymentHistoryList);
        if(repaymentHistoryList.isEmpty()){
            return BaseAnswer.success(pageData);
        }
        List<FinancingRepaymentHistoryVO> list = repaymentHistoryList.stream().map(r -> {
            FinancingRepaymentHistoryVO vo = new FinancingRepaymentHistoryVO();
            BeanUtils.copyProperties(r, vo);
            return vo;
        }).collect(Collectors.toList());
        pageData.setData(list);
        pageData.setCount(pageInfo.getTotal());
        return BaseAnswer.success(pageData);
    }

    @Override
    public void sendStartFinancingMask(String tradeOrderId) {
        TradeOrderInfo tradeOrderInfo = tradeOrderInfoMapper.selectByPrimaryKey(tradeOrderId);
        if(tradeOrderInfo == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单不存在");
        }
        //这里的合伙人是商品配置的合伙人，不是保理合伙人
        String cooperatorId = tradeOrderInfo.getCooperatorId();
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.partnerInfoById(cooperatorId);
        Data4User data = data4UserBaseAnswer.getData();
        if(data4UserBaseAnswer == null || data == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单对应合伙人账号不存在");
        }
        String partnerName = data.getPartnerName();
        QueryPartnerListParam queryPartnerListParam = new QueryPartnerListParam();
        queryPartnerListParam.setPartnerName(partnerName);
        BaseAnswer<List<Data4User>> partnerList = userFeignClient.getPartnerList(queryPartnerListParam);
        List<Data4User> partnerListData = partnerList.getData();
        //只有已激活的保理账号才能发送短信
        List<Data4User> activatedBaoliCooperatorList = partnerListData.stream().filter(p -> {
            return "activated".equals(p.getCjStatus());
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(activatedBaoliCooperatorList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单对应合伙人保理账号（已激活）不存在");
        }
        Data4User baoliCooperator = activatedBaoliCooperatorList.get(0);
        String phone = baoliCooperator.getPhone();
        if(StringUtils.isEmpty(phone)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"贸易订单对应合伙人保理账号没有手机号");
        }
        baseSmsService.sendMask(phone,startFinancingTempId,null);
    }

    @Override
    public BaseAnswer<Boolean> judgeUserIfHaveEffectiveTrade(String userId) {
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.partnerInfoById(userId);
        Data4User data = data4UserBaseAnswer.getData();
        if(data4UserBaseAnswer == null || data == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"账号不存在");
        }
        Boolean answer = true;
        //这里通过合作伙伴名称来找到对应的合伙人，因为贸易订单表里保存的cooperatorId是商品配置的合伙人id，而非保理账号id
        String partnerName = data.getPartnerName();
        QueryPartnerListParam queryPartnerListParam = new QueryPartnerListParam();
        queryPartnerListParam.setPartnerName(partnerName);
        BaseAnswer<List<Data4User>> partnerList = userFeignClient.getPartnerList(queryPartnerListParam);
        if(partnerList == null || CollectionUtils.isEmpty(partnerList.getData())){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"保理账号无对应合伙人主从账号");
        }
        List<String> userIdList = partnerList.getData().stream().map(p -> {
            return p.getUserId();
        }).collect(Collectors.toList());
        //如果有 非撤销状态 且 未完全还款的贸易订单，该用户就不能停用
        TradeOrderInfoExample example = new TradeOrderInfoExample().createCriteria()
                                        .andCooperatorIdIn(userIdList)
                                        .andBaoliStatusNotEqualTo(FinancingOrderStatusEnum.REVOKE.code)
                                        .example();
        List<TradeOrderInfo> tradeOrderInfos = tradeOrderInfoMapper.selectByExample(example);
        if(tradeOrderInfos.size() == 0){
            return BaseAnswer.success(answer);
        }
        for (TradeOrderInfo tradeOrderInfo : tradeOrderInfos) {
            String tradeNo = tradeOrderInfo.getTradeNo();
            Order2cFinancingRepaymentExample example1 = new Order2cFinancingRepaymentExample().createCriteria().andOrderCodeEqualTo(tradeNo).example();
            List<Order2cFinancingRepayment> order2cFinancingRepayments = order2cFinancingRepaymentMapper.selectByExample(example1);
            if(order2cFinancingRepayments.size() == 0){
                //还未进行过还款，不可停用
                answer = false;
                break;
            }else {
                Order2cFinancingRepayment repayment = order2cFinancingRepayments.get(0);
                if(repayment.getRepayAmount().longValue() < tradeOrderInfo.getPayAmount().longValue()){
                    //未完全还款，不可停用
                    answer = false;
                    break;
                }
            }
        }
        return BaseAnswer.success(answer);
    }

    private void uploadOrderExcel(ByteArrayInputStream inputStream, String fileName){
        SFTPUtil sftpUtil = new SFTPUtil(financingConfig.getFtpUserName(), financingConfig.getFtpPassword(), financingConfig.getFtpHost(), financingConfig.getFtpPort());
        try {
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");
                sftpUtil.upload(financingConfig.getFtpWorkPath(),fileName,inputStream);
            }
        } catch (Exception e) {
            log.error("uploadOrderExcel发生异常",e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"上传订单明细发生异常");
        } finally {
            sftpUtil.logout();
        }
    }

}
