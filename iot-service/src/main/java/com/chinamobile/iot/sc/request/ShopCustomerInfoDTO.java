package com.chinamobile.iot.sc.request;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**

 * 商城客户信息同步传递
 */
@Data
public class ShopCustomerInfoDTO implements Serializable {
    /**
     * 操作类型
     * 1、	新增
     * 2、	修改
     * 3、  注销
     * */
    private String oprType;

    /**
     * 客户编码
     *
     */
    private String custCode;

    /**
     * 取客户手机号信息
     */
    private String custNumber;

    /**
     * 用户标识
     *
     */
    private String userID;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * operType=1时，与客户注册时间
     * operType=2时，取客户信息变更时间
     * operType=3时，取客户信息注销完成时间
     * 格式：yyyyMMddhhmmss
     *
     */
    private String custStatusTime;


    /**
     * 枚举值：0：普通用户  1：一级分销员  2：二级分销员 3:渠道商
     */
    private String roleType;

    /**
     * 客户省份
     *
     */
    private String beId;


    /**
     * 客户地市
     *
     */
    private String location;


    /**
     * 客户区县
     *
     */
    private String regionID;

    /**
     * 客户注册时间
     * 格式：yyyyMMddhhmmss
     */
    private String clientRegister;


    /**
     * 用户状态0：待激活（暂不启用） 1：激活  2：暂停（暂不启用） 3：失效
     *
     */
    private String clientstatus;

    /**
     * oprType=1且 roleType=1、2时必传
     * 分销员名称
     * 无分销员姓名可传空值
     */
    private String distributorName;

    /**
     * 渠道ID
     */
    private String distributorChannelId;

    /**
     * 渠道名称
     */
    private String distributorChannelName;

    /**
     * 推荐码
     *
     */
    private String distributorReferralcode;

    /**
     * 绑定客户经理姓名
     */
    private String distributorMrgInf;

    /**
     * 绑定客户经理工号
     *
     */
    private String distributorMrgCode;

    /**
     * 渠道商编码
     *
     */
    private String agentNumber;

    /**
     * 渠道商全称
     *
     */
    private String agentName;
}