package com.chinamobile.iot.sc.config;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * created by liuxiang on 2023/10/27 11:18
 */
public class ThreadExecutorConfig {

    public static ExecutorService executorService = new ThreadPoolExecutor(8,16,1, TimeUnit.MINUTES,new LinkedBlockingQueue<>(100000));


}
