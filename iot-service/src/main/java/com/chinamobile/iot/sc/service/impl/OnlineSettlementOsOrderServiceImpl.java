package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.OnlineSettlementOsOrderMapper;
import com.chinamobile.iot.sc.dao.ext.OnlineSettlementOsOrderMapperExt;
import com.chinamobile.iot.sc.pojo.dto.OnlineSettlementInfoDTO;
import com.chinamobile.iot.sc.pojo.dto.OsOrderToOnlineOrderDTO;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrderExample;
import com.chinamobile.iot.sc.service.OnlineSettlementOsOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/15
 * @description 线上结算管理商城订单service实现类
 */
@Service
public class OnlineSettlementOsOrderServiceImpl  implements OnlineSettlementOsOrderService {

    @Resource
    private OnlineSettlementOsOrderMapperExt onlineSettlementOsOrderMapperExt;

    @Resource
    private OnlineSettlementOsOrderMapper onlineSettlementOsOrderMapper;

    @Override
    public List<OsOrderToOnlineOrderDTO> listOsOrderToOnlineOrder(String orderId) {
        return onlineSettlementOsOrderMapperExt.listOsOrderToOnlineOrder(orderId);
    }

    @Override
    public void batchAddOnlineSettlementOsOrder(List<OnlineSettlementOsOrder> onlineSettlementOsOrderList) {
        onlineSettlementOsOrderMapper.batchInsert(onlineSettlementOsOrderList);
    }

    @Override
    public void updateOnlineSettlementOsOrderByNeed(OnlineSettlementOsOrder onlineSettlementOsOrder, OnlineSettlementOsOrderExample onlineSettlementOsOrderExample) {
        onlineSettlementOsOrderMapper.updateByExampleSelective(onlineSettlementOsOrder,
                onlineSettlementOsOrderExample);
    }

    @Override
    public List<OnlineSettlementOsOrder> listOnlineSettlementOsOrderByNeed(OnlineSettlementOsOrderExample onlineSettlementOsOrderExample) {
        List<OnlineSettlementOsOrder> settlementOsOrderList = onlineSettlementOsOrderMapper.selectByExample(onlineSettlementOsOrderExample);
        return settlementOsOrderList;
    }

    @Override
    public void deleteOnlineSettlementOsOrderByNeed(OnlineSettlementOsOrderExample onlineSettlementOsOrderExample) {
        onlineSettlementOsOrderMapper.deleteByExample(onlineSettlementOsOrderExample);
    }

    @Override
    public OnlineSettlementInfoDTO getOnlineSettlementInfo(String orderId) {
        OnlineSettlementInfoDTO onlineSettlementInfoDTO = onlineSettlementOsOrderMapperExt.getOnlineSettlementInfo(orderId);
        return onlineSettlementInfoDTO;
    }
}
