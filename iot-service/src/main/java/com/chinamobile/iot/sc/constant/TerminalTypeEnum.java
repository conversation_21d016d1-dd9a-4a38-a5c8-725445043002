package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22
 * @description 终端类型枚举类
 */
public enum TerminalTypeEnum {

    CHA_BO_CARD("0","插拔卡"),
    TIE_PIAN_CARD("1","贴片卡"),
//    M2M_NOT_NULL_CARD("2","M2M芯片非空写卡"),
    M2M_NULL_CARD("3","M2M芯片空写卡"),
    NO_CARD("51","不含卡");

    /**
     * 终端类型
     */
    private String type;

    /**
     * 终端描述
     */
    private String desc;

    TerminalTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(String type) {
        for (TerminalTypeEnum value : TerminalTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public static String getTypeByDesc(String desc) {
        for (TerminalTypeEnum value : TerminalTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return value.type;
            }
        }
        return "";
    }

    public static Boolean containDesc(String desc) {
        for (TerminalTypeEnum value : TerminalTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public static Boolean containType(String type) {
        for (TerminalTypeEnum value : TerminalTypeEnum.values()) {
            if (value.type.equals(type)) {
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
