package com.chinamobile.iot.sc.request.product;

import lombok.Data;

/**
 * @Author: YSC
 * @Date: 2021/11/2 11:37
 * @Description: 原子商品
 * offeringClass=A06时，必传;
 */
@Data
public class AtomOfferingInfoDTO {
    /**
     * 原子商品编码
     */
    private String offeringCode;
    /**
     * 原子商品名称
     */
    private String offeringName;
    /**
     * 原子商品类型
     */
    private String offeringClass;
    /**
     * 原子商品销售目录价
     */
    private Long atomSalePrice;
    /**
     * 数量
     */
    private Long quantity;

    /**
     * 平台软件编码
     */
    private String extSoftOfferingCode;
    /**
     * 终端物料编码
     */
    private String extHardOfferingCode;
    /**
     * 原子商品结算单价
     */
    private Long settlePrice;

    /**
     * 账目项ID
     */
    private String chargeCode;

    /**
     * 颜色
     */
    private String color;
    /**
     * 型号
     */
    private String model;
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 销售省市/区域
     */
    private String offeringSaleRegion;

    /**
     * 原子商品结算单价（专合）
     */
    private String settlePricePartner;

    /**
     * 结算明细服务名称
     */
    private String settleServiceName;

    /**
     * CMIOT关联商品
     */
    private String associatedOffer;

    /**
     * 资费有效期
     * 需带单位，例如：3月、180天
     */
    private String validityPeriod;

    /**
     * 原子商品版本号
     */
    private String atomOfferingVersion;

    /**
     * 操作类型
     */
    private String operType;
    /**
     * 产品类型
     */
    private String productType;

    /**
     * 是否为含卡终端.
     * 1：是
     * 2：否
     * 当offeringClass=A11卡+X、X产品类型（productType）=4/5/6/7/8/9/10/11时必传，其他不传
     */
    private String cardContainingTerminal;

    /**
     * 合作伙伴ID
     *
     */
    private String cooperatorId;

}
