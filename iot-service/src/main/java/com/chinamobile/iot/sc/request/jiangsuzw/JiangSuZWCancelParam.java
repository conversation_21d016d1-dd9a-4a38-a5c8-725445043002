package com.chinamobile.iot.sc.request.jiangsuzw;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/1/11 15:32
 */
@Data
public class JiangSuZWCancelParam {

    @JSONField(name = "ROOT")
    private Root ROOT;

    @Data
    public static class Root{

        @JSONField(name = "HEADER")
        private JiangSuZWHeader HEADER;

        @JSONField(name = "BODY")
        private Body BODY;
    }

    @Data
    public static class Body{

        //操作信息
        @JSONField(name = "OPR_INFO")
        private OprInfo OPR_INFO;

        //业务信息
        @JSONField(name = "BUSI_INFO")
        private BusiInfo BUSI_INFO;

    }

    @Data
    public static class OprInfo{

        //操作工号
        @JSONField(name = "LOGIN_NO")
        private String LOGIN_NO;

    }

    @Data
    public static class BusiInfo{

        //商城订单号
        @JSONField(name = "ORDER_ID")
        private String ORDER_ID;

        //退货原因或者操作备注,暂时为空
        @J<PERSON>NField(name = "REMARK")
        private String REMARK;

    }

}
