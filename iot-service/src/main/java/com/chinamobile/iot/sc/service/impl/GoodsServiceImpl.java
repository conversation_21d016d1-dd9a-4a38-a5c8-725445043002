package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.response.web.good.AtomOrderInfo;
import com.chinamobile.iot.sc.response.web.good.GoodsInfoDTO;
import com.chinamobile.iot.sc.service.IGoodsService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
//import com.chinamobile.iot.sc.util.Excel2PDFUtil;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: GoodsServiceImpl
 * @description: 货物清单Service实现类
 * @author: zyj
 * @create: 2022/1/30 9:15
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class GoodsServiceImpl implements IGoodsService {
    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    @Resource
    private OrderHandleMapper orderHandleMapper;

    @Override
    public BaseAnswer<ResponseEntity<byte[]>> downLoadGoodsList(String orderId, String userId) {
        BaseAnswer<ResponseEntity<byte[]>> baseAnswer = new BaseAnswer<>();
        List<AtomOrderInfo> atomOrderInfos = orderHandleMapper.selectGoodList(orderId);
        if(ObjectUtils.isEmpty(atomOrderInfos)){
            throw new BusinessException(StatusConstant.NO_ORDER);
        }
        // 封装货物清单对象
        GoodsInfoDTO goodsInfo = new GoodsInfoDTO();
        AtomOrderInfo atomOrderInfo = atomOrderInfos.get(0);
        log.info("下载pdf atomOrderInfo = {}",atomOrderInfo);
        try {
            goodsInfo.setOrderId(atomOrderInfo.getOrderId())
                    .setCreateTime(DateTimeUtil.getFormatDate(atomOrderInfo.getCreateTime(),"yyyyMMddHHmmss"))
                    .setCustName(IOTEncodeUtils.decryptSM4(atomOrderInfo.getContactPersonName(), iotSm4Key,iotSm4Iv))
                    .setQuantity(atomOrderInfo.getSkuQuantity() * atomOrderInfo.getAtomQuantity());
        } catch (ParseException e) {
            e.printStackTrace();
            throw new BusinessException("10001", "转换原子商品订单创建时间失败！");
        }
        // 封装货物集合信息
        List<GoodsInfoDTO.Good> goods = convertGoods(atomOrderInfos);
        goodsInfo.setGoodList(goods);
        // 生成excel表格
        ClassPathResource classPathResource = new ClassPathResource("template/goods_list.xls");
        InputStream templateFile = null;
        String excelFillFileName = "ShoppingList_" + atomOrderInfo.getOrderId() + ".xls";
        String pdfFileName = "ShoppingList_" + atomOrderInfo.getOrderId() + ".pdf";
        File excelFile = null;
        File pdfFile = null;
        HttpHeaders httpHeaders = null;
        try {
            try {
                templateFile = classPathResource.getInputStream();
            } catch (IOException e) {
                log.info("获取下载模板失败内容：{}",e.getMessage());
                throw new BusinessException(StatusConstant.GOODS_LIST_TEMP_ERROR);
            }
            log.info("货单下载文件名：{}", pdfFileName);
            //生成excel、pdf文件
            excelFile = new File(excelFillFileName);
            pdfFile = new File(pdfFileName);
            ExcelWriter excelWriter = EasyExcel.write(excelFile).withTemplate(templateFile).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            //构建填充excel参数
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("orderId", goodsInfo.getOrderId());
            map.put("createTime", goodsInfo.getCreateTime());
            map.put("custName", goodsInfo.getCustName());
            map.put("quantity", goodsInfo.getQuantity());
            excelWriter.fill(map, writeSheet);
            log.info("构建填充excel参数成功！");
            // 这里注意 入参用了forceNewRow 代表在写入list的时候不管list下面有没有空行 都会创建一行，然后下面的数据往后移动。默认 是false，会直接使用下一行，如果没有则创建。
            // forceNewRow 如果设置了true,有个缺点 就是他会把所有的数据都放到内存了，所以慎用
            // 简单的说 如果你的模板有list,且list不是最后一行，下面还有数据需要填充 就必须设置 forceNewRow=true 但是这个就会把所有数据放到内存 会很耗内存
            // 如果数据量大 list不是最后一行 参照下一个
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(goodsInfo.getGoodList(), fillConfig, writeSheet);
            excelWriter.finish();
            log.info("fill excel模板成功！");
            //将excel转成pdf
//            Excel2PDFUtil.excel2PDF4POI(excelFillFileName, pdfFileName);
            log.info("excel转成pdf成功！");
            //获取输出pdf文件字节数组
            byte[] bytes = new byte['1'];
//            bytes = Excel2PDFUtil.loadFile(pdfFileName);
            log.info("获取输出pdf文件字节数组成功！");
            //封装返回对象
            httpHeaders = new HttpHeaders();
            httpHeaders.add("Cache-Control", "no-cache, no-store, must-revalidate");
//            httpHeaders.setContentDispositionFormData("attachment", pdfFileName);
            httpHeaders.add("Content-Disposition", "attachment; filename=" + pdfFileName);
            httpHeaders.add("Pragma", "no-cache");
            httpHeaders.add("Expires", "0");
            httpHeaders.add("ETag", String.valueOf(System.currentTimeMillis()));
            ResponseEntity<byte[]> responseEntity = ResponseEntity.ok()
                    .headers(httpHeaders)
                    .contentLength(pdfFile.length())
                    .contentType(MediaType.parseMediaType("application/octet-stream;utf-8"))
                    .body(bytes);
            log.info("组装responseEntity返回对象成功！");
            baseAnswer.setData(responseEntity);
        } catch (Exception e) {
            log.error("生成货物清单excel输出流失败！,内容：{}", e.getMessage());
            throw new BusinessException(StatusConstant.GOODS_LIST_TEMP_ERROR);
        }finally {
            if(excelFile != null){
                excelFile.delete();
                log.info("删除excelFile：{} 文件", excelFillFileName);
            }
            if(pdfFile != null){
                pdfFile.delete();
                log.info("删除pdfFile：{} 文件", pdfFileName);
            }
        }

        return baseAnswer;
    }

    private List<GoodsInfoDTO.Good> convertGoods(List<AtomOrderInfo> atomOrderInfos){
        List<GoodsInfoDTO.Good> goods = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(atomOrderInfos)){
            atomOrderInfos.forEach( atom -> {
                GoodsInfoDTO.Good good = new GoodsInfoDTO.Good();
                Double atomPrice = new Double(atom.getAtomPrice() != null ? atom.getAtomPrice() : 0);
                String atomPriceStr = String.format("%.3f",new Double(atomPrice / 1000));
                String sumPriceStr = String.format("%.3f",new Double(atom.getSkuQuantity()*atom.getAtomQuantity()*atomPrice/1000));
                good.setSkuOfferingName(atom.getSkuOfferingName()).setSkuOfferingCode(atom.getSkuOfferingCode())
                        .setAtomOfferingName(atom.getAtomOfferingName())
                        .setSaQuantity(atom.getSkuQuantity()+"*"+atom.getAtomQuantity())
                        .setAtomPrice(atomPriceStr)
                        .setSumPrice(sumPriceStr);
                goods.add(good);
            });
        }
        return goods;
    }
}
