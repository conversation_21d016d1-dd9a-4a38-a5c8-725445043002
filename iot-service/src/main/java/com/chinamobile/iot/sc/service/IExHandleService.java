package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.exhandle.ExHandleInfo;
import com.chinamobile.iot.sc.request.exhandle.Request4ExHandleAdd;
import com.chinamobile.iot.sc.request.exhandle.Request4ExHandleUpdate;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IExHandleService
 * @description: 异常处理订单Service
 * @author: zyj
 * @create: 2022/1/28 14:48
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IExHandleService {

    BaseAnswer<Void> addExHandleInfo(Request4ExHandleAdd request, String userId);

    BaseAnswer<Void> updateExHandleInfo(Request4ExHandleUpdate request, String userId);

    BaseAnswer<ExHandleInfo> queryExHandleInfo(String orderId, String orderType, String refundOrderId, String userId);

}
