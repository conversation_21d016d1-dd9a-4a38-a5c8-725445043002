package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.dto.OnlineSettlementInfoDTO;
import com.chinamobile.iot.sc.pojo.dto.OsOrderToOnlineOrderDTO;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrder;
import com.chinamobile.iot.sc.pojo.entity.OnlineSettlementOsOrderExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/15
 * @description 线上结算管理商城订单service接口类
 */
public interface OnlineSettlementOsOrderService {

    /**
     * 获取OS订单到线上结算管理订单
     * @param orderId
     * @return
     */
    List<OsOrderToOnlineOrderDTO> listOsOrderToOnlineOrder(String orderId);

    /**
     * 批量新增线上结算管理订单
     * @param onlineSettlementOsOrderList
     */
    void batchAddOnlineSettlementOsOrder(List<OnlineSettlementOsOrder> onlineSettlementOsOrderList);

    /**
     * 更新线上结算管理订单根据需要
     * @param onlineSettlementOsOrder
     * @param onlineSettlementOsOrderExample
     */
    void updateOnlineSettlementOsOrderByNeed(OnlineSettlementOsOrder onlineSettlementOsOrder,
                                             OnlineSettlementOsOrderExample onlineSettlementOsOrderExample);

    /**
     * 根据需要获取线上结算管理订单数据
     * @param onlineSettlementOsOrderExample
     * @return
     */
    List<OnlineSettlementOsOrder> listOnlineSettlementOsOrderByNeed(OnlineSettlementOsOrderExample onlineSettlementOsOrderExample);

    /**
     * 根据需要删除线上订单管理数据
     * @param onlineSettlementOsOrderExample
     */
    void deleteOnlineSettlementOsOrderByNeed(OnlineSettlementOsOrderExample onlineSettlementOsOrderExample);

    /**
     * 获取采购订单编码
     * @param orderId
     * @return
     */
    OnlineSettlementInfoDTO getOnlineSettlementInfo(String orderId);

}
