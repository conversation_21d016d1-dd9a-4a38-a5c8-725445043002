package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ProductVersionCutOver {

    @ExcelProperty(value="spu_code",index=0)
    private String spuOfferingCode;

    @ExcelProperty(value="spu_version",index=1)
    private String spuOfferingVersion;

    @ExcelProperty(value="sku_code",index=2)
    private String skuOfferingCode;

    @ExcelProperty(value="sku_version",index=3)
    private String skuOfferingVersion;
    @ExcelProperty(value="atom_code",index=4)
    private String atomOfferingCode;

    @ExcelProperty(value="atom_version",index=5)
    private String atomOfferingVersion;
}
