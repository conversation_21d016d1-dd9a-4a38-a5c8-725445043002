package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19
 * @description IoT省内融合包信息同步割接
 */
@Data
public class SyncBenefitOfferingsInfoCutOver {

    @ExcelProperty(value="统一产商品编码（goodsId）",index=0)
    private String goodsId;

    @ExcelProperty(value="IoT省内融合包名称（iotMallOfferingName）",index=1)
    private String iotMallOfferingName;

    @ExcelProperty(value="IoT省内融合包状态（iotMallOfferingType）",index=2)
    private String iotMallOfferingType;

    @ExcelProperty(value="IoT省内融合包发布区域（iotReleaseAreaId）",index=3)
    private String iotReleaseAreaId;

    @ExcelProperty(value="规格商品编码（skuOfferingCode）",index=4)
    private String skuOfferingCode;
}
