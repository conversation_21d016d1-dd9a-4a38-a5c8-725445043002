package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/7
 * @description 商城接单反馈的实体类
 */
@Data
public class MsisdnInfoResultRequest {

    /**
     * 订单id
     */
    private String orderId;

    private List<MsisdnInfo> msisdnInfos;

    @Data
    public static class MsisdnInfo{
        /**
         * 正式服务号码
         */
        private String msisdn;

        /**
         * 订购结果
         * 1：成功
         * 2：失败
         */
        private String resultCode;
    }

}
