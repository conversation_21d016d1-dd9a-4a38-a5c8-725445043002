package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.UnionSellOrderExcelHistory;
import com.chinamobile.iot.sc.pojo.YsxOrderServiceNumber;
import com.chinamobile.iot.sc.pojo.dto.BusinessCodeListDTO;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.GetOrderDetailVO;
import com.chinamobile.iot.sc.quartz.QuartzJobConf;
import com.chinamobile.iot.sc.request.*;
import com.chinamobile.iot.sc.request.order2c.OrderRemarkParam;
import com.chinamobile.iot.sc.request.order2c.OrderReminderParam;
import com.chinamobile.iot.sc.request.order2c.OrderSalesReportParam;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.response.iot.QuerySubscriberStatusResponse;
import com.chinamobile.iot.sc.response.iot.ServiceResultInfoResponse;
import com.chinamobile.iot.sc.response.web.*;
import com.chinamobile.iot.sc.response.web.logistics.LogisticsVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/4 17:19
 * @Description:
 */
public interface IOrder2CService {
    IOTAnswer<Void> sync2COrderInfo(IOTRequest baseRequest);

    BaseAnswer<List<LogisticsInfoDTO>> getSupplierList();

    BaseAnswer<Void> syncLogistics(LogisticsInfoRequest request, String userId,String ip);

    BaseAnswer<PageData<Order2CInfoDTO>> getOrderList(OrderListQueryParam param,String userId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<Order2CInfoToBacklogDTO>> getOrderListToBacklog(OrderListBacklogQueryParam param,LoginIfo4Redis loginIfo4Redis);

    PageData<Order2CInfoDetailDTO> getOrderListForOpen(String orderId, String startTime, String endTime, List<Integer> orderStatus,
                                                      String spuOfferingClass, String spuOfferingName, String spuOfferingCode,
                                                      String skuOfferingName, String skuOfferingCode, String atomOfferingCode,
                                                      String atomOfferingName, String partnerName, String cooperatorName,
                                                      String phone, Integer specialAfterMarketHandle, String specialAfterStatus,
                                                      String orderType, Integer qlyStatus, String h5Key,Integer h5Status,
                                                      List<String> h5SpuOfferingClasses,Integer softServiceStatus,String clusterCode ,
                                                       String orderingChannelSource,String custPhone,Integer page, Integer num);

    BaseAnswer<Order2CInfoDetailDTO> getOrderDetail(Order2CInfoDetailParam order2CInfoDetailParam,
                                                    LoginIfo4Redis loginIfo4Redis);

    /**
     * 获取接单详情(内部接口)
     * 商城调用接口内部调用，无需水平权限校验
     * @return
     */
    BaseAnswer<Order2CInfoDetailDTO> getOrderDetailInternal(String id);

    IOTAnswer<Void> refundApplyOrCancel(IOTRequest baseRequest,String ip);


    BaseAnswer<Void> refundAudit(AuditRefundRequest request, String userId,String ip);

    void autoAgreeOrderRoc(QuartzJobConf jobConf);

    BaseAnswer<PageData<OrderRocInfoDTO>> getOrderRocList(String rocId, List<String> refundType, List<Integer> orderStatus, String orderId,String orderType, List<String> spuOfferingClass, String spuOfferingName, String spuOfferingCode, String skuOfferingName, String skuOfferingCode, String atomOfferingCode, String atomOfferingName, String partnerName, String cooperatorName,String orderText,Integer orderRocTabStatus,List<String> spuOfferingClassList, Integer page, Integer num, String userId, LoginIfo4Redis loginIfo4Redis);

    /**
     * 分页获取退换售后待办
     * @param pageSize
     * @param pageNum
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer<PageData<OrderRocBacklogInfoDTO>> getOrderRocBacklogList(String refundOrderId,
                                                                        Integer pageSize, Integer pageNum,
                                                                        LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<OrderRocInfoDetailDTO> getOrderRocDetail(OrderRocDetailParam orderRocDetailParam,LoginIfo4Redis loginIfo4Redis);

    IOTAnswer<Void> syncReturnOrdersLogisInfo(IOTRequest baseRequest);

    BaseAnswer<Void> confirmReturnOrder(ConfirmReturnOrder confirmReturnOrder, String userId,String ip);

    void autoAgreeOrderReceipt(QuartzJobConf jobConf);


//    void exportOrder(OrderExportRequest request, String userId, LoginIfo4Redis loginIfo4Redis);


    /**
     * 发送导出订单的短信验证码
     *
     * @param phone
     */
    BaseAnswer<Void> sendExportOrderMask(String phone);


    BaseAnswer<List<SimpleItemDTO>> getSpuOfferingClassList();

    BaseAnswer<Void> deleteDirtyOrder(DeleteDirtyOrderRequest orders);

    BaseAnswer<Void> testUpdate(String orderId, Integer status, String remark);

    /**
     * 新增订单备注
     *
     * @param orderRemarkParam
     * @param loginIfo4Redis
     */
    void insertOrderRemark(OrderRemarkParam orderRemarkParam, LoginIfo4Redis loginIfo4Redis);

    /**
     * 订单催单
     *
     * @param orderReminderParam
     * @return
     */
    BaseAnswer<String> orderReminder(OrderReminderParam orderReminderParam,LoginIfo4Redis loginIfo4Redis);


    void batchDeliver(/*MultipartFile file,*/
            InputStream inputStream,String fileName,
                      String userId,String ip,
            LoginIfo4Redis loginIfo4Redis,
            String importRedisKey)  throws Exception ;
//    void batchDeliver(MultipartFile file);

    /**
     * 同步订单软件商品信息到订单表
     *
     * @return
     */
    BaseAnswer<Void> synOrderSoftware();

    BaseAnswer<List<BusinessCodeListDTO>> getBusinessCodeList();

    /**
     * 获取物流详情信息
     *
     * @param logisticsCode
     * @param supplierName
     * @param contactPhone
     * @return
     */
    BaseAnswer<LogisticsVO> getLogisticsDetails(String logisticsCode, String supplierName, String contactPhone);

    /**
     * 订单数据割接
     *
     * @param excel
     */
    BaseAnswer orderInfoCutOver(MultipartFile excel);

    /**
     * 下载增量订单状态变更的数据
     */
    void downloadDictOrderInfo();

    IOTAnswer<Void> syncAfterMarketOrderInfo(IOTRequest baseRequest);

    /**
     * 商城同步特殊退货退款状态信息
     *
     * @param baseRequest
     * @return
     */
    IOTAnswer<Void> syncSpecialRefundState(IOTRequest baseRequest);



    /**
     * 导出商城订单销售报表订单信息
     *
     * @param orderSalesReportParam
     * @param response
     */
    void exportStoreSalesReport(OrderSalesReportParam orderSalesReportParam, HttpServletResponse response);



    /**
     * 请求特殊售后
     *
     * @param requestParam
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer requestSpecialAfterMarket(SpecialAfterMarketRequestParam requestParam,
                                         LoginIfo4Redis loginIfo4Redis,String ip) throws ParseException;

    /**
     * 特殊退货退款开启，os系统向iot应用商城发起开启特殊退货退款请求
     *
     * @param order2cInfo
     * @return
     */
    BaseAnswer openSpecialRefundOsLaunchRequest(Order2cInfo order2cInfo);

    /**
     * 获取商城同步客服经理信息
     */
    void sftpOperationAccountManagerData();

    /**
     * 获取商城同步注册用户和分销人员数据信息
     */
    void sftpOperationCustomerData();

    /**
     * 商城客户经理用户数据同步字段新增更新历史数据
     * @param file
     */
    void shopManagerAddFieldImport(MultipartFile file);

    /**
     * 商城普通用户经理用户数据同步字段新增更新历史数据
     * @param file
     */
    void shopCustomerAddFieldImport(MultipartFile file);

    /**
     * 删除商城同步用户操作类型是重复的数据
     * @param param
     */
    void deleteSynAccountManagerRepetition(Integer param,HttpServletResponse response) throws Exception;

    /**
     * 删除商城同步普通用户操作类型是重复的数据
     * @param param
     */
    void deleteSynAccountCustomerRepetition(Integer param,HttpServletResponse response) throws Exception;

    /**
     * 删除已经注销的历史用户
     */
    void deleteCustomerLogout();

    /**
     * 根据主键id 修改商城不能处理同步用户
     * @param param
     */
    void updateCustomerUser(UpdateCustomerInfoParam param);

    void unionSellOrderExport(String date);

    void downloadUnionSellOrder(UnionSellOrderDownloadRequest request, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<UnionSellOrderExcelHistory>> unionSellOrderList(UnionSellOrderListQueryParam param);

    /**
     * 生成联合销售excel(时间段)
     */
    BaseAnswer<Void> unionSellOrderExportBatch(String startTime, String endTime);

    /**
     * 订单数据割接增加支付/退款时间
     *
     * @param excel
     */
    BaseAnswer orderInfoTime(MultipartFile excel);

    BaseAnswer<String> fixOrderStatusTime(Integer pageSize, Integer times);

    /**
     * 同步到商城的数据
     *
     * @param o
     * @param beId
     * @param url
     * @param regionId
     */
    void sendMsgToIot(Object o, String beId, String url, String regionId);

    /**
     * 进行定时任务配置
     *
     * @param jobId
     * @param eventType
     * @param eventTime
     * @param noticeTime
     * @param phone
     * @param orderId
     * @param templateId
     * @return
     */
    QuartzJobConf composeQuartzJobConf(String jobId, String eventType, Date eventTime, Integer noticeTime, List<String> phone, String orderId, String templateId);

    /**
     * 处理定时任务
     *
     * @param order2cAtomInfoId
     */
    void handleQuartzJob(String order2cAtomInfoId);

    /**
     * 订单接单
     *
     * @param orderGetOrderParam
     */
    void orderGetOrderRequest(OrderGetOrderParam orderGetOrderParam,String userId,String ip);

    /**
     * 获取接单详情
     *
     * @param getOrderDetailParam
     * @return
     */
    BaseAnswer<GetOrderDetailVO> getGetOrderDetail(GetOrderDetailParam getOrderDetailParam);


    /**
     * 卡+X更新物流信息
     *
     * @param order2cAtomInfo
     * @param orderId
     * @param iotAnswer
     * @param date
     */
    void handleLogisticsInfo(Order2cAtomInfo order2cAtomInfo,
                             String orderId,
                             IOTAnswer iotAnswer,
                             Date date);

    /**
     * 批量新增物流信息
     *
     * @param logisticsMsgList
     * @param orderId
     * @param orderAtomInfoId
     */
    void addLogistics(List<LogisticsInfoRequest.LogisticsMsg> logisticsMsgList,
                      String orderId,
                      String orderAtomInfoId);

    BaseAnswer<String> initValetOrderCompleteTime();

    /**
     * 是否需要进行b2b同步校验
     *
     * @param addr
     * @return
     */
    boolean isOsContractFlow(String addr);


    boolean isB2bFlow(String addr);

    IOTAnswer<ServiceResultInfoResponse> syncServiceNumberInfo(IOTRequest baseRequest);

    /**
     * 手动操作云视讯订单订阅，退订
     */
    BaseAnswer<String> oprYsxOrder(String orderId, String oprCode);


    /**
     * 异步处理根据物流信息是否向省侧同步售后订单
     */
    void asyncDealServiceOrder(String orderId);


    Integer oprYsxService(String orderId, String oprCode, String softCode,
                              List<YsxOrderServiceNumber> serviceNumbers);


    /**
     * 同步行车卫士订购/退订
     * @param phone
     * @param productCode
     * @param productNum
     * @param orderId
     * @param oprCode
     * @return
     * @throws Exception
     */
    BaseAnswer syn2CarSecurityProductSubscribe(String phone,String productCode,
                                         Integer productNum,String orderId,
                                         Integer oprCode) throws Exception;

    /**
     * 行车卫士订购或退订
     * @param carSecuritySubscribeParam
     * @return
     * @throws Exception
     */
    BaseAnswer turnOnCarSecurityProductSubscribe(CarSecuritySubscribeParam carSecuritySubscribeParam) throws Exception;

    /**
     * 处理是否同意卡+X订单的退款要求
     * @param kxOrderRocParam
     * @param loginIfo4Redis
     */
    BaseAnswer handleKxOrderRoc(KxOrderRocParam kxOrderRocParam,
                          LoginIfo4Redis loginIfo4Redis,String ip);
    /**
     * 商城向物联网OS平台请求分配号卡信息
     *
     * @param baseRequest
     * @return
     */
    IOTAnswer<Void> allocateCardRequest(IOTRequest baseRequest);

    /**
     * 更新卡+X终端空写卡库存信息
     * @param order2cAtomInfo
     * @param date
     * @param sellStatus
     * @param needReduce
     */
    void handleCardXInventoryInfo(Order2cAtomInfo order2cAtomInfo,
                                         Date date,
                                         String sellStatus,
                                         Boolean needReduce);

    /**
     * 号卡分配结果反馈接口
     *
     * @param allocateCardResultRequest
     * @return
     */
    BaseAnswer allocateCardResultRequest(AllocateCardResultRequest allocateCardResultRequest);

    /**
     * 校验是否属于卡+X产品的新5类
     * @param productType
     * @return
     */
    boolean checkSkuNew5ProductType(String productType);
    /**
     * 号卡信息查询接口
     *
     * @param qrySubscribersRequest
     * @return
     */
    BaseAnswer qrySubscribers(QrySubscribersRequest qrySubscribersRequest);

    /**
     * 卡状态（生命周期）实时查询
     * @param orderId
     * @param querySubscriberStatusRequest
     * @return
     */
    BaseAnswer<QuerySubscriberStatusResponse> querySubscriberStatus(String orderId,
                                                                    QuerySubscriberStatusRequest querySubscriberStatusRequest);

    BaseAnswer<String> getOrderCardType(String orderId);


    void syncAllowOrerResult(String orderId);

    BaseAnswer batchExportCardxDeliveryNotes(BatchExportCardxDeliveryNoteParam param, HttpServletResponse response, LoginIfo4Redis loginIfo4Redis, String ip);

    void singleExportCardxDeliveryNotes(String orderId, HttpServletResponse response,
                                        LoginIfo4Redis loginInfo4Redis, String ip,
                                        String importRedisKey);

    void migrateOrderUserId();

    void testExcel(String atomOrderId, LoginIfo4Redis loginInfo4Redis, boolean addLog, String ip);
}
