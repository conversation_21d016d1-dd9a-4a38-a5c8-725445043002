package com.chinamobile.iot.sc.request;

import lombok.Data;

/**
 * @Author: YSC
 * @Date: 2021/12/9 15:01
 * @Description: 退货退款，换货等DTO
 */
@Data
public class RefundApplyOrCancelRequest {
    /**
     * 退款订单请求流水号	本次发起退货退款请求的流水号
     */
    private String refundOrderId;
    /**
     * 退款订单类型
     * 1：售后订单
     * 2：商品订单
      */
    private String refundOrderType;
    /**
     * 业务订单流水号	销售业务订单的唯一标识
     */
    private String orderId;
    /**
     * refundsType	1	String	V1	退款类型
     * 1：仅退款
     * 2：退货退款
     * 3：换货（暂不启用）
     * 4：取消申请（以实时反馈为准）
     */
    private String refundsType;

    /**
     * 退款数量
     * 取商品订单的退款商品数量（DICT类取商品订购的原子数量，非DICT类取商品订购的规格数量）
     * 如果是部分退款（暂只卡+X范式商品支持部分退款），则该字段取退款的商品数量
     */
    private String refundsNumber;

    /**
     * 退款原因
     * 1：不喜欢/不想要
     * 2：商品错发
     * 3：收到商品与描述不符
     * 4：商品质量问题
     * 5：快递/物流一直未送到
     * 6：其他
     * 7：七天无理由退换货
     * 8：商品信息拍错
     * 9：地址/电话信息填写错误
     * 10：拍多了
     * 11：协商一致退款
     * 12：缺货
     * 13：发货速度不满意
     * 14：对售后服务不满意
     * 15：商品退换货，售后服务退款
     * 16：售后服务交付失败
     * 17：卡+X订购失败
     * 18：终端运营人员接单失败
     */
    private String reason;
    /**
     * 退货退款的补充说明
     */
    private String remark;
    /**
     * 图片地址路径
     */
    private String[] picture;
}
