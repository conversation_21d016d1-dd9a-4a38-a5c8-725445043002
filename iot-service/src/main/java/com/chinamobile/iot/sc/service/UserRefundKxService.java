package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.InventoryCutListVO;
import com.chinamobile.iot.sc.pojo.vo.KxCanChooseUserVO;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/14
 * @description 卡+X退货发送短信的相关人员配置service接口类
 */
public interface UserRefundKxService {

    /**
     * 查询卡+X退货发送短信的可选人员
     *
     * @param kxCanChooseUserParam
     * @return
     */
    List<KxCanChooseUserVO> listKxCanChooseUser(KxCanChooseUserParam kxCanChooseUserParam);

    /**
     * 新增卡+X退货人员
     *
     * @param addUserRefundKxParam
     */
    void addUserRefundKx(AddUserRefundKxParam addUserRefundKxParam,
                         LoginIfo4Redis loginIfo4Redis);

    /**
     * 根据id删除卡+X退货人员
     *
     * @param deleteUserRefundKxParam
     */
    void deleteUserRefundKxById(DeleteUserRefundKxParam deleteUserRefundKxParam);

    /**
     * 查询卡+X退货发送短信的人员
     *
     * @return
     */
    List<UserRefundKxVO> listUserRefundKx(QueryNoticeUserParam param);

    List<UserRefundKxVO> listUserRefundKx(Integer noticeType);


    /**
     * 库存模式切换接口
     * @param param
     * @param loginIfo4Redis
     */
    void cutNowInventoryPattern(InventoryPatternCutParam param, LoginIfo4Redis loginIfo4Redis,String ip);

    /**
     * 分页查询切换记录
     * @param param
     * @return
     */
    BaseAnswer<PageData<InventoryCutListVO>> getInventoryCutList(InventoryCutListParam param);

    /**
     * 获取当前库存模式
     * @return
     */
    String getNowInventoryPattern();
}
