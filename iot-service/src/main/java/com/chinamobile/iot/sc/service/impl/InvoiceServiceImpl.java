package com.chinamobile.iot.sc.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.constant.InvoiceConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.ApplyInvoiceRecordMapperExt;
import com.chinamobile.iot.sc.dao.ext.InvoiceReverseRecordMapperExt;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.ReceiptManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.UserCenterOperateEnum;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.Order2cInfoExample;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoDTO;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceInfo;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceRec;
import com.chinamobile.iot.sc.pojo.invoice.InvoiceReverseInfo;
import com.chinamobile.iot.sc.pojo.invoice.InvoiceReverseRec;
import com.chinamobile.iot.sc.pojo.mapper.ApplyInvoiceRecDO;
import com.chinamobile.iot.sc.pojo.mapper.InvoiceReverseRecDO;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoParam;
import com.chinamobile.iot.sc.quartz.QuartzJobConf;
import com.chinamobile.iot.sc.quartz.QuartzManager;
import com.chinamobile.iot.sc.quartz.job.SendInvoiceRushRedJob;
import com.chinamobile.iot.sc.request.invoice.*;
import com.chinamobile.iot.sc.request.revenue.GetInvoiceRequest;
import com.chinamobile.iot.sc.request.revenue.GetTokenRequest;
import com.chinamobile.iot.sc.request.revenue.InvoiceApplyRequest;
import com.chinamobile.iot.sc.response.revenue.GetInvoiceResponse;
import com.chinamobile.iot.sc.response.revenue.GetTokenResponse;
import com.chinamobile.iot.sc.response.revenue.InvoiceApplyResponse;
import com.chinamobile.iot.sc.response.web.Order2CInfoDetailDTO;
import com.chinamobile.iot.sc.response.web.invoice.*;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.constant.InvoiceConstant.*;
import static com.chinamobile.iot.sc.exception.StatusConstant.*;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: InvoiceServiceImpl
 * @description: 发票相关Service实现
 * @author: zyj
 * @create: 2021/11/30 15:02
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class InvoiceServiceImpl implements IInvoiceService {
    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    @Value("${sms.ApplyMakeInvoiceTempId:106054}")
    private String applyMakeInvoiceTempId;
    @Value("${sms.ApplyInvoiceRushTempId:106058}")
    private String applyInvoiceRushTempId;
    @Value("${sms.OrderMakeInvoiceId24:106055}")
    private String orderMakeInvoiceId24;
    @Value("${sms.OrderMakeInvoiceId48:106056}")
    private String orderMakeInvoiceId48;
    @Value("${sms.OrderMakeInvoiceId72:106057}")
    private String orderMakeInvoiceId72;
    @Value("${sms.OrderRushInvoiceId24:106059}")
    private String orderRushInvoiceId24;
    @Value("${sms.OrderRushInvoiceId48:106060}")
    private String orderRushInvoiceId48;
    @Value("${sms.OrderRushInvoiceId72:106061}")
    private String orderRushInvoiceId72;

    @Value("${iot.ftp.name}")
    private String sftpUserName;
    @Value("${iot.ftp.password}")
    private String sftpPassword;
    @Value("${iot.ftp.host}")
    private String sftpHost;
    @Value("${iot.ftp.port}")
    private Integer sftpPort;
    @Value("${iot.ftp.workPath}")
    private String sftpWorkPath;
    @Value("${sms.OrderCancel24Time:1440}")
    private Integer orderCancel24Time;
    @Value("${sms.OrderCancel48Time:2880}")
    private Integer orderCancel48Time;
    @Value("${sms.OrderCancel72Time:4320}")
    private Integer orderCancel72Time;

    @Value("${revenue.token.url}")
    private String revenueTokenUrl;

    @Value("${revenue.token.appKey}")
    private String revenueAppKey;

    @Value("${revenue.token.appSecret}")
    private String revenueAppSecret;

    @Value("${revenue.invoiceApply.url}")
    private String revenueInvoiceApplyUrl;

    @Value("${revenue.invoiceApply.isTest}")
    private String revenueInvoiceApplyIsTest;

    @Value("${revenue.getInvoice.url}")
    private String revenueGetInvoiceUrl;

    @Resource
    private ApplyInvoiceInfoMapper invoiceInfoMapper;
    @Resource
    private ApplyInvoiceRecMapper invoiceRecMapper;
    @Resource
    private ApplyInvoiceRecordMapperExt invoiceRecordMapperExt;
    @Resource
    private InvoiceReverseRecMapper reverseRecMapper;
    @Resource
    private InvoiceReverseInfoMapper reverseInfoMapper;
    @Resource
    private InvoiceReverseRecordMapperExt reverseRecordMapperExt;
    @Resource
    private Order2cInfoMapper order2cInfoMapper;
    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;
    @Resource
    private SmsFeignClient smsFeignClient;
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private IStorageService storageService;
    @Resource
    private IInvoice2IOTService invoice2IOTService;
    @Resource
    private IOrder2CService order2CService;

    @Resource
    private QuartzManager quartzManager;

    @Resource
    private LogService logService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private OrderCooperatorRelationService orderCooperatorRelationService;

    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");

    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);

    /**
     *  定时开票24小时短信  Reverse
     */
    private static final String QUARTZ_MAKE_INVOICE_SMS_24 = "QUARTZ_MAKE_INVOICE_SMS_24";
    /**
     * 定时开票48小时短信
     */
    private static final String QUARTZ_MAKE_INVOICE_SMS_48 = "QUARTZ_MAKE_INVOICE_SMS_48";

    /**
     * 定时开票72小时短信
     */
    private static final String QUARTZ_MAKE_INVOICE_SMS_72 = "QUARTZ_MAKE_INVOICE_SMS_72";

    /**
     *  定时发票冲红24小时短信  Reverse
     */
    private static final String QUARTZ_INVOICE_REVERSE_SMS_24 = "QUARTZ_INVOICE_REVERSE_SMS_24";
    /**
     * 定时发票冲红48小时短信
     */
    private static final String QUARTZ_INVOICE_REVERSE_SMS_48 = "QUARTZ_INVOICE_REVERSE_SMS_48";

    /**
     * 定时发票冲红72小时短信
     */
    private static final String QUARTZ_INVOICE_REVERSE_SMS_72 = "QUARTZ_INVOICE_REVERSE_SMS_72";
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));



    /**
     *@Description: IOT商城同步开票申请至OS系统
     *@param baseRequest:
     *@return: IOTAnswer<Void>
     *@Author: zyj
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IOTAnswer<Void> InvoiceRequest2OS(IOTRequest baseRequest) {
        log.info("同步发票申请信息请求:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        //获取beid
        String beId = baseRequest.getBeId();
        InvoiceApplyDTO invoiceApply = null;
        try {
            invoiceApply = JSON.parseObject(baseRequest.getContent(), InvoiceApplyDTO.class);
        } catch (Exception e) {
            log.error("IOT商城同步开票申请至OS系统-InvoiceApplyRequest，JSON解析失败",e.toString());
            throw new IOTException(iotAnswer, "数据解析异常");
        }
        if(ObjectUtils.isNotEmpty(invoiceApply) && ObjectUtils.isNotEmpty(invoiceApply.getOrderId())){
            //校验发票业务唯一标识
            if(ObjectUtils.isNotEmpty(invoiceApply) && ObjectUtils.isNotEmpty(invoiceApply.getOrderSeq())){
                List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                        .eq(ApplyInvoiceRec::getOrderSeq, invoiceApply.getOrderSeq()));
                if(ObjectUtils.isNotEmpty(applyInvoiceRecs)){
                    throw new IOTException(iotAnswer, "发票业务唯一标识重复！");
                }
            }
            // 获取业务订单信息-获取客户编码
            Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
            Order2cInfoExample.Criteria infoExampleCriteria = order2cInfoExample.createCriteria();
            infoExampleCriteria.andOrderIdEqualTo(invoiceApply.getOrderId());
            List<Order2cInfo> order2cInfos = order2cInfoMapper.selectByExample(order2cInfoExample);
            Order2cInfo order2cInfo = order2cInfos.get(0);
            log.info("查询到业务订单为：{}", JSONObject.toJSONString(order2cInfo));
            //获取客户编码(加密)
            String custCode = order2cInfo.getCustCode();
            // 获取原子商品订单信息(目前只有硬件订单)
            Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
            Order2cAtomInfoExample.Criteria criteria = atomInfoExample.createCriteria();
            criteria.andOrderIdEqualTo(invoiceApply.getOrderId());
            //只查询硬件订单, 软件服务只有A类型开票
            criteria.andAtomOfferingClassIn(Arrays.asList("H", "A"));
            List<Order2cAtomInfo> order2cAtomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
            log.info("查询到原子订单为：{}", JSONObject.toJSONString(order2cAtomInfos));
            if(ObjectUtils.isNotEmpty(order2cAtomInfos)){
                for(Order2cAtomInfo order2cAtomInfo : order2cAtomInfos){
                    //获取原子订单总价，单位：厘
                    Long totalPrice = null;
                    //获取合作伙伴手机号
                    String partnerPhone = null;
                    //启用内部调用新接口，老接口增加水平越权验证
                    BaseAnswer<Order2CInfoDetailDTO> orderDetail = order2CService.getOrderDetailInternal(order2cAtomInfo.getId());
//                    BaseAnswer<Order2CInfoDetailDTO> orderDetail = order2CService.getOrderDetail(order2cAtomInfo.getId());
                    Order2CInfoDetailDTO data = orderDetail.getData();
                    String atomPrice = data.getAtomPrice() != null ? data.getAtomPrice() : "0";
                    /**
                     * 订购数量 skuQuantity*atomQuantity,因目前一个业务订单只有一个单独的一个原子订单，
                     * 故现在原子订单申请记录总价 就等于 业务订单总价
                     */
                    Long quantity = data.getQuantity() != null ? data.getQuantity() : 0L;
                    totalPrice = quantity * Long.parseLong(atomPrice);
                    //获取 合作伙伴id
                    String cooperatorId = order2cAtomInfo.getCooperatorId();
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(cooperatorId);
                    log.info("查询合作伙伴id：{}，用户信息为：{}", cooperatorId , JSONObject.toJSONString(data4UserBaseAnswer));
                    Data4User userBaseAnswerData = data4UserBaseAnswer.getData();
                    partnerPhone = userBaseAnswerData.getPhone();
                    // 根据合作伙伴-原子商品订单，初始化开票申请记录（按照不同合作伙伴-cooperator_id）
                    TaxpayerInfoDTO taxpayerInfo = invoiceApply.getTaxpayerInfo();
                    ApplyInvoiceRec applyInvoiceRec = new ApplyInvoiceRec().setId(BaseServiceUtils.getId())
                            .setOrderId(invoiceApply.getOrderId()).setOrderSeq(invoiceApply.getOrderSeq())
                            .setAtomOrderId(order2cAtomInfo.getId()).setBeId(beId).setCustCode(custCode)
                            .setPrintDate(invoiceApply.getPrintDate()).setFrank(invoiceApply.getFrank()).setPName(taxpayerInfo.getPName())
                            .setIdentifyNum(taxpayerInfo.getIdentifyNum()).setAddressInfo(taxpayerInfo.getAddressInfo())
                            .setPhoneNumber(taxpayerInfo.getPhoneNumber()).setBankName(taxpayerInfo.getBankName())
                            .setBankId(taxpayerInfo.getBankID()).setOrderPrice(totalPrice).setStatus(InvoiceConstant.STATUS_INVOICE_APPLY_ENTRY)
                            .setCooperatorId(cooperatorId)
                            .setCreateTime(new Date()).setUpdateTime(new Date())
                            .setVoucherSum(invoiceApply.getVoucherSum());
                    //保存入库
                    invoiceRecMapper.insert(applyInvoiceRec);
                    log.info("成功记录开具发票申请：{}", JSON.toJSONString(applyInvoiceRec));

                    // 软件服务A类型直接发请求到应收系统去开票
                    if(order2cAtomInfo.getAtomOfferingClass().equals(AtomOfferingClassEnum.A.getAtomOfferingClass())){
                        executor.execute(() -> {
                            try {
                                Thread.sleep(10 * 1000L);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            revenueInvoiceApply(applyInvoiceRec.getId());
                        });
                    }else{
                        // 发送提示短信 - 原子商品订单信息（发送订单id） 应该是原子订单id，原子商品订单信息
                        List<String> mobiles = new ArrayList<>();
                        //查询合作伙伴主账号
                       /* BaseAnswer<Data4User> userPhone = userFeignClient.queryPrimaryUserPhone(data4UserBaseAnswer.getData().getUserId());
                        if (userPhone.getData().getIsSend()){
                            mobiles.add(userPhone.getData().getPhone());
                        }*/
                        Boolean isSend = userBaseAnswerData.getIsSend();
                        if (isSend != null && isSend){
                            mobiles.add(partnerPhone);
                        }

                        // 获取原子订单从合作伙伴
                        List<Data4User> data4UserList = orderCooperatorRelationService.listCooperatorUserInfo(order2cAtomInfo.getId(), order2cAtomInfo.getOrderId());
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(data4UserList)){
                            List<String> cooperatorPhoneList = data4UserList.stream().map(Data4User::getPhone)
                                    .collect(Collectors.toList());
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cooperatorPhoneList)){
                                mobiles.addAll(cooperatorPhoneList);
                            }
                        }

                        if (CollectionUtil.isNotEmpty(mobiles)){
                            sendSmsInvoiceApply(mobiles, applyInvoiceRec.getOrderId(),STATUS_INVOICE_APPLY_ENTRY);

                            //生产改为24小时 60*24
                            QuartzJobConf quartzJobConf24 = composeQuartzJobConf(applyInvoiceRec.getId(), QUARTZ_MAKE_INVOICE_SMS_24, new Date(), orderCancel24Time, mobiles, applyInvoiceRec.getOrderId(), orderMakeInvoiceId24);
                            if (quartzManager.saveQuartzJob(quartzJobConf24, SendInvoiceRushRedJob.class) == 1) {
                                log.info("24小时短信通知开票任务添加成功");
                            } else {
                                log.warn("24小时短信通知开票任务添加失败");
                            }
                            //添加48小时后短信提醒
                            //生产改为48小时 2*60*24
                            QuartzJobConf quartzJobConf48 = composeQuartzJobConf(applyInvoiceRec.getId(), QUARTZ_MAKE_INVOICE_SMS_48, new Date(), orderCancel48Time, mobiles, applyInvoiceRec.getOrderId(), orderMakeInvoiceId48);
                            if (quartzManager.saveQuartzJob(quartzJobConf48, SendInvoiceRushRedJob.class) == 1) {
                                log.info("48小时短信通知开票任务添加成功");
                            } else {
                                log.warn("48小时短信通知开票任务添加失败");
                            }
                            //添加72小时后短信提醒
                            //生产改为72小时 3*60*24
                            QuartzJobConf quartzJobConf72 = composeQuartzJobConf(applyInvoiceRec.getId(), QUARTZ_MAKE_INVOICE_SMS_72, new Date(), orderCancel72Time, mobiles, applyInvoiceRec.getOrderId(), orderMakeInvoiceId72);
                            if (quartzManager.saveQuartzJob(quartzJobConf72, SendInvoiceRushRedJob.class) == 1) {
                                log.info("72小时短信通知开票任务添加成功");
                            } else {
                                log.warn("72小时短信通知开票任务添加失败");
                            }
                        }
                        log.info("发送开具发票提示短信，订单号：{}，手机号：{}", applyInvoiceRec.getOrderId(), mobiles);
                    }

                }
            }
        }else{
            //解析请求为null，则返回失败
            iotAnswer.setResultCode("-1");
            iotAnswer.setResultDesc("解析请求结果为null");
        }
        return iotAnswer;
    }
    /**
     *@Description: 录入发票信息，文件；并同步至IOT商城
     *@param invoiceEntryListJSON: 发票信息
     *@param files: 发票文件
     *@return: BaseAnswer<java.lang.Void>
     *@Author: zyj
     *@date: 2021/12/6 15:10
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> invoicingResult2IOTMall(String invoiceEntryListJSON, String orderReq
            , MultipartFile[] files, String result,String userId,String ip) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        List<InvoiceEntryRequest> invoiceEntryRequests = null;
        ApplyInvoiceRec applyInvoiceRec = null;
        //获取开票开具结果
        result = result != null ? result : InvoiceConstant.IOT_RESULT_INVOICE_SUCC;
        try {
            invoiceEntryRequests = JSONObject.parseArray(invoiceEntryListJSON
                    , InvoiceEntryRequest.class);
        }catch (Exception e){
            log.error("解析invoiceEntryRequests-发票信息请求失败,错误内容{}; 请求json内容：{}", e.getMessage(), invoiceEntryListJSON);
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                        "【录入发票信息】", userId, ip, LogResultEnum.LOG_FAIL.code, INVOICEENTRYREQUEST_JSON_ERROR.getMessage());
            });
            throw new BusinessException(INVOICEENTRYREQUEST_JSON_ERROR);
        }
        //查询原子商品订单开票申请记录
        InvoiceEntryRequest entryRequest = invoiceEntryRequests.get(0);
//        List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
//                .eq(ApplyInvoiceRec::getAtomOrderId, entryRequest.getAtomOrderId()));
        List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                .eq(ApplyInvoiceRec::getAtomOrderId, entryRequest.getAtomOrderId())
                .eq(ApplyInvoiceRec::getOrderSeq, orderReq));
        if (CollectionUtil.isEmpty(applyInvoiceRecs)){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                        "【录入发票信息】", userId, ip, LogResultEnum.LOG_FAIL.code, REQUEST_ORDER_NOT_EXISTS.getMessage());
            });
            throw new BusinessException(REQUEST_ORDER_NOT_EXISTS);
        }
        applyInvoiceRec = applyInvoiceRecs.get(0);
        // 发票开具成功
        if(InvoiceConstant.IOT_RESULT_INVOICE_SUCC.equals(result)){
            //记录发票信息
            List<ApplyInvoiceInfo> invoiceInfos = new ArrayList<>();
            // 4.2 记录发票录入信息（原子订单）
            if(ObjectUtils.isNotEmpty(invoiceEntryRequests) && ObjectUtils.isNotEmpty(files)){
                // 校验录入发票信息与发票文件数量
                if(invoiceEntryRequests.size() != files.length){
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                                "【录入发票信息】", userId, ip, LogResultEnum.LOG_FAIL.code, INVOICE_INFO_FILE_ERROR.getMessage());
                    });
                    throw new BusinessException(INVOICE_INFO_FILE_ERROR);
                }
                //解密客户编码
                String custCode = IOTEncodeUtils.decryptSM4(applyInvoiceRec.getCustCode(), iotSm4Key, iotSm4Iv);
                // 原子订单总价
                Long atomOrderInvoicePrice = 0L;
                for(int i=0; i<invoiceEntryRequests.size(); i++){
                    ApplyInvoiceInfo applyInvoiceInfo = new ApplyInvoiceInfo();
                    InvoiceEntryRequest request = invoiceEntryRequests.get(i);
                    // 封装发票文件名
                    String voucherFileName = String.format("%s_Voucher_%s_%s_%s_%s.pdf", applyInvoiceRec.getBeId(), InvoiceConstant.MONTH_INVOICE
                            ,custCode, applyInvoiceRec.getOrderSeq(), ShareCodeUtil.getRandomNum(6));
                    //叠加发票金额
                    atomOrderInvoicePrice += request.getVoucherSum();
                    //封装发票信息
                    applyInvoiceInfo.setId(BaseServiceUtils.getId())
                            .setOrderId(applyInvoiceRec.getOrderId())
                            .setAtomOrderId(request.getAtomOrderId()).setVoucherNum(request.getVoucherNum())
                            .setVoucherId(request.getVoucherID()).setVoucherSum(request.getVoucherSum())
                            .setBillingDate(request.getBillingDate())
                            .setCooperatorId(applyInvoiceRec.getCooperatorId()).setVoucherType(VOUCHER_TYPE_APPLY)
                            .setCustCode(applyInvoiceRec.getCustCode()).setOrderSeq(applyInvoiceRec.getOrderSeq())
                            .setBeId(applyInvoiceRec.getBeId()).setVoucherFile(voucherFileName).setCreateTime(new Date());
                    //记录发票信息
                    invoiceInfos.add(applyInvoiceInfo);
                    invoiceInfoMapper.insert(applyInvoiceInfo);
                }
                /**
                 * 校验订单金额与发票金额必须一致
                 */
                if(atomOrderInvoicePrice.compareTo(applyInvoiceRec.getOrderPrice()) != 0L){
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                                "【录入发票信息】", userId, ip, LogResultEnum.LOG_FAIL.code, INVOICE_TOTAL_PRICE_ERROR.getMessage());
                    });
                    throw new BusinessException(INVOICE_TOTAL_PRICE_ERROR);
                }
                // 根据跟IOT同事协商，先上传文件，再反馈同步（********-尤寅龙）
                for(int i=0; i<invoiceInfos.size(); i++){
                    ApplyInvoiceInfo applyInvoiceInfo = invoiceInfos.get(i);
                    // 封装发票文件名
                    String voucherFileName = applyInvoiceInfo.getVoucherFile();
                    //获取对应发票文件
                    MultipartFile invoiceFile = files[i];
                    File file = null;
                    FileInputStream fileInputStream = null;
                    // 4.4 上传至对象存储-发票文件
                    try {
                        file = multipartFileToFile(invoiceFile);
                        String filePathName = String.format("%s/%s", custCode,voucherFileName);
                        //上传至对象存储系统
                        BaseAnswer<UpResult> data = storageService.uploadFile(file, filePathName
                                , InvoiceConstant.IS_COVER, InvoiceConstant.EXPIRED_DAY);
                        UpResult upResult = data.getData();
                        fileInputStream = new FileInputStream(file);
                        applyInvoiceInfo.setVoucherInnerUrl(upResult.getInnerUrl());
                        applyInvoiceInfo.setVoucherOuterUrl(upResult.getOuterUrl());
                        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
                        try {
                            // 4.5 按命名要求，上传文件至IOT商城
                            log.info("上传sftp地址，host：{}，port：{}，name：{}，password：{}，workPath：{}， voucherFileName：{}",
                                    sftpHost, sftpPort, sftpUserName, sftpPassword
                                    , sftpWorkPath, voucherFileName);
                            if(sftpUtil.login()){
                                log.info("sftp连接成功！");
                                log.info("开始上传文件原名:{}，上传后文件名：{}", file.getName(), voucherFileName);
                                sftpUtil.upload(sftpWorkPath, voucherFileName, fileInputStream);
                            }
                        }catch (Exception e){
                            log.error("SFTP上传文件失败！{}", e.getMessage());
                            executorService.execute(() -> {
                                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                                        "【录入发票信息】", userId, ip, LogResultEnum.LOG_FAIL.code, OSS_UPLOAD_ERROR.getMessage());
                            });
                            throw new BusinessException(OSS_UPLOAD_ERROR);
                        }finally {
                            sftpUtil.logout();
                            log.info("登出sftp服务！");
                        }
                    }catch (Exception e){
                        log.error("OS系统上传对象存储失败！{}", e.getMessage());
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                                    "【录入发票信息】", userId, ip, LogResultEnum.LOG_FAIL.code, OSS_UPLOAD_ERROR.getMessage());
                        });
                        throw new BusinessException(OSS_UPLOAD_ERROR);
                    }finally {
                        if(fileInputStream !=null){
                            try {
                                fileInputStream.close();
                            } catch (IOException e) {
                                log.error("关闭文件流失败！{}", e.getMessage());
                            }
                        }
                        if(file != null && file.exists()){
                            file.delete();
                        }
                    }
                    //更新发票信息-存储地址
                    invoiceInfoMapper.updateById(applyInvoiceInfo);
                }
            }else{
                baseAnswer.setStatus(REQUEST_FILE_ERROR);
            }
            // 发票开具失败
        }else if(InvoiceConstant.IOT_RESULT_INVOICE_FAIL.equals(result)){

        }
        // 4.3 同步至IOT商城-电子发票开具结果反馈接口（业务订单id下所有子订单完成发票录入后进行反馈）(原子订单状态更新后是否能马上查到)
        invoice2IOTService.invoicingResult2IOTMall(applyInvoiceRec, result);
        //开具发票成功删除定时任务 避免发送短信
        try {
            String id = applyInvoiceRec.getId();
            QuartzJobConf quartzJobConf = composeQuartzJobConf(id, QUARTZ_MAKE_INVOICE_SMS_24, new Date(), 1, null, "", "");
            QuartzJobConf quartzJobConf1 = composeQuartzJobConf(id, QUARTZ_MAKE_INVOICE_SMS_48, new Date(), 1, null, "", "");
            QuartzJobConf quartzJobConf2 = composeQuartzJobConf(id, QUARTZ_MAKE_INVOICE_SMS_72, new Date(), 1, null, "", "");
            quartzManager.removeQuartzJobByTaskId(quartzJobConf1);
            quartzManager.removeQuartzJobByTaskId(quartzJobConf2);
            quartzManager.removeQuartzJobByTaskId(quartzJobConf);
        } catch (Exception e) {
            log.warn("移除订单开票定时任务抛出异常，异常描述:{}", e.toString());
        }

        //记录日志
        logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code, ReceiptManageOperateEnum.INVOICING.code,
                IotLogUtil.invoicingResultContentFromRequest(applyInvoiceRec.getOrderId(),invoiceEntryRequests),LogResultEnum.LOG_SUCESS.code, null);
        return baseAnswer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> invoiceVoidCallback(InvoiceRevNewRequest invoiceRevRequest, String result, String errorDesc,String ip,String userId) {
        log.info("invoiceVoidCallback invoiceRequest = {}",invoiceRevRequest);
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        InvoiceReverseRec invoiceReverseRec = null;
        String orderReq = invoiceRevRequest.getOrderSeq();
//        List<InvoiceRevRequest> invoiceRevRequests = invoiceRevRequest.getInvoiceRevList();
        String arrayString = invoiceRevRequest.getInvoiceRevList();
        log.info("invoiceVoidCallback arrayString = {}",arrayString);
        List<InvoiceRevRequest> invoiceRevRequests = JSONArray.parseArray(arrayString, InvoiceRevRequest.class);
        log.info("invoiceVoidCallback orderId = {}, list = {}",orderReq, invoiceRevRequests);
        //获取开票开具结果
        result = result != null ? result : InvoiceConstant.IOT_RESULT_INVOICE_REV_SUCC;
        //查询原子商品订单冲红申请记录
        InvoiceRevRequest revRequest = invoiceRevRequests.get(0);
//        List<InvoiceReverseRec> invoiceReverseRecs = reverseRecMapper.selectList(new QueryWrapper<InvoiceReverseRec>().lambda()
//                .eq(InvoiceReverseRec::getAtomOrderId, revRequest.getAtomOrderId()));
        List<InvoiceReverseRec> invoiceReverseRecs = reverseRecMapper.selectList(new QueryWrapper<InvoiceReverseRec>().lambda()
                .eq(InvoiceReverseRec::getAtomOrderId, revRequest.getAtomOrderId())
                .eq(InvoiceReverseRec::getOrderSeq, orderReq));
        if(ObjectUtils.isEmpty(invoiceReverseRecs)){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.RECEIPT_MANAGE.code,ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.invoiceVoidContentFromRequest(orderReq,invoiceRevRequests), userId, ip, LogResultEnum.LOG_FAIL.code, REVERSE_INFO_REC_NULL.getMessage());
            });
            throw new BusinessException(REVERSE_INFO_REC_NULL);
        }
        invoiceReverseRec = invoiceReverseRecs.get(0);
        //校验冲红记录，若为冲红已成功，则不允许再次提交
        if(InvoiceConstant.STATUS_INVOICE_REVERSE_SUCC.equals(invoiceReverseRec.getStatus())){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.RECEIPT_MANAGE.code,ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.invoiceVoidContentFromRequest(orderReq,invoiceRevRequests), userId, ip, LogResultEnum.LOG_FAIL.code, REVERSE_REC_SUCC_REPEAT.getMessage());
            });
            throw new BusinessException(REVERSE_REC_SUCC_REPEAT);
        }
//        List<InvoiceReverseInfo> invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
//                .eq(InvoiceReverseInfo::getAtomOrderId, revRequest.getAtomOrderId()));
        List<InvoiceReverseInfo> invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
                .eq(InvoiceReverseInfo::getAtomOrderId, revRequest.getAtomOrderId())
                .eq(InvoiceReverseInfo::getOrderSeq, orderReq));
        if(ObjectUtils.isEmpty(invoiceReverseInfos)){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.RECEIPT_MANAGE.code,ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.invoiceVoidContentFromRequest(orderReq,invoiceRevRequests), userId, ip, LogResultEnum.LOG_FAIL.code, REVERSE_REC_SUCC_REPEAT.getMessage());
            });
            throw new BusinessException(REVERSE_INFO_REC_NULL);
        }
        InvoiceReverseInfo invoiceReverseInfo = invoiceReverseInfos.get(0);
        String beId = invoiceReverseInfo.getBeId();
        log.info("atomOrderInfo PrimaryKey = {}",invoiceReverseInfo.getAtomOrderId());
        Order2cAtomInfo atomInfo = atomOrderInfoMapper.selectByPrimaryKey(invoiceReverseInfo.getAtomOrderId());
        String regionId = "";
        if(null == atomInfo){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.RECEIPT_MANAGE.code,ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.invoiceVoidContentFromRequest(orderReq,invoiceRevRequests), userId, ip, LogResultEnum.LOG_FAIL.code, REVERSE_INFO_ATOMORDER_NULL.getMessage());
            });
            throw new BusinessException(REVERSE_INFO_ATOMORDER_NULL);
        }else{
            regionId = atomInfo.getRegionId();
        }
//        String regionId = atomOrderInfoMapper.selectByPrimaryKey(invoiceReverseInfo.getAtomOrderId()).getRegionId();
        // 封装冲红反馈请求
        IotRevInovBackRequest iotRevInovBackRequest = new IotRevInovBackRequest();
        iotRevInovBackRequest.setOrderSeq(invoiceReverseRec.getOrderSeq());
        iotRevInovBackRequest.setCustomerNumber(invoiceReverseRec.getCustomerNumber());
        iotRevInovBackRequest.setOperType(invoiceReverseRec.getOperType());
        iotRevInovBackRequest.setBeId(beId);
        iotRevInovBackRequest.setResult(result);
        iotRevInovBackRequest.setErrorDesc(errorDesc);
        // 冲红发票成功
        if(InvoiceConstant.IOT_RESULT_INVOICE_REV_SUCC.equals(result)){
            List<IotRevInovBackRequest.VoucherInfo> voucherInfos = new ArrayList<>();
            iotRevInovBackRequest.setVoucherInfo(voucherInfos);
            // 校验录入发票信息与发票文件数量
            /*if(invoiceRevRequests.size() != files.length){
                throw new BusinessException(INVOICE_INFO_FILE_ERROR);
            }*/
            if(ObjectUtils.isNotEmpty(invoiceRevRequests)){
                // 订单总价（目前为业务订单-硬件总价）
                Long atomOrderInvoicePrice = 0L;
                for(int i=0; i<invoiceRevRequests.size(); i++){
                    InvoiceRevRequest request = invoiceRevRequests.get(i);
                    InvoiceReverseInfo invoiceInfo = invoiceReverseInfos.get(0);
                    Long voucherSum = request.getVoucherSum() != null ? request.getVoucherSum() : 0L;
                    IotRevInovBackRequest.VoucherInfo voucherInfo = new IotRevInovBackRequest.VoucherInfo();
                    voucherInfo.setCreditNoteNum(invoiceInfo.getCreditNoteNum());
                    // A13的传固定值
                    if(atomInfo.getAtomOfferingClass().equals("A")){
                        voucherInfo.setVoucherID("100000000000");
                        voucherInfo.setVoucherNum("30000000");
                        voucherInfo.setCreditNoteID("200000000000");
                    }else{
                        voucherInfo.setCreditNoteID(invoiceInfo.getCreditNoteId());
                        voucherInfo.setVoucherID(invoiceInfo.getVoucherId());
                        voucherInfo.setVoucherNum(request.getVoucherNum());
                    }
                    voucherInfo.setVoucherSum(request.getVoucherSum());
                    atomOrderInvoicePrice+= voucherSum;
                    voucherInfo.setBillingDate(request.getBillingDate());
                    voucherInfo.setRemark(request.getRemark());
                    voucherInfos.add(voucherInfo);
                }
                /**
                 * 校验订单金额与发票金额必须一致
                 */
                if(atomOrderInvoicePrice.compareTo(invoiceReverseRec.getOrderPrice()) != 0L){
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.RECEIPT_MANAGE.code,ReceiptManageOperateEnum.REVERSAL.code,
                                IotLogUtil.invoiceVoidContentFromRequest(orderReq,invoiceRevRequests), userId, ip, LogResultEnum.LOG_FAIL.code, INVOICE_TOTAL_PRICE_ERROR.getMessage());
                    });
                    throw new BusinessException(INVOICE_TOTAL_PRICE_ERROR);
                }
            }else{
                baseAnswer.setStatus(REQUEST_FILE_ERROR);
            }
        }
        // 同步至IOT商城-电子发票冲红结果反馈接口、更新冲红发票记录状态（业务订单id下所有子订单完成发票录入后进行反馈）
        invoice2IOTService.invoiceVoidCallback(iotRevInovBackRequest, result, regionId);

        //发票冲红成功删除定时任务 避免发送短信
        try {
            String id = invoiceReverseRec.getId();
            QuartzJobConf quartzJobConf = composeQuartzJobConf(id, QUARTZ_INVOICE_REVERSE_SMS_24, new Date(), 1, null, "", "");
            QuartzJobConf quartzJobConf1 = composeQuartzJobConf(id, QUARTZ_INVOICE_REVERSE_SMS_48, new Date(), 1, null, "", "");
            QuartzJobConf quartzJobConf2 = composeQuartzJobConf(id, QUARTZ_INVOICE_REVERSE_SMS_72, new Date(), 1, null, "", "");
            quartzManager.removeQuartzJobByTaskId(quartzJobConf1);
            quartzManager.removeQuartzJobByTaskId(quartzJobConf2);
            quartzManager.removeQuartzJobByTaskId(quartzJobConf);
        } catch (Exception e) {
            log.warn("移除发票冲红定时任务抛出异常，异常描述:{}", e.toString());
        }

        //记录日志
        logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,ReceiptManageOperateEnum.REVERSAL.code,
                IotLogUtil.invoiceVoidContentFromRequest(orderReq,invoiceRevRequests),LogResultEnum.LOG_SUCESS.code, null);
        return baseAnswer;
    }

    /**
     * @Des IOT发票冲红申请同步至OS
     * @param baseRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IOTAnswer<Void> InvoiceVoid(IOTRequest baseRequest) {
        log.info("发票冲红请求:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        InvoiceReverseDTO invoiceReverseReq = null;
        try {
            invoiceReverseReq = JSON.parseObject(baseRequest.getContent(), InvoiceReverseDTO.class);
        } catch (Exception e) {
            log.error("IOT商城同步发票冲红申请至OS系统-InvoiceReverseDTO，JSON解析失败",e.toString());
            throw new IOTException(iotAnswer, "数据解析异常");
        }
        // 获取业务订单信息-获取客户编码
        Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        Order2cInfoExample.Criteria infoExampleCriteria = order2cInfoExample.createCriteria();
        infoExampleCriteria.andOrderIdEqualTo(invoiceReverseReq.getOrderId());
        List<Order2cInfo> order2cInfos = order2cInfoMapper.selectByExample(order2cInfoExample);
        Order2cInfo order2cInfo = order2cInfos.get(0);
        log.info("查询到业务订单为：{}", JSONObject.toJSONString(order2cInfo));
        // 获取原子商品订单信息(目前只有硬件订单)
        Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
        Order2cAtomInfoExample.Criteria criteria = atomInfoExample.createCriteria();
        criteria.andOrderIdEqualTo(invoiceReverseReq.getOrderId());
        //只查询硬件订单, 软件服务只有A类型开票
        criteria.andAtomOfferingClassIn(Arrays.asList("H", "A"));
        List<Order2cAtomInfo> order2cAtomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
        if(ObjectUtils.isNotEmpty(invoiceReverseReq) && ObjectUtils.isNotEmpty(invoiceReverseReq.getOrderId())){
            if(ObjectUtils.isNotEmpty(order2cAtomInfos)){
                for(Order2cAtomInfo order2cAtomInfo : order2cAtomInfos) {
                    //获取业务订单id
                    Long totalPrice = 0L;
                    Long atomPrice = order2cAtomInfo.getAtomPrice() != null ? order2cAtomInfo.getAtomPrice() : 0L;
                    Long atomQuantity = order2cAtomInfo.getAtomQuantity() != null ? order2cAtomInfo.getAtomQuantity() : 0L;
                    Long skuQuantity = order2cAtomInfo.getSkuQuantity() != null ? order2cAtomInfo.getSkuQuantity() : 0L;
                    totalPrice = atomPrice * atomQuantity * skuQuantity;
                    // 封装发票冲红申请记录对象
                    InvoiceReverseRec invoiceReverseRec = new InvoiceReverseRec().setId(BaseServiceUtils.getId())
                            .setOrderId(invoiceReverseReq.getOrderId()).setOrderSeq(invoiceReverseReq.getOrderSeq())
                            .setAtomOrderId(order2cAtomInfo.getId()).setCooperatorId(order2cAtomInfo.getCooperatorId())
                            .setOperType(invoiceReverseReq.getOperType()).setCustomerType(invoiceReverseReq.getCustomerType())
                            .setCustomerNumber(invoiceReverseReq.getCustomerNumber())
                            .setStatus(InvoiceConstant.STATUS_INVOICE_REVERSE_ENTRY)
                            /**
                             * 计算订单总价：atomPrice * atomQuantity * skuQuantity
                             */
                            .setOrderPrice(totalPrice).setCreateTime(new Date()).setUpdateTime(new Date());
                    reverseRecMapper.insert(invoiceReverseRec);
                    // 封装发票冲红申请记录-发票信息对象
                    List<VoucherInfoDTO> voucherInfos = invoiceReverseReq.getVoucherInfo();
                    if (ObjectUtils.isNotEmpty(voucherInfos)) {
                        for (VoucherInfoDTO voucher : voucherInfos) {
                            InvoiceReverseInfo reverseInfo = new InvoiceReverseInfo().setId(BaseServiceUtils.getId())
                                    .setOrderId(invoiceReverseReq.getOrderId()).setAtomOrderId(order2cAtomInfo.getId())
                                    .setOrderSeq(invoiceReverseReq.getOrderSeq()).setBeId(voucher.getBeId())
                                    .setHomeCity(voucher.getHomeCity()).setBillingDate(voucher.getBillingDate())
                                    .setFrank(voucher.getFrank()).setDes(voucher.getDes()).setVoucherType(voucher.getVoucherType())
                                    .setCreditNoteId(voucher.getVoucherID()).setCreditNoteNum(voucher.getVoucherNum())
                                    .setCreditNoteSum(voucher.getVoucherSum())
                                    .setVoucherId(voucher.getVoucherID()).setVoucherNum(voucher.getVoucherNum())
                                    .setVoucherSum(voucher.getVoucherSum())
                                    .setCooperatorId(order2cAtomInfo.getCooperatorId())
                                    .setCreateTime(new Date()).setUpdateTime(new Date());
                            reverseInfoMapper.insert(reverseInfo);
                        }
                    }
                    //获取 合作伙伴id
                    String cooperatorId = order2cAtomInfo.getCooperatorId();
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(cooperatorId);
                    log.info("查询合作伙伴id：{}，用户信息为：{}", cooperatorId, JSONObject.toJSONString(data4UserBaseAnswer));
                    Data4User userBaseAnswerData = data4UserBaseAnswer.getData();
                    String partnerPhone = userBaseAnswerData.getPhone();

                    List<String> mobiles = new ArrayList<>();

                    //查询合作伙伴主账号
                    /*BaseAnswer<Data4User> userPhone = userFeignClient.queryPrimaryUserPhone(data4UserBaseAnswer.getData().getUserId());
                    if (userPhone.getData().getIsSend()) {
                        mobiles.add(userPhone.getData().getPhone());
                    }*/
                    Boolean isSend = userBaseAnswerData.getIsSend();
                    if (isSend != null && isSend){
                        mobiles.add(partnerPhone);
                    }

                    // 获取订单从合作伙伴信息
                    List<Data4User> data4UserList = orderCooperatorRelationService.listCooperatorUserInfo(order2cAtomInfo.getId(), order2cAtomInfo.getOrderId());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(data4UserList)){
                        List<String> cooperatorPhoneList = data4UserList.stream().map(Data4User::getPhone)
                                .collect(Collectors.toList());
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cooperatorPhoneList)){
                            mobiles.addAll(cooperatorPhoneList);
                        }
                    }

                    if (CollectionUtil.isNotEmpty(mobiles)) {
                        sendSmsInvoiceApply(mobiles, invoiceReverseReq.getOrderId(),STATUS_INVOICE_REVERSE_ENTRY);
                        // TODO 添加24小时后短信提醒  更新模板  发票冲红成功后删除定时任务
                        //生产改为24小时 60*24
                        QuartzJobConf quartzJobConf24 = composeQuartzJobConf(invoiceReverseRec.getId(), QUARTZ_INVOICE_REVERSE_SMS_24, new Date(), orderCancel24Time, mobiles, invoiceReverseRec.getOrderId(), orderRushInvoiceId24);
                        if (quartzManager.saveQuartzJob(quartzJobConf24, SendInvoiceRushRedJob.class) == 1) {
                            log.info("24小时短信通知发票冲红任务添加成功");
                        } else {
                            log.warn("24小时短信通知发票冲红任务添加失败");
                        }
                        //添加48小时后短信提醒
                        //生产改为48小时 2*60*24
                        QuartzJobConf quartzJobConf48 = composeQuartzJobConf(invoiceReverseRec.getId(), QUARTZ_INVOICE_REVERSE_SMS_48, new Date(), orderCancel48Time, mobiles, invoiceReverseRec.getOrderId(), orderRushInvoiceId48);
                        if (quartzManager.saveQuartzJob(quartzJobConf48, SendInvoiceRushRedJob.class) == 1) {
                            log.info("48小时短信通知发票冲红任务添加成功");
                        } else {
                            log.warn("48小时短信通知发票冲红任务添加失败");
                        }
                        //添加48小时后短信提醒
                        //生产改为48小时 2*60*24
                        QuartzJobConf quartzJobConf72 = composeQuartzJobConf(invoiceReverseRec.getId(), QUARTZ_INVOICE_REVERSE_SMS_72, new Date(), orderCancel72Time, mobiles, invoiceReverseRec.getOrderId(), orderRushInvoiceId72);
                        if (quartzManager.saveQuartzJob(quartzJobConf72, SendInvoiceRushRedJob.class) == 1) {
                            log.info("72小时短信通知发票冲红任务添加成功");
                        } else {
                            log.warn("72小时短信通知发票冲红任务添加失败");
                        }
                    }
                }
            }
        }else{
            //解析请求为null，则返回失败
            iotAnswer.setResultCode("-1");
            iotAnswer.setResultDesc("解析请求结果为null");
        }
        return iotAnswer;
    }

    // 数据权限-联合销售开具发票
    @Override
    public BaseAnswer<PageData<Data4ApplyInvoiceRec>> findPageByInvoiceIdOrOrderId(Request4InvoRecPage request
            , String userId, LoginIfo4Redis loginIfo4Redis) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        BaseAnswer<PageData<Data4ApplyInvoiceRec>> answer = new BaseAnswer<>();
        PageData<Data4ApplyInvoiceRec> pageData = new PageData<>();
        // 根据角色类型，查询申请开发票记录
        String roleType = loginIfo4Redis.getRoleType();
        Integer pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
        Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
        String invoiceApplyId = request.getInvoiceApplyId();
        String orderId = request.getOrderId();
        Integer status = ObjectUtils.isNotEmpty(request.getStatus()) ? request.getStatus() : null;
        String orderSeq = request.getOrderSeq();
        log.info("findPageByInvoiceIdOrOrderId status = {}, userId = {}, roleType = {}",status, userId, roleType);
        Long count = 0L;
        List<String> userIdList = new ArrayList<>();
        List<ApplyInvoiceRecDO> results = new ArrayList();
        // 超管、运营管理员 客服管理员角色查询
        if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_SYSTEM)){
            results = invoiceRecordMapperExt.page4InvoiceRec(null, invoiceApplyId,orderId, status,orderSeq, (pageNum - 1)*pageSize, pageSize);
            count = invoiceRecordMapperExt.pageCount4InvoiceRec(null, invoiceApplyId,orderId, status,orderSeq);
            // 从合作伙伴查询
        }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)){
            userIdList.add(userId);
            results = invoiceRecordMapperExt.page4InvoiceRec(userIdList, invoiceApplyId,orderId, status, orderSeq,(pageNum - 1)*pageSize, pageSize);
            count = invoiceRecordMapperExt.pageCount4InvoiceRec(userIdList, invoiceApplyId,orderId, status,orderSeq);
        //主合作伙伴
        }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)){
            BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
            if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                userIdList = downUserIds.getData();
            }
            userIdList.add(userId);
            results = invoiceRecordMapperExt.page4InvoiceRec(userIdList, invoiceApplyId,orderId, status,orderSeq, (pageNum - 1)*pageSize, pageSize);
            count = invoiceRecordMapperExt.pageCount4InvoiceRec(userIdList, invoiceApplyId,orderId, status,orderSeq);
        }
        List<Data4ApplyInvoiceRec> applyInvoiceRecs = new ArrayList<>();
        results.forEach(x -> {
            Data4ApplyInvoiceRec data4ApplyInvoiceRec = new Data4ApplyInvoiceRec();
            BeanUtils.copyProperties(x, data4ApplyInvoiceRec);
            if (x.getFinishCooperatorId() != null && !x.getFinishCooperatorId().equals(x.getCooperatorId())) {
                if (STATUS_INVOICE_SUCC.equals(x.getStatus())
                        || STATUS_INVOICE_REVERSE_SUCC.equals(x.getStatus())) {
                    data4ApplyInvoiceRec.setCooperatorId(x.getFinishCooperatorId());
                    data4ApplyInvoiceRec.setCooperatorName(x.getFinishCooperatorName());
                }
            }
            //原子商品类型显示
            String orgOfferingClass = x.getOfferingClass();
            String atomType = "";
            if(orgOfferingClass!=null){
                switch(orgOfferingClass){
                    case "H":
                        atomType = "硬件";
                        break;
                    case "S":
                        atomType = "软件功能费";
                        break;
                    case "D":
                        atomType = "(DICT)产品增值服务包";
                        break;
                    case "O":
                        atomType = "OneNET独立服务";
                        break;
                    default:
                        break;
                }
            }
            log.info("findPageByInvoiceIdOrOrderId offeringClass = {}, atomType = {}",orgOfferingClass, atomType);
            data4ApplyInvoiceRec.setOfferingClass(atomType);

            data4ApplyInvoiceRec.setSpuClassOfferingName(SPUOfferingClassEnum.getDisplay(x.getSpuOfferingClass()));

            applyInvoiceRecs.add(data4ApplyInvoiceRec);
        });

        // 封装成分页响应对象
        pageData.setData(applyInvoiceRecs);
        pageData.setCount(count);
        pageData.setPage(request.getPageNum());
        answer.setData(pageData);
        return answer;
    }

    @Override
    public BaseAnswer<List<ApplyInvoiceInfo>> findInvoicesByOrderId(String orderId, String orderReq, String userId
            , LoginIfo4Redis loginIfo4Redis) {
        String content = "【查看发票详情】\n"
                .concat("订单号").concat(orderId);
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)
        )) {
            logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                    ReceiptManageOperateEnum.INVOICING.code,
                    content, LogResultEnum.LOG_FAIL.code, StatusConstant.UN_PERMISSION.getMessage());
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        BaseAnswer<List<ApplyInvoiceInfo>> answer = new BaseAnswer<>();
        // 根据角色类型，查询申请开发票记录
        String roleType = loginIfo4Redis.getRoleType();
        List<ApplyInvoiceInfo> applyInvoiceInfos = null;
        // 若业务订单id为空，则返回空值
        if(ObjectUtils.isNotEmpty(orderId)){
            // 超管、运营管理员角色查询
            if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_SYSTEM)){
//                applyInvoiceInfos = invoiceInfoMapper.selectList(new QueryWrapper<ApplyInvoiceInfo>().lambda()
//                        .eq(ObjectUtils.isNotEmpty(orderId), ApplyInvoiceInfo::getOrderId, orderId));
                applyInvoiceInfos = invoiceInfoMapper.selectList(new QueryWrapper<ApplyInvoiceInfo>().lambda()
                        .eq(ObjectUtils.isNotEmpty(orderId), ApplyInvoiceInfo::getOrderId, orderId)
                        .eq(ApplyInvoiceInfo::getOrderSeq, orderReq));
                // 合作伙伴查询
            }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)){
//                applyInvoiceInfos = invoiceInfoMapper.selectList(new QueryWrapper<ApplyInvoiceInfo>().lambda()
//                        .eq(ObjectUtils.isNotEmpty(orderId), ApplyInvoiceInfo::getOrderId, orderId)
//                        .eq(ApplyInvoiceInfo::getCooperatorId, userId));
                // 获取从合作伙伴关联的原子订单信息
                List<String> cooperatorOrderIdList = null;
                OrderCooperatorInfoParam orderCooperatorInfoParam = new OrderCooperatorInfoParam();
                orderCooperatorInfoParam.setCooperatorId(userId);
                List<OrderCooperatorInfoDTO> orderCooperatorInfoDTOList
                        = orderCooperatorRelationService.listCooperatorInfo(orderCooperatorInfoParam);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderCooperatorInfoDTOList)){
                    cooperatorOrderIdList = orderCooperatorInfoDTOList.stream().map(OrderCooperatorInfoDTO::getOrderId)
                            .collect(Collectors.toList());
                }

                applyInvoiceInfos = invoiceInfoMapper.selectList(new QueryWrapper<ApplyInvoiceInfo>().lambda()
                        .eq(ObjectUtils.isNotEmpty(orderId), ApplyInvoiceInfo::getOrderId, orderId)
//                        .eq(ApplyInvoiceInfo::getCooperatorId, userId)
                                .in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(cooperatorOrderIdList),ApplyInvoiceInfo::getOrderId,cooperatorOrderIdList)
                        .eq(ApplyInvoiceInfo::getOrderSeq, orderReq));
            }else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)){
                // 主合作伙伴
                /*BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                List<String> data = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                    data = downUserIds.getData();
                }
                data.add(userId);*/
//                applyInvoiceInfos = invoiceInfoMapper.selectList(new QueryWrapper<ApplyInvoiceInfo>().lambda()
//                        .eq(ObjectUtils.isNotEmpty(orderId), ApplyInvoiceInfo::getOrderId, orderId)
//                        .in(ApplyInvoiceInfo::getCooperatorId,data));
                applyInvoiceInfos = invoiceInfoMapper.selectList(new QueryWrapper<ApplyInvoiceInfo>().lambda()
                        .eq(ObjectUtils.isNotEmpty(orderId), ApplyInvoiceInfo::getOrderId, orderId)
                        .eq(ApplyInvoiceInfo::getOrderSeq, orderReq)
//                        .in(ApplyInvoiceInfo::getCooperatorId,data)
                        .eq(ApplyInvoiceInfo::getCooperatorId, userId));
            }
        }
        answer.setData(applyInvoiceInfos);

        //记录日志

        logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                ReceiptManageOperateEnum.INVOICING.code,
                content, LogResultEnum.LOG_SUCESS.code, null);
        return answer;
    }

    @Override
    public BaseAnswer<Boolean> isSuccInvoiceByAtomOrderId(String atomOrderId) {
        BaseAnswer<Boolean> answer = new BaseAnswer<>();
        Boolean isSuccInvoice = false;
        List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                .eq(ApplyInvoiceRec::getAtomOrderId, atomOrderId));
        //获取第一个，判断是否已开票
        if(ObjectUtils.isNotEmpty(applyInvoiceRecs)){
            ApplyInvoiceRec applyInvoiceRec = applyInvoiceRecs.get(0);
            if(InvoiceConstant.STATUS_INVOICE_SUCC.equals(applyInvoiceRec.getStatus())){
                isSuccInvoice = true;
            }
        }
        answer.setData(isSuccInvoice);
        return answer;
    }

    // 数据权限-联合销售发票冲红
    @Override
    public BaseAnswer<PageData<Data4InvoiceRevRec>> findPageByRevIdOrOrderId(Request4InvoRevPage request, String userId
            , LoginIfo4Redis loginIfo4Redis) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        BaseAnswer<PageData<Data4InvoiceRevRec>> answer = new BaseAnswer<>();
        PageData<Data4InvoiceRevRec> pageData = new PageData<>();
        // 根据角色类型，查询申请开发票记录
        String roleType = loginIfo4Redis.getRoleType();
        Integer pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
        Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
        String invoiceReverseId = request.getInvoiceReverseId();
        String orderId = request.getOrderId();
        Integer status = ObjectUtils.isNotEmpty(request.getStatus()) ? request.getStatus() : null;
        String orderSeq = request.getOrderSeq();

        List<String> userIdList = new ArrayList<>();
        Long count = 0L;
        List<InvoiceReverseRecDO> results = new ArrayList();
        // 超管、运营管理员角色 客服管理员查询
        if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_SYSTEM)){
            results = reverseRecordMapperExt.page4InvoRevRec(null, invoiceReverseId,orderId, status,orderSeq, (pageNum - 1)*pageSize, pageSize);
            count = reverseRecordMapperExt.pageCount4InvoRevRec(null, invoiceReverseId,orderId, status,orderSeq);
            // 从合作伙伴查询
        }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_PERSONAL)){
            userIdList.add(userId);
            results = reverseRecordMapperExt.page4InvoRevRec(userIdList, invoiceReverseId,orderId, status,orderSeq, (pageNum - 1)*pageSize, pageSize);
            count = reverseRecordMapperExt.pageCount4InvoRevRec(userIdList, invoiceReverseId,orderId, status,orderSeq);
            //主合作
        }else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_COMPANY)){
            BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
            if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                userIdList = downUserIds.getData();
            }
            userIdList.add(userId);
            results = reverseRecordMapperExt.page4InvoRevRec(userIdList, invoiceReverseId,orderId, status,orderSeq, (pageNum - 1)*pageSize, pageSize);
            count = reverseRecordMapperExt.pageCount4InvoRevRec(userIdList, invoiceReverseId,orderId, status,orderSeq);
        }
        List<Data4InvoiceRevRec> invoiceRevRecs = new ArrayList<>();
        results.forEach(x -> {
            Data4InvoiceRevRec data4InvoiceRevRec = new Data4InvoiceRevRec();
            BeanUtils.copyProperties(x, data4InvoiceRevRec);
            data4InvoiceRevRec.setSpuOfferingClassName(SPUOfferingClassEnum.getDisplay(x.getSpuOfferingClass()));
            if (x.getFinishCooperatorId() != null && !x.getFinishCooperatorId().equals(x.getCooperatorId())) {
                if (STATUS_INVOICE_SUCC.equals(x.getStatus())
                        || STATUS_INVOICE_REVERSE_SUCC.equals(x.getStatus())) {
                    data4InvoiceRevRec.setCooperatorId(x.getFinishCooperatorId());
                    data4InvoiceRevRec.setCooperatorName(x.getFinishCooperatorName());
                }
            }
            invoiceRevRecs.add(data4InvoiceRevRec);
        });
        // 封装成分页响应对象
        pageData.setData(invoiceRevRecs);
        pageData.setCount(count);
        pageData.setPage(request.getPageNum());
        answer.setData(pageData);
        return answer;
    }

    @Override
    public BaseAnswer<List<InvoiceReverseInfo>> findRevInfoByOrderId(String orderId, String orderReq
            , String userId, LoginIfo4Redis loginIfo4Redis) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        BaseAnswer<List<InvoiceReverseInfo>> answer = new BaseAnswer<>();
        // 根据角色类型，查询申请开发票记录
        String roleType = loginIfo4Redis.getRoleType();
        List<InvoiceReverseInfo> invoiceReverseInfos = null;
        // 若业务订单id为空，则返回空值
        if(ObjectUtils.isNotEmpty(orderId)){
            // 超管、运营管理员角色查询
            if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_SYSTEM)){
//                invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
//                        .eq(ObjectUtils.isNotEmpty(orderId), InvoiceReverseInfo::getOrderId, orderId));
                invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
                        .eq(ObjectUtils.isNotEmpty(orderId), InvoiceReverseInfo::getOrderId, orderId).eq(InvoiceReverseInfo::getOrderSeq, orderReq));
                // 从合作伙伴查询
            }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_PERSONAL)){
//                invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
//                        .eq(ObjectUtils.isNotEmpty(orderId), InvoiceReverseInfo::getOrderId, orderId)
//                        .eq(InvoiceReverseInfo::getCooperatorId, userId));
                // 获取从合作伙伴关联的原子订单信息
                List<String> cooperatorOrderIdList = null;
                OrderCooperatorInfoParam orderCooperatorInfoParam = new OrderCooperatorInfoParam();
                orderCooperatorInfoParam.setCooperatorId(userId);
                List<OrderCooperatorInfoDTO> orderCooperatorInfoDTOList
                        = orderCooperatorRelationService.listCooperatorInfo(orderCooperatorInfoParam);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderCooperatorInfoDTOList)){
                    cooperatorOrderIdList = orderCooperatorInfoDTOList.stream().map(OrderCooperatorInfoDTO::getOrderId)
                            .collect(Collectors.toList());
                }
                invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
                        .eq(ObjectUtils.isNotEmpty(orderId), InvoiceReverseInfo::getOrderId, orderId)
                        .eq(InvoiceReverseInfo::getOrderSeq, orderReq)
//                        .eq(InvoiceReverseInfo::getCooperatorId, userId)
                                .in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(cooperatorOrderIdList),InvoiceReverseInfo::getOrderId,cooperatorOrderIdList)
                );
                //主合作伙伴
            }else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_COMPANY)){
                /*BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                List<String> data =new ArrayList<>();
                if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                    data = downUserIds.getData();
                }
                data.add(userId);*/
//                invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
//                        .eq(ObjectUtils.isNotEmpty(orderId), InvoiceReverseInfo::getOrderId, orderId)
//                        .in(InvoiceReverseInfo::getCooperatorId,data ));
                invoiceReverseInfos = reverseInfoMapper.selectList(new QueryWrapper<InvoiceReverseInfo>().lambda()
                        .eq(ObjectUtils.isNotEmpty(orderId), InvoiceReverseInfo::getOrderId, orderId)
                        .eq(InvoiceReverseInfo::getOrderSeq, orderReq)
//                        .in(InvoiceReverseInfo::getCooperatorId,data )
                                .eq(InvoiceReverseInfo::getCooperatorId,userId)
                );
            }
        }
        answer.setData(invoiceReverseInfos);
        return answer;
    }

    /**
     * 判断订单是否已开发票成功,true已开发票成功、false未开发票
     * @param orderId
     * @return
     */
    @Override
    public BaseAnswer<Boolean> isSuccInvoiceByOrderId(String orderId) {
        BaseAnswer<Boolean> answer = new BaseAnswer<>();
        answer.setData(false);
        if(ObjectUtils.isEmpty(orderId)){
            throw new BusinessException(StatusConstant.ORDER_NULL_ERROR);
        }
        List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                .eq(ApplyInvoiceRec::getOrderId, orderId).eq(ApplyInvoiceRec::getStatus, InvoiceConstant.STATUS_INVOICE_SUCC));

        answer.setData(ObjectUtils.isNotEmpty(applyInvoiceRecs));

        return answer;
    }


    @Override
    public void exportNotInvoiceExcel(LoginIfo4Redis loginIfo4Redis, String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)
        )) {
            logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                    ReceiptManageOperateEnum.INVOICING.code,
                    IotLogUtil.exportNotInvoiceContent(),LogResultEnum.LOG_FAIL.code, StatusConstant.UN_PERMISSION.getMessage());
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        try{
            String roleType = loginIfo4Redis.getRoleType();
            log.info("exportNotInvoiceExcel roleType = {}",roleType);
            List<Data4UnInvoice> unInvoiceRecs = null;
            List<String> userIdList = new ArrayList<>();
            if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_SYSTEM)){
                //超管客户经理,查询所有的
//                unInvoiceRecs = invoiceRecMapper.getUnInvoiceRec(null, 1);
                unInvoiceRecs = invoiceRecordMapperExt.getUnInvoiceRec(null, 1);
            }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)){
                //主账号查询从账号的
                BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                    userIdList = downUserIds.getData();
                }
                userIdList.add(userId);

                unInvoiceRecs = invoiceRecordMapperExt.getUnInvoiceRec(userIdList, 1);
            }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)){
                //从账号只能查看自己名下的
                userIdList.add(userId);
                unInvoiceRecs = invoiceRecordMapperExt.getUnInvoiceRec(userIdList, 1);
            }else{
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                        ReceiptManageOperateEnum.INVOICING.code,
                        IotLogUtil.exportNotInvoiceContent(),LogResultEnum.LOG_FAIL.code, StatusConstant.AUTH_ERROR.getMessage());
                throw new BusinessException(StatusConstant.AUTH_ERROR);
            }

            if(unInvoiceRecs.isEmpty()){
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                        ReceiptManageOperateEnum.INVOICING.code,
                        IotLogUtil.exportNotInvoiceContent(),LogResultEnum.LOG_FAIL.code, StatusConstant.INVOICE_RECORD_NOT_EXIST.getMessage());
                throw new BusinessException(StatusConstant.INVOICE_RECORD_NOT_EXIST);
            }

            //excel DTO
            List<UnInvoiceExportDTO> unInvoiceExcel = new ArrayList<>();
            unInvoiceRecs.forEach(unInvoiceItem->{
                UnInvoiceExportDTO dto = new UnInvoiceExportDTO();
                BeanUtils.copyProperties(unInvoiceItem,dto);
//                dto.setOrderPrice(unInvoiceItem.getOrderPrice() != null ? unInvoiceItem.getOrderPrice() : null);
                //计算总价，厘 转化为 元避免SQL语句复杂化
                if(unInvoiceItem.getOrderPrice() != null){
//                    dto.setOrderPrice(dto.getOrderPrice()/1000);
                    BigDecimal divider = new BigDecimal(unInvoiceItem.getOrderPrice());
                    BigDecimal divid = new BigDecimal(1000);
                    String excelPrice = divider.divide(divid,2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
                    dto.setOrderPrice(excelPrice);
                }
                if("0".equals(unInvoiceItem.getFrank())){
                    //0专票
                    dto.setFrank("专票");

                }else if("1".equals(unInvoiceItem.getFrank())){
                    //1普票
                    dto.setFrank("普票");
                }
                String dateStr = DateUtils.dateToStr(unInvoiceItem.getCreateTime(), DateUtils.DATE_INVOICE_FORMAT);
                dto.setStatus("待开票");
                dto.setInvoiceTime(dateStr);
                dto.setPname(unInvoiceItem.getPName());
                unInvoiceExcel.add(dto);
            });
            //

            if(unInvoiceExcel.isEmpty()){
                log.error("exportNotInvoiceExcel 空excel 列表");
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                        ReceiptManageOperateEnum.INVOICING.code,
                        IotLogUtil.exportNotInvoiceContent(),LogResultEnum.LOG_FAIL.code, StatusConstant.INVOICE_RECORD_NOT_EXIST.getMessage());
                throw new BusinessException(StatusConstant.INVOICE_RECORD_NOT_EXIST);
            }

            Date now = new Date();
            String filename = DateUtils.dateToStr(now, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            log.info("exportNotInvoiceExcel formatFilename = {}",filename);
            //导出excel
            ServletOutputStream outputStream = null;
            try {
                Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("未开票发票列表_"+filename, "发票列表", ExcelType.XSSF),
                        UnInvoiceExportDTO.class, unInvoiceExcel);
                outputStream = response.getOutputStream();
                response.setContentType("application/octet-stream");
                workbook.write(outputStream);

                //记录日志
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                        ReceiptManageOperateEnum.INVOICING.code,
                        IotLogUtil.exportNotInvoiceContent(),LogResultEnum.LOG_SUCESS.code, null);
            } catch (Exception e) {
                log.error("导出未开票发票列表excel出错",e);
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code,
                        ReceiptManageOperateEnum.INVOICING.code,
                        IotLogUtil.exportNotInvoiceContent(),LogResultEnum.LOG_FAIL.code,"导出未开票发票列表excel出错");
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"导出未开票发票列表excel出错");
            }finally {
                if(outputStream != null){
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }catch(Exception e){
            //便于前端拿到异常
            try {
                if(e instanceof BusinessException){
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode",businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                }else {
                    response.addHeader("stateCode",BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(),"UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public void exportNotRedFlushExcel(LoginIfo4Redis loginIfo4Redis, String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_PERSONAL)
        )) {
            logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code, ReceiptManageOperateEnum.REVERSAL.code,
                    IotLogUtil.exportNotRedFlushContent(),LogResultEnum.LOG_FAIL.code, StatusConstant.UN_PERMISSION.getMessage());
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        try{
            String roleType = loginIfo4Redis.getRoleType();
            log.info("exportNotRedFlushExcel roleType = {}",roleType);

            List<Data4UnRedFlush> unRedFlushRecs = null;
            List<String> userIdList = new ArrayList<>();
            if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_SYSTEM)){
                log.info("superadmin get redflushexcel");
                //超管客户经理,查询所有的
//                unRedFlushRecs = reverseRecMapper.getUnRedFlushRec(null, 4);
                unRedFlushRecs = reverseRecordMapperExt.getUnRedFlushRec(null, 4);
            }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_COMPANY)){
                //主账号查询从账号的
                BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                    userIdList = downUserIds.getData();
                }
                userIdList.add(userId);
                unRedFlushRecs = reverseRecordMapperExt.getUnRedFlushRec(userIdList, 4);
            }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_REVERSE_INVOICE_PERSONAL)){
                //从账号只能查看自己名下的
                userIdList.add(userId);
                unRedFlushRecs = reverseRecordMapperExt.getUnRedFlushRec(userIdList, 4);
            }else{
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code, ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.exportNotRedFlushContent(),LogResultEnum.LOG_FAIL.code, StatusConstant.AUTH_ERROR.getMessage());
                throw new BusinessException(StatusConstant.AUTH_ERROR);
            }

            if(unRedFlushRecs.isEmpty()){
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code, ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.exportNotRedFlushContent(),LogResultEnum.LOG_FAIL.code, StatusConstant.INVOICE_RECORD_NOT_EXIST.getMessage());
                throw new BusinessException(StatusConstant.INVOICE_RECORD_NOT_EXIST);
            }

            //excel DTO
            List<UnRedFlushExportDTO> unRedFlushExcel = new ArrayList<>();
            unRedFlushRecs.forEach(unRedFlushItem->{
                log.info("unRedFlushRecs = {}",unRedFlushItem);
                UnRedFlushExportDTO dto = new UnRedFlushExportDTO();
                BeanUtils.copyProperties(unRedFlushItem,dto);
//                dto.setOrderPrice(unRedFlushItem.getOrderPrice() != null ? unRedFlushItem.getOrderPrice() : null);
                //计算总价，厘 转化为 元避免SQL语句复杂化
                if(unRedFlushItem.getOrderPrice() != null){
//                    DecimalFormat df = new DecimalFormat("0.00");
//                    String excelPrice = df.format((float)unRedFlushItem.getOrderPrice()/1000);
                    BigDecimal divider = new BigDecimal(unRedFlushItem.getOrderPrice());
                    BigDecimal divid = new BigDecimal(1000);
                    String excelPrice = divider.divide(divid,2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
                    dto.setOrderPrice(excelPrice);
                }
                if("0".equals(unRedFlushItem.getCustomerType())){
                    //个人
                    dto.setCustomerType("个人");
                }else if("1".equals(unRedFlushItem.getCustomerType())){
                    //1集团
                    dto.setCustomerType("团体");
                }else{
                    //没有值默认集团
                    dto.setCustomerType("团体");
                }
                String dateStr = DateUtils.dateToStr(unRedFlushItem.getCreateTime(), DateUtils.DATE_INVOICE_FORMAT);
                dto.setStatus("待录入冲红信息");
                dto.setFlushTime(dateStr);
                unRedFlushExcel.add(dto);
            });
            //


            Date now = new Date();
            String filename = DateUtils.dateToStr(now, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            log.info("exportNotRedFlushExcel formatFilename = {}",filename);

            //导出excel
            ServletOutputStream outputStream = null;
            try {
                Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("未冲红发票列表_"+filename, "发票列表", ExcelType.XSSF),
                        UnRedFlushExportDTO.class, unRedFlushExcel);
                outputStream = response.getOutputStream();
                response.setContentType("application/octet-stream");
                workbook.write(outputStream);

                //记录日志
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code, ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.exportNotRedFlushContent(),LogResultEnum.LOG_SUCESS.code, null);
            } catch (Exception e) {
                log.error("导出未冲红发票列表excel出错",e);
                logService.recordOperateLog(ModuleEnum.RECEIPT_MANAGE.code, ReceiptManageOperateEnum.REVERSAL.code,
                        IotLogUtil.exportNotRedFlushContent(),LogResultEnum.LOG_FAIL.code, "导出未冲红发票列表excel出错");
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"导出未冲红发票列表excel出错");
            }finally {
                if(outputStream != null){
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

        }catch(Exception e){
            //便于前端拿到异常
            try {
                if(e instanceof BusinessException){
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode",businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                }else {
                    response.addHeader("stateCode",BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(),"UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
        }


        reverseRecMapper.getUnRedFlushRec(null, 1);

    }

    /**
     *@Description: 发送提醒合作伙伴处理发票申请短信,发票冲红短信
     *@param phone: 手机号
     *@param orderId: 订单号
     *@return: java.lang.Integer
     *@Author: zyj
     *@date: 2021/11/1 9:52
     * 短信模板：【IoT应用商城】你有一个订单发票申请，订单号xxxx，请尽快登录系统处理，发票状态为待开票。
     * 短信模板：【IoT应用商城】你有一个订单发票冲红申请，订单号xxxx，请尽快登录系统处理，发票状态为待开票。
     */
    private Msg4Request sendSmsInvoiceApply(List<String> phone, String orderId,Integer status){
        Msg4Request request = new Msg4Request();
       /* List<String> mobiles = new ArrayList<>();
        mobiles.add(phone);*/
        phone = phone.stream().distinct().collect(Collectors.toList());
        Map<String,String> message = new HashMap<>();
        message.put("orderId",orderId);
        request.setMobiles(phone);
        request.setMessage(message);
       //开发票模板
        if (STATUS_INVOICE_APPLY_ENTRY.equals(status)){
            request.setTemplateId(applyMakeInvoiceTempId);
        }
        //TODO 发票冲红模板
        if (STATUS_INVOICE_REVERSE_ENTRY.equals(status)){
            request.setTemplateId(applyInvoiceRushTempId);
        }
        BaseAnswer<Void> messageAnswer = smsFeignClient.asySendMessage(request);
        return request;
    }

    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    //获取流文件
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param jobId      任务ID
     * @param eventType  事件类型
     * @param eventTime  事件时间
     * @param noticeTime 通知时间 分钟
     * @Description: 组织定时任务信息
     * @return: com.chinamobile.iot.sgs.quartz.QuartzJobConf
     * @Author: zyj
     * @date: 2021/8/20 16:30
     */
    private QuartzJobConf composeQuartzJobConf(String jobId, String eventType, Date eventTime, Integer noticeTime, List<String> phone, String orderId, String templateId) {
        QuartzJobConf quartzJobConf = null;

        quartzJobConf = new QuartzJobConf();
        //JobId_取消业务流水单号 方便后面查询

        quartzJobConf.setJobId(jobId);
        quartzJobConf.setJobName("JobName_" + jobId);
        quartzJobConf.setJobGroup("JobGroup_" + eventType);
        quartzJobConf.setTriggerName("TriggerName_" + jobId);
        quartzJobConf.setTriggerGroup("TriggerGroup_" + eventType);
        //获取消除预警状态触发时间
        Calendar calendar = DateTimeUtil.getAddTime(eventTime, noticeTime);
        //生成triggerCron
        quartzJobConf.setTriggerCron(String.format("%d %d %d %d %d ? %d",
                calendar.get(Calendar.SECOND), calendar.get(Calendar.MINUTE), calendar.get(Calendar.HOUR_OF_DAY)
                , calendar.get(Calendar.DAY_OF_MONTH), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.YEAR)));
        quartzJobConf.setPhone(phone);
        quartzJobConf.setOrderId(orderId);
        quartzJobConf.setRefundOrderId(jobId);
        quartzJobConf.setTemplateId(templateId);
        return quartzJobConf;
    }


    /**
     * @return
     * @Description: 请求应收系统返回发票信息和链接；并同步至IOT商城
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void invoicingResult2IOTMall1(String orderSeq) {

        List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                .eq(ApplyInvoiceRec::getOrderSeq, orderSeq));
        if (CollectionUtil.isEmpty(applyInvoiceRecs)){
            throw new BusinessException(REQUEST_ORDER_NOT_EXISTS);
        }

        for (ApplyInvoiceRec applyInvoiceRec : applyInvoiceRecs){
            // 请求应收系统获取信息
            GetInvoiceRequest getInvoiceRequest = new GetInvoiceRequest();
            getInvoiceRequest.setCurrentPage(1);
            getInvoiceRequest.setPageSize(5);
            getInvoiceRequest.setAccessToken(getRevenueToken());
            getInvoiceRequest.setApplyDocumentNumber(applyInvoiceRec.getApplyDocumentNumber());
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(getInvoiceRequest), headers);

                log.info("请求应收系统获取开票信息 request:{}", JSON.toJSONString(requestEntity));
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                ResponseEntity<GetInvoiceResponse> response = restTemplateHttps.postForEntity(revenueGetInvoiceUrl, requestEntity, GetInvoiceResponse.class);
                log.info("请求应收系统获取开票信息 response:{}", JSON.toJSONString(response));
                GetInvoiceResponse getInvoiceResponse = response.getBody();
                if (!getInvoiceResponse.getSuccessFlag().equals("Y")) {
                    log.info("请求应收系统获取开票信息失败");
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "请求应收系统获取开票信息");
                }else{
                    // 最后再更新状态，防止获取到了但是给商城失败了
                    // 4.2 记录发票录入信息（原子订单）
                    if(ObjectUtils.isNotEmpty(getInvoiceResponse.getList())){
                        //解密客户编码
                        String custCode = IOTEncodeUtils.decryptSM4(applyInvoiceRec.getCustCode(), iotSm4Key, iotSm4Iv);
                        for(GetInvoiceResponse.invoiceList invoiceInfo : getInvoiceResponse.getList()){
                            if(invoiceInfo.getInvoiceStatus() == 1){
                                for(int i=0; i<invoiceInfo.getSalesInvoiceAddressList().size(); i++){
                                    // 封装发票文件名
                                    String voucherFileName = String.format("%s_Voucher_%s_%s_%s_%s.pdf", applyInvoiceRec.getBeId(), InvoiceConstant.MONTH_INVOICE
                                            ,custCode, applyInvoiceRec.getOrderSeq(), ShareCodeUtil.getRandomNum(6));
                                    ApplyInvoiceInfo applyInvoiceInfo = new ApplyInvoiceInfo();
                                    applyInvoiceInfo.setId(BaseServiceUtils.getId())
                                            .setOrderId(applyInvoiceRec.getOrderId())
                                            .setAtomOrderId(applyInvoiceRec.getAtomOrderId())
                                            .setVoucherNum(invoiceInfo.getInvoiceNumber())
                                            .setVoucherId(invoiceInfo.getInvoiceCode())
                                            // 开票那边是元 转换成厘
                                            .setVoucherSum(invoiceInfo.getValoremTotal().multiply(new BigDecimal(1000)).longValue())
                                            .setBillingDate(invoiceInfo.getLogInfoList().get(0).getCreateTime().toString())
                                            .setCooperatorId(applyInvoiceRec.getCooperatorId())
                                            .setVoucherType(VOUCHER_TYPE_APPLY)
                                            .setCustCode(applyInvoiceRec.getCustCode())
                                            .setOrderSeq(applyInvoiceRec.getOrderSeq())
                                            .setBeId(applyInvoiceRec.getBeId())
                                            .setVoucherFile(voucherFileName)
                                            .setCreateTime(new Date());
                                    //记录发票信息
//                                    invoiceInfos.add(applyInvoiceInfo);
//                                    invoiceInfoMapper.insert(applyInvoiceInfo);
                                    String voucherUrl = invoiceInfo.getSalesInvoiceAddressList().get(i).getUrl();
                                    applyInvoiceInfo.setRevenueUrl(voucherUrl);
                                    // 4.4 上传至对象存储-发票文件
                                    try {
                                        URL url = new URL(voucherUrl);
                                        InputStream inputStream = url.openStream();
                                        byte[] fileBytes = downloadBytesFromUrl(voucherUrl);
                                        String filePathName = String.format("%s/%s", custCode,voucherFileName);
                                        //上传至对象存储系统
                                        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
                                        byteArrayUpload.setFileName(filePathName);
                                        byteArrayUpload.setBytes(fileBytes);
                                        BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
                                        if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传文件出错,请联系管理员");
                                        }
                                        UpResult upResult = resultBaseAnswer.getData();
                                        applyInvoiceInfo.setVoucherInnerUrl(upResult.getInnerUrl());
                                        applyInvoiceInfo.setVoucherOuterUrl(upResult.getOuterUrl());
                                        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
                                        try {
                                            // 4.5 按命名要求，上传文件至IOT商城
                                            log.info("上传sftp地址，host：{}，port：{}，name：{}，password：{}，workPath：{}， voucherFileName：{}",
                                                    sftpHost, sftpPort, sftpUserName, sftpPassword
                                                    , sftpWorkPath, voucherFileName);
                                            if(sftpUtil.login()){
                                                log.info("sftp连接成功！");
                                                log.info("上传后文件名：{}", voucherFileName);
                                                sftpUtil.upload(sftpWorkPath, voucherFileName, inputStream);
                                            }
                                        }catch (Exception e){
                                            log.error("SFTP上传文件失败！{}", e.getMessage());
                                            throw new BusinessException(OSS_UPLOAD_ERROR);
                                        }finally {
                                            sftpUtil.logout();
                                            log.info("登出sftp服务！");
                                        }
                                    }catch (Exception e){
                                        log.error("OS系统上传对象存储失败！{}", e.getMessage());
                                        throw new BusinessException(OSS_UPLOAD_ERROR);
                                    }finally {
                                    }

                                    invoiceInfoMapper.insert(applyInvoiceInfo);

                                    // 4.3 同步至IOT商城-电子发票开具结果反馈接口（业务订单id下所有子订单完成发票录入后进行反馈）(原子订单状态更新后是否能马上查到)
                                    invoice2IOTService.invoicingResult2IOTMall(applyInvoiceRec, "1");
                                }
                            }else if (invoiceInfo.getInvoiceStatus() == 4 || invoiceInfo.getInvoiceStatus() == -1){
                                // 开票失败
                                applyInvoiceRec.setStatus(-1);
                                applyInvoiceRec.setRemark(invoiceInfo.getInvoiceStatus() == 4 ? "开票失败":"未找到对应订单");
                                invoiceRecMapper.updateById(applyInvoiceRec);
                            }else{
                                //开票中 存一哈留个底
//                                applyInvoiceRec.setStatus(invoiceInfo.getInvoiceStatus());
//                                invoiceRecMapper.updateById(applyInvoiceRec);
                            }
                        }
                    }else{
                        //列表为空直接过
                    }
                }
            }
            catch (Exception e) {
                log.info("请求应收系统获取开票信息失败: {}",e.getMessage());
            }
        }
        return null;
    }

    public static byte[] downloadBytesFromUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            try (InputStream inputStream = url.openStream();
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                }
                return byteArrayOutputStream.toByteArray();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *@Description: 应收系统返回token
     */
    @Override
    public String getRevenueToken() {
        String token = (String) redisTemplate.opsForValue().get(Constant.REDIS_KEY_REVENUE_TOKEN);
        if (null == token) {
            String timeStamp = DateTimeUtil.formatDate(new Date(), DateTimeUtil.DEFAULT_DATE_DEFAULT);
            GetTokenRequest request = new GetTokenRequest();
            request.setAppKey(revenueAppKey);
            request.setAppSecret(revenueAppSecret);
            request.setTimestamp(timeStamp);
            String md5Str = revenueAppKey + revenueAppSecret + timeStamp;
            String sign = getMD5ForString(md5Str);
            request.setSign(sign);
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(request), headers);

                log.info("请求应收系统token request:{}", JSON.toJSONString(requestEntity));
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                ResponseEntity<GetTokenResponse> response = restTemplateHttps.postForEntity(revenueTokenUrl, requestEntity, GetTokenResponse.class);
                log.info("请求应收系统token response:{}", JSON.toJSONString(response));
                GetTokenResponse getTokenResponse = response.getBody();
                if(getTokenResponse.getSuccessFlag().equals("Y")){
                    token = getTokenResponse.getAccessToken();
                    redisTemplate.opsForValue().setIfAbsent(Constant.REDIS_KEY_REVENUE_TOKEN, token, 120,TimeUnit.MINUTES);
                }
            } catch (Exception e) {
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
            }
        }
        return token;
    }

    /**
     * 字符串MD5计算
     *
     * @param str
     *            字符串
     * @return 计算后的字符串
     */
    private String getMD5ForString(String str) {
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md.update(str.getBytes());
            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
            String md5 = new BigInteger(1, md.digest()).toString(16);
            // BigInteger会把0省略掉，需补全至32位
            return fillMD5(md5);
        } catch (Exception e) {
            throw new RuntimeException("MD5加密错误:" + e.getMessage(), e);
        }
    }

    /**
     * 将字符串填补为32位
     *
     * @param md5
     *            MD5字符串
     * @return 填补后的字符串
     */
    private  String fillMD5(String md5) {
        return md5.length() == 32 ? md5 : fillMD5("0" + md5);
    }


    /**
     *@Description: 开票
     */
    @Override
    public BaseAnswer<Void> revenueInvoiceApply(String recId) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                .eq(ApplyInvoiceRec::getId, recId));
        if(CollectionUtils.isEmpty(applyInvoiceRecs)){
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "发票申请不存在");
        }
        ApplyInvoiceRec applyInvoiceRec = applyInvoiceRecs.get(0);
        InvoiceApplyRequest invoiceApplyRequest = new InvoiceApplyRequest();
        invoiceApplyRequest.setAccessToken(getRevenueToken());
        invoiceApplyRequest.setOrderNumber(applyInvoiceRec.getOrderId());
        invoiceApplyRequest.setClientProperty("0");//先传0
        if(revenueInvoiceApplyIsTest.equals("Y")){
            invoiceApplyRequest.setInvoiceTitle("中移物联网有限公司虚拟单位");
        }else{
            invoiceApplyRequest.setInvoiceTitle(applyInvoiceRec.getPName());
        }
        if(StringUtils.isNotEmpty(applyInvoiceRec.getIdentifyNum())){
            invoiceApplyRequest.setTaxpayerNumber(applyInvoiceRec.getIdentifyNum());
        }else{
            if(revenueInvoiceApplyIsTest.equals("Y")){
                invoiceApplyRequest.setTaxpayerNumber("919901080000289272");
            }else{
                invoiceApplyRequest.setTaxpayerNumber("00000");
            }
        }
        invoiceApplyRequest.setClientOpeningBank(applyInvoiceRec.getBankName());
        invoiceApplyRequest.setClientAccount(applyInvoiceRec.getBankId());
        invoiceApplyRequest.setClientAddress(applyInvoiceRec.getAddressInfo());
        invoiceApplyRequest.setClientPhone(applyInvoiceRec.getPhoneNumber());
        if(applyInvoiceRec.getFrank().equals("1")){
            invoiceApplyRequest.setInvoiceType(0);
        }else{
            invoiceApplyRequest.setInvoiceType(1);
        }
        //有税号传0 无税号传1
        if(StringUtils.isNotEmpty(applyInvoiceRec.getIdentifyNum())){
            invoiceApplyRequest.setIsGmfzrrbz(0);
        }else{
            invoiceApplyRequest.setIsGmfzrrbz(1);
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(invoiceApplyRequest), headers);

            log.info("请求应收系统开票 request:{}", JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<InvoiceApplyResponse> response = restTemplateHttps.postForEntity(revenueInvoiceApplyUrl, requestEntity, InvoiceApplyResponse.class);
            log.info("请求应收系统开票 response:{}", JSON.toJSONString(response));
            InvoiceApplyResponse invoiceApplyResponse = response.getBody();
            if (!invoiceApplyResponse.getCode().equals("200")) {
                log.info("请求应收系统开票失败");
                applyInvoiceRec.setStatus(-1);
                applyInvoiceRec.setRemark(invoiceApplyResponse.getMsg());
                invoiceRecMapper.updateById(applyInvoiceRec);
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, invoiceApplyResponse.getMsg());
            }else{
                applyInvoiceRec.setApplyDocumentNumber(invoiceApplyResponse.getData().getApplyDocumentNumber());
                applyInvoiceRec.setStatus(1);
                invoiceRecMapper.updateById(applyInvoiceRec);
            }
        }
        catch (Exception e) {
            log.info("请求应收系统开票失败");
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        return  baseAnswer;
    }
}
