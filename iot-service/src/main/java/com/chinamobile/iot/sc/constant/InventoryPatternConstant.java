package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/2/4 11:07
 * @description: 库存模式常量类
 **/
public class InventoryPatternConstant {


    /**
     * 拍下减库存
     */
    public static final String INVENTORY_PATTERN_DOWN ="拍下减库存";


    /**
     * 付款减库存
     */
    public static final String INVENTORY_PATTERN_PAYMENT ="付款减库存";
    /**
     * 查询/预占 省库存
     */
    public static final Integer PROVINCE_INVENTORY = 0;

    /**
     * 查询/预占 地市库存
     */
    public static final Integer CITY_INVENTORY = 1;

    /**
     * 提单人类型：省
     */
    public static final Integer BOOK_RPOVINCE = 0;

    /**
     * 提单人类型：市
     */
    public static final Integer BOOK_CITY = 1;

    /**
     * 产品类型
     */
    public static final String KX_PRODUCT_TYPE_ONE = "1";

    /**
     * 产品类型
     */
    public static final String KX_PRODUCT_TYPE_TWO = "2";

    /**
     * 产品类型
     */
    public static final String KX_PRODUCT_TYPE_THREE = "3";

}
