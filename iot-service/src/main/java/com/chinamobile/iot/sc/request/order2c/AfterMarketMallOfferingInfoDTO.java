package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

/**
 * @Author: dgj
 * @Description:售后订单服务包关联的商品信息
 */
@Data
public class AfterMarketMallOfferingInfoDTO {
    /**
     * 销售商品编码
     */
    private String spuOfferingCode;
    /**
     * 规格商品编码
     */
    private String skuOfferingCode;
    /**
     * 原子商品编码,售后商品与原子商品绑定下单的，此字段必传；
     */
    private String atomOfferingCode;

    /**
     * 销售商品版本号
     */
    private String spuOfferingVersion;

    /**
     * 规格商品版本号
     */
    private String skuOfferingVersion;

    /**
     * 原子商品版本号
     */
    private String atomOfferingVersion;

}
