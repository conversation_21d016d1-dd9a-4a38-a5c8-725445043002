package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.request.express.SubscribeExpressParam;
import com.chinamobile.iot.sc.request.express.SubscribeReq;
import com.chinamobile.iot.sc.response.iot.express.BaseKD100Resp;
import com.chinamobile.iot.sc.response.iot.express.KD100SyncParamWithSign;
import com.chinamobile.iot.sc.response.iot.express.QueryTrackResp;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> xiemaohua
 * @date : 2022/4/13 16:39
 * @description: 100快递接口类
 **/
public interface IOT100ExpressService {

    /**
     *实时查询快递信息
     * @param baseRequest
     * @return
     */
    IOTAnswer<JSONObject> queryRealTimeExpress(IOTRequest baseRequest);

    /**
     *快递100物流回调
     * @param param
     * @return
     */
    BaseKD100Resp syncByKD100(KD100SyncParamWithSign param);

    /**
     *实时查询快递信息
     * @param param
     * @return
     */
    BaseAnswer<Void> subscribe(SubscribeExpressParam param);
}
