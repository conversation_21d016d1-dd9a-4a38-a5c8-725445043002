package com.chinamobile.iot.sc.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IImportDBService
 * @description: IOT商城数据导入数据库Service
 * @author: zyj
 * @create: 2022/3/3 10:28
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IImportDBService {
    /**
     * 解析、处理商品信息
     * @param file
     * @param resMap
     */
    void analyseProductInfo(MultipartFile file, Map<String, String> resMap);
    /**
     * 解析、处理订单信息
     * @param file
     * @param resMap
     */
    void analyse2COrderInfo(MultipartFile file, Map<String, String> resMap);

    /**
     * 解析、处理订单组织机构信息、操作员信息
     * @param file
     * @param resMap
     */
    void analyseOrderOrgBizInfo(MultipartFile file, Map<String, String> resMap);

}
