package com.chinamobile.iot.sc.request;

import lombok.Data;

@Data
public class IopMonitorRequest {
    /**
     * 接口编码
     */
    private String interfaceCode;

    /**
     * 成功1，失败0
     */
    private Integer upResult;

    /**
     * 日上报格式 yyyyMMdd
     * 月上报格式 yyyyMM
     */
    private String upPeriod;

    /**
     * 推送时间，格式 yyyy-MM-dd HH:mm:ss
     */
    private String pushTime;

    /**
     * 告警通知内容，当结果失败时需要告警展示的内容
     */
    private String alarmContent;
}
