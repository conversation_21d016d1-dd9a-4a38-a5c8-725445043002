package com.chinamobile.iot.sc.request.product;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/2 10:16
 * @Description: 商品组/销售商品
 */
@Data
public class SpuOfferingInfoDTO {
    /**
     * 商品组/销售商品编码
     */
    private String offeringCode;
    /**
     * 上架平台
     */
    private String listPlatform;
    /**
     * 商品组/销售商品名称
     */
    private String offeringName;
    /**
     * 商品状态
     * 0：测试
     * 1：发布;
     * 测试状态，表示仅用于咨询，未上架销售的商品。
     * 发布状态，表示已上架，可用于销售的商品。
     * <br>发布状态暂不启用。<br/>
     */
    private String offeringStatus;
//    /**
//     * 销售对象
//     */
//    private String saleObject;
    /**
     * 销售目录信息
     */
    private CategoryInfoDTO categoryInfo;
    /**
     * 操作类型 A-新增 M-修改
     */
    private String operType;
    /**
     * 创建时间
     */
    private String spucreationTime;
    /**
     * 商品详情页链接
     */
    private String productLinks;
    /**
     * 主图文件名
     */
    private String productImage;
    /**
     * 商品简介
     */
    private String productDescription;
    /**
     * 商品标签信息
     */
    private List<ProductLabelInfoDTO> productLabelInfo;
    /**
     * SKU信息
     */
    private List<SkuOfferingInfoDTO> skuOfferingInfo;

    /**
     * spu版本号
     */
    private String spuOfferingVersion;

    /**
     * 导航信息
     */
    private List<NavigationInfoDTO> navigationInfo;

    /**
     * 商品关键字,如有多组以“,”分隔进行传值
     */
    private String productKeywords;

    /**
     * 是否隐秘上架,0：是 1：否
     */
    private String secretlyListed;

    /**
     * 产品详情文件名
     */
    private List<String> productDetails;

    /**
     * 售后规则文件名
     */
    private List<String> afterSalesRules;
    /**
     * DICT范式产品线条 “00：和对讲”,“01：云视讯”
     */
    private String dictProductLines;

}
