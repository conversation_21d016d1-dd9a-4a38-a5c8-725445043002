package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceSkuMapperExt;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSkuDTO;
import com.chinamobile.iot.sc.pojo.param.ProductFlowSkuParam;
import com.chinamobile.iot.sc.service.ProductFlowInstanceSkuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5
 * @description 产品流程sku service实现类
 */
@Service
public class ProductFlowInstanceSkuServiceImpl implements ProductFlowInstanceSkuService {

    @Resource
    private ProductFlowInstanceSkuMapperExt productFlowInstanceSkuMapperExt;

    @Override
    public BaseAnswer<List<ProductFlowInstanceSkuDTO>> listProductFlowInstanceSku(ProductFlowSkuParam productFlowSkuParam) {
        BaseAnswer<List<ProductFlowInstanceSkuDTO>> baseAnswer = new BaseAnswer<>();

        List<Integer> shelfStatusList = new ArrayList<>();
        shelfStatusList.add(3);
        productFlowSkuParam.setShelfStatusList(shelfStatusList);

        List<Integer> flowTypeList = new ArrayList<>();
        flowTypeList.add(1);
        productFlowSkuParam.setFlowTypeList(flowTypeList);

        List<ProductFlowInstanceSkuDTO> productFlowInstanceSkuList = productFlowInstanceSkuMapperExt.listProductFlowInstanceSkuOnLine(productFlowSkuParam);
        if (CollectionUtils.isNotEmpty(productFlowInstanceSkuList)) {
            baseAnswer.setData(productFlowInstanceSkuList);
        }
        return baseAnswer;
    }
}
