package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.constant.ProvinceCodeEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.entity.b2b.OrderBackOrCancelDTO;
import com.chinamobile.iot.sc.entity.b2b.OrderUpdateDTO;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.B2BFeignClient;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoDetailHandle;
import com.chinamobile.iot.sc.pojo.param.OrderRealNameUpdateParam;
import com.chinamobile.iot.sc.pojo.vo.OrderRealNameVO;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.IOrderRealNameService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.AESUtils;
import com.chinamobile.iot.sc.util.DesensitizationUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestAttribute;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.API_RETURN_ERROR;

/**
 * <AUTHOR> xiemaohua
 * @date : 2025/3/4 17:29
 * @description:
 **/
@Service
@Slf4j
public class IOrderRealNameServiceImpl implements IOrderRealNameService {


    @Resource
    private OrderHandleMapper orderHandleMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private B2BFeignClient b2BFeignClient;

    @Resource
    private LogService logService;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    private static final String B2B_ORDER_PREFIX = "jkiot";

    @Override
    public BaseAnswer<OrderRealNameVO> getOrderRealName(String orderId) {

        BaseAnswer<OrderRealNameVO> baseAnswer = new BaseAnswer<>();
        OrderRealNameVO orderRealNameVO = new OrderRealNameVO();
        List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
            throw new BusinessException("10004","未获取到订单信息");
        }
        OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);
        //查询是否是河南合同履约  查询是否是卡+x 1 2 3类型  是否是待交货状态
        Integer orderStatus = orderInfoDetailHandle.getOrderStatus();
        String contactPersonName = orderInfoDetailHandle.getContactPersonName();
        String contactPhone = orderInfoDetailHandle.getContactPhone();
        String atomOfferingCode = orderInfoDetailHandle.getAtomOfferingCode();
        String spuOfferingCode = orderInfoDetailHandle.getSpuOfferingCode();
        String skuOfferingCode = orderInfoDetailHandle.getSkuOfferingCode();

        String addr1 = orderInfoDetailHandle.getAddr1();
        String addr2 = orderInfoDetailHandle.getAddr2();
        String addr3 = orderInfoDetailHandle.getAddr3();
        String addr4 = orderInfoDetailHandle.getAddr4();
        String usaddr = orderInfoDetailHandle.getUsaddr();

        boolean b2bFlow = isB2bFlow(addr1);

        if (StringUtils.isNotEmpty(addr1)) {
            addr1 = IOTEncodeUtils.decryptSM4(addr1, iotSm4Key, iotSm4Iv);
        }
        if (StringUtils.isNotEmpty(addr2)) {
            addr2 = IOTEncodeUtils.decryptSM4(addr2, iotSm4Key, iotSm4Iv);
        }
        if (StringUtils.isNotEmpty(addr3)) {
            addr3 = IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv);
        }else {
            addr3 ="";
        }
        if (StringUtils.isNotEmpty(addr4)) {
            addr4 = IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv);
        }else {
            addr4 ="";
        }
        if (StringUtils.isNotEmpty(usaddr)) {
            usaddr = IOTEncodeUtils.decryptSM4(usaddr, iotSm4Key, iotSm4Iv);
        }else {
            usaddr ="";
        }

        String productType ="";
        String spuOfferingClass = orderInfoDetailHandle.getSpuOfferingClass();

        Optional<SkuOfferingInfo> skuOfferingInfoOptional = skuOfferingInfoMapper.selectByExample(new SkuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(skuOfferingCode).example()).stream().findFirst();
        if (skuOfferingInfoOptional.isPresent() && SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(spuOfferingClass)){
            SkuOfferingInfo skuOfferingInfo = skuOfferingInfoOptional.get();
            productType = skuOfferingInfo.getProductType();
        }
        boolean b2bProductType = isB2bProductType(productType);
        Optional<AtomOfferingInfo> atomOfferingInfoOptional = atomOfferingInfoMapper.selectByExample(new AtomOfferingInfoExample().createCriteria()
                .andOfferingCodeEqualTo(atomOfferingCode).andSpuCodeEqualTo(spuOfferingCode).andSkuCodeEqualTo(skuOfferingCode).example()).stream().findFirst();
        if (!atomOfferingInfoOptional.isPresent()){
            throw new BusinessException(StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getStateCode(), StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getMessage());
        }
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoOptional.get();
        if (b2bFlow && ((spuOfferingClass.equals(SPUOfferingClassEnum.A11.getSpuOfferingClass()) && b2bProductType && AtomOfferingClassEnum.X.name().equals(atomOfferingInfo.getOfferingClass()))
                || (spuOfferingClass.equals(SPUOfferingClassEnum.A07.getSpuOfferingClass())  && AtomOfferingClassEnum.H.name().equals(atomOfferingInfo.getOfferingClass())))
          && orderStatus.equals(OrderStatusInnerEnum.WAIT_SEND.getStatus())){
            //符合条件的 才查询出来,判断是否已经更新过了
            String henanRealName = orderInfoDetailHandle.getHenanRealName();
            String henanRealPhone = orderInfoDetailHandle.getHenanRealPhone();
            String henanRealAddress = orderInfoDetailHandle.getHenanRealAddress();
            if (StringUtils.isNotEmpty(henanRealName) || StringUtils.isNotEmpty(henanRealPhone) || StringUtils.isNotEmpty(henanRealAddress)){
                orderRealNameVO.setOrderId(orderInfoDetailHandle.getOrderId());
                orderRealNameVO.setProvinceName(addr1);
                orderRealNameVO.setLocationName(addr2);
                orderRealNameVO.setHenanRealName(IOTEncodeUtils.decryptSM4(henanRealName, iotSm4Key, iotSm4Iv));
                orderRealNameVO.setHenanRealPhone(IOTEncodeUtils.decryptSM4(henanRealPhone, iotSm4Key, iotSm4Iv));
                orderRealNameVO.setHenanRealAddress(IOTEncodeUtils.decryptSM4(henanRealAddress, iotSm4Key, iotSm4Iv));
            }else {
                orderRealNameVO.setHenanRealName(IOTEncodeUtils.decryptSM4(contactPersonName, iotSm4Key, iotSm4Iv));
                orderRealNameVO.setHenanRealPhone(IOTEncodeUtils.decryptSM4(contactPhone, iotSm4Key, iotSm4Iv));
                orderRealNameVO.setOrderId(orderInfoDetailHandle.getOrderId());
                orderRealNameVO.setProvinceName(addr1);
                orderRealNameVO.setLocationName(addr2);
                orderRealNameVO.setHenanRealAddress(addr3+addr4+usaddr);
            }
        }else {
            throw new BusinessException(StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getStateCode(), StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getMessage());
        }
        return baseAnswer.setData(orderRealNameVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> updateOrderRealName(OrderRealNameUpdateParam param,LoginIfo4Redis loginIfo4Redis) {
        List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(param.getOrderId());
        if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
            throw new BusinessException("10004","未获取到订单信息");
        }
        OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);
        //查询是否是河南合同履约  查询是否是卡+x 1 2 3类型  是否是待交货状态
        Integer orderStatus = orderInfoDetailHandle.getOrderStatus();

        String addr1 = orderInfoDetailHandle.getAddr1();
        String addr3 = orderInfoDetailHandle.getAddr3();
        String addr4 = orderInfoDetailHandle.getAddr4();
        String usaddr = orderInfoDetailHandle.getUsaddr();

        if (StringUtils.isNotEmpty(addr3)) {
            addr3 = IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv);
        }else {
            addr3 ="";
        }
        if (StringUtils.isNotEmpty(addr4)) {
            addr4 = IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv);
        }else {
            addr4 ="";
        }
        if (StringUtils.isNotEmpty(usaddr)) {
            usaddr = IOTEncodeUtils.decryptSM4(usaddr, iotSm4Key, iotSm4Iv);
        }else {
            usaddr ="";
        }
        String skuOfferingCode = orderInfoDetailHandle.getSkuOfferingCode();
        boolean b2bFlow = isB2bFlow(addr1);
        String productType ="";
        String spuOfferingClass = orderInfoDetailHandle.getSpuOfferingClass();
        String atomOfferingCode = orderInfoDetailHandle.getAtomOfferingCode();
        String spuOfferingCode = orderInfoDetailHandle.getSpuOfferingCode();
        Optional<SkuOfferingInfo> skuOfferingInfoOptional = skuOfferingInfoMapper.selectByExample(new SkuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(skuOfferingCode).example()).stream().findFirst();
        if (skuOfferingInfoOptional.isPresent() && SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(spuOfferingClass)){
            SkuOfferingInfo skuOfferingInfo = skuOfferingInfoOptional.get();
            productType = skuOfferingInfo.getProductType();
        }
        boolean b2bProductType = isB2bProductType(productType);
        Optional<AtomOfferingInfo> atomOfferingInfoOptional = atomOfferingInfoMapper.selectByExample(new AtomOfferingInfoExample().createCriteria()
                .andOfferingCodeEqualTo(atomOfferingCode).andSpuCodeEqualTo(spuOfferingCode).andSkuCodeEqualTo(skuOfferingCode).example()).stream().findFirst();
        if (!atomOfferingInfoOptional.isPresent()){
            throw new BusinessException(StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getStateCode(), StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getMessage());
        }
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoOptional.get();
        //符合条件的才让修改
        if (b2bFlow && ((spuOfferingClass.equals(SPUOfferingClassEnum.A11.getSpuOfferingClass()) && b2bProductType && AtomOfferingClassEnum.X.name().equals(atomOfferingInfo.getOfferingClass()))
                || (spuOfferingClass.equals(SPUOfferingClassEnum.A07.getSpuOfferingClass()) && AtomOfferingClassEnum.H.name().equals(atomOfferingInfo.getOfferingClass())))
                && orderStatus.equals(OrderStatusInnerEnum.WAIT_SEND.getStatus())){
            //修改数据并同步到b2b
            Order2cInfo order2cInfo = new Order2cInfo();
            order2cInfo.setOrderId(param.getOrderId());
            String rcvContactOld =IOTEncodeUtils.decryptSM4(orderInfoDetailHandle.getContactPersonName(), iotSm4Key, iotSm4Iv);
            String rcvContactPhoneOld =IOTEncodeUtils.decryptSM4(orderInfoDetailHandle.getContactPhone(), iotSm4Key, iotSm4Iv);
            String rcvContactAddressOld = addr3+addr4+usaddr;
            if (StringUtils.isNotEmpty(orderInfoDetailHandle.getHenanRealName())){
                rcvContactOld = IOTEncodeUtils.decryptSM4(orderInfoDetailHandle.getHenanRealName(), iotSm4Key, iotSm4Iv);
            }
            if (StringUtils.isNotEmpty(orderInfoDetailHandle.getHenanRealPhone())){
                rcvContactPhoneOld = IOTEncodeUtils.decryptSM4(orderInfoDetailHandle.getHenanRealPhone(), iotSm4Key, iotSm4Iv);
            }
            if (StringUtils.isNotEmpty(orderInfoDetailHandle.getHenanRealAddress())){
                rcvContactAddressOld= IOTEncodeUtils.decryptSM4(orderInfoDetailHandle.getHenanRealAddress(), iotSm4Key, iotSm4Iv);
            }

            if (StringUtils.isNotEmpty(param.getHenanRealName())){
                order2cInfo.setHenanRealName(IOTEncodeUtils.encryptSM4(param.getHenanRealName(), iotSm4Key, iotSm4Iv));
            }
            if (StringUtils.isNotEmpty(param.getHenanRealPhone())){
                order2cInfo.setHenanRealPhone(IOTEncodeUtils.encryptSM4(param.getHenanRealPhone(), iotSm4Key, iotSm4Iv));
            }
            if (StringUtils.isNotEmpty(param.getHenanRealAddress())){
                order2cInfo.setHenanRealAddress(IOTEncodeUtils.encryptSM4(param.getHenanRealAddress(), iotSm4Key, iotSm4Iv));
            }
            order2cInfo.setUpdateTime(new Date());

            //同步b2b
            OrderBackOrCancelDTO orderBackOrCancelDTO = new OrderBackOrCancelDTO();
            orderBackOrCancelDTO.setOrderNum(B2B_ORDER_PREFIX + orderInfoDetailHandle.getOrderId());
            String provinceCode = ProvinceCodeEnum.getProvinceCode(IOTEncodeUtils.decryptSM4(addr1, iotSm4Key, iotSm4Iv));
            orderBackOrCancelDTO.setProvinceCode(provinceCode);
            if (StringUtils.isNotEmpty(param.getHenanRealName())){
                orderBackOrCancelDTO.setRcvContact(AESUtils.getEncryptString(param.getHenanRealName()));
            }

            if (StringUtils.isNotEmpty(param.getHenanRealPhone())){
                orderBackOrCancelDTO.setRcvContactPhone(AESUtils.getEncryptString(param.getHenanRealPhone()));
            }
            if (StringUtils.isNotEmpty(param.getHenanRealAddress())){
                orderBackOrCancelDTO.setRcvContactAddress(AESUtils.getEncryptString(param.getHenanRealAddress()));
            }
            orderBackOrCancelDTO.setType("6");
            try {
                BaseAnswer<Void> b2bAnswer = b2BFeignClient.updateReceiveGoodsOrderInternalInternal(orderBackOrCancelDTO,rcvContactOld
                        ,rcvContactPhoneOld,rcvContactAddressOld,loginIfo4Redis.getUserId());
                log.info("同步b2b收货人信息修改结果记录:{},请求内容:{}", b2bAnswer, JSON.toJSONString(orderBackOrCancelDTO));
                if (!"00000".equals(b2bAnswer.getStateCode())) {
                    if(StringUtils.isEmpty(b2bAnswer.getMessage())){
                        log.info("同步b2b返回错误，且信息为空");
                        throw new BusinessException(API_RETURN_ERROR,"b2b返回信息为空");
                    }else {
                        throw new BusinessException(API_RETURN_ERROR.getStateCode(),b2bAnswer.getMessage());
                    }

                } else {
                    log.info("同步b2b收货人信息修改返回成功orderBackOrCancel");
                    //成功后更新表数据
                    order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                    //记录日志
                    OrderUpdateDTO orderUpdateDTO = new OrderUpdateDTO();
                    orderUpdateDTO.setRcvContactOld(rcvContactOld);
                    orderUpdateDTO.setRcvContactPhoneOld(rcvContactPhoneOld);
                    orderUpdateDTO.setRcvContactAddressOld(rcvContactAddressOld);
                    logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.GOODS_ORDER_MAIN.code,
                            OrderUpdateReceiveGoodsContentFromOrders(orderBackOrCancelDTO,orderUpdateDTO), LogResultEnum.LOG_SUCESS.code, null);
                }
            } catch (Exception e) {
                log.error("同步b2b收货人信息修改失败，同步内容:{}，失败异常描述:{}", JSON.toJSONString(orderBackOrCancelDTO), e.toString());
             throw new BusinessException(API_RETURN_ERROR.getStateCode(),e.getMessage());
            }

        }else {
            throw new BusinessException(StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getStateCode(), StatusConstant.ORDER_HENAN_REAL_NAME_UPDATE.getMessage());
        }
        return new BaseAnswer<>();
    }

    /**
     * 判断是否是河南订单
     * @param addr
     * @return
     */
    public boolean isB2bFlow(String addr) {
        if ("Henan".equals(ProvinceCodeEnum.getProvinceCode(IOTEncodeUtils.decryptSM4(addr, iotSm4Key, iotSm4Iv)))) {
            return true;
        }
        return false;
    }


    /**
     * 修改收货地址日志内容
     * @param orderBackOrCancelDTO
     * @param orderUpdateDTO
     * @return
     */
    public static String OrderUpdateReceiveGoodsContentFromOrders(OrderBackOrCancelDTO orderBackOrCancelDTO, OrderUpdateDTO orderUpdateDTO) {
        StringBuilder result = new StringBuilder();
        String rcvContactAddress = AESUtils.getDecryptString(orderBackOrCancelDTO.getRcvContactAddress());
        String rcvContact = AESUtils.getDecryptString(orderBackOrCancelDTO.getRcvContact());
        String rcvContactPhone = AESUtils.getDecryptString(orderBackOrCancelDTO.getRcvContactPhone());

        String rcvContactOld = orderUpdateDTO.getRcvContactOld();
        String rcvContactPhoneOld = orderUpdateDTO.getRcvContactPhoneOld();
        String rcvContactAddressOld = orderUpdateDTO.getRcvContactAddressOld();


        if ("6".equals(orderBackOrCancelDTO.getType())) {
            result.append("【收货实名】\n");
            if (!rcvContact.equals(rcvContactOld)){
                rcvContact = custNameDesensitization(rcvContact);
                result.append("姓名由").append(rcvContactOld).append("修改为").append(rcvContact).append("\n");
            }
            if (!rcvContactPhone.equals(rcvContactPhoneOld)){
                rcvContactPhone = DesensitizationUtils.replaceWithStar(rcvContactPhone);
                result.append("电话由").append(rcvContactPhoneOld).append("修改为").append(rcvContactPhone).append("\n");
            }
            if (!rcvContactAddress.equals(rcvContactAddressOld)){
                result.append("地址由").append(rcvContactAddressOld).append("修改为").append(rcvContactAddress).append("\n");
            }
        } else {
            result.append("【操作类型错误】\n").append("订单号").append(orderBackOrCancelDTO.getOrderNum());
        }

        return result.toString();
    }

    /**
     * 判断卡+x的是否是1 2 3 类型的
     * @param productType
     * @return
     */
    public boolean isB2bProductType(String productType) {
        List<String> productTypeList = new ArrayList<>();
        productTypeList.add("1");
        productTypeList.add("2");
        productTypeList.add("3");
        if (StringUtils.isNotEmpty(productType) && productTypeList.contains(productType)) {
            return true;
        }
        return false;
    }

    /**
     * 名称脱敏处理
     * @param custName
     * @return
     */
    public static String custNameDesensitization(String custName) {
        // 规则说明：
        // 姓名：字符长度小于5位；企业名称：字符长度大于等于5位。
        // 姓名规则
        // 规则一：1个字则不脱敏，如"张"-->"张"
        // 规则二：2个字则脱敏第二个字，如"张三"-->"张*"
        // 规则三：3个字则脱敏第二个字，如"张三丰"-->"张*丰"
        // 规则四：4个字则脱敏中间两个字，如"易烊千玺"-->"易**玺"
        // 企业名称规则：
        // 从第4位开始隐藏，最多隐藏6位。

        if (StringUtils.isNotEmpty(custName)) {
            char[] chars = custName.toCharArray();
            if (chars.length < 5) {// 表示姓名
                if (chars.length > 1) {
                    StringBuffer sb = new StringBuffer();
                    for (int i = 0; i < chars.length - 2; i++) {
                        sb.append("*");
                    }
                    custName = custName.replaceAll(custName.substring(1, chars.length - 1), sb.toString());
                }
            } else {// 企业名称
                int start = 4;
                // 第一部分
                String str1 = custName.substring(0, start);
                // 第二部分
                String str2 = "";
                if (chars.length == 5) {
                    str2 = "*";
                } else if (chars.length == 6) {
                    str2 = "**";
                } else if (chars.length == 7) {
                    str2 = "***";
                } else if (chars.length == 8) {
                    str2 = "****";
                } else if (chars.length == 9) {
                    str2 = "*****";
                } else {
                    str2 = "******";
                }
                // 通过计算得到第三部分需要从第几个字符截取
                int subIndex = start + str2.length();
                // 第三部分
                String str3 = custName.substring(subIndex);
                StringBuffer sb = new StringBuffer();
                sb.append(str1);
                sb.append(str2);
                sb.append(str3);
                custName = sb.toString();
            }
        }
        return custName;
    }
}
