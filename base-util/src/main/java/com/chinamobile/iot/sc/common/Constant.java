package com.chinamobile.iot.sc.common;

/**
 * 常量表，存储常量定义
 */
public class Constant {

    //预占完成后存入redis中缓存起来。 hashKey 为 inventory:bookId value {"skuOfferingCode":"","quantity":xxx}
    public static final String REDIS_RESERVE_INVENTORY_PREFIX = "RESERVE_INVENTORY:";
    //卡+X预占的提单人区域数据缓存
    public static final String REDIS_COMMIT_PERSON_PREFIX = "COMMIT_PERSON:";
    //卡+x预占原子商品预占数据x终端数 省级或地市级预占数常量类
    public static final String REDIS_COMMIT_INVENTORY_AREA = "COMMIT_INVENTORY_AREA:";

    //卡+x预占码号库存 redis常量类
    public static final String REDIS_CARD_INVENTORY_CONSTANT = "CARD_INVENTORY_CONSTANT:";

    //A04 产品线条额度预占 提单人区域数据缓存
    public static final String REDIS_LIMIT_COMMIT_PERSON_PREFIX = "LIMIT_COMMIT_PERSON:";
    public static final String TRACE_ID = "traceId";
    public static final String X_Real_IP = "X-Real-IP";
    public static final String X_Forwarded_For = "X-Forwarded-For";
    public static final String IP = "ip";
    public static final String URI = "uri";

    public static final String REDIS_PRE_KEY_CAPTCHA_SESSIONID = "SC:CAPTCHA:SESSIONID:";
    public static final String REDIS_PRE_KEY_SMS_VALID = "SC:SMS:VALID:";
    public static final String REDIS_PRE_KEY_EDIT_SMS = "SC:EDIT:SMS:";
    public static final String REDIS_PRE_KEY_SMS_VALID_NEXT_KEY = "SC:SMS:VALID:NEXT:KEY:";
    public static final String REDIS_PRE_KEY_SMS_NUM_ERRORS_KEY = "SC:SMS:NUM:ERRORS:KEY:";
    public static final String REDIS_PRE_KEY_SMS_NUM_ERRORS_RETAIL_KEY = "SC:SMS:NUM:ERRORS:RETAIL:KEY:";
    // 4A金库临时session id 存放
    public static final String REDIS_PRE_KEY_4A_SESSIONID = "SC:4A:SESSION:KEY:";
    /**
     * 导出文件短信验证码
     */
    public static final String REDIS_PRE_KEY_EXPORT_SMS_VALID = "SC:EXPORT:SMS:VALID:";

    /**
     * 用于各处加密的秘钥，比如jwt和密码加盐
     */
    public static final String ENCODE_PWD = "<EMAIL>";
    public static final String REDIS_KEY_USER_TOKEN = "SC:USER:TOKEN:";

    public static final String REDIS_KEY_USER_LOGIN_LOG = "SC:USER:LOG4A";
    /**
     * 用户id
     */
    public static final String HEADER_KEY_USER_ID = "userId";
    public static final String HEADER_KEY_TOKEN = "token";
    /**
     * 网关放置request信息
     */
    public static final String ATTR_KEY_AUTH_INFO = "authorInfo";

    public static final String REDIS_HENAN_ZW_SYS_TOKEN = "henan_zw_sys_token";
    public static final String REDIS_KEY_HENAN_ZW_SYS_TOKEN_COMMON = "henan_zw_sys_token_common";

    /**
     * 权限
     */
    public static final String REDIS_KEY_OPERATE_PERMISSION = "SC:PERMISSION:OPERATE:";
    public static final String REDIS_KEY_DATA_PERMISSION = "SC:PERMISSION:DATA:";
    public static final String REDIS_KEY_ROLE_OPERATE_PERMISSION = "SC:ROLE:PERMISSION:OPERATE:";
    public static final String REDIS_KEY_ROLE_DATA_PERMISSION = "SC:ROLE:PERMISSION:DATA:";
    public static final String REDIS_KEY_ROLE_DATA_PERMISSION_CODE = "SC:ROLE:PERMISSION:DATA:CODE:";
    public static final String REDIS_KEY_OPERATE_PERMISSION_ID = "SC:PERMISSION:OPERATE:ID:";
    public static final String REDIS_KEY_AUTH_ID = "SC:AUTH:ID:";
    public static final String REDIS_KEY_AUTH_CODE = "SC:AUTH:CODE:";

    /**
     * 网关放置request信息
     */
    public static final String ATTR_KEY_TOP_MODULE_AUTH_CODE = "topModuleAuthCode";
    public static final String ATTR_KEY_SUB_MODULE_AUTH_CODE = "subModuleAuthCode";
    public static final String ATTR_KEY_FUNCTION_MODULE_AUTH_CODE = "functionModuleAuthCode";

    /**
     * 大屏用户前端回显
     */
    public static final String SCREEN_USER_ECHO_LIST = "screenUserEchoList";
    /**
     * brm文本上传 SC:OPEN:ABILITY:API:LIST
     */
    public static final String REDIS_KEY_BRM_UPLOAD_TXT_PARAM = "SC:BRM:UPLOAD_TXT";
    public static final String REDIS_KEY_BRM_DETAIL_UPLOAD_TXT_PARAM = "BRM_DETAIL_UPLOAD_TXT_PARAM";
    /**
     * 省业管员用户前端回显
     */
    public static final String USER_PROVINCE_BUSINESS_LIST = "provinceBusinessUserEchoList";

    public static final String ATTR_KEY_OPEN_APP_INFO = "openAppInfo";
    public static final String ATTR_KEY_OPEN_ORGANIZATION_INFO = "openOrganizationInfo";
    public static final String HEADER_KEY_OPEN_APP_ID = "appId";
    public static final String HEADER_KEY_OPEN_ORGANIZATION_ID = "organizationId";
    public static final String HEADER_KEY_TIMESTAMP = "timestamp";

    public static final String HEADER_KEY_SIGN = "sign";

    public static final String HEADER_KEY_RAND = "rand";

    /**开放能力接口列表*/
    public static final String REDIS_KEY_OPEN_ABILITY_API_LIST = "SC:OPEN:ABILITY:API:LIST";

    /**开放能力应用详情key*/
    public static final String REDIS_KEY_OPEN_ABILITY_APP_ID = "SC:OPEN:ABILITY:APP:ID:";

    /**开放能力部门详情key*/
    public static final String REDIS_KEY_OPEN_ABILITY_ORGANIZATION_ID = "SC:OPEN:ABILITY:ORGANIZATION:ID:";

    /**应收平台token*/
    public static final String REDIS_KEY_REVENUE_TOKEN = "revenue_sys_token";

    /**即刻办token*/
    public static final String REDIS_KEY_JKBAN_TOKEN = "jkban_token";

    /**同步市场成功的单子数量*/
    public static final String REDIS_KEY_BILL_NO = "bill_no_token";

    public static final String REDIS_KEY_BILL_NO_YUNTONG = "bill_no_token_yuntong";
    public static final String REDIS_KEY_MINI_ACTIVITY_REGION = "SC:MINI:ACTIVITY:REGION:";
    public static final String REDIS_KEY_MINI_ACTIVITY_ID = "SC:MINI:ACTIVITY:ID:";
    public static final String REDIS_KEY_MINI_ACTIVITY_USER = "SC:MINI:ACTIVITY:USER:";
    public static final String REDIS_KEY_MINI_HOME_REGION = "SC:MINI:HOME:REGION:";
    public static final String REDIS_KEY_MINI_HOME_ID = "SC:MINI:HOME:ID:";

    public static final String REDIS_KEY_MINI_INFO_ID = "SC:MINI:INFO:ID:";
    public static final String REDIS_KEY_MINI_KNOWLEDGE_HOME_LIST = "SC:MINI:KNOWLEDGE:HOME:LIST";
    public static final String REDIS_KEY_MINI_INFO_LIST_PARAM = "SC:MINI:KNOWLEDGE:HOME:LIST:PARAM:";

    public static final String REDIS_KEY_MINI_PRODUCT_DETAIL = "SC:MINI:PRODUCT:DETAIL:";
    public static final String REDIS_KEY_MINI_PRODUCT_SKU_POINT = "SC:MINI:PRODUCT:SKU_POINT:";
    public static final String REDIS_KEY_MINI_USER = "SC:MINI:USER:";
    public static final String REDIS_KEY_PRODUCT_FLOW_INSTANCE_ATTACHMENT = "SC:PRODUCT:FLOW:INSTANCE:ATTACHMENT:";
    public static final String REDIS_KEY_PRODUCT_NAVIGATION_DIRECTORY = "SC:PRODUCT:NAVIGATION:DIRECTORY:";
    public static final String REDIS_KEY_MINI_PRODUCT_LIST = "SC:MINI:PRODUCT:LIST:";
    public static final String REDIS_KEY_MINI_PRODUCT_COUNT = "SC:MINI:PRODUCT:COUNT:";
    public static final String REDIS_KEY_MINI_PRODUCT_SEARCH = "SC:MINI:PRODUCT:SEARCH:";
    public static final String REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT = "SC:MINI:PRODUCT:SEARCH:COUNT:";
    public static final String REDIS_KEY_MINI_ACTIVITY_LIST = "SC:MINI:ACTIVITY:LIST:";
    public static final String REDIS_KEY_MINI_ACTIVITY_DETAIL = "SC:MINI:ACTIVITY:DETAIL:";

    public static final String REDIS_KEY_MINI_SCENE_ID = "SC:MINI:SCENE:ID:";
    public static final String REDIS_KEY_MINI_SCENE_DIRECTORY = "SC:MINI:SCENE:DIRECTORY:";
    public static final String REDIS_KEY_MINI_SCENE_LIST = "SC:MINI:SCENE:LIST:";
    public static final String REDIS_KEY_MINI_SCENE_COUNT = "SC:MINI:SCENE:COUNT:";
    public static final String REDIS_KEY_MINI_SCENE_RELATED_SPU = "SC:MINI:SCENE:RELATED_SPU:";
    public static final String REDIS_KEY_MINI_SCENE_DETAIL = "SC:MINI:SCENE:DETAIL:";

    public static final String REDIS_KEY_SCENE_NAVIGATION_DIRECTORY = "SC:SCENE:NAVIGATION:DIRECTORY";
    public static final String REDIS_KEY_PROVINCE_AND_CITY = "SC:MINI:PROVINCE_AND_CITY";
    public static final String REDIS_KEY_MINI_PRODUCT_NAVIGATION_DIRECTORY = "SC:MINI:PRODUCT:NAVIGATION:DIRECTORY";
    public static final String REDIS_KEY_MINI_SCENE_REQUIREMENT_TEMPLATE = "SC:MINI:SCENE:REQUIREMENT_TEMPLATE:";

    public static final String REDIS_KEY_MINI_TEMPLATE_ID = "SC:MINI:TEMPLATE:ID:";

    /**
     * 订单的线上结算采购订单保存的结算成功前的订单结算状态redis key，用于在线上采购订单取消时状态的恢复
     */
    public static final String REDIS_KEY_SC_ORDER_SETTLE_STATUS_PURCHASE = "SC:ORDER:SETTLE:STATUS:PURCHASE:";

    public static final String REDIS_KEY_MINI_USER_SALE_YEAR_REPORT = "SC:MINI:USER:SALE:YEAR:REPORT:";

    public static final String REDIS_KEY_MINI_VIEWED_SALE_YEAR_REPORT = "SC:MINI:VIEWED:SALE:YEAR:REPORT:";

    public static final String REDIS_KEY_YUNTONG_NONCE = "SC:YUNTONG:NONCE:";
    public static final String REDIS_KEY_YUNTONG_ACTIVITY_VIEWED = "SC:YUNTONG:ACTIVITY:VIEWED:";
    public static final String REDIS_KEY_YUNTONG_ACTIVITY_USER = "SC:YUNTONG:ACTIVITY:USER:";
    public static final String REDIS_KEY_YUNTONG_ACTIVITY_RAFFLE = "SC:YUNTONG:ACTIVITY:RAFFLE:";
    public static final String REDIS_KEY_SPU_SKU_ATTACHMENT = "SC:PRODUCT:SPU:SKU:ATTACHMENT:";

    public static final String REDIS_KEY_ESTEWARD_DIRECTORY_LIST = "SC:ESTEWARD:DIRECTORY:LIST:";

    public static final String REDIS_KEY_HOME_PROVINCE_AND_CITY = "SC:MINI:HOME:PROVINCE_AND_CITY";



}
