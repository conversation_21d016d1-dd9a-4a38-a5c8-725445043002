package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceRec;
import com.chinamobile.iot.sc.request.invoice.IotRevInovBackRequest;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IInvoice2IOTService
 * @description: 发票同步IOT商城Service
 * @author: zyj
 * @create: 2021/12/8 17:49
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IInvoice2IOTService {

    Boolean isAllInvoiceSuccByOrderId(String orderId);

    BaseAnswer<Void> invoicingResult2IOTMall(ApplyInvoiceRec applyInvoiceRec, String result);

    BaseAnswer<Void> invoiceVoidCallback(IotRevInovBackRequest request, String result, String regionId);

}
