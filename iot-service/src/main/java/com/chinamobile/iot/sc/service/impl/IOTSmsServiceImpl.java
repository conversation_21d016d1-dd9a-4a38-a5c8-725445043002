package com.chinamobile.iot.sc.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.dynamodbv2.xspec.M;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.request.sms.Msg4Request;
import com.chinamobile.iot.sc.response.iot.IOTSmsResponse;
import com.chinamobile.iot.sc.service.IOTSmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.protocol.HTTP;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.util.HashMap;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * created by liuxiang on 2022/4/12 10:27
 */
@Service
@Slf4j
public class IOTSmsServiceImpl implements IOTSmsService {


    private static final String URL = "http://api.sms.heclouds.com/tempSignSmsSend?sicode={sicode}&tempid={tempid}&signId={signId}&mobiles={mobiles}";

    @Resource
    RestTemplate restTemplate;

    @Override
    @Retryable(include = {RuntimeException.class}, maxAttempts = 4)
    public IOTAnswer<JSONObject> sendSms(IOTRequest baseRequest) {

        log.info("接受到发送短信请求,data:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<JSONObject> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        Msg4Request msg4Request;
        try {
             msg4Request = JSON.parseObject(baseRequest.getContent(), Msg4Request.class);
           // log.info("封装传入的json字符串：{}",baseRequest.getContent());
        } catch (Exception e) {
            log.error("解析异常:" + e);
            throw new IOTException(iotAnswer, "数据解析异常");
        }

        AtomicReference<String> url = new AtomicReference<>(URL);
        String mobiles = msg4Request.getMobiles();
        url.set(url.get().replace("{sicode}", msg4Request.getSicode()).replace("{mobiles}", mobiles).
                replace("{tempid}", msg4Request.getTempid()).replace("{signId}", msg4Request.getSignId()));
     /*   if (msg4Request.getCustomCode() != null) {
            msg4Request.getCustomCode().forEach((key, value) -> url.set(url + "&" + key + "=" + value));
            log.info(url.get());
        }*/
     //封装URL自定义参数
        encapsulationNoteUrlParam(msg4Request,url);
        IOTSmsResponse iotSmsResponse = null;
        try {
    /*        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url.get());
            UriComponents uriComponents = builder.build();
            String result = restTemplate.postForObject(uriComponents.toUri(), null, String.class);*/
          /*  String result = HttpUtil.createPost(url.get())
                    .header("Content-Type","application/x-www-form-urlencoded;charset=utf-8")
                    .header("Accept","text/plain;charset=utf-8")
                    .execute().body();*/
            StringBuffer sb = null;
            java.net.URL realUrl = new URL(url.get());
            URLConnection connection = realUrl.openConnection();
            InputStream stream = null;
            try {
                connection.setRequestProperty("Accept","text/plain;charset=utf-8");
                connection.setRequestProperty("Content-Type","application/x-www-form-urlencoded;charset=utf-8");
                connection.setDoOutput(true);
                connection.setDoInput(true);
                connection.getOutputStream().close();
                stream = connection.getInputStream();
                BufferedReader br = new BufferedReader(new InputStreamReader(stream, "utf-8"));
                String str=null;
                sb = new StringBuffer();
                while((str=br.readLine())!=null){
                    sb.append(str);
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (Optional.ofNullable(stream).isPresent()){
                    stream.close();
                }
            }
            iotSmsResponse = JSONObject.parseObject(sb.toString(),IOTSmsResponse.class);
        } catch (Exception e) {
            log.error("短信发送失败：{}",e);
            throw new IOTException(iotAnswer, "短信发送失败");
        }
        JSONObject jsonObject = (JSONObject) JSON.toJSON(iotSmsResponse);
        log.info("短信调用服务返回数据：{}",jsonObject.toJSONString());
         iotAnswer.setContent(jsonObject);
        return iotAnswer;
    }


    /**
     * 封装短信URL自定义参数数据
     * @param msg4Request
     * @param url
     */
    private void encapsulationNoteUrlParam(Msg4Request msg4Request,AtomicReference<String> url){
        String param1 = msg4Request.getPARAM1();
        String param2 = msg4Request.getPARAM2();
        String param3 = msg4Request.getPARAM3();
        String param4 = msg4Request.getPARAM4();
        String param5 = msg4Request.getPARAM5();
        String param6 = msg4Request.getPARAM6();
        String param7 = msg4Request.getPARAM7();
        String param8 = msg4Request.getPARAM8();
        String param9 = msg4Request.getPARAM9();
        String param10 = msg4Request.getPARAM10();
        if (StringUtils.isNotEmpty(param1)){
            url.set(url.get().concat("&PARAM1="+param1));
        }else {
            url.set(url.get().concat("&PARAM1="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param2)){
            url.set(url.get().concat("&PARAM2="+param2));
        }else {
            url.set(url.get().concat("&PARAM2="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param3)){
            url.set(url.get().concat("&PARAM3="+param3));
        }else {
            url.set(url.get().concat("&PARAM3="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param4)){
            url.set(url.get().concat("&PARAM4="+param4));
        }else {
            url.set(url.get().concat("&PARAM4="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param5)){
            url.set(url.get().concat("&PARAM5="+param5));
        }else {
            url.set(url.get().concat("&PARAM5="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param6)){
            url.set(url.get().concat("&PARAM6="+param6));
        }else {
            url.set(url.get().concat("&PARAM6="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param7)){
            url.set(url.get().concat("&PARAM7="+param7));
        }else {
            url.set(url.get().concat("&PARAM7="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param8)){
            url.set(url.get().concat("&PARAM8="+param8));
        }else {
            url.set(url.get().concat("&PARAM8="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param9)){
            url.set(url.get().concat("&PARAM9="+param9));
        }else {
            url.set(url.get().concat("&PARAM9="+StringUtils.EMPTY));
        }

        if (StringUtils.isNotEmpty(param10)){
            url.set(url.get().concat("&PARAM10="+param10));
        }else {
            url.set(url.get().concat("&PARAM10="+StringUtils.EMPTY));
        }
        log.info("请求短信参数：{}",url);
    }


}

