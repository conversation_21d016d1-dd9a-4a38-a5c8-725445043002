package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfo;
import com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfoExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19
 * @description 省内融合包信息同步service接口类
 */
public interface BenefitOfferingsInfoService {

    /**
     * 根据需要获取省内融合包信息
     * @param benefitOfferingsInfoExample
     * @return
     */
    List<BenefitOfferingsInfo> listBenefitOfferingsInfoByNeed(BenefitOfferingsInfoExample benefitOfferingsInfoExample);

    /**
     * 批量新增省内融合包信息
     * @param benefitOfferingsInfoList
     */
    void batchAddBenefitOfferingsInfo(List<BenefitOfferingsInfo> benefitOfferingsInfoList);

    /**
     * 新增省内融合包信息
     * @param benefitOfferingsInfo
     */
    void addBenefitOfferingsInfo(BenefitOfferingsInfo benefitOfferingsInfo);

    /**
     * 根据需要更新省内融合包信息
     * @param benefitOfferingsInfo
     * @param benefitOfferingsInfoExample
     */
    void updateBenefitOfferingsInfoByNeed(BenefitOfferingsInfo benefitOfferingsInfo,
                                          BenefitOfferingsInfoExample benefitOfferingsInfoExample);

    /**
     * 根据id更新省内融合包信息
     * @param benefitOfferingsInfo
     */
    void updateBenefitOfferingsInfoById(BenefitOfferingsInfo benefitOfferingsInfo);

}
