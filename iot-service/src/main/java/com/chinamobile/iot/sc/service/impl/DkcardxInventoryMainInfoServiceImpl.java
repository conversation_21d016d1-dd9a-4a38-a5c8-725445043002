package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.constant.TerminalTypeEnum;
import com.chinamobile.iot.sc.dao.DkcardxInventoryMainInfoMapper;
import com.chinamobile.iot.sc.dao.ext.DkcardxInventoryMainInfoMapperExt;
import com.chinamobile.iot.sc.enums.log.GoodsManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailInfoDTO;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailLocationDTO;
import com.chinamobile.iot.sc.pojo.dto.DxInventoryMainAndDetailExportDTO;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfoExample;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryCardDetailParam;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryMainInfoParam;
import com.chinamobile.iot.sc.pojo.vo.DkcardxInventoryMainInfoVO;
import com.chinamobile.iot.sc.service.DkcardxInventoryMainInfoService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;
import static com.chinamobile.iot.sc.util.excel.EasyExcelUtils.setEasyExcelDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16
 * @description X终端库存主要信息service实现类
 */
@Service
@Slf4j
public class DkcardxInventoryMainInfoServiceImpl implements DkcardxInventoryMainInfoService {

    @Resource
    private DkcardxInventoryMainInfoMapper dkcardxInventoryMainInfoMapper;

    @Resource
    private DkcardxInventoryMainInfoMapperExt dkcardxInventoryMainInfoMapperExt;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private LogService logService;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Override
    public void batchInsertInventoryMainInfo(List<DkcardxInventoryMainInfo> mainInfoList) {
        dkcardxInventoryMainInfoMapper.batchInsert(mainInfoList);
    }

    @Override
    public List<DkcardxInventoryMainInfo> listDkcardxInventoryMainInfoByNeed(DkcardxInventoryMainInfoExample mainInfoExample) {
        return dkcardxInventoryMainInfoMapper.selectByExample(mainInfoExample);
    }

    @Override
    public PageData<DkcardxInventoryMainInfoVO> pageCardMainInfo(DkcardxInventoryMainInfoParam dkcardxInventoryMainInfoParam,
                                                                 LoginIfo4Redis loginIfo4Redis) {
        PageData<DkcardxInventoryMainInfoVO> pageData = new PageData<>();
        Integer pageNum = dkcardxInventoryMainInfoParam.getPageNum();
        Integer pageSize = dkcardxInventoryMainInfoParam.getPageSize();

        handleDkcardxInventoryMainInfoParam(dkcardxInventoryMainInfoParam, loginIfo4Redis);

        Page<DkcardxInventoryMainInfoVO> page = new Page<>(pageNum, pageSize);

        List<DkcardxInventoryMainInfo> mainInfoList
                = dkcardxInventoryMainInfoMapperExt.listDkcardxInventoryMainInfo(page, dkcardxInventoryMainInfoParam);
        List<DkcardxInventoryMainInfoVO> mainInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mainInfoList)) {
            mainInfoList.forEach(mainInfo -> {
                DkcardxInventoryMainInfoVO dkcardxInventoryMainInfoVO = new DkcardxInventoryMainInfoVO();
                String terimalTypeName = TerminalTypeEnum.getDescByType(mainInfo.getTerminalType());
                dkcardxInventoryMainInfoVO.setTerminalTypeName(terimalTypeName);
                BeanUtils.copyProperties(mainInfo, dkcardxInventoryMainInfoVO);
                mainInfoVOList.add(dkcardxInventoryMainInfoVO);
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(mainInfoVOList);

        return pageData;
    }

    @Override
    public void exportDxInventoryMainAndDetail(DkcardxInventoryMainInfoParam dkcardxInventoryMainInfoParam,
                                               LoginIfo4Redis loginIfo4Redis,
                                               HttpServletResponse response) throws Exception {
        handleDkcardxInventoryMainInfoParam(dkcardxInventoryMainInfoParam, loginIfo4Redis);
        List<DxInventoryMainAndDetailExportDTO> dxInventoryMainAndDetailExportDTOList
                = dkcardxInventoryMainInfoMapperExt.exportDxInventoryMainAndDetail(dkcardxInventoryMainInfoParam);
        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dxInventoryMainAndDetailExportDTOList)) {
            dxInventoryMainAndDetailExportDTOList = new ArrayList<>();
        }else{
            dxInventoryMainAndDetailExportDTOList.stream().forEach(exportDTO->{
                exportDTO.setTerminalTypeName(TerminalTypeEnum.getDescByType(exportDTO.getTerminalType()));
            });
        }

        Map exportMap = new HashMap();
        exportMap.put("now",new Date());
        // x终端数据详情
        EasyExcelDTO easyExcelDTO = setEasyExcelDTO(0, "x终端库存导出", "list",
                dxInventoryMainAndDetailExportDTOList, exportMap);

        easyExcelDTOList.add(easyExcelDTO);
        String excelName = "x终端库存导出";
        excelName = URLEncoder.encode(excelName, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource("template/x_inventory_main_detail_export_template.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        List<Integer> needCellWriteHandlerList = new ArrayList<>();
        needCellWriteHandlerList.add(0);
        List<CellWriteHandler> cellWriteHandlerList = new ArrayList<>();
        // 需要合并的列
     /*   int[] mergeThridColumnIndex = new int[9];
        for (int i = 0; i < 2; i++) {
            mergeThridColumnIndex[i] = i;
        }

        //设置第几行开始合并
        int mergeRowIndex = 2;
        // Excel单元格行合并处理策略
        ExcelDxInventoryMergeStrategy dxInventoryColumnMergeStrategy
                = new ExcelDxInventoryMergeStrategy(mergeRowIndex, mergeThridColumnIndex,dxInventoryMainAndDetailExportDTOList);
        cellWriteHandlerList.add(dxInventoryColumnMergeStrategy);*/
        // 导出x终端数据
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,needCellWriteHandlerList,cellWriteHandlerList,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());

        //记录日志
        String content = "【x终端库存导出】\n"
                .concat("导出时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT));
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                content, LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    public List<DkcardxInventoryCardDetailInfoDTO> listCardRelationByInventory(DkcardxInventoryCardDetailParam cardDetailParam) {
        return dkcardxInventoryMainInfoMapperExt.listCardRelationByInventory(cardDetailParam);
    }

    @Override
    public PageData<DkcardxInventoryCardDetailInfoDTO> pageCardRelationByInventory(DkcardxInventoryCardDetailParam cardDetailParam) {
        PageData<DkcardxInventoryCardDetailInfoDTO> pageData = new PageData<>();
        Integer pageNum = cardDetailParam.getPageNum();
        Integer pageSize = cardDetailParam.getPageSize();

        Page<DkcardxInventoryCardDetailInfoDTO> page = new Page<>(pageNum, pageSize);
        List<DkcardxInventoryCardDetailInfoDTO> detailInfoDTOList = dkcardxInventoryMainInfoMapperExt.listCardRelationByInventory(page, cardDetailParam);

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(detailInfoDTOList);
        return pageData;
    }

    @Override
    public List<DkcardxInventoryCardDetailLocationDTO> listXDetailLocation(String inventoryMainId) {
        List<DkcardxInventoryCardDetailLocationDTO> locationDTOList = new ArrayList<>();
        DkcardxInventoryCardDetailLocationDTO locationDTO = new DkcardxInventoryCardDetailLocationDTO();
        locationDTO.setLocation("全部");
        locationDTO.setLocationName("全部");
        locationDTOList.add(locationDTO);
        List<DkcardxInventoryCardDetailLocationDTO> detailLocationDTOList = dkcardxInventoryMainInfoMapperExt.listXDetailLocation(inventoryMainId);
        if (CollectionUtils.isNotEmpty(detailLocationDTOList)){
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            detailLocationDTOList.stream().forEach(detailLocationDTO->{
                String location = detailLocationDTO.getLocation();
                if (StringUtils.isEmpty(location)){
                    detailLocationDTO.setLocation("省级");
                    detailLocationDTO.setLocationName("省级");
                }else{
                    detailLocationDTO.setLocationName(locationCodeNameMap.get(location)+"");
                }
                locationDTOList.add(detailLocationDTO);
            });
        }
        return locationDTOList;
    }

    /**
     * 处理参数信息
     *
     * @param dkcardxInventoryMainInfoParam
     * @param loginIfo4Redis
     */
    private void handleDkcardxInventoryMainInfoParam(DkcardxInventoryMainInfoParam dkcardxInventoryMainInfoParam,
                                                     LoginIfo4Redis loginIfo4Redis) {

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();
        String beId = dkcardxInventoryMainInfoParam.getBeId();
        List<String> beIdList = dkcardxInventoryMainInfoParam.getBeIdList();

        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = BaseConstant.PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = BaseConstant.PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);

            if (!isProvinceUser) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
            }
            // 合作伙伴主账号、合作伙伴从账号、合作伙伴省管可以查看归属本省x终端明细
            //合作伙伴是单个省市的
            if ((isPartnerLordRole && isProvinceUser && CollectionUtils.isEmpty(beIdList))
                    || ((isPartnerRole || isPartnerProvince) && isProvinceUser)) {
                dkcardxInventoryMainInfoParam.setBeId(data4User.getBeIdPartner());
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince && CollectionUtils.isEmpty(beIdList))
                    && isProvinceUser) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    dkcardxInventoryMainInfoParam.setBeId(data4User.getBeIdPartner());
                } else {
                    dkcardxInventoryMainInfoParam.setLocation(userLocation);
                }

            }
        }
    }
}
