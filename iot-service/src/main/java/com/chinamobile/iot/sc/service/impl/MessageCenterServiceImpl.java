package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.KafkaTopic;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.dao.MessageMapper;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.AddMessageParam;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Message;
import com.chinamobile.iot.sc.pojo.MessageExample;
import com.chinamobile.iot.sc.pojo.param.MessageListParam;
import com.chinamobile.iot.sc.pojo.vo.MessageDetailVO;
import com.chinamobile.iot.sc.pojo.vo.MessageListVO;
import com.chinamobile.iot.sc.service.MessageCenterService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2023/9/1 10:34
 */
@Service
@Slf4j
public class MessageCenterServiceImpl implements MessageCenterService {

    @Resource
    private MessageMapper messageMapper;

    @Autowired
    private KafkaTemplate<String,byte[]> kafkaTemplate;

    @Override
    public BaseAnswer<PageData<MessageListVO>> getMessageList(MessageListParam param, String userId) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if(pageNum < 0 || pageSize < 0){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"页码和每页数量必须大于0");
        }
        PageData<MessageListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        Boolean read = param.getIsread();
        String searchParam = param.getSearchParam();
        Integer source = param.getSource();
        PageHelper.startPage(pageNum,pageSize);
        MessageExample example = new MessageExample();
        //关键词涉及到两个字段模糊匹配，所以分开写条件，然后or连接
        MessageExample.Criteria criteria = example.createCriteria().andUserIdEqualTo(userId);
        MessageExample.Criteria criteria1 = example.createCriteria().andUserIdEqualTo(userId);
        if(source != null){
            criteria.andSourceEqualTo(source);
            criteria1.andSourceEqualTo(source);
        }
        if(read != null){
            criteria.andIsreadEqualTo(read);
            criteria1.andIsreadEqualTo(read);
        }
        if(StringUtils.isNotEmpty(searchParam)){
            criteria.andTypeLike("%"+searchParam+"%");
            criteria1.andContentLike("%"+searchParam+"%");
        }
        example.or(criteria1);
        example.orderBy("isRead ASC,create_time DESC");
        List<Message> messages = messageMapper.selectByExample(example);
        PageInfo<Message> pageInfo = new PageInfo<>(messages);
        if(CollectionUtils.isEmpty(messages)){
            return BaseAnswer.success(pageData);
        }
        List<MessageListVO> data = messages.stream().map(m -> {
            MessageListVO messageListVO = new MessageListVO();
            BeanUtils.copyProperties(m, messageListVO);
            return messageListVO;
        }).collect(Collectors.toList());
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(data);
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer<MessageDetailVO> getMessageDetail(String id) {
        Message message = messageMapper.selectByPrimaryKey(id);
        if(message == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"消息不存在");
        }
        message.setIsread(true);
        messageMapper.updateByPrimaryKey(message);
        MessageDetailVO messageDetailVO = new MessageDetailVO();
        BeanUtils.copyProperties(message,messageDetailVO);
        //通过kafka推送消息，更新未读数量
        ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
        kafkaTemplate.send(record);
        log.info("getMessageDetail完成websocket推送");
        return BaseAnswer.success(messageDetailVO);
    }

    @Override
    public BaseAnswer<Integer> getUnReadMsgCount(String userId, Integer source) {
        MessageExample.Criteria criteria = new MessageExample().createCriteria().andUserIdEqualTo(userId).andIsreadEqualTo(false);
        if(source != null){
            criteria.andSourceEqualTo(source);
        }
        MessageExample example = criteria.example();
        long count = messageMapper.countByExample(example);
        return BaseAnswer.success(count);
    }

    @Override
    public BaseAnswer<Void> addMessage(AddMessageParam param) {
        Message message = new Message();
        message.setId(BaseServiceUtils.getId());
        message.setIsread(false);
        message.setCreateTime(new Date());
        BeanUtils.copyProperties(param,message);
        messageMapper.insertSelective(message);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<Void> deleteMessage(String id, String userId) {
        Message message = messageMapper.selectByPrimaryKey(id);
        if(message == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"消息不存在");
        }
        Integer source = message.getSource();
        if(source == 1){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"OS消息不支持删除");
        }
        if(!message.getUserId().equals(userId)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只能删除自己的消息");
        }
        messageMapper.deleteByPrimaryKey(id);
        //通过kafka推送消息，更新未读数量
        ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
        kafkaTemplate.send(record);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer initModule() {
        String type = "订单导出";
        MessageExample example = new MessageExample().createCriteria().andTypeEqualTo(type).example();
        Message message = new Message();
        message.setModule(ModuleEnum.ORDER_MANAGE.name);
        messageMapper.updateByExampleSelective(message,example);
        return BaseAnswer.success(null);
    }
}
