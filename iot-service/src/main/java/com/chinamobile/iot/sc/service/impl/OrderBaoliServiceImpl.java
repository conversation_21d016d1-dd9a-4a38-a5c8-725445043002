package com.chinamobile.iot.sc.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.RedisLockConstant;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.dao.OrderBaoliMapper;
import com.chinamobile.iot.sc.dao.ext.OrderBaoliMapperExt;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.FinancingOrderStatusEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.FinancingManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.excel.ExcelBaoliOrderNotUseImportHandler;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample;
import com.chinamobile.iot.sc.pojo.TradeOrderInfo;
import com.chinamobile.iot.sc.pojo.dto.BaoliOrderExportDTO;
import com.chinamobile.iot.sc.pojo.dto.BatchGenerateTradeOrderDTO;
import com.chinamobile.iot.sc.pojo.dto.BatchGenerateTradeOrderErrorDTO;
import com.chinamobile.iot.sc.pojo.dto.ChangeUseBaoliOrderDTO;
import com.chinamobile.iot.sc.pojo.dto.ExportBaoliOrderDTO;
import com.chinamobile.iot.sc.pojo.dto.ImportBaoliOrderNotUseDTO;
import com.chinamobile.iot.sc.pojo.dto.InfoToImportBaoliNotUseValidDTO;
import com.chinamobile.iot.sc.pojo.entity.OrderBaoli;
import com.chinamobile.iot.sc.pojo.entity.OrderBaoliExample;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.BaoliOrderVO;
import com.chinamobile.iot.sc.pojo.vo.OrderBaoliVO;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.OrderBaoliService;
import com.chinamobile.iot.sc.service.TradeOrderInfoService;
import com.chinamobile.iot.sc.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13
 * @description 保理订单service实现类
 */
@Service
@Slf4j
public class OrderBaoliServiceImpl implements OrderBaoliService {

    @Resource
    private OrderBaoliMapperExt orderBaoliMapperExt;

    @Resource
    private OrderBaoliMapper orderBaoliMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private ExcelBaoliOrderNotUseImportHandler excelBaoliOrderNotUseImportHandler;

    @Resource
    private FileUtils fileUtils;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private TradeOrderInfoService tradeOrderInfoService;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private LogService logService;

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Override
    public List<BaoliOrderVO> listBaoliOrder(BaoliOrderParam baoliOrderParam) {
        return orderBaoliMapperExt.listBaoliOrder(baoliOrderParam);
    }
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));


    @Override
    public PageData<BaoliOrderVO> pageBaoliOrder(BaoliOrderParam baoliOrderParam,
                                                 LoginIfo4Redis loginIfo4Redis) {
        PageData<BaoliOrderVO> pageData = new PageData<>();
        Integer pageNum = baoliOrderParam.getPageNum();
        Integer pageSize = baoliOrderParam.getPageSize();

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 商品名称搜索支持反斜杠适配
        if(baoliOrderParam.getSkuOfferingName() != null){
            baoliOrderParam.setSkuOfferingName(baoliOrderParam.getSkuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        // 是否保理账号
        if (BaseConstant.PARTNER_BAOLI.equals(roleType)) {
            baoliOrderParam.setUserId(userId);
        }

        Boolean isPrimary = loginIfo4Redis.getIsPrimary();
        // 如果是主合作伙伴
        if (isPrimary != null && isPrimary) {
            List<String> downUserIdList = listDownUserId(userId);
            baoliOrderParam.setDownUserIdList(downUserIdList);
        }

        Page<BaoliOrderVO> page = new Page<>(pageNum, pageSize);
        List<BaoliOrderVO> baoliOrderVOList = orderBaoliMapperExt.listBaoliOrder(page, baoliOrderParam);
        if (CollectionUtils.isNotEmpty(baoliOrderVOList)) {
            baoliOrderVOList.stream().forEach(baoliOrderVO -> {
                String totalPrice = baoliOrderVO.getTotalPrice();
                if (StringUtils.isNotEmpty(totalPrice)){
                    String totalPriceStr = IOTEncodeUtils.decryptSM4(totalPrice, iotSm4Key, iotSm4Iv);
                    baoliOrderVO.setTotalPrice((new BigDecimal(totalPriceStr).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP))+"");
                }
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(baoliOrderVOList);

        return pageData;
    }

    @Override
    public void exportBaoliOrderCanNotUse(BaoliOrderExportParam baoliOrderExportParam,
                                          HttpServletResponse response) throws IOException {
        List<BaoliOrderExportDTO> baoliOrderExportDTOList
                = orderBaoliMapperExt.exportBaoliOrderCanNotUse(baoliOrderExportParam);
        if (CollectionUtils.isNotEmpty(baoliOrderExportDTOList)) {
            baoliOrderExportDTOList.stream().forEach(baoliOrderExportDTO -> {
                String deductPriceStr = baoliOrderExportDTO.getDeductPriceStr();
                if (StringUtils.isNotEmpty(deductPriceStr)) {
                    String deductPrice = IOTEncodeUtils.decryptSM4(deductPriceStr, iotSm4Key, iotSm4Iv);
                    baoliOrderExportDTO.setDeductPrice(new BigDecimal(deductPrice).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP));
                }
                String atomOfferingClass = baoliOrderExportDTO.getAtomOfferingClass();
                String spuOfferingClass = baoliOrderExportDTO.getSpuOfferingClass();
                if (AtomOfferingClassEnum.S.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass("软件");
                } else if (AtomOfferingClassEnum.H.name().equals(atomOfferingClass)) {
                    if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClass)) {
                        baoliOrderExportDTO.setAtomOfferingClass("合同履约类硬件");
                    } else {
                        baoliOrderExportDTO.setAtomOfferingClass("代销类硬件");
                    }

                } else if (AtomOfferingClassEnum.O.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.O.getDescribe());
                } else if (AtomOfferingClassEnum.D.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.D.getDescribe());
                } else if (AtomOfferingClassEnum.P.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.P.getDescribe());
                }else if (AtomOfferingClassEnum.F.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.F.getDescribe());
                } else if (AtomOfferingClassEnum.K.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.K.getDescribe());
                } else if (AtomOfferingClassEnum.C.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.C.getDescribe());
                } else if (AtomOfferingClassEnum.X.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.X.getDescribe());
                } else if (AtomOfferingClassEnum.A.name().equals(atomOfferingClass)) {
                    baoliOrderExportDTO.setAtomOfferingClass(AtomOfferingClassEnum.A.getDescribe());
                }
            });
        }
        String fileName = "导出不可用订单";
        ExportParams exportParams = new ExportParams(fileName,
                fileName, ExcelType.XSSF);
        ExcelUtils.exportExcel(baoliOrderExportDTOList, BaoliOrderExportDTO.class,
                fileName, exportParams, response);

        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.ORDER_MARK.code,
                IotLogUtil.baoliOrderExportCanNotUse(baoliOrderExportParam),LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeBaoliOrderStatus(OrderBaoliStatusChangeParam orderBaoliStatusChangeParam,String userId,String ip) {
        String orderAtomInfoId = orderBaoliStatusChangeParam.getOrderAtomInfoId();
        String orderId = orderBaoliStatusChangeParam.getOrderId();
        ChangeUseBaoliOrderDTO changeUseBaoliOrder = orderBaoliMapperExt.getChangeUseBaoliOrder(orderId);
        if (!Optional.ofNullable(changeUseBaoliOrder).isPresent()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.ORDER_MARK.code,
                        IotLogUtil.baoliOrderCanUse(orderId), userId, ip, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.NOT_FOUND_CAN_CHANGE_ORDER_BAOLI.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.NOT_FOUND_CAN_CHANGE_ORDER_BAOLI);
        }

        Integer specialAfterMarketHandle = changeUseBaoliOrder.getSpecialAfterMarketHandle();
        if (specialAfterMarketHandle == 1) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.ORDER_MARK.code,
                        IotLogUtil.baoliOrderCanUse(orderId), userId, ip, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.SPECIAL_AFTERMARKET_HANDLE_CAN_NOT_MARK.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.SPECIAL_AFTERMARKET_HANDLE_CAN_NOT_MARK);
        }

        // 更新保理状态为可用
        Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
        order2cAtomInfo.setBaoliStatus(1);
        Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample();
        order2cAtomInfoExample.createCriteria().andOrderIdEqualTo(orderId);
        order2cAtomInfoMapper.updateByExampleSelective(order2cAtomInfo,order2cAtomInfoExample);

        Date date = new Date();
        OrderBaoli orderBaoli = setOrderBaoli(date, changeUseBaoliOrder);

        orderBaoliMapper.insertSelective(orderBaoli);

        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.ORDER_MARK.code,
                IotLogUtil.baoliOrderCanUse(orderId),LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importBaoliOrderCanNotUse(MultipartFile file,
                                          HttpServletRequest request,
                                          HttpServletResponse response) throws Exception {
        response.setHeader("content-type", "application/octet-stream");
        if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }

        ExcelImportResult<ImportBaoliOrderNotUseDTO> result
                = ExcelUtils.importExcel(file.getInputStream(), 0, 1, excelBaoliOrderNotUseImportHandler, ImportBaoliOrderNotUseDTO.class);
        List<ImportBaoliOrderNotUseDTO> failList = result.getFailList();

        if (CollectionUtils.isNotEmpty(failList)) {
            long millis = System.currentTimeMillis();
            String fileName = "导入保理不可用订单".concat(millis + "-fail.xls");
            String failFile = System.getProperty("user.dir")
                    .concat(File.separator).concat("execl");
            File fileC = new File(failFile);
            if (!fileC.exists()) {
                fileC.mkdirs();
            }
            String failFilePath = failFile.concat(File.separator).concat(fileName);
            FileOutputStream fos = new FileOutputStream(failFilePath);
            result.getFailWorkbook().write(fos);
            fos.close();
            File downErrorFile = new File(failFilePath);
            response.setHeader("statecode", "99999");
            response.setHeader("message", "");
            fileUtils.downloadFile(downErrorFile, fileName, request, response);
        } else {
            List<ImportBaoliOrderNotUseDTO> baoliOrderNotUseDTOList = result.getList();
            if (CollectionUtils.isEmpty(baoliOrderNotUseDTOList)) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            List<OrderBaoli> orderBaoliList = new ArrayList<>();
            List<String> orderIdList = new ArrayList<>();
            Date date = new Date();

            baoliOrderNotUseDTOList.stream().forEach(importBaoliOrderNotUseDTO -> {
                String orderId = importBaoliOrderNotUseDTO.getOrderId();
                /*List<InfoToImportBaoliNotUseValidDTO> notUseValidDTOList = getInfoToImportBaoliNotUseValid(orderId);
                notUseValidDTOList.stream().forEach(infoToImportBaoliNotUseValidDTO -> {
                    String orderAtomInfoId = infoToImportBaoliNotUseValidDTO.getOrderAtomInfoId();
                });*/

                ChangeUseBaoliOrderDTO changeUseBaoliOrder = orderBaoliMapperExt.getChangeUseBaoliOrder(orderId);
                if (Optional.ofNullable(changeUseBaoliOrder).isPresent()) {
                    orderIdList.add(orderId);

                    OrderBaoli orderBaoli = setOrderBaoli(date, changeUseBaoliOrder);
                    orderBaoliList.add(orderBaoli);
                }
            });


            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("导入成功", "UTF-8").replaceAll("\\+", "%20"));

            // 更新保理状态为可用
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                order2cAtomInfo.setBaoliStatus(1);

                Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample();
                order2cAtomInfoExample.createCriteria()
                        .andOrderIdIn(orderIdList);
                order2cAtomInfoMapper.updateByExampleSelective(order2cAtomInfo, order2cAtomInfoExample);

                // 新增保理订单
                orderBaoliMapper.batchInsert(orderBaoliList);

                logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.ORDER_MARK.code,
                        IotLogUtil.baoliOrderImportCanUse(orderIdList),LogResultEnum.LOG_SUCESS.code, null);
            }
        }
    }

    @Override
    public List<InfoToImportBaoliNotUseValidDTO> getInfoToImportBaoliNotUseValid(String orderId) {
        return orderBaoliMapperExt.getInfoToImportBaoliNotUseValid(orderId);
    }

    @Override
    public PageData<OrderBaoliVO> pageOrderBaoli(OrderBaoliParam orderBaoliParam,
                                                 LoginIfo4Redis loginIfo4Redis) {
        PageData<OrderBaoliVO> pageData = new PageData<>();
        Integer pageNum = orderBaoliParam.getPageNum();
        Integer pageSize = orderBaoliParam.getPageSize();

        Page<BaoliOrderVO> page = new Page<>(pageNum, pageSize);
        List<OrderBaoliVO> orderBaoliVOList;
        // 商品名称搜索支持反斜杠适配
        if(orderBaoliParam.getSkuOfferingName() != null){
            orderBaoliParam.setSkuOfferingName(orderBaoliParam.getSkuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 判断是否是超管
        if (isAdmin) {
            orderBaoliVOList = orderBaoliMapperExt.listOrderBaoli(page, orderBaoliParam);
        } else {
            String roleType = loginIfo4Redis.getRoleType();
            String userId = loginIfo4Redis.getUserId();

            // 是否保理账号
            if (BaseConstant.PARTNER_BAOLI.equals(roleType)) {
                orderBaoliParam.setUserId(userId);
            }

            Boolean isPrimary = loginIfo4Redis.getIsPrimary();
            // 如果是主合作伙伴
            if (isPrimary != null && isPrimary) {
                List<String> downUserIdList = listDownUserId(userId);
                orderBaoliParam.setDownUserIdList(downUserIdList);
            }
            orderBaoliVOList = orderBaoliMapperExt.listOrderBaoliNotAdmin(page, orderBaoliParam);
        }

        if (CollectionUtils.isNotEmpty(orderBaoliVOList)) {
            orderBaoliVOList.stream().forEach(orderBaoliVO -> {
                Integer baoliStatus = orderBaoliVO.getBaoliStatus();
                String baoliStatusName = FinancingOrderStatusEnum.getDesc(baoliStatus);
                orderBaoliVO.setBaoliStatusName(baoliStatusName);

                BigDecimal thousand = new BigDecimal(1000);
                Long totalPrice = orderBaoliVO.getTotalPrice();
                BigDecimal totalPriceDec = new BigDecimal(totalPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                orderBaoliVO.setTotalPriceDec(totalPriceDec);
                Long payAmount = orderBaoliVO.getPayAmount();

                Long returnPrice = orderBaoliVO.getReturnPrice();
                if (returnPrice != null) {
                    BigDecimal returnPriceDec = new BigDecimal(returnPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                    orderBaoliVO.setReturnPriceDec(returnPriceDec);
                    BigDecimal remainPrice = new BigDecimal(payAmount - returnPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                    orderBaoliVO.setRemainPrice(remainPrice);
                }else {
                    // 已放款才有待还款金额
                    if ((int)FinancingOrderStatusEnum.BANK_PAYED.getCode() == baoliStatus){
                        BigDecimal payAmountDec = new BigDecimal(payAmount).divide(thousand, 2, RoundingMode.HALF_UP);
                        orderBaoliVO.setRemainPrice(payAmountDec);
                    }
                }
            });
        }
        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(orderBaoliVOList);

        return pageData;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> generateTradeOrderInfo(GenerateTradeOrderInfoParam generateTradeOrderInfoParam,
                                               LoginIfo4Redis loginIfo4Redis,
                                               Boolean addLog,String ip) {
        generateTradeOrderInfoParam.setUserId(loginIfo4Redis.getUserId());
        List<OrderBaoliVO> orderBaoliVOList = orderBaoliMapperExt.listCanGenerateTradeOrder(generateTradeOrderInfoParam);
        if (CollectionUtils.isEmpty(orderBaoliVOList)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code,
                        "【生成贸易订单】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"未获取到相关的保理订单信息");
            });
            throw new BusinessException("10028", "未获取到相关的保理订单信息");
        }

        List<OrderBaoliVO> canGenerateTradeList = orderBaoliVOList.stream().filter(orderBaoli -> {
            Integer baoliStatus = orderBaoli.getBaoliStatus();
            if (baoliStatus == (int) FinancingOrderStatusEnum.BEFORE_APPLY.getCode()
                    || baoliStatus == (int) FinancingOrderStatusEnum.REVOKE.getCode()) {
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canGenerateTradeList)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code,
                        "【生成贸易订单】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"未获取到相关的保理订单信息");
            });
            throw new BusinessException("10028", "保理订单没有可用于生成贸易订单的单子");
        }

        if (orderBaoliVOList.size() != canGenerateTradeList.size()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code,
                        "【生成贸易订单】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"选择的保理订单包含了不可生成贸易订单的单子");
            });
            throw new BusinessException("10028", "选择的保理订单包含了不可生成贸易订单的单子");
        }

        // 用于保存合同_合作伙伴id和保理订单id关系
        Map<String, List<String>> contractBaoliIdMap = new HashMap<>();
        // 用于保存合同_合作伙伴id和贸易订单关系
        Map<String, List<TradeOrderInfo>> contractTradeMap = new HashMap<>();
        Date date = new Date();

        orderBaoliVOList.forEach(orderBaoli -> {
            String contractNum = orderBaoli.getContractNum();
            String cooperatorId = orderBaoli.getCooperatorId();
            String contractCooperator = contractNum.concat("_").concat(cooperatorId);
            List baoliIdList = contractBaoliIdMap.get(contractCooperator);
            List<TradeOrderInfo> tradeOrderInfoList = contractTradeMap.get(contractCooperator);
            String id = orderBaoli.getOrderBaoliId();
            if (CollectionUtils.isEmpty(baoliIdList)) {
                baoliIdList = new ArrayList();
                baoliIdList.add(id);
                contractBaoliIdMap.put(contractCooperator, baoliIdList);

                tradeOrderInfoList = new ArrayList<>();
                TradeOrderInfo tradeOrderInfo = setTradeOrderInfo(date, orderBaoli);
                String nowToStr = DateUtils.dateToStr(date, DateUtils.DATE_FORMAT_NO_SYMBOL);
                String redisTradeKey = RedisLockConstant.TRADE_NO_KEY + nowToStr;
                // 获取最新编码
                redisUtil.smartLock(redisTradeKey + 1, () -> {
                    Object tradeNumber = redisUtil.get(redisTradeKey);
                    if (tradeNumber == null) {
                        tradeNumber = 1;
                    } else {
                        tradeNumber = (int) tradeNumber + 1;
                    }
                    String tradeNo = "IOTMALL".concat(nowToStr).concat(String.format("%06d", tradeNumber));
                    tradeOrderInfo.setTradeNo(tradeNo);
                    redisTemplate.opsForValue().set(redisTradeKey, tradeNumber, 24, TimeUnit.HOURS);
                    return null;
                });
                tradeOrderInfoList.add(tradeOrderInfo);
                contractTradeMap.put(contractCooperator, tradeOrderInfoList);

            } else {
                baoliIdList.add(id);
                TradeOrderInfo tradeOrderInfo = tradeOrderInfoList.get(0);
                long tradePrice = tradeOrderInfo.getTradePrice() + orderBaoli.getTotalPrice();
                tradeOrderInfo.setTradePrice(tradePrice);
            }
        });
        List<TradeOrderInfo> tradeOrderInfoList = new ArrayList<>();
        List<String> tradeNoList = new ArrayList<>();
        contractTradeMap.forEach((contract, tradeOrderInfos) -> {
            TradeOrderInfo tradeOrderInfo = tradeOrderInfos.get(0);
            BigDecimal maxFinancingPrice = new BigDecimal(tradeOrderInfo.getTradePrice()).multiply(new BigDecimal(85))
                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            tradeOrderInfo.setMaxFinancingPrice(maxFinancingPrice);
            tradeOrderInfoList.add(tradeOrderInfo);
            String tradeNo = tradeOrderInfo.getTradeNo();
            OrderBaoli orderBaoli = new OrderBaoli();
            orderBaoli.setTradeNo(tradeNo);
            orderBaoli.setBaoliStatus(FinancingOrderStatusEnum.PREPARE_APPLY.getCode());
            orderBaoli.setUpdateTime(date);
            OrderBaoliExample baoliExample = new OrderBaoliExample();
            baoliExample.createCriteria()
                    .andIdIn(contractBaoliIdMap.get(contract));
            orderBaoliMapper.updateByExampleSelective(orderBaoli, baoliExample);

            tradeNoList.add(tradeNo);
        });

        tradeOrderInfoService.batchInsert(tradeOrderInfoList);

        if(addLog){
            logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code,
                    IotLogUtil.generateTradeOrderInfo(tradeNoList),LogResultEnum.LOG_SUCESS.code, null);
        }
        return tradeNoList;
    }


    /**
     * 获取从合作伙伴用户id
     *
     * @param userId
     * @return
     */
    @Override
    public List<String> listDownUserId(String userId) {
        BaseAnswer<List<String>> downUserIdList = userFeignClient.getDownUserIds(userId);
        List<String> downUserIdListData = downUserIdList.getData();
        if (CollectionUtils.isEmpty(downUserIdListData)) {
            throw new BusinessException("10008", "该合作伙伴主账号下面没有从合作伙伴信息");
        }
        return downUserIdListData;
    }

    @Override
    public List<OrderBaoli> listOrderBaoli(OrderBaoliExample orderBaoliExample) {
        return orderBaoliMapper.selectByExample(orderBaoliExample);
    }

    @Override
    public void exportBaoliOrder(LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        ServletOutputStream outputStream = null;
        Workbook workbook = null;
        try {
            List<ExportBaoliOrderDTO> list = orderBaoliMapperExt.getExportBaoliOrderList(loginIfo4Redis.getUserId());
            if(list.isEmpty()){
                logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.BAOLI_ORDER.code,"【导出待保理订单】",loginIfo4Redis.getUserId(),0,LogResultEnum.LOG_SUCESS.code,"无数据");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"无数据");
            }
            for (ExportBaoliOrderDTO exportBaoliOrderDTO : list) {
                exportBaoliOrderDTO.setStatus(FinancingOrderStatusEnum.getDesc(exportBaoliOrderDTO.getBaoliStatus()));
            }
            workbook = ExcelUtils.exportSimpleExcel("baoli-order", ExportBaoliOrderDTO.class, list);
            outputStream = response.getOutputStream();
            workbook.write(outputStream);

            //记录日志
            logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code,FinancingManageOperateEnum.BAOLI_ORDER.code,"【导出待保理订单】",loginIfo4Redis.getUserId(),0,LogResultEnum.LOG_SUCESS.code,null);
        } catch (Exception e) {
            try {
                if (e instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode", businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                } else {
                    log.error("导出待保理订单发生异常,", e);
                    response.addHeader("stateCode", BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
        }finally {
            if(workbook != null){
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(outputStream != null){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void batchGenerateTradeOrder(MultipartFile file, LoginIfo4Redis loginIfo4Redis,String ip) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        List<BatchGenerateTradeOrderErrorDTO> failList = new ArrayList<>();
        //成功导入的日志
        List<String> tradeOrderIdList = new ArrayList<>();
        InputStream inputStream = null;
        //标记是否 没有失败的导入记录
        boolean noError = true;
        List<String> baoliOrderIdlist = new ArrayList<>();
        List<String> orderIdlist = new ArrayList<>();
        try {
            inputStream = file.getInputStream();
            List<BatchGenerateTradeOrderDTO> importList = EasyExcel.read(inputStream, BatchGenerateTradeOrderDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            if(CollectionUtils.isEmpty(importList)){
                logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code, "【导入生成贸易订单】",LogResultEnum.LOG_FAIL.code, "无数据");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"无数据");
            }
            if(importList.size() > 10000){
                logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code, "【导入生成贸易订单】",LogResultEnum.LOG_FAIL.code, "最多允许导入10000条数据");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"最多允许导入10000条数据");
            }
            orderIdlist = importList.stream().map(o -> {return o.getOrderId();}).collect(Collectors.toList());
            Iterator<String> iterator = orderIdlist.iterator();
            while (iterator.hasNext()){
                String orderId = iterator.next();
                try {
                    //校验订单 保理状态是否为待申请/已撤销，是否属于合作伙伴保理
                    List<OrderBaoli> orderBaoliList = orderBaoliMapper.selectByExample(new OrderBaoliExample().createCriteria().andOrderIdEqualTo(orderId).example());
                    if(orderBaoliList.isEmpty()){
                        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code, "【导入生成贸易订单】",LogResultEnum.LOG_FAIL.code, "订单号不存在");

                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"订单号不存在");
                    }
                    //目前保理订单和订单是一对一关系，因为目前只有一个原子订单的订单才能进行保理融资
                    OrderBaoli orderBaoli = orderBaoliList.get(0);
                    Integer baoliStatus = orderBaoli.getBaoliStatus();
                    if(baoliStatus.intValue() != FinancingOrderStatusEnum.BEFORE_APPLY.code && baoliStatus.intValue() != FinancingOrderStatusEnum.REVOKE.code ){
                        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code, "【导入生成贸易订单】",LogResultEnum.LOG_FAIL.code, "订单保理状态不符合条件");

                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"订单保理状态不符合条件");
                    }

                    //根据公司比对保理订单是否属于合作伙伴保理，因为保理订单的cooperatorId是合作伙伴id而非保理合作伙伴id
                    BaseAnswer<Data4User> userBaseAnswer = userFeignClient.partnerInfoById(orderBaoli.getCooperatorId());
                    if(userBaseAnswer == null || userBaseAnswer.getData() == null){
                        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code, "【导入生成贸易订单】",LogResultEnum.LOG_FAIL.code, "订单对应合伙人账号信息不存在");

                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"订单对应合伙人账号信息不存在");
                    }
                    String partnerName = userBaseAnswer.getData().getPartnerName();
                    String loginUserPartnerName = loginIfo4Redis.getPartnerName();
                    if(!partnerName.equals(loginUserPartnerName)){
                        logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code, "【导入生成贸易订单】",LogResultEnum.LOG_FAIL.code, "订单不存在");

                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"订单不存在");
                    }
                    baoliOrderIdlist.add(orderBaoli.getId());
                }catch (Exception e){
                    noError = false;
                    BatchGenerateTradeOrderErrorDTO dto = new BatchGenerateTradeOrderErrorDTO();
                    dto.setOrderId(orderId);
                    dto.setFailReason((e instanceof BusinessException ? ((BusinessException)e).getStatus().getMessage() :  e.getMessage()));
                    failList.add(dto);
                    //不符合条件的订单移除
                    iterator.remove();
                }
            }

        } catch (Exception e) {
            noError = false;
            log.error("读取文件出错",e);
            BatchGenerateTradeOrderErrorDTO dto = new BatchGenerateTradeOrderErrorDTO();
            dto.setFailReason("读取文件出错:"+(e instanceof BusinessException ? ((BusinessException)e).getStatus().getMessage() :  e.getMessage()));
            failList.add(dto);
        }finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        if(CollectionUtils.isNotEmpty(baoliOrderIdlist)){
            try {
                //批量生成贸易订单
                GenerateTradeOrderInfoParam param = new GenerateTradeOrderInfoParam();
                param.setOrderBaoliIdList(baoliOrderIdlist);
                param.setUserId(loginIfo4Redis.getUserId());
                tradeOrderIdList = generateTradeOrderInfo(param,loginIfo4Redis,false,ip);
            } catch (Exception e) {
                noError = false;
                log.error("批量生成贸易订单出错",e);
                BatchGenerateTradeOrderErrorDTO dto = new BatchGenerateTradeOrderErrorDTO();
                dto.setFailReason("批量生成贸易订单出错:"+ (e instanceof BusinessException ? ((BusinessException)e).getStatus().getMessage() :  e.getMessage()));
                failList.add(dto);
            }
        }


        //记录导入成功的日志
        if(CollectionUtils.isNotEmpty(tradeOrderIdList)){
            StringBuilder content = new StringBuilder("【导入生成贸易订单】\n贸易订单编号"+String.join("、",tradeOrderIdList));
            logService.recordOperateLog(ModuleEnum.FINANCING_MANAGE.code, FinancingManageOperateEnum.BAOLI_ORDER.code, content.toString(), LogResultEnum.LOG_SUCESS.code, null);
        }

        if(CollectionUtils.isNotEmpty(failList)){
            //生成订单失败的excel
            writeFailReason(response,failList);
        }
        if(noError){
            //如果没有发生错误，直接把返回信息写入header中，便于区分发生错误时直接返回二进制流的情况
            response.addHeader("stateCode", "00000");
            try {
                response.addHeader("message", URLEncoder.encode("导入成功","UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 设置订单保理参数
     *
     * @param date
     * @param changeUseBaoliOrder
     * @return
     */
    private OrderBaoli setOrderBaoli(Date date,
                                     ChangeUseBaoliOrderDTO changeUseBaoliOrder) {
        OrderBaoli orderBaoli = new OrderBaoli();
        orderBaoli.setId(BaseServiceUtils.getId());
        orderBaoli.setCreateTime(date);
        orderBaoli.setUpdateTime(date);
        orderBaoli.setBaoliStatus(FinancingOrderStatusEnum.BEFORE_APPLY.getCode());

        BeanUtils.copyProperties(changeUseBaoliOrder, orderBaoli);

        return orderBaoli;
    }

    /**
     * 设置贸易订单参数
     *
     * @param date
     * @param orderBaoli
     * @return
     */
    private TradeOrderInfo setTradeOrderInfo(Date date,
                                             OrderBaoliVO orderBaoli) {
        TradeOrderInfo tradeOrderInfo = new TradeOrderInfo();
        tradeOrderInfo.setId(BaseServiceUtils.getId());
        tradeOrderInfo.setContractNum(orderBaoli.getContractNum());
        tradeOrderInfo.setBuyerName(orderBaoli.getBuyerName());
        tradeOrderInfo.setSellerName(orderBaoli.getSellerName());
        tradeOrderInfo.setCooperatorId(orderBaoli.getCooperatorId());
        Long totalPrice = orderBaoli.getTotalPrice();
        tradeOrderInfo.setTradePrice(totalPrice);
        tradeOrderInfo.setBaoliStatus(FinancingOrderStatusEnum.PREPARE_APPLY.getCode());
        tradeOrderInfo.setCreateTime(date);
        tradeOrderInfo.setUpdateTime(date);
        return tradeOrderInfo;
    }

    private void writeFailReason(HttpServletResponse response, List<BatchGenerateTradeOrderErrorDTO> failList) {
        Workbook workbook = ExcelUtils.exportSimpleExcel("failReason", BatchGenerateTradeOrderErrorDTO.class, failList);
        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            workbook.write(outputStream);
        } catch (IOException ex) {
            ex.printStackTrace();
        }finally {
            if(workbook != null){
                try {
                    workbook.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
            if(outputStream != null){
                try {
                    outputStream.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

}
