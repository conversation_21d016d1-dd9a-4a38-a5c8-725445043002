package com.chinamobile.iot.sc.constant.gio;

import com.chinamobile.iot.sc.constant.ManagerStatusEnum;

import java.util.Objects;

/**
 * @Author: wang<PERSON>qi
 * @Description: 订购渠道枚举量
 */
public enum OrderingChannelNameEnum {

    // 订单状态
    OWN_CHANNEL("019030", "商城"),
    SSO_CHANNEL("000000", "其他"),
    MINI_PROGRAM_CHANNEL("000001", "商城小程序");



    private final String code;

    private final String message;

    OrderingChannelNameEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getStatus() {
        return code;
    }


    public String getMessage() {
       return message;
    }
    public static OrderingChannelNameEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        OrderingChannelNameEnum[] values = OrderingChannelNameEnum.values();
        for (OrderingChannelNameEnum value : values) {
            if (Objects.equals(value.code, code)) {
                return value;
            }
        }
        return null;
    }

}
