package com.chinamobile.iot.sc.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.NoticeTypeConstant;
import com.chinamobile.iot.sc.constant.PlatformClassEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.*;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig;
import com.chinamobile.iot.sc.pojo.entity.ChargeItemConfigExample;
import com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo;
import com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfoExample;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import com.chinamobile.iot.sc.request.order2c.*;
import com.chinamobile.iot.sc.response.SoftServiceResponse;
import com.chinamobile.iot.sc.service.UserRefundKxService;
import com.chinamobile.iot.sc.service.impl.Order2CServiceImpl;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.Sm4Util;
import com.chinamobile.iot.sc.util.softServiceUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exception.StatusConstant.FAILED_GENERATE_SOFTWARE_SERVICES;
import static com.chinamobile.iot.sc.util.DateUtils.*;

/**
 * <AUTHOR> xiemaohua
 * @date : 2025/4/25 11:12
 * @description: 订单开通服务定时生效，及特殊退款的开通服务定时任务
 **/

@Component
@Slf4j
public class OrderTakeEffectTimeTask {


    @Resource
    private ServiceOpenInfoMapper serviceOpenInfoMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private ChargeItemConfigMapper chargeItemConfigMapper;
    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private UserRefundKxService userRefundKxService;

    @Resource
    private QlySoftOpenClient qlySoftOpenClient;
    @Resource
    private OnenetSoftOpenClient onenetSoftOpenClient;
    @Resource
    private OyeCyberSoftOpenClient oyeCyberSoftOpenClient;

    @Resource
    private GxMiNiSoftOpenClient gxMiNiSoftOpenClient;

    @Resource
    private XCWSSoftOpenClient xcwsSoftOpenClient;

    @Resource
    private JsKangYangSoftOpenClient jsKangYangSoftOpenClient;

    @Resource
    private YtSoftOpenClient ytSoftOpenClient;

    @Resource
    private XingCheWeishiSoftOpenClient xingCheWeishiSoftOpenClient;

    @Resource
    private HemuSoftOpenClient hemuSoftOpenClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Value("${sms.smsSoftServiceTemplateId: 107683}")
    private String smsSoftServiceTemplateId;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;
    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    @Value("${softService.sm4Key}")
    private String sm4Key;
    @Value("${softService.secretKey}")
    private String secretKeyPlatform;


    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");
    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);


    //每天6点执行
    @Scheduled(cron = "0 0 6 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void work() {
        //查询A13软件服务订单，不是特殊退款订单，生效规则是定时生效，生效时间范围是今天的，订单状态是0
        //获取今日时间起始
        Date date = new Date();
        String dayBegin = DateUtils.dateToStr(DateUtils.getDayBegin(date), DATE_FORMAT_NO_SYMBOL);
       // String dayEnd = DateUtils.dateToStr(DateUtils.getDayEnd(date), DATETIME_FORMAT_NO_SYMBOL);
        log.info("定时任务开始执行定时开通软件服务：dayBegin：{}",dayBegin);
        Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        Order2cInfoExample.Criteria criteria = order2cInfoExample.createCriteria();
        criteria.andSpuOfferingClassEqualTo(SPUOfferingClassEnum.A13.getSpuOfferingClass())
                .andSpecialAfterMarketHandleEqualTo(0).andEffectiveRulesEqualTo("1")
                .andStatusEqualTo(0).andEffectiveTimeEqualTo(dayBegin);
        List<Order2cInfo> order2cInfoList = order2cInfoMapper.selectByExample(order2cInfoExample);
        if (CollectionUtils.isEmpty(order2cInfoList)) {
            log.info("今天没有需要开通的软件服务订单dateOrder2cInfoList：{}", date);
        }else {
            List<String> collect = order2cInfoList.stream().map(Order2cInfo::getOrderId).collect(Collectors.toList());
            log.info("今天需要开通的软件服务订单dateOrder2cInfoList：{}", new Gson().toJson(collect));
        } /*else {
            for (Order2cInfo order2cInfo : order2cInfoList) {
                processAtomicInformationOrder(order2cInfo);
            }
        }*/
        //开通成功后进行数据添加addSoftService  这个在订单同步时添加数据
        //通过订单id查询开通信息 更新当前订单开通状态
       /* if (atomOpenInfos != null && atomOpenInfos.size() > 0) {
            serviceOpenInfoMapper.batchInsert(atomOpenInfos);
        }*/
        // 特殊退款逻辑   查询所有为处理的特殊退款单  没完全开通的还需要查询出来在开通么（不在开通）
        Order2cInfoExample order2cInfoSpecialExample = new Order2cInfoExample();
        Order2cInfoExample.Criteria criteriaSpecial = order2cInfoSpecialExample.createCriteria();
        criteriaSpecial.andSpuOfferingClassEqualTo(SPUOfferingClassEnum.A13.getSpuOfferingClass())
                .andSpecialAfterMarketHandleEqualTo(1).andEffectiveRulesEqualTo("1")
                .andEffectiveTimeLessThanOrEqualTo(dayBegin).andSpecialAfterStatusNotIn(Arrays.asList("2","3"));

        List<Order2cInfo> order2cInfoSpecialListAll = order2cInfoMapper.selectByExample(order2cInfoSpecialExample);
        List<Order2cInfo> order2cInfoSpecialList = new ArrayList<>();
        //过滤出开通失败的特殊退款订单
        if (CollectionUtils.isNotEmpty(order2cInfoSpecialListAll)){
            for (Order2cInfo order2cInfo : order2cInfoSpecialListAll) {
                String orderId = order2cInfo.getOrderId();
                List<ServiceOpenInfo> serviceOpenInfos = serviceOpenInfoMapper.selectByExample(new ServiceOpenInfoExample().createCriteria()
                        .andOrderIdEqualTo(orderId).example());
                //能查询到说明开通过的 状态不是全是开通中的
                List<ServiceOpenInfo> collect = serviceOpenInfos.stream().filter(serviceOpenInfo -> serviceOpenInfo.getSoftServiceOpenStatus().equals(2)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(serviceOpenInfos) && serviceOpenInfos.size()==collect.size()){
                    order2cInfoSpecialList.add(order2cInfo);
                }
            }
        }

        //TODO 处理特殊退款部分退款完成开通
        List<Order2cInfo> order2cInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(order2cInfoSpecialList)) {
            log.info("今天有可能开通的特殊退款软件服务订单order2cInfoSpecialList：{}", new Gson().toJson(order2cInfoSpecialListAll));
            //部分退款完成
            List<Order2cInfo> collectPartiallyCompleted = order2cInfoSpecialList.stream().filter(order2cInfo -> order2cInfo.getSpecialAfterStatus().equals("6")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectPartiallyCompleted)) {
                    order2cInfos.addAll(collectPartiallyCompleted);
            }
            //全额退款取消、部分退款取消
            List<Order2cInfo> collectPartiallyCancel = order2cInfoSpecialList.stream().filter(order2cInfo -> (order2cInfo.getSpecialAfterStatus().equals("4") || order2cInfo.getSpecialAfterStatus().equals("5"))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectPartiallyCancel)) {
                order2cInfos.addAll(collectPartiallyCancel);
            }
            //、特殊退货退款开启期间未发起退款
            List<Order2cInfo> orderSpecialAfter = new ArrayList<>();
            order2cInfoSpecialList.forEach(order2cInfo -> {
                String specialAfterStatus = order2cInfo.getSpecialAfterStatus();
                String specialAfterLatestTime = order2cInfo.getSpecialAfterLatestTime();
                try {
                    /*Date strToDate = DateUtils.strToDate(specialAfterLatestTime, DATETIME_FORMAT_NO_SYMBOL);
                    String specialAfterBegin = DateUtils.dateToStr(strToDate, DATE_FORMAT_NO_SYMBOL);*/
                    String dayBeginA = DateUtils.dateToStr(date, DATETIME_FORMAT_NO_SYMBOL);
                   if (specialAfterStatus.equals("1") && (specialAfterLatestTime.compareTo(dayBeginA) < 0)){
                       orderSpecialAfter.add(order2cInfo);
                   }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            if (CollectionUtils.isNotEmpty(orderSpecialAfter)) {
                order2cInfos.addAll(orderSpecialAfter);
            }
            //执行特殊退款定时订单
            if (CollectionUtils.isEmpty(order2cInfos)){
                log.info("定时生效特殊退款订单没有dateTimeTask：{}",dayBegin);
            }else {
                List<String> collect = order2cInfos.stream().map(Order2cInfo::getOrderId).collect(Collectors.toList());
                log.info("定时生效特殊退款订单dateTimeTask：{}", new Gson().toJson(collect));
            }
        }
        //总的执行订单
        if (CollectionUtils.isNotEmpty(order2cInfoList) || CollectionUtils.isNotEmpty(order2cInfos)){
            order2cInfoList.addAll(order2cInfos);
            for (Order2cInfo order2cInfo : order2cInfoList) {
                processAtomicInformationOrder(order2cInfo);
            }
        }else {
            log.info("定时生效特殊退款和正常订单没有 dayBeginTask：{}",dayBegin);
        }
        log.info("定时任务结束执行定时开通软件服务：dayBegin：{}",dayBegin);
    }


    /**
     * 开通、退订软件服务
     */
    public void softServiceOpenTask(List<ServiceOpenInfo> serviceOpenInfoList,
                                    List<String> atomCodeListAll,Order2cInfo order2cInfo) {
        // 剔除同一订单下相同软件平台商品编码的原子数量
        ServiceOpenInfo serviceOpenInfo = serviceOpenInfoList.get(0);
        // 目前一个订单只有一个sku
        List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(
                new AtomOfferingInfoExample().createCriteria().andSpuCodeEqualTo(serviceOpenInfo.getSpuOfferingCode())
                        .andSkuCodeEqualTo(serviceOpenInfo.getSkuOfferingCode())
                        .andOfferingCodeEqualTo(serviceOpenInfo.getAtomOfferingCode())
                        .example());
        AtomOfferingInfo atomOfferingInfo = null;
        if (atomOfferingInfoList != null && atomOfferingInfoList.size() > 0) {
            atomOfferingInfo = atomOfferingInfoList.get(0);
        }

        String platform = judgePlatform(serviceOpenInfo.getSpuOfferingCode(), serviceOpenInfo.getSkuOfferingCode(),
                serviceOpenInfo.getAtomOfferingCode());
        if (platform == null) {
            log.info("定时生效未找到对应的业务平台,将开通状态设置为失败serviceOpenInfoTask:{}",serviceOpenInfo);
            // Order2cAtomInfo order2cAtomInfo =
            // order2cAtomInfoMapper.selectByPrimaryKey(serviceOpenInfo.getAtomOrderId());
            List<String> atomCodeList = new ArrayList<>();
            // 更新软件服务状态为开通失败
            for (ServiceOpenInfo serviceOpenInfoItem : serviceOpenInfoList) {
                if (!atomCodeList.contains(serviceOpenInfoItem.getAtomOrderId())) {
                    atomCodeList.add(serviceOpenInfoItem.getAtomOrderId());
                }
                ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                BeanUtils.copyProperties(serviceOpenInfoItem, serviceOpenInfo1);
                serviceOpenInfo1.setSoftServiceOpenStatus(1);
                serviceOpenInfo1.setOpenFailReason("未找到对应的业务平台");
                serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo1);
            }
            for (String atomCode : atomCodeList) {
                Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                order2cAtomInfo.setId(atomCode);
                order2cAtomInfo.setSoftServiceStatus(1);
                order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo);
            }

            if (atomOfferingInfo != null && atomOfferingInfo.getCooperatorId() != null) {
                sendMsg(serviceOpenInfoList.get(0), 0, atomOfferingInfo.getCooperatorId());
            } else {
                sendMsg(serviceOpenInfoList.get(0), 0, null);
            }

        } else {
            List<String> atomCodeList = new ArrayList<>();
       /*     if (orderInfo != null) {
                orderInfo.getSpuOfferingInfo().getSkuOfferingInfo().forEach(skuOfferingInfoDTO -> {
                    skuOfferingInfoDTO.getAtomOfferingInfo().forEach(atomOfferingInfoItem -> {
                        atomCodeList.add(atomOfferingInfoItem.getOfferingCode());
                    });
                });
            }
            List<String> atomCodeListAllNew = orderInfo == null ? atomCodeListAll : atomCodeList;*/
            List<String> atomCodeListAllNew = atomCodeListAll;
            for (int i = 0; i < atomCodeListAllNew.size(); i++) {
                List<ServiceOpenInfo> needOpenList = new ArrayList<>();
                for (ServiceOpenInfo serviceOpenInfoItem : serviceOpenInfoList) {
                    if (atomCodeListAllNew.get(i).equals(serviceOpenInfoItem.getAtomOfferingCode())) {
                        needOpenList.add(serviceOpenInfoItem);
                    }
                }
                try {
                    if (i > 0) {
                        Thread.sleep(2000);
                    }
                 /*   if (orderInfo == null) {
                        CustInfoDTO custInfoDTO = new CustInfoDTO();
                        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(serviceOpenInfo.getOrderId());
                        custInfoDTO.setCustCode(order2cInfo.getCustCode());
                        custInfoDTO.setBeId(order2cInfo.getBeId());
                        custInfoDTO.setLocation(order2cInfo.getLocation());
                        custInfoDTO.setRegionID(order2cInfo.getRegionId());
                        OrderInfoDTO orderInfoDTO1 = new OrderInfoDTO();
                        orderInfoDTO1.setCustInfo(custInfoDTO);
                        softOpen(orderInfoDTO1, needOpenList, platform);

                    } else {

                    }*/
                    softOpen(order2cInfo, needOpenList, platform);
                } catch (Exception e) {
                    log.error("软件服务开通失败:{}", e.getMessage());
                }
            }
        }
    }


    public String judgePlatform(String spuOfferingCode, String skuOfferingCode, String atomOfferingCode) {
        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample().createCriteria()
                .andSpuCodeEqualTo(spuOfferingCode)
                .andSkuCodeEqualTo(skuOfferingCode)
                .andOfferingCodeEqualTo(atomOfferingCode)
                .example();
        List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
        try {
            if (atomOfferingInfoList.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应原子信息");

            }
            AtomOfferingInfo atomOfferingInfoAll = atomOfferingInfoList.get(0);
            // 判断开通平台
            ChargeItemConfigExample chargeItemConfigExample = new ChargeItemConfigExample();
            ChargeItemConfigExample.Criteria criteria = chargeItemConfigExample.createCriteria();
            if (atomOfferingInfoAll.getChargeId() != null) {
                criteria.andChargeIdEqualTo(atomOfferingInfoAll.getChargeId());
            }
            if (atomOfferingInfoAll.getProductType() != null) {
                criteria.andProductTypeIdEqualTo(atomOfferingInfoAll.getProductType());
            }
            List<ChargeItemConfig> chargeItemConfigList = chargeItemConfigMapper
                    .selectByExample(chargeItemConfigExample);
            if (chargeItemConfigList.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应账目项配置");

            }
            ChargeItemConfig chargeItemConfig = chargeItemConfigList.get(0);
            if (chargeItemConfig.getPlatformCode() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该原子没有绑定业务平台");
            }
            return chargeItemConfig.getPlatformCode();
        } catch (Exception e) {
            log.error("软件服务同步业务平台失败，同步更新软件服务开通状态", e);
            return null;
        }

    }

    public void sendMsg(ServiceOpenInfo serviceOpenInfoItem, Integer operateType, String cooperatorId) {
        try {
            // 获取原子商品信息
            AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample().createCriteria()
                    .andSpuCodeEqualTo(serviceOpenInfoItem.getSpuOfferingCode())
                    .andSkuCodeEqualTo(serviceOpenInfoItem.getSkuOfferingCode())
                    .andOfferingCodeEqualTo(serviceOpenInfoItem.getAtomOfferingCode())
                    .example();
            List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper
                    .selectByExample(atomOfferingInfoExample);
            if (atomOfferingInfoList == null || atomOfferingInfoList.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到原子商品信息");
            }
            AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
            // 判断开通平台
            ChargeItemConfigExample chargeItemConfigExample = new ChargeItemConfigExample();
            ChargeItemConfigExample.Criteria criteria = chargeItemConfigExample.createCriteria();
            if (atomOfferingInfo.getChargeId() != null) {
                criteria.andChargeIdEqualTo(atomOfferingInfo.getChargeId());
            }
            if (atomOfferingInfo.getProductType() != null) {
                criteria.andProductTypeIdEqualTo(atomOfferingInfo.getProductType());
            }
            List<ChargeItemConfig> chargeItemConfigList = chargeItemConfigMapper
                    .selectByExample(chargeItemConfigExample);
            if (chargeItemConfigList.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应账目项配置");

            }
            ChargeItemConfig chargeItemConfig = chargeItemConfigList.get(0);
            if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.Q.getPlatformCode())) {
                // 千里眼业务平台
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                        .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_QLY_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList, "千里眼软件服务",
                            operateType, serviceOpenInfoItem.getOrderId(), cooperatorId);
                    log.info("千里眼软件服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("未配置千里眼软件服务通失败的数据通知指定人员");
                }

            }else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.GXMINI.getPlatformCode())) {
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                        .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_GXMINI_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList, "广西打印小程序业务平台软件服务",
                            operateType, serviceOpenInfoItem.getOrderId(), cooperatorId);
                    log.info("广西打印小程序业务平台服开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("广西打印小程序业务平台软件服务开通失败的数据通知指定人员");
                }

            }else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.JSKY.getPlatformCode())) {
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_JSKY_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList, "江苏康养软件服务", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                    log.info("JSKY江苏康养软件服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("JSKY江苏康养软件服务开通失败的数据通知指定人员");
                }

            } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.X.getPlatformCode())) {
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                        .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_XCWS_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList, "行车卫士短信预警软件服务",
                            operateType, serviceOpenInfoItem.getOrderId(), cooperatorId);
                    log.info("行车卫士短信预警软件服开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("未配置行车卫士短信预警软件服务开通失败的数据通知指定人员");
                }

            }else if(chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.XCWS.getPlatformCode())){
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                        .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_XCWSYW_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList, "行车卫士业务平台服务",
                            operateType, serviceOpenInfoItem.getOrderId(), cooperatorId);
                    log.info("行车卫士业务平台服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("行车卫士业务平台服务开通失败的数据通知指定人员");
                }
            } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.O.getPlatformCode())) {
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                        .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_ONENET_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList, "OneNET公有云服务",
                            operateType, serviceOpenInfoItem.getOrderId(), cooperatorId);
                    log.info("OneNET公有云服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("未配置OneNET公有云服务开通失败的数据通知指定人员");
                }
            } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.H.getPlatformCode())) {
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                        .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_HM_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList, "和目业务平台",
                            operateType, serviceOpenInfoItem.getOrderId(), cooperatorId);
                    log.info("和目业务平台软件服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("未配置和目业务平台软件服务开通失败的数据通知指定人员");
                }
            } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.OC.getPlatformCode())) {
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                        .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_ONECYBER_SOFTSERVICE_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMSSoftService(userRefundKxVOList,
                            "OneCyber5G 专网运营平台", operateType, serviceOpenInfoItem.getOrderId(), cooperatorId);
                    log.info("OneCyber5G 专网运营平台服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                } else {
                    log.warn("未配置OneCyber5G 专网运营平台服务开通失败的数据通知指定人员");
                }
            } else {
                log.info("软件服务开通失败短信通知未找到业务开通平台");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到业务开通平台");

            }
        } catch (Exception e) {
            log.error("发送短信失败:{}", e.getMessage());
        }

    }

    public void softOpen(Order2cInfo orderInfo, List<ServiceOpenInfo> serviceOpenInfoList, String platform) {
        // 自动开通
        ServiceOpenInfo serviceOpenInfo = serviceOpenInfoList.get(0);
        //CustInfoDTO custInfo = orderInfo.getCustInfo();
        Integer specialAfterMarketHandle = orderInfo.getSpecialAfterMarketHandle();
        String specialAfterStatus = orderInfo.getSpecialAfterStatus();
        String specialAfterRefundsNumber = orderInfo.getSpecialAfterRefundsNumber();

        AtomOfferingInfo atomOfferingInfo = null;
        try {
            List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper
                    .selectByExample(new AtomOfferingInfoExample().createCriteria()
                            .andSpuCodeEqualTo(serviceOpenInfo.getSpuOfferingCode())
                            .andSkuCodeEqualTo(serviceOpenInfo.getSkuOfferingCode())
                            .andOfferingCodeEqualTo(serviceOpenInfo.getAtomOfferingCode())
                            .example());
            if (atomOfferingInfoList == null || atomOfferingInfoList.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到原子商品信息");
            }
            atomOfferingInfo = atomOfferingInfoList.get(0);
            // 构造body参数
            SoftServiceParam softServiceParam = new SoftServiceParam();
            SoftServiceParam.OrderInfo orderInfoParam = new SoftServiceParam.OrderInfo();
            orderInfoParam.setOrderId(serviceOpenInfo.getAtomOrderId());
            orderInfoParam.setCustCode(orderInfo.getCustCode());
            // 手机号加密处理
            final String encrypt = Sm4Util.encrypt(serviceOpenInfo.getSoftServicePhone(), sm4Key);
            orderInfoParam.setOrderPhone(encrypt);
            orderInfoParam.setEquipmentCode(serviceOpenInfo.getEquipmentCode() == null ? null
                    : IOTEncodeUtils.decryptSM4(serviceOpenInfo.getEquipmentCode(), iotSm4Key, iotSm4Iv));
            orderInfoParam.setProvinceCode(orderInfo.getBeId());
            log.info("软件服务同步业务平台设备码号:{}",
                    serviceOpenInfo.getEquipmentCode() == null ? null : serviceOpenInfo.getEquipmentCode());
            orderInfoParam.setCityCode(orderInfo.getLocation());
            orderInfoParam.setCustCode(IOTEncodeUtils.decryptSM4(orderInfo.getCustCode(), iotSm4Key, iotSm4Iv));
            orderInfoParam.setCountryCode(orderInfo.getRegionId());
            orderInfoParam.setOrderType("01");// 开通
            orderInfoParam.setMallOrderId(serviceOpenInfo.getOrderId());
            orderInfoParam.setOrgType(String.valueOf(Integer.parseInt(orderInfo.getCustomerType()) + 1));
            orderInfoParam.setOrderingChannelSource(orderInfo.getOrderingChannelSource());
            orderInfoParam.setSxtSoftOfferingCode(serviceOpenInfo.getExtSoftOfferingCode());
            List<SoftServiceParam.productInfo> productList = serviceOpenInfoList.stream().map(a -> {
                SoftServiceParam.productInfo vo = new SoftServiceParam.productInfo();
                vo.setExtSoftOfferingCode(a.getExtSoftOfferingCode());
                vo.setOrderSerialNumber(a.getId());
                return vo;
            }).collect(Collectors.toList());
            // 千里眼采用新开通方式
            List<String> pList = new ArrayList<>();
            pList.add(PlatformClassEnum.Q.getPlatformCode());
            pList.add(PlatformClassEnum.YT.getPlatformCode());
            pList.add(PlatformClassEnum.GXMINI.getPlatformCode());
            pList.add(PlatformClassEnum.XCWS.getPlatformCode());
            pList.add(PlatformClassEnum.JSKY.getPlatformCode());
            if (pList.contains(platform)) {
                //千里眼的判断 是否是特殊退款
                if (specialAfterMarketHandle != null && specialAfterMarketHandle==0){
                    orderInfoParam.setNum(serviceOpenInfo.getOpenNum() == null ? null : serviceOpenInfo.getOpenNum());
                }else if (specialAfterMarketHandle != null && specialAfterMarketHandle==1 && specialAfterStatus.equals("6")){
                    //特殊退款部分退款成功  开通剩余数
                    Long openNum = serviceOpenInfo.getOpenNum() == null ? null : serviceOpenInfo.getOpenNum();
                    long refundsNum = Long.parseLong(specialAfterRefundsNumber);
                    orderInfoParam.setNum(openNum -refundsNum);
                }else{
                    //其他特殊退款状态
                    orderInfoParam.setNum(serviceOpenInfo.getOpenNum() == null ? null : serviceOpenInfo.getOpenNum());
                }

            } else {
                softServiceParam.setProductList(productList);
            }
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(orderInfoParam);
            softServiceParam.setOrderInfo(jsonString);
            // String sign =
            // com.chinamobile.iot.sc.util.SignUtils.getSign(objectMapper.writeValueAsString(sortedKeys),
            // secretKeySoft);
            String sign = softServiceUtil.toCheckIotMallSign(jsonString, secretKeyPlatform);
            Map<String, String> signParam = new HashMap<>();
            signParam.put("sign", sign);
            SoftServiceResponse softServiceResponse;
            log.info("软件服务同步业务平台参数:{}", softServiceParam);
            log.info("软件服务同步业务平台签名:{}", sign);
            if (platform.equals(PlatformClassEnum.Q.getPlatformCode())) {
                log.info("千里眼平台开通");
                // 千里眼业务平台开通
                softServiceResponse = qlySoftOpenClient.softServiceOpen(signParam, softServiceParam);

            } else if (platform.equals(PlatformClassEnum.X.getPlatformCode())) {
                log.info("行车卫士平台开通");
                softServiceResponse = xingCheWeishiSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else if (platform.equals(PlatformClassEnum.O.getPlatformCode())) {
                log.info("onenet平台开通");
                softServiceResponse = onenetSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else if (platform.equals(PlatformClassEnum.H.getPlatformCode())) {
                log.info("HEMU平台开通");
                softServiceResponse = hemuSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else if (platform.equals(PlatformClassEnum.OC.getPlatformCode())) {
                log.info("OneCyber5G 专网运营平台");
                softServiceResponse = oyeCyberSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            }else if (platform.equals(PlatformClassEnum.YT.getPlatformCode())) {
                log.info("云瞳平台开通");
                softServiceResponse = ytSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            }else if (platform.equals(PlatformClassEnum.GXMINI.getPlatformCode())) {
                log.info("GXMINI定时任务 广西打印小程序业务平台开通");
                softServiceResponse = gxMiNiSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            }else if(platform.equals(PlatformClassEnum.XCWS.getPlatformCode())){
                log.info("XCWSYW行车卫士业务平台");
                softServiceResponse = xcwsSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            }else if (platform.equals(PlatformClassEnum.JSKY.getPlatformCode())) {
                log.info("JSKY定时任务 江苏康养软件业务平台开通");
                softServiceResponse = jsKangYangSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else {
                log.info("软件服务同步业务平台失败", "未找到业务开通平台");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到业务开通平台");

            }
            if (!softServiceResponse.getResultCode().equals("0")) {
                log.error("软件服务开通失败", softServiceResponse.getResultDesc());
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,
                        "软件服务开通失败" + softServiceResponse.getResultDesc());

            } else {
                log.info("软件服务请求业务平台操作成功，请求业务结果返回为:{}", softServiceResponse);
            }

        } catch (Exception e) {
            log.error("软件服务同步业务平台失败，同步更新软件服务开通状态", e);
            // 更新原子订单状态和软件服务状态为开通失败
            Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper
                    .selectByPrimaryKey(serviceOpenInfo.getAtomOrderId());
            order2cAtomInfo.setSoftServiceStatus(1);
            order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo);
            // 更新软件服务状态为开通失败
            for (ServiceOpenInfo serviceOpenInfoItem : serviceOpenInfoList) {
                ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                BeanUtils.copyProperties(serviceOpenInfoItem, serviceOpenInfo1);
                serviceOpenInfo1.setSoftServiceOpenStatus(1);
                serviceOpenInfo1.setOpenFailReason(
                        e.getMessage() != null && e.getMessage().length() > 50 ? e.getMessage().substring(0, 50)
                                : e.getMessage());
                serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo1);
            }
            if (atomOfferingInfo != null && atomOfferingInfo.getCooperatorId() != null) {
                sendMsg(serviceOpenInfoList.get(0), 0, atomOfferingInfo.getCooperatorId());
            } else {
                sendMsg(serviceOpenInfoList.get(0), 0, null);
            }

            throw new BusinessException(BaseErrorConstant.API_RETURN_ERROR, "软件服务同步业务平台失败" + e.getMessage());

        }
    }

    /**
     * 软件服务开通失败的短信调用
     *
     * @param userRefundKxVOList
     * @param param1
     * @param param2
     * @return
     */
    private BaseAnswer<Void> sendTurnOnFailSMSSoftService(List<UserRefundKxVO> userRefundKxVOList,
                                                          String param1, Integer param2, String param3, String cooperatorId) {
        List<String> kxPhoneList = userRefundKxVOList.stream().map(UserRefundKxVO::getPhone)
                .collect(Collectors.toList());

        Msg4Request requestKx = new Msg4Request();
        List<String> mobileKxList = new ArrayList<>();
        mobileKxList.addAll(kxPhoneList);
        // 查询主从合作伙伴
        if (cooperatorId != null) {
            Data4User userInfo = userFeignClient.userInfo(cooperatorId).getData();
            if (userInfo.getIsSend()) {
                mobileKxList.add(userInfo.getPhone());
            }
            Data4User lordUser = null;
            if (!userInfo.getIsPrimary()) {
                BaseAnswer<Data4User> answer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
                lordUser = answer.getData();
            }
            if (lordUser != null && lordUser.getIsSend()) {
                mobileKxList.add(lordUser.getPhone());
            }
        }

        mobileKxList = mobileKxList.stream().distinct().collect(Collectors.toList());
        Map<String, String> messageKx = new HashMap<>();
        messageKx.put("softService", param1);
        messageKx.put("operate", param2 == 0 ? "开通" : "退订");
        messageKx.put("orderId", param3);
        requestKx.setMobiles(mobileKxList);
        requestKx.setMessage(messageKx);
        requestKx.setTemplateId(smsSoftServiceTemplateId);
        BaseAnswer<Void> messageKxAnswer = smsFeignClient.asySendMessage(requestKx);
        return messageKxAnswer;
    }

    private Boolean addSoftService(List<ServiceOpenInfo> atomOpenInfos,
                                   OrderInfoDTO orderInfo, SpuOfferingInfoDTO spuOfferingInfo, AtomOfferingInfoDTO atomInfoDto,
                                   AtomOfferingInfo resultAtom, List<Order2cAtomInfo> atomInfos, SkuOfferingInfoDTO skuInfoDto,
                                   Order2cInfo order2cInfo, String atomOrderId) {
        // 添加软件服务开通信息,根据开通信息，判断原子订单软件服务状态
        Boolean isSoftService = false;
        List<String> spuList = Arrays.asList("A13", "A14");
        if (spuList.contains(spuOfferingInfo.getOfferingClass())) {
            // 排除软件平台商品编码一样的原子，只开通一次
            try {
                String phone = spuOfferingInfo.getOfferingClass().equals("A13")
                        ? IOTEncodeUtils.decryptSM4(orderInfo.getServiceNumberInfo().get(0).getServiceNumber(),
                        iotSm4Key, iotSm4Iv)
                        : IOTEncodeUtils.decryptSM4(orderInfo.getContactInfo().getContactPhone(), iotSm4Key, iotSm4Iv);
                if (atomOpenInfos != null && atomOpenInfos.size() > 0) {
                    ServiceOpenInfo serviceOpenInfo = atomOpenInfos.get(0);

                    if (!serviceOpenInfo.getAtomOfferingCode().equals(atomInfoDto.getOfferingCode()) &&
                            ((serviceOpenInfo.getExtSoftOfferingCode().equals(resultAtom.getExtSoftOfferingCode())
                                    && spuOfferingInfo.getOfferingClass().equals("A13"))
                                    || (serviceOpenInfo.getExtSoftOfferingCode().equals(resultAtom.getChargeId())
                                    && spuOfferingInfo.getOfferingClass().equals("A14")))) {
                        // 判断第二个原子是不是无账目项原子，是则去除第一个原子软件服务(两个原子，一个是账目项，一个不是)

                        if (resultAtom.getChargeId() == null) {
                            atomInfos.get(0).setSoftServiceStatus(null);
                            atomOpenInfos.clear();
                            // 根据sku数量生成对应数量的流水号 如果是千里眼平台，则只生成一条，添加路数字段
                            String platform = judgePlatform(serviceOpenInfo.getSpuOfferingCode(),
                                    serviceOpenInfo.getSkuOfferingCode(), serviceOpenInfo.getAtomOfferingCode());
                            List<String> pList = new ArrayList<>();
                            pList.add(PlatformClassEnum.Q.getPlatformCode());
                            if (pList.contains(platform)) {
                                ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                                String id = BaseServiceUtils.getId();
                                serviceOpenInfo1.withId(id)
                                        .withOrderId(order2cInfo.getOrderId())
                                        .withAtomOrderId(atomOrderId)
                                        .withSpuOfferingCode(order2cInfo.getSpuOfferingCode())
                                        .withSkuOfferingCode(skuInfoDto.getOfferingCode())
                                        .withAtomOfferingCode(atomInfoDto.getOfferingCode())
                                        .withExtSoftOfferingCode(spuOfferingInfo.getOfferingClass().equals("A13")
                                                ? resultAtom.getExtSoftOfferingCode()
                                                : resultAtom.getChargeId())
                                        .withSoftServicePhone(phone)
                                        .withEquipmentCode(orderInfo.getServiceNumberInfo() == null ? null
                                                : orderInfo.getServiceNumberInfo().get(0).getEquipmentCode())
                                        .withSoftServiceOpenStatus(2)
                                        .withSyncIotFailStatus(0)
                                        .withOpenNum(skuInfoDto.getQuantity())
                                        .withOpenSucessNum(0L)
                                        .withRetailSucessNum(0L);
                                log.info("定时任务添加软件服务开通信息千里眼:" + serviceOpenInfo1);
                                atomOpenInfos.add(serviceOpenInfo1);
                            } else {
                                for (int i = 0; i < skuInfoDto.getQuantity(); i++) {
                                    ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                                    String id = BaseServiceUtils.getId();
                                    serviceOpenInfo1.withId(id)
                                            .withOrderId(order2cInfo.getOrderId())
                                            .withAtomOrderId(atomOrderId)
                                            .withSpuOfferingCode(order2cInfo.getSpuOfferingCode())
                                            .withSkuOfferingCode(skuInfoDto.getOfferingCode())
                                            .withAtomOfferingCode(atomInfoDto.getOfferingCode())
                                            .withExtSoftOfferingCode(spuOfferingInfo.getOfferingClass().equals("A13")
                                                    ? resultAtom.getExtSoftOfferingCode()
                                                    : resultAtom.getChargeId())
                                            .withSoftServicePhone(phone)
                                            .withEquipmentCode(orderInfo.getServiceNumberInfo() == null ? null
                                                    : orderInfo.getServiceNumberInfo().get(0).getEquipmentCode())
                                            .withSoftServiceOpenStatus(2)
                                            .withSyncIotFailStatus(0);
                                    log.info("定时任务添加软件服务开通信息:" + serviceOpenInfo1);
                                    atomOpenInfos.add(serviceOpenInfo1);
                                }
                            }

                            isSoftService = true;
                        }

                    } else {
                        // 根据sku数量生成对应数量的流水号 如果是千里眼平台，则只生成一条，添加路数字段
                        String platform = judgePlatform(serviceOpenInfo.getSpuOfferingCode(),
                                serviceOpenInfo.getSkuOfferingCode(), serviceOpenInfo.getAtomOfferingCode());
                        List<String> pList = new ArrayList<>();
                        pList.add(PlatformClassEnum.Q.getPlatformCode());
                        if (pList.contains(platform)) {
                            ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                            String id = BaseServiceUtils.getId();
                            serviceOpenInfo1.withId(id)
                                    .withOrderId(order2cInfo.getOrderId())
                                    .withAtomOrderId(atomOrderId)
                                    .withSpuOfferingCode(order2cInfo.getSpuOfferingCode())
                                    .withSkuOfferingCode(skuInfoDto.getOfferingCode())
                                    .withAtomOfferingCode(atomInfoDto.getOfferingCode())
                                    .withExtSoftOfferingCode(spuOfferingInfo.getOfferingClass().equals("A13")
                                            ? resultAtom.getExtSoftOfferingCode()
                                            : resultAtom.getChargeId())
                                    .withSoftServicePhone(phone)
                                    .withEquipmentCode(orderInfo.getServiceNumberInfo() == null ? null
                                            : orderInfo.getServiceNumberInfo().get(0).getEquipmentCode())
                                    .withSoftServiceOpenStatus(2)
                                    .withSyncIotFailStatus(0)
                                    .withOpenNum(skuInfoDto.getQuantity())
                                    .withOpenSucessNum(0L)
                                    .withRetailSucessNum(0L);
                            log.info("定时任务添加软件服务开通信息千里眼:" + serviceOpenInfo1);
                            atomOpenInfos.add(serviceOpenInfo1);
                        } else {
                            for (int i = 0; i < skuInfoDto.getQuantity(); i++) {
                                ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                                String id = BaseServiceUtils.getId();
                                serviceOpenInfo1.withId(id)
                                        .withOrderId(order2cInfo.getOrderId())
                                        .withAtomOrderId(atomOrderId)
                                        .withSpuOfferingCode(order2cInfo.getSpuOfferingCode())
                                        .withSkuOfferingCode(skuInfoDto.getOfferingCode())
                                        .withAtomOfferingCode(atomInfoDto.getOfferingCode())
                                        .withExtSoftOfferingCode(spuOfferingInfo.getOfferingClass().equals("A13")
                                                ? resultAtom.getExtSoftOfferingCode()
                                                : resultAtom.getChargeId())
                                        .withSoftServicePhone(phone)
                                        .withEquipmentCode(orderInfo.getServiceNumberInfo() == null ? null
                                                : orderInfo.getServiceNumberInfo().get(0).getEquipmentCode())
                                        .withSoftServiceOpenStatus(2)
                                        .withSyncIotFailStatus(0);
                                log.info("定时任务添加软件服务开通信息:" + serviceOpenInfo1);
                                atomOpenInfos.add(serviceOpenInfo1);
                            }
                        }
                        isSoftService = true;
                    }

                } else {
                    // 根据sku数量生成对应数量的流水号 如果是千里眼平台，则只生成一条，添加路数字段
                    String platform = judgePlatform(order2cInfo.getSpuOfferingCode(), skuInfoDto.getOfferingCode(),
                            atomInfoDto.getOfferingCode());
                    List<String> pList = new ArrayList<>();
                    pList.add(PlatformClassEnum.Q.getPlatformCode());
                    if (pList.contains(platform)) {
                        ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                        String id = BaseServiceUtils.getId();
                        serviceOpenInfo1.withId(id)
                                .withOrderId(order2cInfo.getOrderId())
                                .withAtomOrderId(atomOrderId)
                                .withSpuOfferingCode(order2cInfo.getSpuOfferingCode())
                                .withSkuOfferingCode(skuInfoDto.getOfferingCode())
                                .withAtomOfferingCode(atomInfoDto.getOfferingCode())
                                .withExtSoftOfferingCode(spuOfferingInfo.getOfferingClass().equals("A13")
                                        ? resultAtom.getExtSoftOfferingCode()
                                        : resultAtom.getChargeId())
                                .withSoftServicePhone(phone)
                                .withEquipmentCode(orderInfo.getServiceNumberInfo() == null ? null
                                        : orderInfo.getServiceNumberInfo().get(0).getEquipmentCode())
                                .withSoftServiceOpenStatus(2)
                                .withSyncIotFailStatus(0)
                                .withOpenNum(skuInfoDto.getQuantity())
                                .withOpenSucessNum(0L)
                                .withRetailSucessNum(0L);
                        log.info("定时任务添加软件服务开通信息千里眼:" + serviceOpenInfo1);
                        atomOpenInfos.add(serviceOpenInfo1);
                    } else {
                        for (int i = 0; i < skuInfoDto.getQuantity(); i++) {
                            ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                            String id = BaseServiceUtils.getId();
                            serviceOpenInfo1.withId(id)
                                    .withOrderId(order2cInfo.getOrderId())
                                    .withAtomOrderId(atomOrderId)
                                    .withSpuOfferingCode(order2cInfo.getSpuOfferingCode())
                                    .withSkuOfferingCode(skuInfoDto.getOfferingCode())
                                    .withAtomOfferingCode(atomInfoDto.getOfferingCode())
                                    .withExtSoftOfferingCode(spuOfferingInfo.getOfferingClass().equals("A13")
                                            ? resultAtom.getExtSoftOfferingCode()
                                            : resultAtom.getChargeId())
                                    .withSoftServicePhone(phone)
                                    .withEquipmentCode(orderInfo.getServiceNumberInfo() == null ? null
                                            : orderInfo.getServiceNumberInfo().get(0).getEquipmentCode())
                                    .withSoftServiceOpenStatus(2)
                                    .withSyncIotFailStatus(0);
                            log.info("定时任务添加软件服务开通信息:" + serviceOpenInfo1);
                            atomOpenInfos.add(serviceOpenInfo1);
                        }
                    }
                    isSoftService = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException(FAILED_GENERATE_SOFTWARE_SERVICES.getStateCode(), FAILED_GENERATE_SOFTWARE_SERVICES.getMessage());

            }
        }

        if (isSoftService) {
            return true;
        }
        return false;
    }

    public void processAtomicInformationOrder(Order2cInfo order2cInfo) {
        String orderId = order2cInfo.getOrderId();
        List<Order2cAtomInfo> order2cAtomInfoSelect = order2cAtomInfoMapper.selectByExample(
                new Order2cAtomInfoExample().createCriteria().andOrderIdEqualTo(orderId).example());
        List<String> atomCodeList = new ArrayList<>();
        //定时任务 软件服务开通信息在订单同步时已经入库 查询数据
        List<ServiceOpenInfo> atomOpenInfos = serviceOpenInfoMapper.selectByExample(new ServiceOpenInfoExample().createCriteria().andOrderIdEqualTo(orderId).example());
        if (order2cAtomInfoSelect != null && order2cAtomInfoSelect.size() > 0) {
            for (Order2cAtomInfo o : order2cAtomInfoSelect) {
                AtomOfferingInfoDTO atomInfoDto = new AtomOfferingInfoDTO();
                atomInfoDto.setOfferingCode(o.getAtomOfferingCode());
                SkuOfferingInfoDTO skuInfoDto = new SkuOfferingInfoDTO();
                skuInfoDto.setOfferingCode(o.getSkuOfferingCode());
                skuInfoDto.setQuantity(o.getSkuQuantity());
                List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper
                        .selectByExample(new AtomOfferingInfoExample().createCriteria()
                                .andSpuCodeEqualTo(o.getSpuOfferingCode())
                                .andSkuCodeEqualTo(o.getSkuOfferingCode())
                                .andOfferingCodeEqualTo(o.getAtomOfferingCode())
                                .example());

                if (CollectionUtil.isNotEmpty(atomOfferingInfoList)) {
                    AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
                    atomCodeList.add(atomOfferingInfo.getOfferingCode());
                } else {
                    log.error("原子订单信息异常,找不到对应原子，原子订单ID:{}", orderId);
                }
            }
            if (atomOpenInfos.size() > 0) {
                executor.execute(() -> {
                    try {
                        // 延迟2s开通，防止数据未写入数据库，业务平台就反馈
                        Thread.sleep(2000);
                        softServiceOpenTask(atomOpenInfos, atomCodeList,order2cInfo);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }else {
                log.error("未查询到订单软件服务基础信息，原子订单ID:{}", orderId);
            }
        }
    }


}
