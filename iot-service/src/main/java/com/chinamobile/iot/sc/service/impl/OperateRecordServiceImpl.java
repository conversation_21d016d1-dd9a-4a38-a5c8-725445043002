package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.dao.OperateRecordMapper;
import com.chinamobile.iot.sc.entity.iot.CreateOperateRecordParam;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.RoleEnum;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.OperateRecordDTO;
import com.chinamobile.iot.sc.pojo.entity.OperateRecord;
import com.chinamobile.iot.sc.pojo.entity.OperateRecordExample;
import com.chinamobile.iot.sc.pojo.param.OperateRecordQueryParam;
import com.chinamobile.iot.sc.service.IOperateRecordService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 日志管理service
 */
@Service
@Slf4j
public class OperateRecordServiceImpl implements IOperateRecordService {

    @Resource
    private OperateRecordMapper operateRecordMapper;

    @Override
    public BaseAnswer<PageData<OperateRecordDTO>> getLogList(OperateRecordQueryParam param) {
        PageData<OperateRecordDTO> pageData = new PageData<>();
        List<OperateRecordDTO> data = null;
        OperateRecordExample example = new OperateRecordExample();
        example.setOrderByClause("time DESC");
        OperateRecordExample.Criteria criteria = example.createCriteria();

        if (param.getBegin() != null && param.getEnd() != null) {
            criteria.andTimeBetween(new Date(param.getBegin()), new Date(param.getEnd()));
        } else if (param.getBegin() != null && param.getEnd() == null) {
            criteria.andTimeGreaterThanOrEqualTo(new Date(param.getBegin()));
        } else if (param.getBegin() == null && param.getEnd() != null) {
            criteria.andTimeLessThanOrEqualTo(new Date(param.getEnd()));
        }

        if (param.getModule() != null) {
            criteria.andModuleEqualTo(param.getModule());
        }

        if (param.getSubModule() != null) {
            criteria.andSubModuleEqualTo(param.getSubModule());
        }

        if (StringUtils.isNotEmpty(param.getOperatorAccount())) {
            criteria.andOperatorAccountEqualTo(param.getOperatorAccount());
        }

        if (StringUtils.isNotEmpty(param.getOperatorName())) {
            criteria.andOperatorNameEqualTo(param.getOperatorName());
        }

        if (StringUtils.isNotEmpty(param.getContent())) {
            criteria.andContentLike("%" + param.getContent() + "%");
        }
       if(param.getResult() != null){
           criteria.andResultEqualTo(param.getResult());
       }
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        //解决content查不出来
        List<OperateRecord> operateRecords = operateRecordMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isNotEmpty(operateRecords)) {
            PageInfo<OperateRecord> pageInfo = new PageInfo<>(operateRecords);
            data = operateRecords.stream().map(operateRecord -> {
                OperateRecordDTO dto = new OperateRecordDTO();
                BeanUtils.copyProperties(operateRecord,dto);
                if(operateRecord.getRole() != null){
                    RoleEnum roleEnum = RoleEnum.fromCode(operateRecord.getRole());
                    if (roleEnum != null && StringUtils.isEmpty(operateRecord.getRoleName())) {
                        dto.setRoleStr(roleEnum.name);
                    }
                }
                if(StringUtils.isNotEmpty(operateRecord.getRoleName())){
                    dto.setRoleStr(operateRecord.getRoleName());
                }
                dto.setModulesStr(ModuleEnum.moduleName(operateRecord.getModule()));
                dto.setSubModuleStr(ModuleEnum.subModuleName(operateRecord.getModule(), operateRecord.getSubModule()));
                Integer dataFrom = operateRecord.getDataFrom();
                operateRecord.setDataFrom(dataFrom);
                if (dataFrom == 0){
                    dto.setDataFromStr("PC端");
                }else if (dataFrom == 1){
                    dto.setDataFromStr("H5端");
                } else if (dataFrom == 2){
                    dto.setDataFromStr("小程序端");
                }
                return dto;
            }).collect(Collectors.toList());

            pageData.setPage(param.getPageNum());
            pageData.setCount(pageInfo.getTotal());
            pageData.setData(data);
        }


        return BaseAnswer.success(pageData);
    }

    @Override
    public void createLog(CreateOperateRecordParam param) {
        log.info("操作日志记录入库：{}", JSON.toJSONString(param));
        OperateRecord operateRecord = new OperateRecord();
        BeanUtils.copyProperties(param,operateRecord);
        operateRecord.setId(BaseServiceUtils.getId());
        operateRecord.setDeleted(0);
        operateRecordMapper.insertSelective(operateRecord);
    }

    /**
     * 每天凌晨0点清除超过180天的日志
     *  */
    @Scheduled(cron = "0 0 0 * * ? ")
    public void cleanTimeoutLog(){
        OperateRecordExample example = new OperateRecordExample();
        OperateRecordExample.Criteria criteria = example.createCriteria();
        criteria.andTimeLessThan(DateUtils.addDay(-180));
        OperateRecord operateRecord = new OperateRecord();
        operateRecord.setDeleted(1);
        operateRecordMapper.updateByExampleSelective(operateRecord,example);
    }
}
