package com.chinamobile.iot.sc.constant;

import lombok.Data;

/**
 * created by liuxiang on 2022/7/21 16:35
 */

public enum BusinessCodeEnum {
    SyncGrpOrderInfo("集团订单同步"),
    SyncIndividualOrderInfo("个人客户订单同步"),
    SyncValetOrderInfo ("代客下单订单同步")
    ;

    private String chnName;

    BusinessCodeEnum(String chnName) {
        this.chnName = chnName;
    }

    public String getChnName(){
        return chnName;
    }

    public static String getChnName(String name){
        BusinessCodeEnum[] values = BusinessCodeEnum.values();
        for (BusinessCodeEnum value : values) {
            if(value.name().equals(name)){
                return value.chnName;
            }
        }
        return null;
    }
}
