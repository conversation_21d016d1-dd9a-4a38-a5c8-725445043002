package com.chinamobile.iot.sc.constant.productflow;

import org.apache.commons.lang3.StringUtils;

public enum ProductStandardEnum {

    STANDARD(1,"标准类"),
    PLAN(2,"方案类"),
    ;


    public Integer code;
    public String name;

    ProductStandardEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean contains(Integer productStandard) {
        if(productStandard == null){
            return false;
        }
        ProductStandardEnum[] values = ProductStandardEnum.values();
        for (ProductStandardEnum value : values) {
            if(value.code.intValue() == productStandard.intValue()){
                return true;
            }
        }
        return false;
    }

    public static boolean containsName(String name) {
        if(StringUtils.isEmpty(name)){
            return false;
        }
        ProductStandardEnum[] values = ProductStandardEnum.values();
        for (ProductStandardEnum value : values) {
            if(value.name.equals(name)){
                return true;
            }
        }
        return false;
    }

    public static String getName(Integer code) {
        for (ProductStandardEnum value : ProductStandardEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public static Integer getCode(String name) {
        for (ProductStandardEnum value : ProductStandardEnum.values()) {
            if (value.name.equals(name)) {
                return value.code;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
