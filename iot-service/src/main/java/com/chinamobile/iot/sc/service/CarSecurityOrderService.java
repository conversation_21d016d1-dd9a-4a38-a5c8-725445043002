package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.CarSecurityOrder;
import com.chinamobile.iot.sc.pojo.CarSecurityOrderExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/3
 * @description 行车卫士订单关联service接口类
 */
public interface CarSecurityOrderService {

    /**
     * 新增行车卫士订单关联数据
     * @param carSecurityOrder
     */
    void addCarSecurityOrder(CarSecurityOrder carSecurityOrder);

    /**
     * 根据需要查询数据
     * @param carSecurityOrderExample
     * @return
     */
    List<CarSecurityOrder> getCarSecurityOrderByExample(CarSecurityOrderExample carSecurityOrderExample);

    /**
     * 根据需要更新行车卫士订单数据
     * @param carSecurityOrder
     * @param carSecurityOrderExample
     */
    void updateCarSecurityOrderByExample(CarSecurityOrder carSecurityOrder,CarSecurityOrderExample carSecurityOrderExample);

}
