package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2022/7/11 14:56
 */
@Slf4j
public class BatchDeliverExcelListener extends AnalysisEventListener<BatchDeliver> {

    private List<BatchDeliver> list = new ArrayList<>();

    /**
     * 正文起始行
     */
    private Integer headRowNumber;

    /**
     * 合并单元格
     */
    private List<CellExtra> extraMergeInfoList = new ArrayList<>();

    public BatchDeliverExcelListener(Integer headRowNumber){
        this.headRowNumber = headRowNumber;
    }


    @Override
    public void invoke(BatchDeliver batchDeliver, AnalysisContext analysisContext) {
        log.info("invoke Enter");

        int currentRow = analysisContext.readRowHolder().getRowIndex() + 1;
        log.info("解析到一条数据:{},当前行:{}", batchDeliver, currentRow);
        list.add(batchDeliver);
//        Map<String, List<String>> orderMap = new HashMap<>();
//        List<String> logisticList = new ArrayList<>();
//
//        logisticList.add()
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完成！");
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        super.extra(extra, context);
        log.info("读取到了一条额外信息：{}",JSON.toJSONString(extra));
        log.info("extraType = {}",extra.getType());
        switch(extra.getType()){
            case COMMENT:
                log.info("额外信息是批注，在rowIndex:{},columnIndex：{}，内容是 {}",extra.getRowIndex(),extra.getColumnIndex(),extra.getText());
                break;
            case HYPERLINK:
                log.info("额外信息是超链接，在rowIndex:{},columnIndex：{}，内容是{}",extra.getRowIndex(),extra.getColumnIndex(),extra.getText());
                break;
            case MERGE:
                log.info("额外信息是合并单元格，而且覆盖了一个区间，在firstRowIndex: {},firstColumnIndex: {}, lastRowIndex: {} ，lastColumnIndex :{}"
                        ,extra.getFirstRowIndex(),extra.getFirstColumnIndex(),extra.getLastRowIndex(), extra.getLastColumnIndex());
                if(extra.getRowIndex()>=headRowNumber){
                    extraMergeInfoList.add(extra);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
    }


    public List<BatchDeliver> getData(){
        return list;
    }

    public List<CellExtra> getExtraMergeInfoList(){
        return extraMergeInfoList;
    }




//    private void explainMergeData(Map<Integer, T> data, List<CellExtra> extraMergeInfoList){
//        //循环所有合并单元格信息
//        extraMergeInfoList.forEach(new Consumer<CellExtra>() {
//            @Override
//            public void accept(CellExtra cellExtra) {
//                int firstRowIndex = cellExtra.getFirstRowIndex();
//                int lastRowIndex = cellExtra.getLastRowIndex();
//                T first = data.get(firstRowIndex);
//                //把剩下几个填充进去
//                for(int i = firstRowIndex + 1;i<=lastRowIndex; i++){
//                    T value = data.get(i);
//                }
//            }
//        });
//    }

}
