package com.chinamobile.iot.sc.constant;

public enum ProvinceCodeEnum {
    Beijing("北京", "Beijing"),
    Shanghai("上海", "Shanghai"),
    Tianjin("天津", "Tianjin"),
    Chongqing("重庆", "Chongqing"),
    Henan("河南省", "Henan"),
    Hebei("河北省", "Hebei"),
    Qinghai("青海省", "Qinghai"),
    Anhui("安徽省", "Anhui"),
    Fujian("福建省", "Fujian"),
    Guangdong("广东省", "Guangdong"),
    Guangxi("广西壮族自治区", "Guangxi"),
    Guizhou("贵州省", "Guizhou"),
    Gansu("甘肃省", "Gansu"),
    Hainan("海南省", "Hainan"),
    Heilongjiang("黑龙江省", "Heilongjiang"),
    Hubei("湖北省", "Hubei"),
    Hunan("湖南省", "Hunan"),
    Jilin("吉林省", "Jilin"),
    Jiangsu("江苏省", "Jiangsu"),
    Jiangxi("江西省", "Jiangxi"),
    Liaoning("辽宁省", "Liaoning"),
    Neimenggu("内蒙古自治区", "Neimenggu"),
    Ningxia("宁夏回族自治区", "Ningxia"),
    Shanxi1("陕西省", "Shanxi1"),
    Shanxi2("山西省", "Shanxi2"),
    Shandong("山东省", "Shandong"),
    Sichuan("四川省", "Sichuan"),
    Xizang("西藏自治区", "Xizang"),
    Xinjiang("新疆维吾尔自治区", "Xinjiang"),
    Yunnan("云南省", "Yunnan"),
    Zhejang("浙江省", "Zhejang");

    /**
     * 省名字
     */
    private final String provinceName;
    /**
     * 省代码
     */
    private final String provinceCode;

    ProvinceCodeEnum(String provinceName, String provinceCode) {
        this.provinceName = provinceName;
        this.provinceCode = provinceCode;
    }

    public static String getProvinceCode(String provinceName) {
        for (ProvinceCodeEnum value : ProvinceCodeEnum.values()) {
            if (value.provinceName.equals(provinceName)) {
                return value.provinceCode;
            }
        }
        return "UnKnown";
    }

}
