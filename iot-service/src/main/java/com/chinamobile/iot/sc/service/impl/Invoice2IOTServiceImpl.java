package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.constant.InvoiceConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceInfo;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceRec;
import com.chinamobile.iot.sc.pojo.invoice.InvoiceReverseRec;
import com.chinamobile.iot.sc.request.invoice.IotInvoiceBackRequest;
import com.chinamobile.iot.sc.request.invoice.IotRevInovBackRequest;
import com.chinamobile.iot.sc.service.IInvoice2IOTService;
import com.chinamobile.iot.sc.util.IOTRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.chinamobile.iot.sc.exception.StatusConstant.REVERSE_INFO_REC_NULL;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: Invoice2IOTServiceImpl
 * @description: 发票同步IOT商城实现Service
 * @author: zyj
 * @create: 2021/12/8 17:50
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class Invoice2IOTServiceImpl implements IInvoice2IOTService {

    @Value("${iot.invoicingResult2IoTMallUrl}")
    private String invoicingResult2IoTMallUrl;
    @Value("${iot.invoiceVoidCallbackUrl}")
    private String invoiceVoidCallbackUrl;
    @Value("${iot.secretKey}")
    private String secretKey;
    @Resource
    private ApplyInvoiceInfoMapper invoiceInfoMapper;
    @Resource
    private ApplyInvoiceRecMapper invoiceRecMapper;
    @Resource
    private InvoiceReverseRecMapper reverseRecMapper;
    @Resource
    Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    /**
     * @param orderId: 业务订单号
     * @Description: 校验业务订单下所有子订单开票状态
     * @return: java.lang.Boolean
     * @Author: zyj
     */
    @Override
    public Boolean isAllInvoiceSuccByOrderId(String orderId) {
        List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                .eq(ApplyInvoiceRec::getOrderId, orderId)
                .notIn(ApplyInvoiceRec::getStatus, InvoiceConstant.STATUS_INVOICE_SUCC
                        , InvoiceConstant.STATUS_INVOICE_REVERSE, InvoiceConstant.STATUS_INVOICE_REVERSE_ENTRY
                        , InvoiceConstant.STATUS_INVOICE_REVERSE_SUCC));
        return ObjectUtils.isEmpty(applyInvoiceRecs);
    }

    @Override
//    @Transactional(propagation = Propagation.NESTED)
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> invoicingResult2IOTMall(ApplyInvoiceRec applyInvoiceRec, String result){
        //初始化IOT请求
        IotInvoiceBackRequest iotInvoiceBackRequest = new IotInvoiceBackRequest();
        iotInvoiceBackRequest.setOrderSeq(applyInvoiceRec.getOrderSeq());
        iotInvoiceBackRequest.setResult(result);
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(applyInvoiceRec.getOrderId());
        //开票成功反馈
        if(InvoiceConstant.IOT_RESULT_INVOICE_SUCC.equals(result)){
            // 4.5.1 校验业务订单id下是否所有原子订单的状态为已开票
            if(!isAllInvoiceSuccByOrderId(applyInvoiceRec.getOrderId())){
                // 4.5.2 封装反馈发票接口中的所有发票信息
                List<ApplyInvoiceInfo> applyInvoiceInfos = invoiceInfoMapper.selectList(new QueryWrapper<ApplyInvoiceInfo>().lambda()
                        .eq(ApplyInvoiceInfo::getOrderId, applyInvoiceRec.getOrderId())
                        .eq(ApplyInvoiceInfo::getOrderSeq, applyInvoiceRec.getOrderSeq()));
                List<IotInvoiceBackRequest.PrintInfo> printInfos = new ArrayList<>();
                for(ApplyInvoiceInfo invoiceInfo : applyInvoiceInfos){
                    IotInvoiceBackRequest.PrintInfo printInfo = new IotInvoiceBackRequest.PrintInfo();
                    // A13的传固定值
                    if(order2cInfo.getSpuOfferingClass().equals(SPUOfferingClassEnum.A13.getSpuOfferingClass())){
                        printInfo.setVoucherID("100000000000");
                        printInfo.setVoucherNum("30000000");
                    }else{
                        printInfo.setVoucherID(invoiceInfo.getVoucherId());
                        printInfo.setVoucherNum(invoiceInfo.getVoucherNum());
                    }
                    printInfo.setVoucherSum(invoiceInfo.getVoucherSum()+"");
                    printInfo.setVoucherFile(invoiceInfo.getVoucherFile());
                    printInfos.add(printInfo);
                }
                iotInvoiceBackRequest.setPrintInfo(printInfos);
            }
        }else if(InvoiceConstant.IOT_RESULT_INVOICE_FAIL.equals(result)){

        }
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");
        String regionId = order2cAtomInfoMapper.selectByPrimaryKey(applyInvoiceRec.getAtomOrderId()).getRegionId();
        // 4.5.3 请求IOT商城-电子发票开具结果反馈接口
        //封装对应的结构
        String iotRequest = IOTRequestUtils.getIotRequest(JSON.toJSONString(iotInvoiceBackRequest), secretKey, applyInvoiceRec.getBeId(), regionId);
        log.info("发票反馈接口，请求IOT商城内容为:" + iotRequest);
        HttpEntity<String> requestEntity = new HttpEntity<>(iotRequest, headers);
        ResponseEntity<IOTAnswer> response;
        try {
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            response = restTemplateHttps.postForEntity(invoicingResult2IoTMallUrl, requestEntity, IOTAnswer.class);
//            response = restTemplateHttps.postForEntity(invoicingResult2IoTMallUrl, iotRequest, IOTAnswer.class);
        } catch (Exception e) {
            log.error("发票反馈接口请求IOT商城出错",e);
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED);
        }
        IOTAnswer iotAnswer = response.getBody();
        log.info("收到发票反馈响应:{}",JSON.toJSONString(iotAnswer));
        if (iotAnswer == null || !"0".equals(iotAnswer.getResultCode())) {
            //没返回信息则为提示同步失败，返回错误码则提示返回错误描述
            log.error("发票反馈到IOT商城失败，返回结果为:{}", iotAnswer);
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED, iotAnswer == null ? "同步到IOT商城失败，请重试" : iotAnswer.getResultDesc());
        }else{
            log.info("发票反馈到IOT商城成功！");
            //发票反馈到IOT商城成功
            if(InvoiceConstant.IOT_RESULT_INVOICE_SUCC.equals(result)){
                //将申请开票-原子订单记录状态变更为已开票
                applyInvoiceRec.setStatus(InvoiceConstant.STATUS_INVOICE_SUCC);
                //软件服务只有A类去开票了 直接改对应结算状态就行
                if(order2cInfo.getSpuOfferingClass().equals(SPUOfferingClassEnum.A13.getSpuOfferingClass())){
                    Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                    //结算状态，0-销售草稿（锁定），1-销售草稿， 2-销售审批中， 3-销售已审批， 4-计收完成， 5-订单取消
                    order2cAtomInfo.setSettleStatus(4);
                    order2cAtomInfoMapper.updateByExampleSelective(order2cAtomInfo, new Order2cAtomInfoExample().createCriteria()
                            .andOrderIdEqualTo(order2cInfo.getOrderId())
                            .andAtomOfferingClassEqualTo("A")
                            .example());
                }

            }else if(InvoiceConstant.IOT_RESULT_INVOICE_FAIL.equals(result)){
                applyInvoiceRec.setStatus(InvoiceConstant.STATUS_INVOICE_FAIL);
            }
            applyInvoiceRec.setUpdateTime(new Date());
            invoiceRecMapper.updateById(applyInvoiceRec);
        }

        return new BaseAnswer<>();
    }

    /**
     * 发票冲红反馈至IOT商城
     *
     * @param request
     * @param result
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> invoiceVoidCallback(IotRevInovBackRequest request, String result, String regionId) {
        String orderSeq = request.getOrderSeq();
        List<InvoiceReverseRec> invoiceReverseRecs = reverseRecMapper.selectList(new QueryWrapper<InvoiceReverseRec>().lambda()
                .eq(InvoiceReverseRec::getOrderSeq, orderSeq));
        if (ObjectUtils.isEmpty(invoiceReverseRecs)) {
            throw new BusinessException(REVERSE_INFO_REC_NULL);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");
        InvoiceReverseRec invoiceReverseRec = invoiceReverseRecs.get(0);
        //封装对应的结构
        String iotRequest = IOTRequestUtils.getIotRequest(JSON.toJSONString(request), secretKey, request.getBeId(), regionId);
        log.info("冲红发票反馈接口，请求IOT商城内容为:" + iotRequest);
        HttpEntity<String> requestEntity = new HttpEntity<>(iotRequest, headers);
        ResponseEntity<IOTAnswer> response;
        try {
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            response = restTemplateHttps.postForEntity(invoiceVoidCallbackUrl, requestEntity, IOTAnswer.class);
//            response = restTemplateHttps.postForEntity(invoiceVoidCallbackUrl, iotRequest, IOTAnswer.class);
        } catch (Exception e) {
            log.error(e.toString());
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED);
        }
        IOTAnswer iotAnswer = response.getBody();
        if (iotAnswer == null || !"0".equals(iotAnswer.getResultCode())) {
            //没返回信息则为提示同步失败，返回错误码则提示返回错误描述
            log.error("冲红发票反馈到IOT商城失败，返回结果为:{}", iotAnswer);
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED, iotAnswer == null ? "同步到IOT商城失败，请重试" : iotAnswer.getResultDesc());
        }else{
            log.info("冲红发票反馈到IOT商城成功！");
            //发票反馈到IOT商城成功
            if(InvoiceConstant.IOT_RESULT_INVOICE_REV_SUCC.equals(result)){
                //将申请开票-原子订单记录状态变更为已开票
                invoiceReverseRec.setStatus(InvoiceConstant.STATUS_INVOICE_REVERSE_SUCC);
            }else if(InvoiceConstant.IOT_RESULT_INVOICE_REV_FAIL.equals(result)){
                invoiceReverseRec.setStatus(InvoiceConstant.STATUS_INVOICE_REVERSE_FAIL);
            }
            invoiceReverseRec.setUpdateTime(new Date());
            reverseRecMapper.updateById(invoiceReverseRec);
        }
        return new BaseAnswer<>();
    }
}
