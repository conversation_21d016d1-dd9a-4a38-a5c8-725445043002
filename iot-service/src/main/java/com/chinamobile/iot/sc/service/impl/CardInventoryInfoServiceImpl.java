package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.ServiceConfig;
import com.chinamobile.iot.sc.constant.NoticeTypeConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.CardInventoryMainInfoMapperExt;
import com.chinamobile.iot.sc.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.user.UserPartner;
import com.chinamobile.iot.sc.enums.log.GoodsManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfo;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.dto.CardInventoryInfoDTO;
import com.chinamobile.iot.sc.pojo.dto.InventoryAreaDTO;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.mapper.Order2cAtomInfoHistoryDO;
import com.chinamobile.iot.sc.pojo.param.CardInfoBYInventoryParam;
import com.chinamobile.iot.sc.pojo.param.CardInfoInventoryErrorParam;
import com.chinamobile.iot.sc.pojo.param.CardInfoParam;
import com.chinamobile.iot.sc.pojo.param.CardInventoryWarningParam;
import com.chinamobile.iot.sc.pojo.vo.CardInfoByInventoryVO;
import com.chinamobile.iot.sc.pojo.vo.CardInfoVO;
import com.chinamobile.iot.sc.pojo.vo.CardInventoryInfoVO;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import com.chinamobile.iot.sc.service.CardInventoryInfoService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.UserRefundKxService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.Constant.REDIS_CARD_INVENTORY_CONSTANT;
import static com.chinamobile.iot.sc.common.Constant.REDIS_COMMIT_INVENTORY_AREA;
import static com.chinamobile.iot.sc.constant.CardTypeEnum.NO_INCLUDED_CARD;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/12/9 9:59
 * @description: 码号库存实现类
 **/
@Service
@Slf4j
public class CardInventoryInfoServiceImpl implements CardInventoryInfoService {

    @Resource
    private Order2cAtomInfoMapperExt order2cAtomInfoMapperExt;

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private CardInventoryMainInfoMapper cardInventoryMainInfoMapper;

    @Resource
    private CardMallSyncMapper cardMallSyncMapper;

    @Resource
    private CardInventoryAtomInfoMapper cardInventoryAtomInfoMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private UserRefundKxService userRefundKxService;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Autowired
    private ServiceConfig serviceConfig;

    @Resource
    private LogService logService;

    /**
     * 库存不足短信模板
     */
    @Value("${sms.cardInventoryMainTemplateId:108185}")
    private String cardInventoryMainTemplateId;

    @Resource
    private CardInventoryMainInfoMapperExt cardInventoryMainInfoMapperExt;

    @Resource
    private DkcardxInventoryMainInfoMapper dkcardxInventoryMainInfoMapper;


    @Override
    public PageData<CardInventoryInfoVO> pageCardInventoryMainInfo(CardInfoParam cardInfoParam, LoginIfo4Redis loginIfo4Redis) {
        PageData<CardInventoryInfoVO> pageData = new PageData<>();
        Integer pageNum = cardInfoParam.getPageNum();
        Integer pageSize = cardInfoParam.getPageSize();
        handleCardInfoParam(cardInfoParam, loginIfo4Redis);
        Page<CardInventoryInfoVO> page = new Page<>(pageNum, pageSize);
        List<CardInventoryInfoVO> cardInventoryInfoVOS = cardInventoryMainInfoMapperExt.listCardInventoryMainInfo(page, cardInfoParam);

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(cardInventoryInfoVOS);
        return pageData;
    }

    @Override
    public PageData<CardInfoByInventoryVO> getCardInfoByInventoryId(CardInfoBYInventoryParam param) {
        PageData<CardInfoByInventoryVO> pageData = new PageData<>();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        String cardInventoryMainId = param.getCardInventoryMainId();
        CardInventoryMainInfo cardInventoryMainInfo = cardInventoryMainInfoMapper.selectByPrimaryKey(cardInventoryMainId);
        Page<CardInfoByInventoryVO> page = new Page<>(pageNum, pageSize);
        List<CardInfoByInventoryVO> cardInfoByInventoryVOS = cardInventoryMainInfoMapperExt.queryCardInfoByInventoryId(page, param);
        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(cardInfoByInventoryVOS);
        String content = "【查看码号库存明细】\n"
                .concat("开卡模板名称").concat(cardInventoryMainInfo.getTemplateName()).concat("\n")
                .concat("卡服务商EC名称").concat(cardInventoryMainInfo.getCustName());
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.MSISDN_MANAGE.code,
                content);
        return pageData;
    }

    @Override
    public void settingsCardInventoryForewarning(CardInventoryWarningParam param) {
        String id = param.getId();
        Integer inventoryThreshold = param.getInventoryThreshold();
        CardInventoryMainInfo cardInventoryMainInfo = cardInventoryMainInfoMapper.selectByPrimaryKey(id);
        Integer inventoryThresholdOld = cardInventoryMainInfo.getInventoryThreshold();
        Integer totalInventory = cardInventoryMainInfo.getTotalInventory();
        CardInventoryMainInfo cardInventoryMainInfoEn =new CardInventoryMainInfo();
        if (totalInventory<inventoryThreshold){
            //查询当前码号库存省份是否创建过省公司合作伙伴
            BaseAnswer<List<UserPartner>> provinceList = userFeignClient.getUserPartnerByProvinceList(cardInventoryMainInfo.getBeId());
            if (provinceList == null || !provinceList.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || provinceList.getData() == null) {
                throw new RuntimeException("调用获取省公司合作伙伴用户信息失败。从合作伙伴ID:");
            }
            List<UserPartner> data = provinceList.getData();
            if (CollectionUtils.isEmpty(data)){
                // TODO 是空的就发送给管理员，设置短信报警参数
                List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_CARD_INVENTORY_WARNING);
                if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                    //短信通知库存警报对应配置人员
                    List<String> kxPhoneList = userRefundKxVOList.stream().map(UserRefundKxVO::getPhone)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(kxPhoneList)) {
                        BaseAnswer<Void> voidBaseAnswer = sendCardInventoryCooperationSMS(kxPhoneList, cardInventoryMainInfo.getTemplateName(), cardInventoryMainInfo.getCustName());
                        log.info("码号总库存小于预警值 库存不足短信通知指定人员的短信发送结果:{}", JSON.toJSONString(voidBaseAnswer));
                    }
                }
            }else {
                List<String> collect = data.stream().map(UserPartner::getPhone).collect(Collectors.toList());
                List<String> phoneList = new ArrayList<>();
                // 电话号码解密
                collect.forEach(x->{
                    String encryptPhone = IOTEncodeUtils.decryptIOTMessage(x, serviceConfig.getEncodeKey());
                    phoneList.add(encryptPhone);
                });
                sendCardInventoryCooperationSMS(phoneList,cardInventoryMainInfo.getTemplateName(),cardInventoryMainInfo.getCustName());
            }
            cardInventoryMainInfoEn.setInventoryStatus("0");
        }else {
            cardInventoryMainInfoEn.setInventoryStatus("1");
        }
        //更新码号库存预警值
        cardInventoryMainInfoEn.setId(cardInventoryMainInfo.getId());
        cardInventoryMainInfoEn.setInventoryThreshold(inventoryThreshold);
        cardInventoryMainInfoEn.setUpdateTime(new Date());
        cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);

        String content = "【配置库存预警值】\n"
                .concat("开卡模板名称").concat(cardInventoryMainInfo.getTemplateName()).concat("\n")
                        .concat("卡服务商EC名称").concat(cardInventoryMainInfo.getCustName()).concat("\n")
                        .concat("库存预警值由").concat(String.valueOf(inventoryThresholdOld)).concat("配置为").concat(String.valueOf(inventoryThreshold));
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.MSISDN_MANAGE.code,
                content);

    }

    @Override
    public List<CardInventoryInfoVO> getAtomCardInfoInventoryList(String cardInventoryMainId, String atomId) {
        List<CardInventoryInfoVO> cardInventoryInfoVOS = cardInventoryMainInfoMapperExt.listAtomCardInventoryMainInfo(cardInventoryMainId, atomId);
        return cardInventoryInfoVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHistoryOrderCardInventory() {
        //sql条件 inventory_main_id 不为空能排除代客 卡+x的类型不是1,2，3的了,
        List<Order2cAtomInfoHistoryDO> handleHistoryOrderCard = order2cAtomInfoMapperExt.getHandleHistoryOrderCard();
        //TODO 通过订单redis预占信息 可以确定对应原子关联的码号库存预占？？？
        Integer count = 0;
        for (Order2cAtomInfoHistoryDO x : handleHistoryOrderCard) {
            log.info("处理码号预占的历史订单：cardInfoOrder:{}",x);
            String spuOfferingCode = x.getSpuOfferingCode();
            String skuOfferingCode = x.getSkuOfferingCode();
            String atomOfferingCode = x.getAtomOfferingCode();
            String orderId = x.getOrderId();
            String atomId = x.getAtomId();
            String inventoryMainId = x.getInventoryMainId();
            String cardInfoContent = stringRedisTemplate.opsForValue().get(REDIS_CARD_INVENTORY_CONSTANT + orderId + "_" + skuOfferingCode);
            if (cardInfoContent !=null ){
                log.info("cardInfoContent该订单已经预占了,删除重新来 初始 等下在改回来：cardInfoContent：{}，orderId:{}",cardInfoContent,orderId);
                //stringRedisTemplate.delete(REDIS_CARD_INVENTORY_CONSTANT + orderId + "_" + skuOfferingCode);
                continue;
            }
            //TODO 要判断终端类型是否为不含卡的 不含卡的没得码号预占（）
            String terminalType = x.getTerminalType();
            if (StringUtils.isEmpty(terminalType) || NO_INCLUDED_CARD.getType().equals(terminalType)){
                log.info("terminalType：终端类型为不含卡的商品订单或为空，不做码号库存预占");
            }else {
                //TODO 先把sku数据割接了来
                Optional<SkuOfferingInfo> skuOfferingInfoOptional = skuOfferingInfoMapper.selectByExample(new SkuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(skuOfferingCode).example()).stream().findFirst();
                if (skuOfferingInfoOptional.isPresent()){
                    SkuOfferingInfo skuOfferingInfo = skuOfferingInfoOptional.get();
                    String custCode = skuOfferingInfo.getCustCode();
                    String templateName = skuOfferingInfo.getTemplateName();
                    String templateId = skuOfferingInfo.getTemplateId();
                    if (StringUtils.isEmpty(templateId)){
                        log.info("inventoryMainInfoOptional：码号库存不存在custCode:{},templateId:{},skuCode:{},orderId:{}",custCode,templateId,skuOfferingInfo.getOfferingCode(),orderId);
                       continue;
                    }
                    Optional<CardInventoryMainInfo> inventoryMainInfoOptional = cardInventoryMainInfoMapper.selectByExample(new CardInventoryMainInfoExample().createCriteria().andCustCodeEqualTo(custCode)
                            .andTemplateIdEqualTo(templateId).example()).stream().findFirst();
                    if (!inventoryMainInfoOptional.isPresent()){
                        log.info("inventoryMainInfoOptional：码号库存不存在custCode:{},templateId:{},skuCode:{},orderId:{}",custCode,templateId,skuOfferingInfo.getOfferingCode(),orderId);
                        continue;
                        //throw new BusinessException("10004", "inventoryMainInfoOptional：码号库存不存在"+custCode+"_"+templateId+"_"+skuOfferingInfo.getOfferingCode()+orderId);
                    }
                    CardInventoryMainInfo cardInventoryMainInfo = inventoryMainInfoOptional.get();
                    //获取订单redis的预占数据
                    String contentX = stringRedisTemplate.opsForValue().get(REDIS_COMMIT_INVENTORY_AREA + orderId + "_" + atomId);
                    log.info("redis中的预占卡+x省市信息:{}", contentX);
                    if (contentX == null) {
                        log.info("未查询到RedisContentX中该订单流水号,x终端库存数:{}", orderId);
                        //throw new BusinessException("10004", "contentX该笔订单:" + orderId + "终端库存数未查询");
                      continue;
                    }
                    //获取redis保存的x终端省份地市信息
                    List<InventoryAreaDTO> inventoryAreaList = JSON.parseArray(contentX, InventoryAreaDTO.class);
                    long reserveQuantity = inventoryAreaList.stream().mapToLong(InventoryAreaDTO::getReserveQuantity).sum();
                    Integer historyQuantity = Integer.parseInt(String.valueOf(reserveQuantity));
                    Integer totalInventory = cardInventoryMainInfo.getTotalInventory();
                    Integer currentInventory = cardInventoryMainInfo.getCurrentInventory();
                    Integer reserveQuatity = cardInventoryMainInfo.getReserveQuatity();
                    CardInventoryMainInfo cardInventoryMainInfoEn = new CardInventoryMainInfo();
                    cardInventoryMainInfoEn.setId(cardInventoryMainInfo.getId());
                    //cardInventoryMainInfoEn.setTotalInventory(totalInventory -historyQuantity);
                    cardInventoryMainInfoEn.setCurrentInventory(currentInventory -historyQuantity);
                    cardInventoryMainInfoEn.setReserveQuatity(reserveQuatity+historyQuantity);
                    cardInventoryMainInfoEn.setUpdateTime(new Date());
                    cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);
                    //设置历史订单原子商品与码号库存关联  先查询是否存在
                    Optional<CardInventoryAtomInfo> inventoryAtomInfo = cardInventoryAtomInfoMapper.selectByExample(new CardInventoryAtomInfoExample().createCriteria()
                            .andCardInventoryMainIdEqualTo(cardInventoryMainInfo.getId()).andAtomIdEqualTo(atomId).example()).stream().findFirst();
                    if (!inventoryAtomInfo.isPresent()){
                        CardInventoryAtomInfo cardInventoryAtomInfo = new CardInventoryAtomInfo();
                        cardInventoryAtomInfo.setId(BaseServiceUtils.getId());
                        cardInventoryAtomInfo.setCardInventoryMainId(cardInventoryMainInfo.getId());
                        cardInventoryAtomInfo.setAtomId(atomId);
                        cardInventoryAtomInfo.setAtomInventory(historyQuantity);
                        cardInventoryAtomInfo.setSpuCode(spuOfferingCode);
                        cardInventoryAtomInfo.setSkuCode(skuOfferingCode);
                        cardInventoryAtomInfo.setOfferingCode(atomOfferingCode);
                        cardInventoryAtomInfo.setCreateTime(new Date());
                        cardInventoryAtomInfo.setUpdateTime(new Date());
                        cardInventoryAtomInfoMapper.insert(cardInventoryAtomInfo);
                        //原子商品添加码号库存主键id
                        //AtomOfferingInfo atomOfferingInfo = atomOfferingInfoMapper.selectByPrimaryKey(atomId);
                        AtomOfferingInfo atomOfferingInfoEn = new AtomOfferingInfo();
                        atomOfferingInfoEn.setId(atomId);
                        atomOfferingInfoEn.setCardInfoInventoryMainId(cardInventoryMainInfo.getId());
                        atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfoEn);
                    }else {
                        CardInventoryAtomInfo cardInventoryAtomInfoEn = inventoryAtomInfo.get();
                        CardInventoryAtomInfo cardInventoryAtomInfo = new CardInventoryAtomInfo();
                        cardInventoryAtomInfo.setId(cardInventoryAtomInfoEn.getId());
                        cardInventoryAtomInfo.setAtomInventory(cardInventoryAtomInfoEn.getAtomInventory()+historyQuantity);
                        cardInventoryAtomInfo.setUpdateTime(new Date());
                        cardInventoryAtomInfoMapper.updateByPrimaryKeySelective(cardInventoryAtomInfo);
                        AtomOfferingInfo atomOfferingInfoEn = new AtomOfferingInfo();
                        atomOfferingInfoEn.setId(atomId);
                        atomOfferingInfoEn.setCardInfoInventoryMainId(cardInventoryMainInfo.getId());
                        atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfoEn);
                    }

                    //TODO 设置保存redis码号库存 组装redis要保存数据
                    List<CardInventoryInfoDTO> cardInventoryInfoDTOS = new ArrayList<>();
                    CardInventoryInfoDTO cardInventoryInfoDTO = new CardInventoryInfoDTO();
                    cardInventoryInfoDTO.setId(cardInventoryMainInfo.getId());
                    cardInventoryInfoDTO.setAtomId(atomId);
                    cardInventoryInfoDTO.setCustCode(cardInventoryMainInfo.getCustCode());
                    cardInventoryInfoDTO.setTemplateId(cardInventoryMainInfo.getTemplateId());
                    cardInventoryInfoDTO.setReserveQuatity(historyQuantity);
                    cardInventoryInfoDTOS.add(cardInventoryInfoDTO);
                    stringRedisTemplate.opsForValue().set(REDIS_CARD_INVENTORY_CONSTANT+orderId+"_"+skuOfferingCode,JSON.toJSONString(cardInventoryInfoDTOS));
                    log.info("预占成功的订单数据cardInfoOrder：orderId:{},cardInventoryInfoDTOS:{}",orderId,cardInventoryInfoDTOS);
                }else {
                    throw new BusinessException("10004", "skuOfferingInfoOptional订单sku商品不存在");
                }
                count ++;
            }

        }
        log.info("历史订单成功预占码号库存数：cardCount:{}",count);

    }

    @Override
    public void handleErrorCardInventoryMainInfo(CardInfoInventoryErrorParam param) {
        String id = param.getId();
        String errorLabel = param.getErrorLabel();
        if ("1".equals(errorLabel)){
          //修改码号库存
            Integer reserveQuatity = param.getReserveQuatity();
            Integer currentInventory = param.getCurrentInventory();
            Integer totalInventory = param.getTotalInventory();
            CardInventoryMainInfo cardInventoryMainInfo = new CardInventoryMainInfo();
            cardInventoryMainInfo.setId(id);
            cardInventoryMainInfo.setReserveQuatity(reserveQuatity);
            cardInventoryMainInfo.setCurrentInventory(currentInventory);
            cardInventoryMainInfo.setTotalInventory(totalInventory);
            cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfo);

        }else if ("2".equals(errorLabel)){
            //删除重复的  并修改同步的商城同步的卡信息
            cardInventoryMainInfoMapper.deleteByPrimaryKey(id);
        }else {
            String errorId = param.getErrorId();
            //修改同步的号卡 关联的库存信息id
            List<CardMallSync> cardMallSyncs = cardMallSyncMapper.selectByExample(new CardMallSyncExample().createCriteria().andCardInventoryMainIdEqualTo(errorId).example());
            for (CardMallSync cardMallSync : cardMallSyncs) {
                cardMallSync.setCardInventoryMainId(id);
                cardMallSyncMapper.updateByPrimaryKeySelective(cardMallSync);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHistoryAtomCardInventoryId(String atomId) {
        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
        AtomOfferingInfoExample.Criteria criteria = atomOfferingInfoExample.createCriteria();
        if (StringUtils.isNotEmpty(atomId)){
            criteria.andIdEqualTo(atomId);
        }
        criteria.andInventoryMainIdIsNotNull().andCardInfoInventoryMainIdIsNull()
                .andCardContainingTerminalEqualTo("1").example();
        List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
      Integer count =0;
        for (AtomOfferingInfo atomOfferingInfo : atomOfferingInfos) {
            String skuCode = atomOfferingInfo.getSkuCode();
            String skuId = atomOfferingInfo.getSkuId();
            SkuOfferingInfo skuOfferingInfo = skuOfferingInfoMapper.selectByPrimaryKey(skuId);
            String templateId = skuOfferingInfo.getTemplateId();
            String custCode = skuOfferingInfo.getCustCode();
            if (StringUtils.isEmpty(templateId) || StringUtils.isEmpty(custCode)){
                log.info("该原子商品sku下模板编码或服务编码是空atomOfferingInfoCard：{},templateId：{}，custCode：{}",atomOfferingInfo,templateId,custCode);
                continue;
            }
            Optional<CardInventoryMainInfo> inventoryMainInfoOptional = cardInventoryMainInfoMapper.selectByExample(new CardInventoryMainInfoExample().createCriteria().andTemplateIdEqualTo(templateId)
                    .andCustCodeEqualTo(custCode).example()).stream().findFirst();
            if (!inventoryMainInfoOptional.isPresent()){
                log.info("改原子商品sku下不存在码号库存信息atomOfferingInfoCard：{}}",atomOfferingInfo);
                continue;
            }
            CardInventoryMainInfo cardInventoryMainInfo = inventoryMainInfoOptional.get();
            String id = cardInventoryMainInfo.getId();
            List<CardInventoryAtomInfo> cardInventoryAtomInfos = cardInventoryAtomInfoMapper.selectByExample(new CardInventoryAtomInfoExample().createCriteria()
                    .andCardInventoryMainIdEqualTo(id).andAtomIdEqualTo(atomOfferingInfo.getId()).example());
            if (CollectionUtils.isNotEmpty(cardInventoryAtomInfos)){
                log.info("该原子商品已经关联了码号库存cardInventoryAtomInfos：{}",atomOfferingInfo);
                continue;
            }

            AtomOfferingInfo atomOfferingInfoEn = new AtomOfferingInfo();
            atomOfferingInfoEn.setId(atomOfferingInfo.getId());
            atomOfferingInfoEn.setCardInfoInventoryMainId(id);
            atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfoEn);
            //码号库存与原子商品关联关系

            CardInventoryAtomInfo cardInventoryAtomInfo = new CardInventoryAtomInfo();
            cardInventoryAtomInfo.setId(BaseServiceUtils.getId());
            cardInventoryAtomInfo.setCardInventoryMainId(cardInventoryMainInfo.getId());
            cardInventoryAtomInfo.setAtomId(atomOfferingInfo.getId());
            cardInventoryAtomInfo.setAtomInventory(0);
            cardInventoryAtomInfo.setSpuCode(atomOfferingInfo.getSpuCode());
            cardInventoryAtomInfo.setSkuCode(atomOfferingInfo.getSkuCode());
            cardInventoryAtomInfo.setOfferingCode(atomOfferingInfo.getOfferingCode());
            cardInventoryAtomInfo.setCreateTime(new Date());
            cardInventoryAtomInfo.setUpdateTime(new Date());
            cardInventoryAtomInfoMapper.insert(cardInventoryAtomInfo);
            count ++;
        }
        log.info("历史原子商品码号库存主键id关联：cardIdCount:{}",count);
    }


    /**
     * 处理查询参数信息
     *
     * @param cardInfoParam
     * @param loginIfo4Redis
     */
    private void handleCardInfoParam(CardInfoParam cardInfoParam,
                                     LoginIfo4Redis loginIfo4Redis) {
        String openCardStartTime = cardInfoParam.getOpenCardStartTime();
        String openCardEndTime = cardInfoParam.getOpenCardEndTime();
        if (StringUtils.isNotEmpty(openCardStartTime)) {
            openCardStartTime = DateUtils.toStringDate(openCardStartTime, DateUtils.DEFAULT_DATETIME_FORMAT, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            cardInfoParam.setOpenCardStartTime(openCardStartTime);
        }

        if (StringUtils.isNotEmpty(openCardEndTime)) {
            openCardEndTime = DateUtils.toStringDate(openCardEndTime, DateUtils.DEFAULT_DATETIME_FORMAT, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            cardInfoParam.setOpenCardEndTime(openCardEndTime);
        }

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 如果是主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = BaseConstant.PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = BaseConstant.PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String beId = cardInfoParam.getBeId();
            String regionId = cardInfoParam.getRegionId();
            // 合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole && StringUtils.isEmpty(beId)) {
                cardInfoParam.setBeId(data4User.getBeIdPartner());
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince)
                    && StringUtils.isEmpty(regionId)) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    cardInfoParam.setBeId(data4User.getBeIdPartner());
                } else {
                    cardInfoParam.setRegionId(userLocation);
                }

            }
        }
    }


    /**
     * 发送合作伙伴短信库存不足短信
     * @param phones
     * @param templateName
     * @param custName
     * @return
     */
    private BaseAnswer<Void> sendCardInventoryCooperationSMS(List<String> phones, String templateName, String custName) {

        phones = phones.stream().distinct().collect(Collectors.toList());
        Msg4Request requestCo = new Msg4Request();
        Map<String, String> messageCo = new HashMap<>();
        messageCo.put("templateName", templateName);
        messageCo.put("custName", custName);
        requestCo.setMobiles(phones);
        requestCo.setMessage(messageCo);
        requestCo.setTemplateId(cardInventoryMainTemplateId);
        BaseAnswer<Void> messageKxAnswer = smsFeignClient.asySendMessage(requestCo);
        return messageKxAnswer;
    }
}
