<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.BRMMapperExt">
  <select id="getBRMUploadList"  resultType="com.chinamobile.iot.sc.pojo.dto.BRMUploadDTO">
      select atom.id,
             sku.id as skuId,
             atom.inventory_main_id as inId,
             sku.template_id            as cardTempleCode,
             sku.template_name          as cardTempleName,
             sku.cust_code              as cardVenderCode,
             sku.cust_name              as cardVenderName,
             if(didi.location is null, didi.be_id, didi.location) as region,
             boi.goods_id               as bossId,
             dimi.device_version        as xModelName,
             boi.iot_mall_offering_name as vasName,
             didi.reserve_quatity       as xPickNum,
      if(didi.current_inventory &lt; 0,0,didi.current_inventory)     as xStockNum,
      if(cimi.current_inventory &lt; 0,0,cimi.current_inventory)      as cardStockNum,
             cimi.reserve_quatity       as cardPickNum,
             if(s.saleNum is null,0,s.saleNum)                  as saleNum
      from
          sku_offering_info sku
              left join benefit_offering bo on sku.offering_code = bo.sku_offering_code
              left join benefit_offerings_info boi on bo.benefit_offerings_info_id = boi.id

              left join atom_offering_info atom on atom.sku_code = bo.sku_offering_code
              left join dkcardx_inventory_main_info dimi on dimi.id = atom.inventory_main_id
              inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
              left join card_inventory_main_info cimi
                        on sku.template_id = cimi.template_id and sku.cust_code = cimi.cust_code
              LEFT JOIN (SELECT subquery.sku_offering_code,
                                COUNT(subquery.sku_quantity) AS saleNum
                         FROM (
                                  SELECT DISTINCT o2cai.order_id, o2cai.sku_offering_code,o2cai.sku_quantity
                                  FROM order_2c_atom_info o2cai
                                  WHERE o2cai.order_status = 7

                              ) AS subquery
                         GROUP BY subquery.sku_offering_code) s ON s.sku_offering_code = sku.offering_code
      where 1 = 1
        and boi.goods_id is not null
        and sku.offering_status = '1'
        and sku.delete_time is null
        and boi.iot_release_area_id in
            ('200', '7530', '7600', '7620', '7520', '6600', '7540', '7510', '7500', '6680', '7680', '2000', '7630', '7660',
             '6620', '7560', '7590', '7570', '7690', '7550', '7580', '6630')
        and atom.card_containing_terminal = '1'
        and sku.template_id is not null
  </select>

  <select id="getBRMDetailUploadList" resultType="com.chinamobile.iot.sc.pojo.dto.BRMDetailUploadDTO">
      select
          cr.device_version as deviceModel,
          COALESCE(cr.template_id, '') as cardTemplateCode,
          '中国移动' as brand,
          'IOT-BRM-ECSS' as dataSource,
          if(cr.location is null, cr.be_id, cr.location) as regionCode,
          COALESCE(MAX(inventory_summary.total_reserve_quatity), 0) as reserveQuantity,
          COALESCE(MAX(inventory_summary.total_current_inventory), 0) as currentInventory,
          COALESCE(MAX(inventory_summary.total_total_inventory), 0) as totalInventory,
          COALESCE(GROUP_CONCAT(DISTINCT CASE WHEN cr.sell_status = '1' THEN cr.imei END SEPARATOR '&amp;,'), '') as availableDeviceDetails,
          -- 销量数（昨日销售数）
          if(yesterday_sales.yesterdaySaleNum is null, 0, yesterday_sales.yesterdaySaleNum) as salesQuantity,
          COALESCE(GROUP_CONCAT(DISTINCT CASE WHEN yesterday_sales_imei.imei IS NOT NULL THEN yesterday_sales_imei.imei END SEPARATOR '&amp;,'), '') as salesDeviceDetails,
          -- 关联规格商品（SKU商品编码|预占数|昨日销量数|SKU商品名称，用&amp;,隔开，字段用&amp;#隔开）
          COALESCE(GROUP_CONCAT(DISTINCT CASE WHEN atom_summary.sku_code IS NOT NULL THEN CONCAT(
      atom_summary.sku_code, '&amp;#',
      if(sku_atom_inventory.totalAtomInventory is null, 0, sku_atom_inventory.totalAtomInventory), '&amp;#',
      if(sku_yesterday_sales.yesterdaySaleNum is null, 0, sku_yesterday_sales.yesterdaySaleNum), '&amp;#',
      COALESCE(sku.offering_name, '')
      ) END SEPARATOR '&amp;,'), '') as relatedSkuProducts,
          -- 可用设备明细条数
          COUNT(DISTINCT CASE WHEN cr.sell_status = '1' THEN cr.imei END) as availableDeviceCount,
          -- 销量设备明细条数
          COUNT(DISTINCT CASE WHEN yesterday_sales_imei.imei IS NOT NULL THEN yesterday_sales_imei.imei END) as salesDeviceCount
      from
          card_relation cr
              -- 优化：先按三个维度汇总库存数据，然后关联
              left join (
              SELECT
                  dimi_sum.device_version,
                  dimi_sum.template_id,
                  if(didi_sum.location is null, dimi_sum.be_id, didi_sum.location) as regionCode,
                  SUM(CASE WHEN didi_sum.reserve_quatity &lt; 0 THEN 0 ELSE didi_sum.reserve_quatity END) as total_reserve_quatity,
                  SUM(CASE WHEN didi_sum.current_inventory &lt; 0 THEN 0 ELSE didi_sum.current_inventory END) as total_current_inventory,
                  SUM(CASE WHEN didi_sum.total_inventory &lt; 0 THEN 0 ELSE didi_sum.total_inventory END) as total_total_inventory
              FROM dkcardx_inventory_main_info dimi_sum
                       INNER JOIN dkcardx_inventory_detail_info didi_sum ON didi_sum.inventory_main_id = dimi_sum.id
              WHERE dimi_sum.be_id = '200'
              GROUP BY dimi_sum.device_version, dimi_sum.template_id, if(didi_sum.location is null, dimi_sum.be_id, didi_sum.location)
          ) inventory_summary ON (
              inventory_summary.device_version = cr.device_version
                  AND inventory_summary.template_id = cr.template_id
                  AND inventory_summary.regionCode = if(cr.location is null, cr.be_id, cr.location)
              )
              -- 为了保持原有逻辑，创建虚拟的dimi字段
              left join (
              SELECT
                  dimi_orig.device_version,
                  dimi_orig.template_id,
                  if(dimi_dtl.location is null, dimi_orig.be_id, dimi_dtl.location) as regionCode,
                  dimi_orig.id as inventory_main_id
              FROM dkcardx_inventory_main_info dimi_orig
                       INNER JOIN dkcardx_inventory_detail_info dimi_dtl ON dimi_dtl.inventory_main_id = dimi_orig.id
              WHERE dimi_orig.be_id = '200'
              GROUP BY dimi_orig.device_version, dimi_orig.template_id, if(dimi_dtl.location is null, dimi_orig.be_id, dimi_dtl.location)
          ) dimi ON (
              dimi.device_version = cr.device_version
                  AND dimi.template_id = cr.template_id
                  AND dimi.regionCode = if(cr.location is null, cr.be_id, cr.location)
              )
              -- 关联原子商品信息（通过三个维度关联）
              left join (
              SELECT DISTINCT
                  dimi_atom.device_version,
                  dimi_atom.template_id,
                  if(dimi_dtl.location is null, dimi_atom.be_id, dimi_dtl.location) as regionCode,
                  atom_info.id as atom_id,
                  atom_info.sku_code,
                  atom_info.offering_code
              FROM dkcardx_inventory_main_info dimi_atom
                       INNER JOIN dkcardx_inventory_detail_info dimi_dtl ON dimi_dtl.inventory_main_id = dimi_atom.id
                       INNER JOIN dkcardx_inventory_atom_info diai_atom ON diai_atom.inventory_main_id = dimi_atom.id
                       INNER JOIN atom_offering_info atom_info ON atom_info.id = diai_atom.atom_id
              WHERE dimi_atom.be_id = '200'
          ) atom_summary ON (
              atom_summary.device_version = cr.device_version
                  AND atom_summary.template_id = cr.template_id
                  AND atom_summary.regionCode = if(cr.location is null, cr.be_id, cr.location)
              )
              -- 根据sku_code获取SKU商品信息
              left join sku_offering_info sku on sku.offering_code = atom_summary.sku_code and sku.delete_time is null
              -- SKU维度的库存聚合（按三个维度聚合原子商品的库存，汇总所有terminal_type）
              LEFT JOIN (
              SELECT
                  atom_inner.sku_code,
                  dimi_inner.device_version,
                  dimi_inner.template_id,
                  if(dimi_dtl.location is null, dimi_inner.be_id, dimi_dtl.location) as regionCode,
                  SUM(diai_inner.atom_inventory) AS totalAtomInventory
              FROM dkcardx_inventory_main_info dimi_inner
                       INNER JOIN dkcardx_inventory_detail_info dimi_dtl ON dimi_dtl.inventory_main_id = dimi_inner.id
                       INNER JOIN dkcardx_inventory_atom_info diai_inner ON diai_inner.inventory_main_id = dimi_inner.id
                       INNER JOIN atom_offering_info atom_inner ON atom_inner.id = diai_inner.atom_id
              WHERE dimi_inner.be_id = '200'
              GROUP BY atom_inner.sku_code, dimi_inner.device_version, dimi_inner.template_id, if(dimi_dtl.location is null, dimi_inner.be_id, dimi_dtl.location)
          ) sku_atom_inventory ON sku_atom_inventory.sku_code = atom_summary.sku_code
              AND sku_atom_inventory.device_version = cr.device_version
              AND sku_atom_inventory.template_id = cr.template_id
              AND sku_atom_inventory.regionCode = if(cr.location is null, cr.be_id, cr.location)
              -- 原子商品昨日销售数查询（从order_2c_atom_history表查询inner_status=64且create_time为昨天的数据）
              LEFT JOIN (
              SELECT
                  o2cai.atom_offering_code,
                  SUM(o2cai.sku_quantity * o2cai.atom_quantity) AS yesterdaySaleNum
              FROM order_2c_atom_history o2cah
                       INNER JOIN order_2c_atom_info o2cai ON o2cah.atom_order_id = o2cai.id
              WHERE o2cah.inner_status = 64
                AND DATE(o2cah.create_time) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY))
      GROUP BY o2cai.atom_offering_code
          ) atom_yesterday_sales ON atom_yesterday_sales.atom_offering_code = atom_summary.offering_code
          -- SKU维度的昨日销售数查询（通过card_relation关联，按sku_code聚合昨日销售数据）
          LEFT JOIN (
          SELECT
          o2cai_sku.sku_offering_code,
          cr_sku.device_version,
          cr_sku.template_id,
          cr_sku.be_id,
          COUNT(DISTINCT cr_sku.imei) AS yesterdaySaleNum
          FROM order_2c_atom_history o2cah_sku
          INNER JOIN order_2c_atom_info o2cai_sku ON o2cah_sku.atom_order_id = o2cai_sku.id
          LEFT JOIN card_relation cr_sku ON cr_sku.order_atom_info_id = o2cai_sku.id
          WHERE o2cah_sku.inner_status = 64
          AND DATE(o2cah_sku.create_time) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY))
          AND cr_sku.delete_time IS NULL
          GROUP BY o2cai_sku.sku_offering_code, cr_sku.device_version, cr_sku.template_id, cr_sku.be_id
          ) sku_yesterday_sales ON sku_yesterday_sales.sku_offering_code = atom_summary.sku_code
          AND sku_yesterday_sales.device_version = cr.device_version
          AND sku_yesterday_sales.template_id = cr.template_id
          AND sku_yesterday_sales.be_id = cr.be_id
          -- 设备型号维度的昨日销售数汇总
          LEFT JOIN (
          SELECT
          cr_sales.device_version,
          cr_sales.template_id,
          if(cr_sales.location is null, cr_sales.be_id, cr_sales.location) as regionCode,
          SUM(cr_sales.id) AS yesterdaySaleNum
          FROM order_2c_atom_history o2cah_sales
          INNER JOIN order_2c_atom_info o2cai_sales ON o2cah_sales.atom_order_id = o2cai_sales.id
          LEFT JOIN card_relation cr_sales ON cr_sales.order_atom_info_id = o2cai_sales.id
          WHERE o2cah_sales.inner_status = 64
          AND DATE(o2cah_sales.create_time) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY))
          GROUP BY cr_sales.device_version, cr_sales.template_id, if(cr_sales.location is null, cr_sales.be_id, cr_sales.location)
          ) yesterday_sales ON yesterday_sales.device_version = cr.device_version
          AND yesterday_sales.template_id = cr.template_id
          AND yesterday_sales.regionCode = if(cr.location is null, cr.be_id, cr.location)
          -- 昨日销售设备IMEI查询
          LEFT JOIN (
          SELECT DISTINCT
          cr_imei.imei,
          cr_imei.device_version,
          cr_imei.template_id,
          if(cr_imei.location is null, cr_imei.be_id, cr_imei.location) as regionCode
          FROM order_2c_atom_history o2cah_imei
          INNER JOIN order_2c_atom_info o2cai_imei ON o2cah_imei.atom_order_id = o2cai_imei.id
          LEFT JOIN card_relation cr_imei ON cr_imei.order_atom_info_id = o2cai_imei.id
          WHERE o2cah_imei.inner_status = 64
          AND DATE(o2cah_imei.create_time) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY))
          AND cr_imei.imei IS NOT NULL
          ) yesterday_sales_imei ON yesterday_sales_imei.device_version = cr.device_version
          AND yesterday_sales_imei.template_id = cr.template_id
          AND yesterday_sales_imei.regionCode = if(cr.location is null, cr.be_id, cr.location)
          AND yesterday_sales_imei.imei = cr.imei
      where 1 = 1
        and cr.delete_time is null
        and cr.device_version is not null
        and cr.be_id ='200'
      GROUP BY cr.device_version, cr.template_id, if(cr.location is null, cr.be_id, cr.location)
  </select>
</mapper>