package com.chinamobile.iot.sc.request;

import lombok.Data;


@Data
public class AllocateCardRequest {
    /**
     * 请求流水号
     */
    private String orderSeq;

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 0：插拔卡
     * 1：贴片卡
     * 2：M2M芯片非空写卡
     * 3：M2M芯片空写卡
     */
    private String cardType;
    /**
     * 数量
     */
    private Long quantity;
    /**
     * 服务号码 cardType=3：M2M芯片空写卡时传输，即需分配临时ICCID、IMEI的服务号码
     */
    private String msisdn;
}
