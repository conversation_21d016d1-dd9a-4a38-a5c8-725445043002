package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.CardInfo;
import com.chinamobile.iot.sc.pojo.CardInfoExample;
import com.chinamobile.iot.sc.pojo.param.CardInfoParam;
import com.chinamobile.iot.sc.pojo.vo.CardInfoVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/7
 * @description 号卡信息service接口类
 */
public interface CardInfoService {

    /**
     * 新增号卡信息
     *
     * @param cardInfo
     */
    void addCardInfo(CardInfo cardInfo);

    /**
     * 根据id获取号卡信息
     *
     * @param id
     * @return
     */
    CardInfo getCardInfoById(String id);

    /**
     * 根据需要获取号卡信息
     *
     * @param cardInfoExample
     * @return
     */
    List<CardInfo> listCardInfoByNeed(CardInfoExample cardInfoExample);

    /**
     * 分页查询码号列表
     *
     * @param cardInfoParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<CardInfoVO> pageCardInfo(CardInfoParam cardInfoParam,
                                      LoginIfo4Redis loginIfo4Redis);

    /**
     * 导出码号信息
     *
     * @param cardInfoParam
     * @param loginIfo4Redis
     * @param response
     * @throws IOException
     */
    void exportCardInfo(CardInfoParam cardInfoParam,
                              LoginIfo4Redis loginIfo4Redis,
                              HttpServletResponse response) throws Exception;

    /**
     * 获取码号信息列表
     *
     * @param cardInfoParam
     * @return
     */
    List<CardInfoVO> listCardInfo(CardInfoParam cardInfoParam);

    /**
     * 用于导入验证获取码号信息列表
     *
     * @param cardInfoParam
     * @return
     */
    List<CardInfoVO> listCardInfoCheckImport(CardInfoParam cardInfoParam);

    Long countCardInfoCheckImport(CardInfoParam cardInfoParam);

    BaseAnswer<List<String>> orderProductTemplateCardList(String orderId, String msisdn);

    BaseAnswer<String> orderProductTemplateName(String orderId);

    /**
     * 处理历史码号库存数据
     */
    void handleHistoryCardInventory(CardInfoParam param);


    BaseAnswer setStatusCanSell(String id);
}
