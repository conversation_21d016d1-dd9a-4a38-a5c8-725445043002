package com.chinamobile.iot.sc.constant.productflow;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/3/14 11:31
 */
public class ProductFlowShelfExcelHeaderConstant {

    public static List<String> planProductShelfExcelHeader = null;
    public static List<String> standardProductSheflContractExcelHeader = null;
    public static List<String> standardProductSheflCooperateSaleExcelHeader = null;
    public static List<String> standardProductSheflDICTExcelHeader = null;
    public static List<String> standardProductSheflProvinceExcelHeader = null;

    static {
        String planProductShelfExcelHeaderStr = "商品上架类目,一级导航目录,二级导航目录,三级导航目录,商品名称（SPU）,产品经理,商品简介,典型应用领域,是否隐秘上架,服务商,销售标签,映射检索词,商品规格名称,商品规格简称,核心部件名称,核心部件及服务内容,发布订购范围,配送范围,游客/合作伙伴可见,标准服务产品名称,产品属性,服务供应商,产品管理部门,标准服务产品经理,商城订单管理账号\n" +
                "（接单）,商城订单管理账号\n" +
                "（交付）,商城订单管理账号\n" +
                "（售后）,发货接口人,原子商品名称,（省-专）结算单价,（专-合）结算单价,计量单位,不需结算（对账单）,结算明细服务名称（产品）,服务包名称,服务内容,订购数量最小值,库存数,服务产品合同信息（销售侧）,物联网公司采购合同信息,物联网公司K3系统物料编码";

        String standardProductSheflContractExcelHeaderStr = "商品上架类目,一级导航目录,二级导航目录,三级导航目录,商品名称（SPU）,服务商,产品经理,售后订单管理员信息,典型应用领域,商品简介,是否隐秘上架,映射检索词,销售标签,商品规格名称,商品规格简称,省公司价格,核心部件名称,核心部件及服务内容,配送范围,游客可见,发布订购范围,硬件原子商品名称,硬件结算单价,硬件原子计量单位,硬件原子数量,颜色,终端物料编码,型号,硬件原子服务内容,软件原子商品名称,软件结算单价,软件原子计量单位,软件原子数量,软件平台商品编码,不需结算,结算明细服务名称,交付周期,软件原子服务内容,库存数,标准服务名称-OS系统配置项,实际产品名称-OS系统配置项,产品属性-OS系统配置项,产品部门-OS系统配置项,供应商名称,商城订单处理人（主）,商城订单处理人（次）";

        String standardProductSheflCooperateSaleExcelHeaderStr = "商品上架类目,一级导航目录,二级导航目录,三级导航目录,服务商,产品管理部门,产品经理,销售标签,商品名称（SPU）,商品简介,商品应用场景,是否隐秘上架,映射检索词,商品规格名称,商品规格简称,起订量,核心部件名称,核心部件及服务内容,商品规格销售价,配送范围,游客是否可见,发布订购范围,是否发酬金,酬金比例,标准服务名称,产品部门,产品属性,合作厂商名,商城订单处理人（主）,商城订单处理人（次）,硬件原子商品名称,硬件原子商品型号,硬件原子商品数量,颜色,硬件原子商品终端物料编码,硬件原子商品计量单位,硬件原子商品结算价,硬件原子商品销售单价,软件原子商品名称,软件原子商品销售价,库存数,售前市场经理,商品发货接口人,商品安装接口人,物联卡套餐接口人,商品软件权限开通接口人,商品售后接口人,质保/售后细则,商品维修联系信息与地址,商品退货联系信息与地址,商品厂商信息,商品通信方式,物联卡套餐说明,硬件商品发货清单,商品参数信息,商品发货地址,硬件商品发货默认快递,商品发货时间信息,商品使用条件,软件平台名称及介绍,软件平台下载方式及地址,APP、小程序名称及介绍,APP、小程序下载方式及地址,安装服务说明";

        String standardProductSheflDICTExcelHeaderStr = "商品上架类目,一级导航目录,二级导航目录,三级导航目录,商品名称（SPU）,产品经理,商品简介,典型应用领域,是否隐秘上架,服务商,销售标签,映射检索词,商品规格名称,商品规格简称,核心部件名称,核心部件及服务内容,发布订购范围,配送范围,游客/合作伙伴可见,标准服务产品名称,产品属性,服务供应商,产品管理部门,标准服务产品经理,商城订单管理账号（接单）,商城订单管理账号（交付）,商城订单管理账号（售后）,发货接口人,原子商品名称,（省-专）结算单价,（专-合）结算单价,计量单位,不需结算（对账单）,结算明细服务名称（产品）,服务包名称,服务内容,订购数量最小值,库存数,服务产品合同信息（销售侧）,物联网公司采购合同信息,物联网公司K3系统物料编码";

        String standardProductSheflProvinceExcelHeaderStr = "商品上架类目,一级导航目录,二级导航目录,三级导航目录,商品名称（SPU）,产品经理,商品简介,典型应用领域,是否隐秘上架,服务商,销售标签,映射检索词,商品规格名称,商品规格简称,核心部件名称,核心部件及服务内容,发布订购范围,配送范围,游客/合作伙伴可见,标准服务产品名称,产品属性,服务供应商,产品管理部门,标准服务产品经理,接单账号,交付账号,售后账号,原子商品名称,结算单价,结算单价核对,计量单位,服务内容,订购数量最小值,库存数,省公司采购合同信息,物联网公司采购合同信息（物联网必填，非物联网非必填）,物联网公司K3系统物料编码（物联网必填，非物联网非必填）";

        planProductShelfExcelHeader = Arrays.asList(planProductShelfExcelHeaderStr.split(","));
        standardProductSheflContractExcelHeader = Arrays.asList(standardProductSheflContractExcelHeaderStr.split(","));
        standardProductSheflCooperateSaleExcelHeader = Arrays.asList(standardProductSheflCooperateSaleExcelHeaderStr.split(","));
        standardProductSheflDICTExcelHeader = Arrays.asList(standardProductSheflDICTExcelHeaderStr.split(","));
        standardProductSheflProvinceExcelHeader = Arrays.asList(standardProductSheflProvinceExcelHeaderStr.split(","));
    }


}
