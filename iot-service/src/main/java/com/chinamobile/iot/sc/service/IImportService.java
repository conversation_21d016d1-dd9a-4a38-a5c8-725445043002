package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IImportService
 * @description: 存量订单数据导入接口
 * @author: zyj
 * @create: 2022/3/2 14:28
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IImportService {

    BaseAnswer<Map<String,String>> importIOTData(MultipartFile[] files, String type, String sign);

    /**
     * 使用excel将地市和区县信息存入redis
     * @param file
     * @return
     */
    BaseAnswer importRegionAndLocation(MultipartFile file);

    BaseAnswer importProductData(MultipartFile file, String sign);

    /**
     * 存量商品数据割接-上架状态修改（Spu、Sku）
     * @param file
     * @return
     */
    BaseAnswer importProductStatus(MultipartFile file);

    /**
     * 导入终端imei相关信息
     * @param file
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    void importCardRelation(MultipartFile file,
                                  HttpServletRequest request,
                                  HttpServletResponse response) throws Exception ;

    BaseAnswer updateOrderOrgBizInfo(MultipartFile file);
}
