package com.chinamobile.iot.sc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.ShopCustomerInfoMapperExt;
import com.chinamobile.iot.sc.excel.ExcelUserCustomerlImport;
import com.chinamobile.iot.sc.excel.ExcelUserCustomerlImportListener;
import com.chinamobile.iot.sc.excel.ExcelUserManagerlImport;
import com.chinamobile.iot.sc.excel.ExcelUserManagerlImportListener;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.mapper.ShopUserUpdateDO;
import com.chinamobile.iot.sc.request.ShopCustomerInfoDTO;
import com.chinamobile.iot.sc.request.ShopManagerInfoDTO;
import com.chinamobile.iot.sc.service.GioBurialPointService;
import com.chinamobile.iot.sc.service.ShopUserOperationService;
import com.chinamobile.iot.sc.util.BatchHandlerUtil;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.SFTPUtil;
import com.jcraft.jsch.ChannelSftp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.chinamobile.iot.sc.constant.OperationTypeConstant.*;
import static com.chinamobile.iot.sc.util.DateUtils.DATE_FORMAT_NO_SYMBOL;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/6/27 10:31
 * @description: 商城用户操作实现类
 **/
@Service
@Slf4j
public class ShopUserOperationServiceImpl implements ShopUserOperationService {

    @Value("${supply.des.key}")
    private String encryptKey;
    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;
    @Resource
    GioBurialPointService gioBurialPointService;

    @Resource
    private ShopManagerInfoMapper shopManagerInfoMapper;

    @Resource
    private ShopManagerInfoHistoryMapper shopManagerInfoHistoryMapper;

    @Resource
    private ShopCustomerInfoMapper shopCustomerInfoMapper;

    @Resource
    private ShopCustomerInfoHistoryMapper shopCustomerInfoHistoryMapper;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Value("${iot.ftp.name}")
    private String sftpUserName;
    @Value("${iot.ftp.password}")
    private String sftpPassword;
    @Value("${iot.ftp.host}")
    private String sftpHost;
    @Value("${iot.ftp.port}")
    private Integer sftpPort;

    @Value("${iot.ftp.sftpAccountManagerData}")
    private String sftpAccountManagerData;

    @Value("${iot.ftp.sftpCustomerData}")
    private String sftpCustomerData;

    @Resource
    private ShopCustomerInfoMapperExt shopCustomerInfoMapperExt;

    @Resource
    private UserRetailMapper userRetailMapper;

    private SimpleDateFormat orderStatusSDF = new SimpleDateFormat("yyyyMMddHHmmss");

    @Resource
    private RedisTemplate redisTemplate;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sftpOperationCustomerList(MultipartFile file) {

               /* ClassPathResource pathResource = new ClassPathResource("excel/accountManagerInformation_20230524_0001.txt");
               pathResource.getFilename();
                InputStream is = pathResource.getInputStream();*/
        try {
            InputStream is = file.getInputStream();
            String fileName = file.getOriginalFilename();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            log.info("BufferedReader流数据：{}", br);
            List<ShopCustomerInfo> shopCustomerInfosAdd = new ArrayList<>();
            List<ShopCustomerInfo> shopCustomerInfosUpdate = new ArrayList<>();
            List<ShopCustomerInfoHistory> shopCustomerInfoHistories = new ArrayList<>();

            List<UserMiniProgram> userMiniProgramsAdd = new ArrayList<>();
            List<UserMiniProgram> userMiniProgramsUpdate = new ArrayList<>();

            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
            Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
            String readStr;
            //Date date = new Date();
            //获取文件名里时间 作为数据创建时间
            String dateStr = StringUtils.substringBetween(fileName, "_");
            Date updateDate = DateUtils.strToDate(dateStr, DATE_FORMAT_NO_SYMBOL);
            Date date = DateTimeUtil.addDay(updateDate, -1);
            boolean success = true;
            log.info("startExecution开始执行解析：{}", dateStr);
            while ((readStr = br.readLine()) != null) {
                // log.info("解析到的分销及注册用户数据：{}", readStr);
                String[] parts = readStr.split("\\|", -1);
                String roleType = parts[6];
                String oprType = parts[4];
                String beId = parts[7];
                //如果省份名称没有匹配到直接跳出当前
                String provinceName = (String) provinceCodeNameMap.get(beId);
                if (StringUtils.isEmpty(provinceName)) {
                    log.info("provinceNotMatch 当前数据省份未匹配到，不保存入库：{}", readStr);
                    continue;
                }
                String location = parts[8];
                String regionID = parts[9];
                String ClientStatus = parts[11];
                //普通用户
                if (parts.length != 22) {
                    if (parts.length == 1) {
                        continue;
                    }
                    log.info("formatException 该条记录格式异常，无法解析,内容:{}", readStr);
                    success = false;
                    break;
                }
                ShopCustomerInfo shopCustomerInfo = new ShopCustomerInfo();
                ShopCustomerInfoHistory shopCustomerInfoHistory = new ShopCustomerInfoHistory();
                shopCustomerInfoHistory.setId(BaseServiceUtils.getId());
                if (StringUtils.isEmpty(parts[0])) {
                    log.info("formatException 该条记录格式异常，客户编码为空无法解析,内容:{}", readStr);
                    success = false;
                    break;
                }
                shopCustomerInfoHistory.setCustCode(StringUtils.isNotEmpty(parts[0]) ? parts[0] : null);
                shopCustomerInfoHistory.setCustId(parts[1]);
                shopCustomerInfoHistory.setUserId(StringUtils.isNotEmpty(parts[2]) ? parts[2] : null);
                shopCustomerInfoHistory.setCustName(StringUtils.isNotEmpty(parts[3]) ? parts[3] : null);
                shopCustomerInfoHistory.setOprType(oprType);
                shopCustomerInfoHistory.setCustStatusTime(orderStatusSDF.parse(parts[5]));
                shopCustomerInfoHistory.setRoleType(parts[6]);
                shopCustomerInfoHistory.setBeId(beId);
                shopCustomerInfoHistory.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                shopCustomerInfoHistory.setLocation(location);
                shopCustomerInfoHistory.setCityName((String) locationCodeNameMap.get(location));
                //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                    shopCustomerInfoHistory.setRegionId((String) regionNameCodeMap.get("綦江区"));
                    shopCustomerInfoHistory.setRegionName("綦江区");
                } else {
                    shopCustomerInfoHistory.setRegionId(regionID);
                    shopCustomerInfoHistory.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                }
                shopCustomerInfoHistory.setClientRegister(StringUtils.isNotEmpty(parts[10]) ? orderStatusSDF.parse(parts[10]) : null);
                shopCustomerInfoHistory.setClientStatus(StringUtils.isNotEmpty(parts[11]) ? parts[11] : null);
                shopCustomerInfoHistory.setNumberLogins(StringUtils.isNotEmpty(parts[12]) ? Integer.parseInt(parts[12]) : 0);
                shopCustomerInfoHistory.setDistributorName(StringUtils.isNotEmpty(parts[13]) ? parts[13] : null);
                shopCustomerInfoHistory.setDistributorInvitationRegisterSuccessfulQuantity(StringUtils.isNotEmpty(parts[14]) ? Integer.parseInt(parts[14]) : null);
                shopCustomerInfoHistory.setDistributorChannelId(StringUtils.isNotEmpty(parts[15]) ? parts[15] : null);
                shopCustomerInfoHistory.setDistributorChannelName(StringUtils.isNotEmpty(parts[16]) ? parts[16] : null);
                shopCustomerInfoHistory.setDistributorReferralCode(StringUtils.isNotEmpty(parts[17]) ? parts[17] : null);
                shopCustomerInfoHistory.setDistributorMrgInf(StringUtils.isNotEmpty(parts[18]) ? parts[18] : null);
                shopCustomerInfoHistory.setDistributorMrgCode(StringUtils.isNotEmpty(parts[19]) ? parts[19] : null);
                shopCustomerInfoHistory.setAgentNumber(StringUtils.isNotEmpty(parts[20]) ? parts[20] : null);
                shopCustomerInfoHistory.setAgentName(StringUtils.isNotEmpty(parts[21]) ? parts[21] : null);
                shopCustomerInfoHistory.setCreateTime(date);
                if (ADD_SHOP_CLIENT.equals(oprType)) {
                   /*     if ("3".equals(ClientStatus)){
                            //  判断该条数据状态是否是失效，失效删除历史表和信息表，新增现在来的数据。  更新后以userid唯一
                            log.info("deleteCustomer删除的注销用户编码：{}，{}",parts[0],parts[1]);
                            shopCustomerInfoMapper.deleteByExample(new ShopCustomerInfoExample().createCriteria().andUserIdEqualTo(parts[2])
                                    .example());
                            shopCustomerInfoHistoryMapper.deleteByExample(new ShopCustomerInfoHistoryExample().createCriteria()
                                    .andUserIdEqualTo(parts[2]).example());
                        }*/
                    ShopCustomerInfoExample example = new ShopCustomerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                    List<ShopCustomerInfo> shopCustomerInfoList = shopCustomerInfoMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(shopCustomerInfoList)) {
                        log.info("formatException 该条记录格式异常，新增的分销或注册用户数据已同步,内容:{}", readStr);
                        success = false;
                        break;
                    }
                    shopCustomerInfo.setId(BaseServiceUtils.getId());
                    shopCustomerInfo.setCustCode(StringUtils.isNotEmpty(parts[0]) ? parts[0] : null);
                    shopCustomerInfo.setCustId(parts[1]);
                    shopCustomerInfo.setUserId(StringUtils.isNotEmpty(parts[2]) ? parts[2] : null);
                    shopCustomerInfo.setCustName(StringUtils.isNotEmpty(parts[3]) ? parts[3] : null);
                    //shopCustomerInfo.setOprType(oprType);
                    shopCustomerInfo.setRegisterDate(orderStatusSDF.parse(parts[5]));
                    shopCustomerInfo.setRoleType(parts[6]);
                    shopCustomerInfo.setBeId(beId);
                    shopCustomerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                    shopCustomerInfo.setLocation(location);
                    shopCustomerInfo.setCityName((String) locationCodeNameMap.get(location));
                    //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                    if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                        shopCustomerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
                        shopCustomerInfo.setRegionName("綦江区");
                    } else {
                        shopCustomerInfo.setRegionId(regionID);
                        shopCustomerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                    }
                    shopCustomerInfo.setClientStatus(StringUtils.isNotEmpty(parts[11]) ? parts[11] : null);
                    shopCustomerInfo.setDistributorName(StringUtils.isNotEmpty(parts[13]) ? parts[13] : null);
                    shopCustomerInfo.setDistributorChannelId(StringUtils.isNotEmpty(parts[15]) ? parts[15] : null);
                    shopCustomerInfo.setDistributorChannelName(StringUtils.isNotEmpty(parts[16]) ? parts[16] : null);
                    shopCustomerInfo.setDistributorReferralCode(StringUtils.isNotEmpty(parts[17]) ? parts[17] : null);
                    shopCustomerInfo.setDistributorMrgInf(StringUtils.isNotEmpty(parts[18]) ? parts[18] : null);
                    shopCustomerInfo.setDistributorMrgCode(StringUtils.isNotEmpty(parts[19]) ? parts[19] : null);
                    shopCustomerInfo.setAgentNumber(StringUtils.isNotEmpty(parts[20]) ? parts[20] : null);
                    shopCustomerInfo.setAgentName(StringUtils.isNotEmpty(parts[21]) ? parts[21] : null);
                    shopCustomerInfo.setIsRinse(false);
                    shopCustomerInfo.setCreateTime(date);
                    shopCustomerInfo.setUpdateTime(date);
                    shopCustomerInfoHistories.add(shopCustomerInfoHistory);
                    shopCustomerInfosAdd.add(shopCustomerInfo);

                    UserMiniProgram userMiniProgram = convertFromShopCustomerInfo(shopCustomerInfo);
                    userMiniProgram.setId(BaseServiceUtils.getId());
                    userMiniProgramsAdd.add(userMiniProgram);
                    userMiniProgramMapper.deleteByExample(
                            new UserMiniProgramExample().createCriteria()
                                    .andUserIdEqualTo(shopCustomerInfo.getUserId())
                                    .example()
                    );
                } else if (UPDATE_SHOP_CLIENT.equals(oprType)) {
                    if (StringUtils.isEmpty(parts[0])) {
                        log.info("formatException 该条记录格式异常，客户编码为空无法解析,内容:{}", readStr);
                        success = false;
                        break;
                    }
                    if ("3".equals(ClientStatus)) {
                        //  判断该条数据状态是否是失效，失效删除历史表和信息表。  更新后以userid唯一
                        log.info("updateDeleteCustomer删除的注销用户编码：{}，{},{}", parts[0], parts[1], parts[2]);
                        shopCustomerInfoMapper.deleteByExample(new ShopCustomerInfoExample().createCriteria().andUserIdEqualTo(parts[2])
                                .example());
                        shopCustomerInfoHistoryMapper.deleteByExample(new ShopCustomerInfoHistoryExample().createCriteria()
                                .andUserIdEqualTo(parts[2]).example());
                        continue;
                    }
                    ShopCustomerInfoExample example = new ShopCustomerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                    List<ShopCustomerInfo> shopCustomerInfoList = shopCustomerInfoMapper.selectByExample(example);
                    if (CollectionUtils.isEmpty(shopCustomerInfoList)) {
                        log.info("formatException 该条记录格式异常，修改的分销或注册用户数据未查询到,内容:{}", readStr);
                        success = false;
                        break;
                    }
                    shopCustomerInfo = shopCustomerInfoList.get(0);
                    shopCustomerInfo.setCustCode(StringUtils.isNotEmpty(parts[0]) ? parts[0] : null);
                    shopCustomerInfo.setCustId(parts[1]);
                    shopCustomerInfo.setUserId(StringUtils.isNotEmpty(parts[2]) ? parts[2] : null);
                    shopCustomerInfo.setCustName(StringUtils.isNotEmpty(parts[3]) ? parts[3] : null);
                    //shopCustomerInfo.setOprType(oprType);
                    //shopCustomerInfo.setRegisterDate(orderStatusSDF.parse(parts[4]));
                    shopCustomerInfo.setRoleType(parts[6]);
                    shopCustomerInfo.setBeId(beId);
                    shopCustomerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                    shopCustomerInfo.setLocation(location);
                    shopCustomerInfo.setCityName((String) locationCodeNameMap.get(location));
                    //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                    if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                        shopCustomerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
                        shopCustomerInfo.setRegionName("綦江区");
                    } else {
                        shopCustomerInfo.setRegionId(regionID);
                        shopCustomerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                    }
                    shopCustomerInfo.setClientStatus(StringUtils.isNotEmpty(parts[11]) ? parts[11] : null);
                    shopCustomerInfo.setDistributorName(StringUtils.isNotEmpty(parts[13]) ? parts[13] : null);
                    shopCustomerInfo.setDistributorChannelId(StringUtils.isNotEmpty(parts[15]) ? parts[15] : null);
                    shopCustomerInfo.setDistributorChannelName(StringUtils.isNotEmpty(parts[16]) ? parts[16] : null);
                    shopCustomerInfo.setDistributorReferralCode(StringUtils.isNotEmpty(parts[17]) ? parts[17] : null);
                    shopCustomerInfo.setDistributorMrgInf(StringUtils.isNotEmpty(parts[18]) ? parts[18] : null);
                    shopCustomerInfo.setDistributorMrgCode(StringUtils.isNotEmpty(parts[19]) ? parts[19] : null);
                    shopCustomerInfo.setAgentNumber(StringUtils.isNotEmpty(parts[20]) ? parts[20] : null);
                    shopCustomerInfo.setAgentName(StringUtils.isNotEmpty(parts[21]) ? parts[21] : null);
                    shopCustomerInfo.setUpdateTime(date);
                    shopCustomerInfoHistories.add(shopCustomerInfoHistory);
                    shopCustomerInfosUpdate.add(shopCustomerInfo);

                    List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(
                            new UserMiniProgramExample().createCriteria()
                                    .andUserIdEqualTo(shopCustomerInfo.getUserId())
                                    .example()
                    );
                    if (CollectionUtils.isNotEmpty(userMiniPrograms)) {
                        UserMiniProgram userMiniProgram = convertFromShopCustomerInfo(shopCustomerInfo);
                        UserMiniProgram savedUser = userMiniPrograms.get(0);
                        userMiniProgram.setId(savedUser.getId());
                        userMiniProgramsUpdate.add(userMiniProgram);
                    }
                } else {
                    log.info("formatException 该条记录格式异常操作类型错误，无法解析,内容:{}", readStr);
                    success = false;
                    break;
                }
            }
            log.info("endExecution 解析完成：{}", success);
            if (success) {
                log.info("downloadCustomerInfo 开始批量插入分销及普通用户文件：{},分销及普通用户数据数量:{}", fileName, shopCustomerInfoHistories.size());
                if (CollectionUtils.isNotEmpty(shopCustomerInfoHistories)) {
                    new BatchHandlerUtil<ShopCustomerInfoHistory>().batchResolve(shopCustomerInfoHistories, 1000,
                            items -> shopCustomerInfoHistoryMapper.batchInsert(items));
                }
                if (CollectionUtils.isNotEmpty(shopCustomerInfosAdd)) {
                    new BatchHandlerUtil<ShopCustomerInfo>().batchResolve(shopCustomerInfosAdd, 1000,
                            items -> shopCustomerInfoMapper.batchInsert(items));
                }
                if (CollectionUtils.isNotEmpty(shopCustomerInfosUpdate)) {
                    for (ShopCustomerInfo shopCustomerInfo : shopCustomerInfosUpdate) {
                        shopCustomerInfoMapper.updateByPrimaryKeySelective(shopCustomerInfo);
                    }
                }
                if (CollectionUtils.isNotEmpty(userMiniProgramsAdd)) {
                    new BatchHandlerUtil<UserMiniProgram>().batchResolve(userMiniProgramsAdd, 1000,
                            items -> userMiniProgramMapper.batchInsert(items));
                }
                if (CollectionUtils.isNotEmpty(userMiniProgramsUpdate)) {
                    for (UserMiniProgram userMiniProgram : userMiniProgramsUpdate) {
                        userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
                        redisTemplate.delete(Constant.REDIS_KEY_MINI_USER + userMiniProgram.getId());
                    }
                }

            }

        } catch (Exception e) {
            log.warn("拉取商城分销及注册用发生异常:{}", e.toString());
            throw new BusinessException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shopManagerAddFieldImportList(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        String oldName = file.getOriginalFilename();
        if (oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"))) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }
        try {
            ExcelUserManagerlImportListener listener = new ExcelUserManagerlImportListener();
            List<Object> list = EasyExcel.read(file.getInputStream(), ExcelUserManagerlImport.class, listener)
                    .sheet(0).headRowNumber(1).doReadSync();
            if (list.size() == 0) {
                throw new BusinessException(BaseErrorConstant.EMPTY_EXCEL);
            }
            List<ExcelUserManagerlImport> succeedList = listener.getSucceedList();
            succeedList.forEach(x -> {
                String createOperCode = x.getCreateOperCode();
                String createOperPhone = x.getCreateOperPhone();
                String userId = x.getUserId();

                List<ShopUserUpdateDO> shopManagerInfoList = shopCustomerInfoMapperExt.getShopManagerInfoList(createOperCode);
                List<ShopUserUpdateDO> shopManagerInfoHistoryList = shopCustomerInfoMapperExt.getShopManagerInfoHistoryList(createOperCode);
                if (CollectionUtils.isNotEmpty(shopManagerInfoList)) {
                    shopManagerInfoList.forEach(y -> {
                        String id = y.getId();
                        ShopManagerInfo shopManagerInfo = new ShopManagerInfo();
                        shopManagerInfo.setId(id);
                        //shopManagerInfo.setCreateOperCode(createOperCode);
                        shopManagerInfo.setCreateOperPhone(createOperPhone);
                        shopManagerInfo.setUserId(userId);
                        shopManagerInfoMapper.updateByPrimaryKeySelective(shopManagerInfo);
                    });
                }
                if (CollectionUtils.isNotEmpty(shopManagerInfoHistoryList)) {
                    shopManagerInfoHistoryList.forEach(y -> {
                        String id = y.getId();
                        ShopManagerInfoHistory shopManagerInfoHistory = new ShopManagerInfoHistory();
                        shopManagerInfoHistory.setId(id);
                        //shopManagerInfoHistory.setCreateOperCode(createOperCode);
                        shopManagerInfoHistory.setCreateOperPhone(createOperPhone);
                        shopManagerInfoHistory.setUserId(userId);
                        shopManagerInfoHistoryMapper.updateByPrimaryKeySelective(shopManagerInfoHistory);
                    });
                }
            });

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shopCustomerAddFieldImportList(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String oldName = file.getOriginalFilename();
        if (oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"))) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }

        try {
            ExcelUserCustomerlImportListener listener = new ExcelUserCustomerlImportListener();
            List<Object> list = EasyExcel.read(file.getInputStream(), ExcelUserCustomerlImport.class, listener)
                    .sheet(0).headRowNumber(1).doReadSync();
            if (list.size() == 0) {
                throw new BusinessException(BaseErrorConstant.EMPTY_EXCEL);
            }
            List<ExcelUserCustomerlImport> succeedList = listener.getSucceedList();
            succeedList.forEach(x -> {
                String custCode = x.getCustCode();
                String custId = x.getCustId();
                String userId = x.getUserId();
                List<ShopUserUpdateDO> shopCustomerInfoList = shopCustomerInfoMapperExt.getShopCustomerInfoList(custCode, custId);
                List<ShopUserUpdateDO> shopCustomerInfoHistoryList = shopCustomerInfoMapperExt.getShopCustomerInfoHistoryList(custCode, custId);
                if (CollectionUtils.isNotEmpty(shopCustomerInfoList)) {
                    shopCustomerInfoList.forEach(y -> {
                        String id = y.getId();
                        ShopCustomerInfo shopCustomerInfo = new ShopCustomerInfo();
                        shopCustomerInfo.setId(id);
                        // shopCustomerInfo.setCustCode(custCode);
                        shopCustomerInfo.setUserId(userId);
                        shopCustomerInfoMapper.updateByPrimaryKeySelective(shopCustomerInfo);
                    });
                }
                if (CollectionUtils.isNotEmpty(shopCustomerInfoHistoryList)) {
                    shopCustomerInfoHistoryList.forEach(y -> {
                        String id = y.getId();
                        ShopCustomerInfoHistory shopCustomerInfoHistory = new ShopCustomerInfoHistory();
                        shopCustomerInfoHistory.setId(id);
                        // shopCustomerInfoHistory.setCustCode(custCode);
                        shopCustomerInfoHistory.setUserId(userId);
                        shopCustomerInfoHistoryMapper.updateByPrimaryKeySelective(shopCustomerInfoHistory);
                    });
                }

            });
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sftpOperationAccountManagerDataBack(String fileNameParam) {
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("解析商城客户经理信息FTP信息，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpAccountManagerData);
        try {
            if (!sftpUtil.login()) {
                log.info("FTP登录失败！");
                return;
            }
            log.info("FTP登录成功！");
            Vector files = sftpUtil.listFiles(sftpAccountManagerData);
            if (files == null || files.isEmpty()) {
                return;
            }
            List<String> list = new ArrayList<>();
            for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext(); ) {
                ChannelSftp.LsEntry str = it.next();
                String filename = str.getFilename();
                list.add(filename);
            }
            log.info("当前文件夹下所有要解析文件名称 fileNameManagerList：{}", list);

            for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext(); ) {
                ChannelSftp.LsEntry str = it.next();
                String filename = str.getFilename();
                if (filename.equals(".") || filename.equals("..") || !filename.contains(fileNameParam)) {
                    continue;
                }
                log.info("开始下载文件原名:{}", filename);
                InputStream is = sftpUtil.getInputStream(sftpAccountManagerData, filename);
             /*   ClassPathResource pathResource = new ClassPathResource("excel/accountManagerInformation_20230524_0001.txt");
            String filename = pathResource.getFilename();
            InputStream is = pathResource.getInputStream();*/

                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String readStr;
                List<ShopManagerInfo> shopManagerInfosAdd = new ArrayList<>();
                List<ShopManagerInfo> shopManagerInfosUpdate = new ArrayList<>();
                List<ShopManagerInfoHistory> shopManagerInfoHistories = new ArrayList<>();

                List<ShopManagerInfo> shopManagerInfosDelete = new ArrayList<>();
                List<ShopManagerInfoHistory> shopManagerInfoHistoriesDelete = new ArrayList<>();

                List<UserMiniProgram> userMiniProgramsAdd = new ArrayList<>();
                List<UserMiniProgram> userMiniProgramsUpdate = new ArrayList<>();

                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
                Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
                //Date date = new Date();
                //获取文件名里时间 作为数据创建时间
                String dateStr = StringUtils.substringBetween(filename, "_");
                Date updateDate = DateUtils.strToDate(dateStr, DATE_FORMAT_NO_SYMBOL);
                Date date = DateTimeUtil.addDay(updateDate, -1);
                boolean success = true;
                while ((readStr = br.readLine()) != null) {
                    // log.info("解析到的客户经理数据：{}",readStr);
                    String[] parts = readStr.split("\\|", -1);
                    if (parts.length != 13) {
                        if (parts.length == 1) {
                            continue;
                        }
                        log.info("formatException 该条记录格式异常，无法解析,内容:{}", readStr);
                        success = false;
                        break;
                    }
                    ShopManagerInfo shopManagerInfo = new ShopManagerInfo();
                    ShopManagerInfoHistory shopManagerInfoHistory = new ShopManagerInfoHistory();
                    String oprType = parts[5];
                    String beId = parts[7];
                    //如果省份名称没有匹配到直接跳出当前
                    String provinceName = (String) provinceCodeNameMap.get(beId);
                    if (StringUtils.isEmpty(provinceName)) {
                        log.info("provinceNotMatch 当前数据省份未匹配到，不保存入库：{}", readStr);
                        continue;
                    }
                    String location = parts[8];
                    String regionID = parts[9];
                    String mrgStatus = parts[10];
                    shopManagerInfoHistory.setId(BaseServiceUtils.getId());
                    shopManagerInfoHistory.setCreateOperCode(parts[0]);
                    shopManagerInfoHistory.setCreateOperPhone(parts[1]);
                    shopManagerInfoHistory.setUserId(parts[2]);
                    shopManagerInfoHistory.setCustomerManagerName(parts[3]);
                    shopManagerInfoHistory.setEmployeeNum(parts[4]);
                    shopManagerInfoHistory.setOprType(oprType);
                    try {
                        shopManagerInfoHistory.setCustStatusTime(orderStatusSDF.parse(parts[6]));
                    } catch (ParseException e) {
                        log.info("日期格式异常");
                        success = false;
                        break;
                    }
                    shopManagerInfoHistory.setBeId(beId);
                    shopManagerInfoHistory.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                    shopManagerInfoHistory.setLocation(location);
                    shopManagerInfoHistory.setCityName((String) locationCodeNameMap.get(location));
                    //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                    if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                        shopManagerInfoHistory.setRegionId((String) regionNameCodeMap.get("綦江区"));
                        shopManagerInfoHistory.setRegionName("綦江区");
                    } else {
                        shopManagerInfoHistory.setRegionId(regionID);
                        shopManagerInfoHistory.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                    }
                    shopManagerInfoHistory.setMrgStatus(parts[10]);
                    shopManagerInfoHistory.setMrgInvitationRegisterSuccessfulQuantity(StringUtils.isNotEmpty(parts[11]) ? Integer.parseInt(parts[11]) : 0);
                    shopManagerInfoHistory.setNumberLogins(StringUtils.isNotEmpty(parts[12]) ? Integer.parseInt(parts[12]) : 0);
                    shopManagerInfoHistory.setCreateTime(date);
                    if (ADD_SHOP_CLIENT.equals(oprType)) {
                        //更新后以
                        ShopManagerInfoExample example = new ShopManagerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                        List<ShopManagerInfo> shopManagerInfosList = shopManagerInfoMapper.selectByExample(example);
                        if (CollectionUtils.isNotEmpty(shopManagerInfosList)) {
                            //产品需求，os存在，商城新增做清除操作进行在进行新增
                            ShopManagerInfoHistoryExample historyExample = new ShopManagerInfoHistoryExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                            shopManagerInfoMapper.deleteByExample(example);
                            shopManagerInfoHistoryMapper.deleteByExample(historyExample);
                            log.info("deleteExist os存在，商城新增清除在新增,内容:{}", readStr);
                         /*   log.info("formatException 该条记录格式异常，新增的客户经理数据已同步,内容:{}", readStr);
                            success = false;
                            break;*/
                        }
                        shopManagerInfo.setId(BaseServiceUtils.getId());
                        shopManagerInfo.setCreateOperCode(parts[0]);
                        shopManagerInfo.setCreateOperPhone(parts[1]);
                        shopManagerInfo.setUserId(parts[2]);
                        shopManagerInfo.setCustomerManagerName(parts[3]);
                        shopManagerInfo.setEmployeeNum(parts[4]);
                        try {
                            shopManagerInfo.setRegisterDate(orderStatusSDF.parse(parts[6]));
                        } catch (ParseException e) {
                            log.info("日期格式异常");
                            success = false;
                            break;
                        }
                        shopManagerInfo.setBeId(beId);
                        shopManagerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                        shopManagerInfo.setLocation(location);
                        shopManagerInfo.setCityName((String) locationCodeNameMap.get(location));
                        //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                        if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                            shopManagerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
                            shopManagerInfo.setRegionName("綦江区");
                        } else {
                            shopManagerInfo.setRegionId(regionID);
                            shopManagerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                        }
                        shopManagerInfo.setMrgStatus(parts[10]);
                        shopManagerInfo.setIsRinse(false);
                        shopManagerInfo.setIsMerchant(false);
                        shopManagerInfo.setCreateTime(date);
                        shopManagerInfo.setUpdateTime(date);
                        shopManagerInfoHistories.add(shopManagerInfoHistory);
                        shopManagerInfosAdd.add(shopManagerInfo);

                        UserMiniProgram userMiniProgram = convertFromShopManagerInfo(shopManagerInfo);
                        userMiniProgram.setId(BaseServiceUtils.getId());
                        userMiniProgramsAdd.add(userMiniProgram);
                        userMiniProgramMapper.deleteByExample(
                                new UserMiniProgramExample().createCriteria()
                                        .andUserIdEqualTo(shopManagerInfo.getUserId())
                                        .example()
                        );
                    } else if (UPDATE_SHOP_CLIENT.equals(oprType)) {
                        if ("3".equals(mrgStatus)) {
                            //  判断该条数据状态是否是失效注销，失效删除历史表和信息表，新增现在来的数据。
                            log.info("updateDeleteManager删除的注销客户经理用户编码：{}", parts[0]);
                            ShopManagerInfoExample example = new ShopManagerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                            ShopManagerInfoHistoryExample exampleHistory = new ShopManagerInfoHistoryExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                            List<ShopManagerInfo> shopManagerInfos = shopManagerInfoMapper.selectByExample(example);
                            List<ShopManagerInfoHistory> shopManagerInfoHistoriesEntity = shopManagerInfoHistoryMapper.selectByExample(exampleHistory);
                            if (CollectionUtils.isNotEmpty(shopManagerInfos)) {
                                shopManagerInfosDelete.add(shopManagerInfos.get(0));
                            }
                            if (CollectionUtils.isNotEmpty(shopManagerInfoHistoriesEntity)) {
                                shopManagerInfoHistoriesDelete.addAll(shopManagerInfoHistoriesEntity);

                            }
                            continue;
                        }
                        ShopManagerInfoExample example = new ShopManagerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                        List<ShopManagerInfo> shopManagerInfosList = shopManagerInfoMapper.selectByExample(example);
                        if (CollectionUtils.isEmpty(shopManagerInfosList)) {
                            //商城有os侧没有  商城传修改，os侧需进行处理对应正常入库
                            setDataShopManagerInfo(parts, shopManagerInfo, date, shopManagerInfosAdd);
                            //单独新增一条是新增操作的历史记录  先查询一下有就不管，没有就新增
                            ShopManagerInfoHistoryExample exampleHistory = new ShopManagerInfoHistoryExample().createCriteria().andUserIdEqualTo(parts[2])
                                    .andOprTypeEqualTo("1").example();
                            List<ShopManagerInfoHistory> shopManagerInfoHistoriesEn = shopManagerInfoHistoryMapper.selectByExample(exampleHistory);
                            if (CollectionUtil.isEmpty(shopManagerInfoHistoriesEn)) {
                                ShopManagerInfoHistory shopManagerInfoHistoryAdd = new ShopManagerInfoHistory();
                                BeanUtils.copyProperties(shopManagerInfoHistory, shopManagerInfoHistoryAdd);
                                shopManagerInfoHistoryAdd.setId(BaseServiceUtils.getId());
                                shopManagerInfoHistoryAdd.setOprType("1");
                                shopManagerInfoHistoryAdd.setMrgInvitationRegisterSuccessfulQuantity(0);
                                shopManagerInfoHistoryAdd.setNumberLogins(0);
                                shopManagerInfoHistories.add(shopManagerInfoHistoryAdd);
                            }
                            shopManagerInfoHistories.add(shopManagerInfoHistory);

                        /*    log.info("formatException 该条记录格式异常，修改的客户经理数据未查询到,内容:{}", readStr);
                            success = false;
                            break;*/
                        } else {
                            shopManagerInfo = shopManagerInfosList.get(0);
                            shopManagerInfo.setCreateOperCode(parts[0]);
                            shopManagerInfo.setCreateOperPhone(parts[1]);
                            shopManagerInfo.setUserId(parts[2]);
                            shopManagerInfo.setCustomerManagerName(parts[3]);
                            shopManagerInfo.setEmployeeNum(parts[4]);
                            shopManagerInfo.setBeId(beId);
                            shopManagerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                            shopManagerInfo.setLocation(location);
                            shopManagerInfo.setCityName((String) locationCodeNameMap.get(location));
                            //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                            if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                                shopManagerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
                                shopManagerInfo.setRegionName("綦江区");
                            } else {
                                shopManagerInfo.setRegionId(regionID);
                                shopManagerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                            }
                            shopManagerInfo.setMrgStatus(parts[10]);
                            shopManagerInfo.setUpdateTime(date);
                            shopManagerInfoHistories.add(shopManagerInfoHistory);
                            shopManagerInfosUpdate.add(shopManagerInfo);
                        }
                        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(
                                new UserMiniProgramExample().createCriteria()
                                        .andUserIdEqualTo(shopManagerInfo.getUserId())
                                        .example()
                        );
                        if (CollectionUtils.isNotEmpty(userMiniPrograms)) {
                            UserMiniProgram userMiniProgram = convertFromShopManagerInfo(shopManagerInfo);
                            UserMiniProgram savedUser = userMiniPrograms.get(0);
                            userMiniProgram.setId(savedUser.getId());
                            userMiniProgramsUpdate.add(userMiniProgram);
                        }
                    } else {
                        log.info("formatException 该条记录格式异常操作类型错误，无法解析,内容:{}", readStr);
                        success = false;
                        break;
                    }
                }
                if (success) {
                    log.info("downloadManagerInfo 开始批量插入商城客户经理文件：{},客户经理数据数量:{}", filename, shopManagerInfoHistories.size());
                    if (CollectionUtils.isNotEmpty(shopManagerInfoHistories)) {
                        new BatchHandlerUtil<ShopManagerInfoHistory>().batchResolve(shopManagerInfoHistories, 1000,
                                items -> shopManagerInfoHistoryMapper.batchInsert(items));
                    }
                    if (CollectionUtils.isNotEmpty(shopManagerInfosAdd)) {
                        new BatchHandlerUtil<ShopManagerInfo>().batchResolve(shopManagerInfosAdd, 1000,
                                items -> shopManagerInfoMapper.batchInsert(items));
                    }
                    if (CollectionUtils.isNotEmpty(shopManagerInfosUpdate)) {
                        for (ShopManagerInfo shopManagerInfo : shopManagerInfosUpdate) {
                            shopManagerInfoMapper.updateByPrimaryKeySelective(shopManagerInfo);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(shopManagerInfosDelete)) {
                        shopManagerInfosDelete.forEach(shopManagerInfo -> {
                            shopManagerInfoMapper.deleteByPrimaryKey(shopManagerInfo.getId());
                        });
                    }
                    if (CollectionUtils.isNotEmpty(shopManagerInfoHistoriesDelete)) {
                        shopManagerInfoHistoriesDelete.forEach(shopManagerInfoEn -> {
                            shopManagerInfoHistoryMapper.deleteByPrimaryKey(shopManagerInfoEn.getId());
                        });
                    }
                    if (CollectionUtils.isNotEmpty(userMiniProgramsAdd)) {
                        new BatchHandlerUtil<UserMiniProgram>().batchResolve(userMiniProgramsAdd, 1000,
                                items -> userMiniProgramMapper.batchInsert(items));
                    }
                    if (CollectionUtils.isNotEmpty(userMiniProgramsUpdate)) {
                        for (UserMiniProgram userMiniProgram : userMiniProgramsUpdate) {
                            userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
                            redisTemplate.delete(Constant.REDIS_KEY_MINI_USER + userMiniProgram.getId());
                        }
                    }
                    log.info("deleteFile删除文件:{}", filename);
                    sftpUtil.delete(sftpAccountManagerData, filename);
                    log.info("deleteFile删除成功");
                }
                br.close();
            }
        } catch (Exception e) {
            log.warn("拉取商城客户经理发生异常:{}", e.toString());
        } finally {
            sftpUtil.logout();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sftpOperationCustomerDataBack(String fileNameParam) {
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("解析商城分销及注册用户信息FTP信息，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpCustomerData);
        try {
            if (!sftpUtil.login()) {
                log.info("FTP登录失败！");
                return;
            }
            log.info("FTP登录成功！");
            Vector files = sftpUtil.listFiles(sftpCustomerData);
            if (files == null || files.isEmpty()) {
                return;
            }
            List<String> list = new ArrayList<>();
            for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext(); ) {
                ChannelSftp.LsEntry str = it.next();
                String filename = str.getFilename();
                list.add(filename);
            }
            log.info("当前文件夹下所有要解析文件名称 fileNameCustomerList：{}", list);

            for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext(); ) {
                ChannelSftp.LsEntry str = it.next();
                String filename = str.getFilename();
                if (filename.equals(".") || filename.equals("..") || !filename.contains(fileNameParam)) {
                    continue;
                }
                log.info("开始下载文件原名:{}", filename);
                InputStream is = sftpUtil.getInputStream(sftpCustomerData, filename);
                log.info("InputStream流数据：{}", is);
               /* ClassPathResource pathResource = new ClassPathResource("excel/accountManagerInformation_20230524_0001.txt");
               pathResource.getFilename();
                InputStream is = pathResource.getInputStream();*/

                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                log.info("BufferedReader流数据：{}", br);
                List<ShopCustomerInfo> shopCustomerInfosAdd = new ArrayList<>();
                List<ShopCustomerInfo> shopCustomerInfosUpdate = new ArrayList<>();
                List<ShopCustomerInfoHistory> shopCustomerInfoHistories = new ArrayList<>();

                List<ShopCustomerInfo> shopCustomerInfosDelete = new ArrayList<>();
                List<ShopCustomerInfoHistory> shopCustomerInfoHistoriesDelete = new ArrayList<>();

                List<UserMiniProgram> userMiniProgramsAdd = new ArrayList<>();
                List<UserMiniProgram> userMiniProgramsUpdate = new ArrayList<>();

                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
                Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
                String readStr;
                //Date date = new Date();
                //获取文件名里时间 作为数据创建时间
                String dateStr = StringUtils.substringBetween(filename, "_");
                Date updateDate = DateUtils.strToDate(dateStr, DATE_FORMAT_NO_SYMBOL);
                Date date = DateTimeUtil.addDay(updateDate, -1);
                boolean success = true;
                log.info("startExecution开始执行解析：{}", dateStr);
                while ((readStr = br.readLine()) != null) {
                    // log.info("解析到的分销及注册用户数据：{}", readStr);
                    String[] parts = readStr.split("\\|", -1);
                    String roleType = parts[6];
                    String oprType = parts[4];
                    String beId = parts[7];
                    //如果省份名称没有匹配到直接跳出当前
                    String provinceName = (String) provinceCodeNameMap.get(beId);
                    if (StringUtils.isEmpty(provinceName)) {
                        log.info("provinceNotMatch 当前数据省份未匹配到，不保存入库：{}", readStr);
                        continue;
                    }
                    String location = parts[8];
                    String regionID = parts[9];
                    String ClientStatus = parts[11];
                    //普通用户
                    if (parts.length != 22) {
                        if (parts.length == 1) {
                            continue;
                        }
                        log.info("formatException 该条记录格式异常，无法解析,内容:{}", readStr);
                        success = false;
                        break;
                    }
                    ShopCustomerInfo shopCustomerInfo = new ShopCustomerInfo();
                    ShopCustomerInfoHistory shopCustomerInfoHistory = new ShopCustomerInfoHistory();
                    shopCustomerInfoHistory.setId(BaseServiceUtils.getId());
                    if (StringUtils.isEmpty(parts[0])) {
                        log.info("formatException 该条记录格式异常，客户编码为空无法解析,内容:{}", readStr);
                        success = false;
                        break;
                    }
                    shopCustomerInfoHistory.setCustCode(StringUtils.isNotEmpty(parts[0]) ? parts[0] : null);
                    shopCustomerInfoHistory.setCustId(parts[1]);
                    shopCustomerInfoHistory.setUserId(StringUtils.isNotEmpty(parts[2]) ? parts[2] : null);
                    shopCustomerInfoHistory.setCustName(StringUtils.isNotEmpty(parts[3]) ? parts[3] : null);
                    shopCustomerInfoHistory.setOprType(oprType);
                    shopCustomerInfoHistory.setCustStatusTime(orderStatusSDF.parse(parts[5]));
                    shopCustomerInfoHistory.setRoleType(parts[6]);
                    shopCustomerInfoHistory.setBeId(beId);
                    shopCustomerInfoHistory.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                    shopCustomerInfoHistory.setLocation(location);
                    shopCustomerInfoHistory.setCityName((String) locationCodeNameMap.get(location));
                    //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                    if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                        shopCustomerInfoHistory.setRegionId((String) regionNameCodeMap.get("綦江区"));
                        shopCustomerInfoHistory.setRegionName("綦江区");
                    } else {
                        shopCustomerInfoHistory.setRegionId(regionID);
                        shopCustomerInfoHistory.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                    }
                    shopCustomerInfoHistory.setClientRegister(StringUtils.isNotEmpty(parts[10]) ? orderStatusSDF.parse(parts[10]) : null);
                    shopCustomerInfoHistory.setClientStatus(StringUtils.isNotEmpty(parts[11]) ? parts[11] : null);
                    shopCustomerInfoHistory.setNumberLogins(StringUtils.isNotEmpty(parts[12]) ? Integer.parseInt(parts[12]) : 0);
                    shopCustomerInfoHistory.setDistributorName(StringUtils.isNotEmpty(parts[13]) ? parts[13] : null);
                    shopCustomerInfoHistory.setDistributorInvitationRegisterSuccessfulQuantity(StringUtils.isNotEmpty(parts[14]) ? Integer.parseInt(parts[14]) : null);
                    shopCustomerInfoHistory.setDistributorChannelId(StringUtils.isNotEmpty(parts[15]) ? parts[15] : null);
                    shopCustomerInfoHistory.setDistributorChannelName(StringUtils.isNotEmpty(parts[16]) ? parts[16] : null);
                    shopCustomerInfoHistory.setDistributorReferralCode(StringUtils.isNotEmpty(parts[17]) ? parts[17] : null);
                    shopCustomerInfoHistory.setDistributorMrgInf(StringUtils.isNotEmpty(parts[18]) ? parts[18] : null);
                    shopCustomerInfoHistory.setDistributorMrgCode(StringUtils.isNotEmpty(parts[19]) ? parts[19] : null);
                    shopCustomerInfoHistory.setAgentNumber(StringUtils.isNotEmpty(parts[20]) ? parts[20] : null);
                    shopCustomerInfoHistory.setAgentName(StringUtils.isNotEmpty(parts[21]) ? parts[21] : null);
                    shopCustomerInfoHistory.setCreateTime(date);
                    if (ADD_SHOP_CLIENT.equals(oprType)) {
                        ShopCustomerInfoExample example = new ShopCustomerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                        List<ShopCustomerInfo> shopCustomerInfoList = shopCustomerInfoMapper.selectByExample(example);
                        if (CollectionUtils.isNotEmpty(shopCustomerInfoList)) {
                            //产品需求，os存在，商城新增做清除操作进行在进行新增
                            ShopCustomerInfoHistoryExample historyExample = new ShopCustomerInfoHistoryExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                            shopCustomerInfoMapper.deleteByExample(example);
                            shopCustomerInfoHistoryMapper.deleteByExample(historyExample);
                            log.info("deleteExist os存在，商城新增清除在新增,内容:{}", readStr);
                         /*   log.info("formatException 该条记录格式异常，新增的客户经理数据已同步,内容:{}", readStr);
                            success = false;
                            break;*/
                        }
                        shopCustomerInfo.setId(BaseServiceUtils.getId());
                        shopCustomerInfo.setCustCode(StringUtils.isNotEmpty(parts[0]) ? parts[0] : null);
                        shopCustomerInfo.setCustId(parts[1]);
                        shopCustomerInfo.setUserId(StringUtils.isNotEmpty(parts[2]) ? parts[2] : null);
                        shopCustomerInfo.setCustName(StringUtils.isNotEmpty(parts[3]) ? parts[3] : null);
                        //shopCustomerInfo.setOprType(oprType);
                        shopCustomerInfo.setRegisterDate(orderStatusSDF.parse(parts[5]));
                        shopCustomerInfo.setRoleType(parts[6]);
                        shopCustomerInfo.setBeId(beId);
                        shopCustomerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                        shopCustomerInfo.setLocation(location);
                        shopCustomerInfo.setCityName((String) locationCodeNameMap.get(location));
                        //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                        if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                            shopCustomerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
                            shopCustomerInfo.setRegionName("綦江区");
                        } else {
                            shopCustomerInfo.setRegionId(regionID);
                            shopCustomerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                        }
                        shopCustomerInfo.setClientStatus(StringUtils.isNotEmpty(parts[11]) ? parts[11] : null);
                        shopCustomerInfo.setDistributorName(StringUtils.isNotEmpty(parts[13]) ? parts[13] : null);
                        shopCustomerInfo.setDistributorChannelId(StringUtils.isNotEmpty(parts[15]) ? parts[15] : null);
                        shopCustomerInfo.setDistributorChannelName(StringUtils.isNotEmpty(parts[16]) ? parts[16] : null);
                        shopCustomerInfo.setDistributorReferralCode(StringUtils.isNotEmpty(parts[17]) ? parts[17] : null);
                        shopCustomerInfo.setDistributorMrgInf(StringUtils.isNotEmpty(parts[18]) ? parts[18] : null);
                        shopCustomerInfo.setDistributorMrgCode(StringUtils.isNotEmpty(parts[19]) ? parts[19] : null);
                        shopCustomerInfo.setAgentNumber(StringUtils.isNotEmpty(parts[20]) ? parts[20] : null);
                        shopCustomerInfo.setAgentName(StringUtils.isNotEmpty(parts[21]) ? parts[21] : null);
                        shopCustomerInfo.setIsRinse(false);
                        shopCustomerInfo.setCreateTime(date);
                        shopCustomerInfo.setUpdateTime(date);
                        shopCustomerInfoHistories.add(shopCustomerInfoHistory);
                        shopCustomerInfosAdd.add(shopCustomerInfo);

                        UserMiniProgram userMiniProgram = convertFromShopCustomerInfo(shopCustomerInfo);
                        userMiniProgram.setId(BaseServiceUtils.getId());
                        userMiniProgramsAdd.add(userMiniProgram);
                        userMiniProgramMapper.deleteByExample(
                                new UserMiniProgramExample().createCriteria()
                                        .andUserIdEqualTo(shopCustomerInfo.getUserId())
                                        .example()
                        );
                    } else if (UPDATE_SHOP_CLIENT.equals(oprType)) {
                        if (StringUtils.isEmpty(parts[0])) {
                            log.info("formatException 该条记录格式异常，客户编码为空无法解析,内容:{}", readStr);
                            success = false;
                            break;
                        }
                        if ("3".equals(ClientStatus)) {
                            //  判断该条数据状态是否是失效，失效删除历史表和信息表。  更新后以userid唯一
                            log.info("updateDeleteCustomer删除的注销用户编码：{}，{},{}", parts[0], parts[1], parts[2]);
                            ShopCustomerInfoExample example = new ShopCustomerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                            ShopCustomerInfoHistoryExample infoHistoryExample = new ShopCustomerInfoHistoryExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                            List<ShopCustomerInfo> shopCustomerInfos = shopCustomerInfoMapper.selectByExample(example);
                            List<ShopCustomerInfoHistory> shopCustomerInfoHistoriesList = shopCustomerInfoHistoryMapper.selectByExample(infoHistoryExample);
                            if (CollectionUtils.isNotEmpty(shopCustomerInfos)) {
                                shopCustomerInfosDelete.add(shopCustomerInfos.get(0));
                            }
                            if (CollectionUtils.isNotEmpty(shopCustomerInfoHistoriesList)) {
                                shopCustomerInfoHistoriesDelete.addAll(shopCustomerInfoHistoriesList);
                            }
                            continue;
                        }
                        ShopCustomerInfoExample example = new ShopCustomerInfoExample().createCriteria().andUserIdEqualTo(parts[2]).example();
                        List<ShopCustomerInfo> shopCustomerInfoList = shopCustomerInfoMapper.selectByExample(example);
                        if (CollectionUtils.isEmpty(shopCustomerInfoList)) {
                            //商城有os侧没有  商城传修改，os侧需进行处理对应正常入库
                            setDataShopCustomerInfo(parts, shopCustomerInfo, date, shopCustomerInfosAdd);
                            //单独新增一条是新增操作的历史记录  先查询一下有就不管，没有就新增
                            ShopCustomerInfoHistoryExample exampleHistory = new ShopCustomerInfoHistoryExample().createCriteria().andUserIdEqualTo(parts[2])
                                    .andOprTypeEqualTo("1").example();
                            List<ShopCustomerInfoHistory> shopCustomerInfoHistoriesEn = shopCustomerInfoHistoryMapper.selectByExample(exampleHistory);
                            if (CollectionUtil.isEmpty(shopCustomerInfoHistoriesEn)) {
                                ShopCustomerInfoHistory shopCustomerInfoHistoryAdd = new ShopCustomerInfoHistory();
                                BeanUtils.copyProperties(shopCustomerInfoHistory, shopCustomerInfoHistoryAdd);
                                shopCustomerInfoHistoryAdd.setId(BaseServiceUtils.getId());
                                shopCustomerInfoHistoryAdd.setNumberLogins(0);
                                shopCustomerInfoHistoryAdd.setDistributorInvitationRegisterSuccessfulQuantity(0);
                                shopCustomerInfoHistoryAdd.setOprType("1");
                                shopCustomerInfoHistories.add(shopCustomerInfoHistoryAdd);
                            }
                            shopCustomerInfoHistories.add(shopCustomerInfoHistory);

                        /*    log.info("formatException 该条记录格式异常，修改的客户经理数据未查询到,内容:{}", readStr);
                            success = false;
                            break;*/
                        } else {
                            shopCustomerInfo = shopCustomerInfoList.get(0);
                            shopCustomerInfo.setCustCode(StringUtils.isNotEmpty(parts[0]) ? parts[0] : null);
                            shopCustomerInfo.setCustId(parts[1]);
                            shopCustomerInfo.setUserId(StringUtils.isNotEmpty(parts[2]) ? parts[2] : null);
                            shopCustomerInfo.setCustName(StringUtils.isNotEmpty(parts[3]) ? parts[3] : null);
                            //shopCustomerInfo.setOprType(oprType);
                            //shopCustomerInfo.setRegisterDate(orderStatusSDF.parse(parts[4]));
                            shopCustomerInfo.setRoleType(parts[6]);
                            shopCustomerInfo.setBeId(beId);
                            shopCustomerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
                            shopCustomerInfo.setLocation(location);
                            shopCustomerInfo.setCityName((String) locationCodeNameMap.get(location));
                            //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
                            if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                                shopCustomerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
                                shopCustomerInfo.setRegionName("綦江区");
                            } else {
                                shopCustomerInfo.setRegionId(regionID);
                                shopCustomerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                            }
                            shopCustomerInfo.setClientStatus(StringUtils.isNotEmpty(parts[11]) ? parts[11] : null);
                            shopCustomerInfo.setDistributorName(StringUtils.isNotEmpty(parts[13]) ? parts[13] : null);
                            shopCustomerInfo.setDistributorChannelId(StringUtils.isNotEmpty(parts[15]) ? parts[15] : null);
                            shopCustomerInfo.setDistributorChannelName(StringUtils.isNotEmpty(parts[16]) ? parts[16] : null);
                            shopCustomerInfo.setDistributorReferralCode(StringUtils.isNotEmpty(parts[17]) ? parts[17] : null);
                            shopCustomerInfo.setDistributorMrgInf(StringUtils.isNotEmpty(parts[18]) ? parts[18] : null);
                            shopCustomerInfo.setDistributorMrgCode(StringUtils.isNotEmpty(parts[19]) ? parts[19] : null);
                            shopCustomerInfo.setAgentNumber(StringUtils.isNotEmpty(parts[20]) ? parts[20] : null);
                            shopCustomerInfo.setAgentName(StringUtils.isNotEmpty(parts[21]) ? parts[21] : null);
                            shopCustomerInfo.setUpdateTime(date);
                            shopCustomerInfoHistories.add(shopCustomerInfoHistory);
                            shopCustomerInfosUpdate.add(shopCustomerInfo);
                        }
                        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(
                                new UserMiniProgramExample().createCriteria()
                                        .andUserIdEqualTo(shopCustomerInfo.getUserId())
                                        .example()
                        );
                        if (CollectionUtils.isNotEmpty(userMiniPrograms)) {
                            UserMiniProgram userMiniProgram = convertFromShopCustomerInfo(shopCustomerInfo);
                            UserMiniProgram savedUser = userMiniPrograms.get(0);
                            userMiniProgram.setId(savedUser.getId());
                            userMiniProgramsUpdate.add(userMiniProgram);
                        }
                    } else {
                        log.info("formatException 该条记录格式异常操作类型错误，无法解析,内容:{}", readStr);
                        success = false;
                        break;
                    }
                }
                log.info("endExecution 解析完成：{}", success);
                if (success) {
                    log.info("downloadCustomerInfo 开始批量插入分销及普通用户文件：{},分销及普通用户数据数量:{}", filename, shopCustomerInfoHistories.size());
                    if (CollectionUtils.isNotEmpty(shopCustomerInfoHistories)) {
                        new BatchHandlerUtil<ShopCustomerInfoHistory>().batchResolve(shopCustomerInfoHistories, 1000,
                                items -> shopCustomerInfoHistoryMapper.batchInsert(items));
                    }
                    if (CollectionUtils.isNotEmpty(shopCustomerInfosAdd)) {
                        new BatchHandlerUtil<ShopCustomerInfo>().batchResolve(shopCustomerInfosAdd, 1000,
                                items -> shopCustomerInfoMapper.batchInsert(items));
                    }
                    if (CollectionUtils.isNotEmpty(shopCustomerInfosUpdate)) {
                        for (ShopCustomerInfo shopCustomerInfo : shopCustomerInfosUpdate) {
                            shopCustomerInfoMapper.updateByPrimaryKeySelective(shopCustomerInfo);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(shopCustomerInfosDelete)) {
                        for (ShopCustomerInfo shopCustomerInfo : shopCustomerInfosDelete) {
                            shopCustomerInfoMapper.deleteByPrimaryKey(shopCustomerInfo.getId());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(shopCustomerInfoHistoriesDelete)) {
                        for (ShopCustomerInfoHistory shopCustomerInfoHistory : shopCustomerInfoHistoriesDelete) {
                            shopCustomerInfoHistoryMapper.deleteByPrimaryKey(shopCustomerInfoHistory.getId());
                        }
                    }

                    if (CollectionUtils.isNotEmpty(userMiniProgramsAdd)) {
                        new BatchHandlerUtil<UserMiniProgram>().batchResolve(userMiniProgramsAdd, 1000,
                                items -> userMiniProgramMapper.batchInsert(items));
                    }
                    if (CollectionUtils.isNotEmpty(userMiniProgramsUpdate)) {
                        for (UserMiniProgram userMiniProgram : userMiniProgramsUpdate) {
                            userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
                            redisTemplate.delete(Constant.REDIS_KEY_MINI_USER + userMiniProgram.getId());
                        }
                    }
                    log.info("deleteFile删除文件:{}", filename);
                    sftpUtil.delete(sftpCustomerData, filename);
                    log.info("deleteFile删除成功");
                }
                br.close();
            }
        } catch (Exception e) {
            log.warn("拉取商城分销及注册用发生异常:{}", e.toString());
            throw new BusinessException(e);
        } finally {
            sftpUtil.logout();
        }
    }


    private UserMiniProgram convertFromShopManagerInfo(ShopManagerInfo info) {
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        BeanUtils.copyProperties(info, userMiniProgram);
        userMiniProgram.setUserId(info.getUserId());
        userMiniProgram.setCode(info.getCreateOperCode());
        userMiniProgram.setName(info.getCustomerManagerName());
        userMiniProgram.setPhone(info.getCreateOperPhone());
        userMiniProgram.setRoleType("4");
        userMiniProgram.setStatus("1".equals(info.getMrgStatus()) ? "1" : "0");
        userMiniProgram.setCreateTime(info.getCreateTime());
        userMiniProgram.setUpdateTime(info.getUpdateTime());
        return userMiniProgram;
    }

    private UserMiniProgram convertFromShopCustomerInfo(ShopCustomerInfo info) {
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        BeanUtils.copyProperties(info, userMiniProgram);
        userMiniProgram.setUserId(info.getUserId());
        userMiniProgram.setCode(info.getCustCode());
        userMiniProgram.setName(info.getCustName());
        userMiniProgram.setPhone(info.getCustId());
        userMiniProgram.setRoleType(info.getRoleType());
        userMiniProgram.setStatus("1".equals(info.getClientStatus()) ? "1" : "0");
        userMiniProgram.setCreateTime(info.getCreateTime());
        userMiniProgram.setUpdateTime(info.getUpdateTime());
        return userMiniProgram;
    }


    /**
     * 对应os没有 商城修改的 直接进行客户经理新增
     *
     * @param parts
     * @param shopManagerInfo
     */
    private void setDataShopManagerInfo(String[] parts, ShopManagerInfo shopManagerInfo, Date date, List<ShopManagerInfo> shopManagerInfosAdd) {
        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
        Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
        String beId = parts[7];
        String location = parts[8];
        String regionID = parts[9];
        shopManagerInfo.setId(BaseServiceUtils.getId());
        shopManagerInfo.setCreateOperCode(parts[0]);
        shopManagerInfo.setCreateOperPhone(parts[1]);
        shopManagerInfo.setUserId(parts[2]);
        shopManagerInfo.setCustomerManagerName(parts[3]);
        shopManagerInfo.setEmployeeNum(parts[4]);
        try {
            shopManagerInfo.setRegisterDate(orderStatusSDF.parse(parts[6]));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        shopManagerInfo.setBeId(beId);
        shopManagerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
        shopManagerInfo.setLocation(location);
        shopManagerInfo.setCityName((String) locationCodeNameMap.get(location));
        //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
        if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
            shopManagerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
            shopManagerInfo.setRegionName("綦江区");
        } else {
            shopManagerInfo.setRegionId(regionID);
            shopManagerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
        }
        shopManagerInfo.setMrgStatus(parts[10]);
        shopManagerInfo.setIsRinse(false);
        shopManagerInfo.setIsMerchant(false);
        shopManagerInfo.setCreateTime(date);
        shopManagerInfo.setUpdateTime(date);
        shopManagerInfosAdd.add(shopManagerInfo);

    }

    /**
     * 对应os没有 商城修改的 直接进行普通用户新增
     *
     * @param parts
     * @param shopCustomerInfo
     * @param date
     * @param shopCustomerInfosAdd
     */
    private void setDataShopCustomerInfo(String[] parts, ShopCustomerInfo shopCustomerInfo, Date date, List<ShopCustomerInfo> shopCustomerInfosAdd) {
        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
        Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
        String beId = parts[7];
        String location = parts[8];
        String regionID = parts[9];
        String ClientStatus = parts[11];
        shopCustomerInfo.setId(BaseServiceUtils.getId());
        shopCustomerInfo.setCustCode(StringUtils.isNotEmpty(parts[0]) ? parts[0] : null);
        shopCustomerInfo.setCustId(parts[1]);
        shopCustomerInfo.setUserId(StringUtils.isNotEmpty(parts[2]) ? parts[2] : null);
        shopCustomerInfo.setCustName(StringUtils.isNotEmpty(parts[3]) ? parts[3] : null);
        //shopCustomerInfo.setOprType(oprType);
        try {
            shopCustomerInfo.setRegisterDate(orderStatusSDF.parse(parts[5]));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        shopCustomerInfo.setRoleType(parts[6]);
        shopCustomerInfo.setBeId(beId);
        shopCustomerInfo.setProvinceName("471".equals(beId) ? "内蒙古" : (String) provinceCodeNameMap.get(beId));
        shopCustomerInfo.setLocation(location);
        shopCustomerInfo.setCityName((String) locationCodeNameMap.get(location));
        //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
        if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
            shopCustomerInfo.setRegionId((String) regionNameCodeMap.get("綦江区"));
            shopCustomerInfo.setRegionName("綦江区");
        } else {
            shopCustomerInfo.setRegionId(regionID);
            shopCustomerInfo.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
        }
        shopCustomerInfo.setClientStatus(StringUtils.isNotEmpty(parts[11]) ? parts[11] : null);
        shopCustomerInfo.setDistributorName(StringUtils.isNotEmpty(parts[13]) ? parts[13] : null);
        shopCustomerInfo.setDistributorChannelId(StringUtils.isNotEmpty(parts[15]) ? parts[15] : null);
        shopCustomerInfo.setDistributorChannelName(StringUtils.isNotEmpty(parts[16]) ? parts[16] : null);
        shopCustomerInfo.setDistributorReferralCode(StringUtils.isNotEmpty(parts[17]) ? parts[17] : null);
        shopCustomerInfo.setDistributorMrgInf(StringUtils.isNotEmpty(parts[18]) ? parts[18] : null);
        shopCustomerInfo.setDistributorMrgCode(StringUtils.isNotEmpty(parts[19]) ? parts[19] : null);
        shopCustomerInfo.setAgentNumber(StringUtils.isNotEmpty(parts[20]) ? parts[20] : null);
        shopCustomerInfo.setAgentName(StringUtils.isNotEmpty(parts[21]) ? parts[21] : null);
        shopCustomerInfo.setIsRinse(false);
        shopCustomerInfo.setCreateTime(date);
        shopCustomerInfo.setUpdateTime(date);
        shopCustomerInfosAdd.add(shopCustomerInfo);
    }

    /**
     * 商城客户经理信息实时同步
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public IOTAnswer<Void> syncAccountManagerDataRealTime(IOTRequest baseRequest) {
        log.info("商城客户经理信息实时同步请求:{}", JSON.toJSONString(baseRequest));
        Date now = new Date();
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        ShopManagerInfoDTO shopManagerInfoDTO;
        Date date;
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        try {
            shopManagerInfoDTO = JSON.parseObject(baseRequest.getContent(), ShopManagerInfoDTO.class);
        } catch (Exception e) {
            log.error("商城客户经理信息解析异常:" + e);
            throw new IOTException(iotAnswer, "商城客户经理信息解析异常");
        }
        try {
            date = DateUtils.strToDate(shopManagerInfoDTO.getCustStatusTime(), "yyyyMMddhhmmss");
        } catch (Exception e) {
            log.error("商城客户经理信息时间转换异常:" + e);
            throw new IOTException(iotAnswer, "商城客户经理信息时间转换异常");
        }

        shopManagerInfoDTO2miniUser(shopManagerInfoDTO, userMiniProgram);

        if (ADD_SHOP_CLIENT.equals(shopManagerInfoDTO.getOprType())) {
            List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria()
                    .andUserIdEqualTo(userMiniProgram.getUserId()).example());
            if (CollectionUtils.isNotEmpty(userMiniPrograms)) {
                log.error("商城客户经理信息同步:客户经理{}已经存在，新增异常", userMiniProgram.getUserId());
                throw new IOTException(iotAnswer, "客户经理" + userMiniProgram.getUserId() + "已经存在，新增异常");
            }
            userMiniProgram.setId(BaseServiceUtils.getId());

            userMiniProgram.setCreateTime(date);
            userMiniProgram.setUpdateTime(date);
            userMiniProgramMapper.insertSelective(userMiniProgram);
            //用户事件埋点
            gioBurialPointService.sendH5Userregistration(userMiniProgram);
        } else if (UPDATE_SHOP_CLIENT.equals(shopManagerInfoDTO.getOprType())) {
            List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria()
                    .andUserIdEqualTo(userMiniProgram.getUserId()).example());
            if (CollectionUtils.isEmpty(userMiniPrograms)) {
                log.error("商城客户经理信息同步:客户经理{}不存在，修改异常", userMiniProgram.getUserId());
                throw new IOTException(iotAnswer, "客户经理" + userMiniProgram.getUserId() + "不存在，修改异常");
            }
            userMiniProgram.setUpdateTime(date);
            userMiniProgram.setId(userMiniPrograms.get(0).getId());
            userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
            redisTemplate.delete(Constant.REDIS_KEY_MINI_USER + userMiniProgram.getId());
        }
        //用户属性埋点
        gioBurialPointService.sendUserMsg(userMiniProgram);


        return iotAnswer;
    }

    /**
     * 商城客户信息（含普通客户及分销员、渠道商角色）实时同步
     */
    public boolean isRetailUser(String roleType) {
        return StringUtils.isNotEmpty(roleType)
                && (roleType.equals("1") || roleType.equals("2") || roleType.equals("3"));
    }

    public static String roleType2Name(String roleType) {
        if (roleType.equals("0")) {
            return "普通用户";
        } else if (roleType.equals("1")) {
            return "一级分销员";
        } else if (roleType.equals("2")) {
            return "二级分销员";
        } else if (roleType.equals("3")) {
            return "渠道商";
        } else {
            return "";
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public IOTAnswer<Void> syncCustomerDataRealTime(IOTRequest baseRequest) {
        log.info("商城客户信息（含普通客户及分销员、渠道商角色）实时同步:{}", JSON.toJSONString(baseRequest));
        Date now = new Date();
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        ShopCustomerInfoDTO shopCustomerInfoDTO;
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        Date date;
        try {
            shopCustomerInfoDTO = JSON.parseObject(baseRequest.getContent(), ShopCustomerInfoDTO.class);
        } catch (Exception e) {
            log.error("商城客户信息解析异常:" + e);
            throw new IOTException(iotAnswer, "数据解析异常");
        }
        try {
            date = DateUtils.strToDate(shopCustomerInfoDTO.getCustStatusTime(), "yyyyMMddhhmmss");
        } catch (Exception e) {
            log.error("商城客户信息时间转换异常:" + e);
            throw new IOTException(iotAnswer, "商城客户信息时间转换异常");
        }

        shopCustomerInfoDTO2miniUser(shopCustomerInfoDTO, userMiniProgram);
        if (ADD_SHOP_CLIENT.equals(shopCustomerInfoDTO.getOprType())) {
            List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria()
                    .andUserIdEqualTo(userMiniProgram.getUserId()).example());
            if (CollectionUtils.isNotEmpty(userMiniPrograms)) {
                log.error("商城客户信息同步:{}{}已经存在，新增异常", roleType2Name(shopCustomerInfoDTO.getRoleType()), userMiniProgram.getUserId());
                throw new IOTException(iotAnswer, roleType2Name(shopCustomerInfoDTO.getRoleType())
                        + userMiniProgram.getUserId() + "已经存在，新增异常");
            }
            userMiniProgram.setId(BaseServiceUtils.getId());

            userMiniProgram.setCreateTime(date);
            userMiniProgram.setUpdateTime(date);
            userMiniProgramMapper.insertSelective(userMiniProgram);
            if (isRetailUser(userMiniProgram.getRoleType())) {
                UserRetail userRetail = new UserRetail();
                shopCustomerInfoDTO2RetailUser(shopCustomerInfoDTO, userRetail);
                userRetailMapper.deleteByPrimaryKey(shopCustomerInfoDTO.getUserID());
                userRetail.setRegTime(date);
                userRetailMapper.insertSelective(userRetail);
            }
            //用户事件埋点
            gioBurialPointService.sendH5Userregistration(userMiniProgram);
        } else if (UPDATE_SHOP_CLIENT.equals(shopCustomerInfoDTO.getOprType())) {
            List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria()
                    .andUserIdEqualTo(userMiniProgram.getUserId()).example());
            String modify = "修改";
            if (CollectionUtils.isEmpty(userMiniPrograms)) {
                log.error("商城客户信息同步:{}{}不存在，{}异常", roleType2Name(userMiniPrograms.get(0).getRoleType()),
                        userMiniProgram.getUserId(), modify);
                throw new IOTException(iotAnswer, roleType2Name(userMiniPrograms.get(0).getRoleType())
                        + userMiniProgram.getUserId() + "不存在，" + modify + "异常");
            }
            userMiniProgram.setUpdateTime(date);
            userMiniProgram.setId(userMiniPrograms.get(0).getId());
            userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
            redisTemplate.delete(Constant.REDIS_KEY_MINI_USER + userMiniProgram.getId());
            List<UserRetail> userRetails = userRetailMapper.selectByExample(new UserRetailExample().createCriteria()
                    .andUserIdEqualTo(userMiniPrograms.get(0).getUserId()).example());
            userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userMiniPrograms.get(0).getId());
            if (isRetailUser(userMiniProgram.getRoleType())) {
                UserRetail userRetail = new UserRetail();
                convertUserMini2UserRetail(userMiniProgram, userRetail);
                if (CollectionUtils.isNotEmpty(userRetails)) {
                    userRetailMapper.updateByPrimaryKeySelective(userRetail);
                } else {
                    userRetailMapper.insertSelective(userRetail);
                }
            }
        } else if (CANCEL_SHOP_CLIENT.equals(shopCustomerInfoDTO.getOprType())) {
            List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria()
                    .andUserIdEqualTo(userMiniProgram.getUserId()).example());
            String modify = "注销";
            if (CollectionUtils.isEmpty(userMiniPrograms)) {
                log.error("商城客户信息同步:{}{}不存在，{}异常", roleType2Name(userMiniPrograms.get(0).getRoleType()),
                        userMiniProgram.getUserId(), modify);
                throw new IOTException(iotAnswer, roleType2Name(userMiniPrograms.get(0).getRoleType())
                        + userMiniProgram.getUserId() + "不存在，" + modify + "异常");
            }
            userMiniProgram = userMiniPrograms.get(0);
            userMiniProgram.setUpdateTime(date);
            userMiniProgram.setStatus("0");
            userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
            //注销时不处理分销用户，保留
        }
        //用户属性埋点
        gioBurialPointService.sendUserMsg(userMiniProgram);

        return iotAnswer;
    }

    private void shopManagerInfoDTO2miniUser(ShopManagerInfoDTO shopManagerInfoDTO, UserMiniProgram userMiniProgram) {
        BeanUtils.copyProperties(shopManagerInfoDTO, userMiniProgram);
        userMiniProgram.setUserId(shopManagerInfoDTO.getUserID());
        userMiniProgram.setRegionId(shopManagerInfoDTO.getRegionID());
        userMiniProgram.setCode(shopManagerInfoDTO.getCreateOperCode());
        userMiniProgram.setNumber(shopManagerInfoDTO.getEmployeeNum());
        userMiniProgram.setName(shopManagerInfoDTO.getCustomerManagerName());
        userMiniProgram.setPhone(shopManagerInfoDTO.getCreateOperPhone());
        userMiniProgram.setRoleType("4");
        if (StringUtils.isNotBlank(shopManagerInfoDTO.getMrgStatus())) {
            userMiniProgram.setStatus("1".equals(shopManagerInfoDTO.getMrgStatus()) ? "1" : "0");
        }

        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
        Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();

        if (StringUtils.isNotBlank(userMiniProgram.getBeId())) {
            userMiniProgram.setProvinceName("471".equals(userMiniProgram.getBeId()) ?
                    "内蒙古" : (String) provinceCodeNameMap.get(userMiniProgram.getBeId()));
        }
        if (StringUtils.isNotBlank(userMiniProgram.getLocation())) {
            userMiniProgram.setCityName((String) locationCodeNameMap.get(userMiniProgram.getLocation()));
        }

        //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
        if (StringUtils.isNotEmpty(userMiniProgram.getRegionId()) && "10003329".equals(userMiniProgram.getRegionId())) {
            userMiniProgram.setRegionId((String) regionNameCodeMap.get("綦江区"));
            userMiniProgram.setRegionName("綦江区");
        } else {
            userMiniProgram.setRegionName(StringUtils.isNotEmpty(userMiniProgram.getRegionId()) ?
                    (String) regionCodeNameMap.get(userMiniProgram.getRegionId()) : null);
        }
    }

    private void shopCustomerInfoDTO2miniUser(ShopCustomerInfoDTO shopCustomerInfoDTO, UserMiniProgram userMiniProgram) {
        BeanUtils.copyProperties(shopCustomerInfoDTO, userMiniProgram);
        userMiniProgram.setUserId(shopCustomerInfoDTO.getUserID());
        userMiniProgram.setRegionId(shopCustomerInfoDTO.getRegionID());
        userMiniProgram.setName(shopCustomerInfoDTO.getCustName());
        userMiniProgram.setPhone(shopCustomerInfoDTO.getCustNumber());
        userMiniProgram.setRoleType(shopCustomerInfoDTO.getRoleType());
        if (StringUtils.isNotBlank(shopCustomerInfoDTO.getClientstatus())) {
            userMiniProgram.setStatus("1".equals(shopCustomerInfoDTO.getClientstatus()) ? "1" : "0");
        }

        if (StringUtils.isNotBlank(shopCustomerInfoDTO.getRoleType())) {
            if ("1".equals(shopCustomerInfoDTO.getRoleType()) || "2".equals(shopCustomerInfoDTO.getRoleType())) {
                userMiniProgram.setCode(shopCustomerInfoDTO.getDistributorReferralcode());
            } else if ("3".equals(shopCustomerInfoDTO.getRoleType())) {
                userMiniProgram.setCode(shopCustomerInfoDTO.getCustCode());
                userMiniProgram.setNumber(shopCustomerInfoDTO.getAgentNumber());
            }
        }

        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
        Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();

        if (StringUtils.isNotBlank(userMiniProgram.getBeId())) {
            userMiniProgram.setProvinceName("471".equals(userMiniProgram.getBeId()) ?
                    "内蒙古" : (String) provinceCodeNameMap.get(userMiniProgram.getBeId()));
        }
        if (StringUtils.isNotBlank(userMiniProgram.getLocation())) {
            userMiniProgram.setCityName((String) locationCodeNameMap.get(userMiniProgram.getLocation()));
        }

        //万盛区归属于綦江区 地图固定到綦江区的，把万盛区转换到綦江区
        if (StringUtils.isNotEmpty(userMiniProgram.getRegionId()) && "10003329".equals(userMiniProgram.getRegionId())) {
            userMiniProgram.setRegionId((String) regionNameCodeMap.get("綦江区"));
            userMiniProgram.setRegionName("綦江区");
        } else {
            userMiniProgram.setRegionName(StringUtils.isNotEmpty(userMiniProgram.getRegionId()) ?
                    (String) regionCodeNameMap.get(userMiniProgram.getRegionId()) : null);
        }
    }

    private void shopCustomerInfoDTO2RetailUser(ShopCustomerInfoDTO shopCustomerInfoDTO, UserRetail userRetail) {
        BeanUtils.copyProperties(shopCustomerInfoDTO, userRetail);
        userRetail.setUserId(shopCustomerInfoDTO.getUserID());
        userRetail.setId(shopCustomerInfoDTO.getUserID());
        userRetail.setName(shopCustomerInfoDTO.getCustName());
        userRetail.setPhone(shopCustomerInfoDTO.getCustNumber());
        if (StringUtils.isNotBlank(shopCustomerInfoDTO.getRoleType())) {
            userRetail.setRoleType(Integer.valueOf(shopCustomerInfoDTO.getRoleType()));
        }

        userRetail.setRecommendCode(shopCustomerInfoDTO.getDistributorReferralcode());
        userRetail.setProvinceCode(shopCustomerInfoDTO.getBeId());
        userRetail.setCityCode(shopCustomerInfoDTO.getLocation());
        userRetail.setRecommendCode(shopCustomerInfoDTO.getDistributorReferralcode());
        userRetail.setWorkNum(shopCustomerInfoDTO.getAgentNumber());


        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();

        if (StringUtils.isNotBlank(userRetail.getProvinceCode())) {
            userRetail.setProvince("471".equals(userRetail.getProvinceCode()) ?
                    "内蒙古" : (String) provinceCodeNameMap.get(userRetail.getProvinceCode()));
        }
        if (StringUtils.isNotBlank(userRetail.getCityCode())) {
            userRetail.setCity((String) locationCodeNameMap.get(userRetail.getCityCode()));
        }
    }

    private void convertUserMini2UserRetail(UserMiniProgram mini, UserRetail userRetail) {
        userRetail.setRoleType(Integer.valueOf(mini.getRoleType()));
        userRetail.setRecommendCode(mini.getCode());
        userRetail.setCustCode(mini.getCode());
        userRetail.setWorkNum(mini.getNumber());
        userRetail.setName(mini.getName());
        userRetail.setPhone(mini.getPhone());
        userRetail.setProvinceCode(mini.getBeId());
        userRetail.setCityCode(mini.getLocation());
        userRetail.setProvince(mini.getProvinceName());
        userRetail.setCity(mini.getCityName());
        userRetail.setUserId(mini.getUserId());
        userRetail.setId(mini.getUserId());
        userRetail.setRegTime(mini.getCreateTime());
        userRetail.setLatestLoginTime(mini.getLatestLoginTime());
        userRetail.setAuditStatus(mini.getAuditStatus());
        userRetail.setAuditReason(mini.getAuditReason());
        userRetail.setAuditHeaderNotice(mini.getAuditHeaderNotice());
    }

}
