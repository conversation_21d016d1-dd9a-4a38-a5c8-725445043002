package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.GenerateOddNumbersUtil;
import com.chinamobile.iot.sc.constant.*;
import com.chinamobile.iot.sc.entity.DelResult;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.ProductManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.UserCenterOperateEnum;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.service.excel.AfterSaleInfoExcel;
import com.chinamobile.iot.sc.service.excel.ComboInfoExcel;
import com.chinamobile.iot.sc.service.excel.ConfigReplenishExcel;
import com.chinamobile.iot.sc.service.excel.ExcelWidthStyleStrategy;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IotLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.chinamobile.iot.sc.constant.NewProductRequestBusinessFileTypeConstant.BUSINESS_CONTRACT;
import static com.chinamobile.iot.sc.constant.NewProductRequestBusinessFileTypeConstant.CASH_DEPOSIT;
import static com.chinamobile.iot.sc.constant.NewProductRequestMaterialFileTypeConstant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架service实现类
 */
@Slf4j
@Service
public class NewProductRequestOnlineOfflineServiceImpl implements NewProductRequestOnlineOfflineService {

    @Resource
    private NewProductRequestOnlineOfflineComboInfoService newProductRequestOnlineOfflineComboInfoService;

    @Resource
    private NewProductRequestOnlineOfflineAfterSaleInfoService newProductRequestOnlineOfflineAfterSaleInfoService;

    @Resource
    private NewProductRequestOnlineOfflineMaterialService newProductRequestOnlineOfflineMaterialService;

    @Resource
    private NewProductRequestOnlineOfflineBusinessService newProductRequestOnlineOfflineBusinessService;

    @Resource
    private NewProductRequestHandlerInfoService newProductRequestHandlerInfoService;

    @Resource
    private NewProductRequestOnlineOfflineConfigReplenishService newProductRequestOnlineOfflineConfigReplenishService;

    @Resource
    private OneNetObjectStorageService storageService;

    @Resource
    private SmsFeignClient smsFeignClient;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private NewProductRequestManageService newProductRequestManageService;

    @Resource
    private StoreCatalogService storeCatalogService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private LogService logService;


    /**
     * 新产品上架申请短信模板
     */
    @Value("${sms.newProductOnlineInitiate:106261}")
    private String newProductOnlineInitiateTemplateId;

    /**
     * 新产品上架审核未通过短信模板
     */
    @Value("${sms.newProductOnlineNotPass:106262}")
    private String newProductOnlineNotPassTemplateId;

    /**
     * 新产品上架审核通过并上架短信模板
     */
    @Value("${sms.newProductOnlinePass:106263}")
    private String newProductOnlinePassTemplateId;

    /**
     * 新产品下架申请短信模板
     */
    @Value("${sms.newProductOfflineInitiate:106264}")
    private String newProductOfflineInitiateTemplateId;

    /**
     * 新产品下架未通过短信模板
     */
    @Value("${sms.newProductOfflineNotPass:106265}")
    private String newProductOfflineNotPassTemplateId;

    /**
     * 新产品下架通过并下架短信模板
     */
    @Value("${sms.newProductOnlinePass:106266}")
    private String newProductOfflinePassTemplateId;

    private final String LOCAL_CACHEDIR = "localCache";
    private final String OSS_CACHEDIR = "OSSCache";
    private final String OSS_DIR = "osProductManage";

    private final String ZIPDIR_SUFFIX = "zipdir";
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOnlineRequestInfo(OnlineRequestParam onlineRequestParam,
                                      LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
//        if (!PARTNER_LORD_ROLE.equals(roleType)) {
//            throw new BusinessException(StatusConstant.AUTH_ERROR);
//        }

        String newProductRequestId = onlineRequestParam.getNewProductRequestId();
        NewProductRequestManage manage = newProductRequestManageService.getNewProductRequestManageById(newProductRequestId);
        if (!Optional.ofNullable(manage).isPresent()) {

            throw new BusinessException(BaseErrorConstant.NWE_PRODUCT_REQUEST_NOT_EXISTS);
        }

        String onlineStatus = manage.getOnlineStatus();
        if (!NewProductOnlineStatusConstant.NOT_ONLINE.equals(onlineStatus)
                && !NewProductOnlineStatusConstant.OFFLINE.equals(onlineStatus)
                && !NewProductOnlineStatusConstant.COPY_ONLINE.equals(onlineStatus)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_OFFLINE_TO_ONLINE);
        }

        Date date = new Date();
        String id = BaseServiceUtils.getId();

        String dateToStr = DateUtils.dateToStr(date, DateUtils.DATE_FORMAT_NO_SYMBOL);
        String redisKey = RedisLockConstant.NEW_PRODUCT_ONLINE_SERIAL.concat(dateToStr);

        String userId = loginIfo4Redis.getUserId();

        // 权限改版后不需要了
//        if (!userId.equals(manage.getCooperatorId())) {
//            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_BELONG_YOU);
//        }

        String comboInfoId = onlineRequestParam.getComboInfoId();
        String requestNo;
        // 获取上次流程更新时间
        Date lastHandlerInfoUpdateTime = date;
        // 新上架
        if (StringUtils.isEmpty(comboInfoId)) {

            comboInfoId = id;

            // 套餐信息
            requestNo = saveComboInfo(onlineRequestParam, userId, id, newProductRequestId, redisKey, date);

            // 售后信息
            saveAfterSaleInfo(onlineRequestParam, id, newProductRequestId, date);

            // 商品素材
            saveMaterial(onlineRequestParam, id, newProductRequestId, date);

            // 商务相关
            saveBusiness(onlineRequestParam, id, newProductRequestId, date);

            // 新增配置补充信息
            saveConfigReplenish(newProductRequestId, comboInfoId, manage.getSalePrice(), manage.getSupplyPrice(), date);
        } else {
            // 重新上架
            NewProductRequestOnlineOfflineComboInfo existComboInfo = newProductRequestOnlineOfflineComboInfoService.getComboInfoById(comboInfoId);
            if (!Optional.ofNullable(existComboInfo).isPresent()) {
                throw new BusinessException(BaseErrorConstant.NWE_PRODUCT_REQUEST_NOT_EXISTS);
            }
            requestNo = existComboInfo.getRequestNo();

            // 套餐信息
            NewProductRequestOnlineOfflineComboInfo comboInfo = new NewProductRequestOnlineOfflineComboInfo();
            comboInfo.setId(comboInfoId);
            comboInfo.setNewProductRequestId(newProductRequestId);
            comboInfo.setUpdateTime(date);
            BeanUtils.copyProperties(onlineRequestParam.getComboInfoParam(), comboInfo);
            newProductRequestOnlineOfflineComboInfoService.updateComboInfoByRequestIdAndComboInfoId(comboInfo);

            // 售后信息
            NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo = new NewProductRequestOnlineOfflineAfterSaleInfo();
            afterSaleInfo.setComboInfoId(comboInfoId);
            afterSaleInfo.setNewProductRequestId(newProductRequestId);
            afterSaleInfo.setUpdateTime(date);
            BeanUtils.copyProperties(onlineRequestParam.getAfterSaleInfoParam(), afterSaleInfo);
            newProductRequestOnlineOfflineAfterSaleInfoService.updateAfterSaleInfoByRequestIdAndComboInfoId(afterSaleInfo);

            // 商品素材
            newProductRequestOnlineOfflineMaterialService.deleteMaterialByRequestIdAndComboInfoId(comboInfoId, newProductRequestId);
            saveMaterial(onlineRequestParam, comboInfoId, newProductRequestId, date);

            // 商务相关
            newProductRequestOnlineOfflineBusinessService.deleteBusinessByRequestIdAndComboInfoId(comboInfoId, newProductRequestId);
            saveBusiness(onlineRequestParam, comboInfoId, newProductRequestId, date);
        }

        String nextHandlerUserId = onlineRequestParam.getNextHandlerUserId();
        // 申请历史记录
        saveHandleInfo(id, comboInfoId,
                onlineRequestParam.getRequestLink(),
                userId,
                loginIfo4Redis.getUserName(),
                nextHandlerUserId,
                onlineRequestParam.getNextHandlerUserName(),
                NewProductRequestFlowHandlerStatusConstant.INITIATE_PROCESS,
                "",
                NewProductRequestFlowOnlineStatusConstant.ONLINE,
                lastHandlerInfoUpdateTime, date);

        // 申请表上下架状态的改变及当前处理人
        newProductRequestManageService.updateOnlineRelatedInfo(newProductRequestId,
                NewProductOnlineStatusConstant.ONLINING,
                NewProductOnlineOfflineRequestStatusConstant.ONLINE_FIRST_TRIAL,
                nextHandlerUserId);

        // 进行上架申请时保存当前的上下架状态60天
        String onlineStatusNextUse = onlineStatus;
        if (NewProductOnlineStatusConstant.NOT_ONLINE.equals(onlineStatus)){
            // 为了避免重复的发起初次上架申请，将该状态设置为下架状态
            onlineStatusNextUse = NewProductOnlineStatusConstant.OFFLINE;
        }
        String onlineStatusRedisKey = RedisLockConstant.NEW_PRODUCT_ONLINE_STATUS.concat("_")
                .concat(manage.getRequestNo());
        redisTemplate.opsForValue().set(onlineStatusRedisKey, onlineStatusNextUse, 60, TimeUnit.DAYS);

        // 短信发送
        Map<String, String> message = new HashMap<>();
        message.put("requestNo", requestNo);
        message.put("spuOfferingName", manage.getSpuOfferingName());
        message.put("skuOfferingName", manage.getSkuOfferingName());
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(nextHandlerUserId);
        List<String> phoneList = new ArrayList<>();
        phoneList.add(data4UserBaseAnswer.getData().getPhone());
        sendPhoneMessage(newProductOnlineInitiateTemplateId, message, phoneList);

        //记录日志
/*        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.ONLINE_OFFLINE_MANAGE.code,
                IotLogUtil.onlineProductContentFromRequest(manage));*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOfflineRequestInfo(NewProductOfflineRequestParam offlineRequestParam,
                                       LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
//        if (!PARTNER_LORD_ROLE.equals(roleType) && !PRODUCT_OPERATOR_FRAME_ROLE.equals(roleType)) {
//            throw new BusinessException(StatusConstant.AUTH_ERROR);
//        }
        String newProductRequestId = offlineRequestParam.getNewProductRequestId();
        String comboInfoId = offlineRequestParam.getComboInfoId();
        String nextHandlerUserId = offlineRequestParam.getNextHandlerUserId();
        String userId = loginIfo4Redis.getUserId();
        Date date = new Date();


        NewProductRequestManage manage = newProductRequestManageService.getNewProductRequestManageById(newProductRequestId);
        if (!Optional.ofNullable(manage).isPresent()) {
            throw new BusinessException(BaseErrorConstant.NWE_PRODUCT_REQUEST_NOT_EXISTS);
        }

        String onlineStatus = manage.getOnlineStatus();
        if (!NewProductOnlineStatusConstant.ONLINE.equals(onlineStatus)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_ONLINE_TO_OFFLINE);
        }

        // 获取上架商品套餐信息
        NewProductRequestOnlineOfflineComboInfo comboInfo = newProductRequestOnlineOfflineComboInfoService.getComboInfoById(comboInfoId);
        if (!Optional.ofNullable(comboInfo).isPresent()) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_ONLINE_OFFLINE_NOT_EXISTS);
        }

        if (!comboInfo.getNewProductRequestId().equals(newProductRequestId)) {

            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_SAME_ONLINE);
        }

        // 申请表上下架状态的改变及当前处理人
        newProductRequestManageService.updateOnlineRelatedInfo(newProductRequestId,
                NewProductOnlineStatusConstant.OFFLINING,
                NewProductOnlineOfflineRequestStatusConstant.OFFLINE_FIRST_TRIAL,
                nextHandlerUserId);

        // 更新架套餐信息下架原因和下架用户id
        newProductRequestOnlineOfflineComboInfoService.updateOfflineReasonAndOfflineUser(comboInfoId,
                offlineRequestParam.getOfflineReason(), userId);

        // 进行流程处理保存
        saveHandleInfo(BaseServiceUtils.getId(),
                comboInfoId,
                offlineRequestParam.getRequestLink(),
                userId,
                loginIfo4Redis.getUserName(),
                nextHandlerUserId,
                offlineRequestParam.getNextHandlerUserName(),
                NewProductRequestFlowHandlerStatusConstant.INITIATE_PROCESS,
                "",
                NewProductRequestFlowOnlineStatusConstant.OFFLINE,
                date,
                date);

        // 进行下架申请时保存当前的上下架状态60天
        String onlineStatusRedisKey = RedisLockConstant.NEW_PRODUCT_ONLINE_STATUS.concat("_")
                .concat(manage.getRequestNo());
        redisTemplate.opsForValue().set(onlineStatusRedisKey, NewProductOnlineStatusConstant.ONLINE, 60, TimeUnit.DAYS);

        // 短信发送
        Map<String, String> message = new HashMap<>();
        message.put("requestNo", comboInfo.getRequestNo());
        message.put("spuOfferingName", manage.getSpuOfferingName());
        message.put("skuOfferingName", manage.getSkuOfferingName());
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(nextHandlerUserId);
        List<String> phoneList = new ArrayList<>();
        phoneList.add(data4UserBaseAnswer.getData().getPhone());
        sendPhoneMessage(newProductOfflineInitiateTemplateId, message, phoneList);

        //记录日志
/*        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.ONLINE_OFFLINE_MANAGE.code,
                IotLogUtil.offlineProductContentFromRequest(manage));*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyNotPassOnlineToReOnline(NewProductCopyOnlineParam copyOnlineParam,
                                            LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
//        if (!PARTNER_LORD_ROLE.equals(roleType)) {
//            throw new BusinessException(StatusConstant.AUTH_ERROR);
//        }
        String newProductRequestId = copyOnlineParam.getNewProductRequestId();
        String comboInfoId = copyOnlineParam.getComboInfoId();
        Date date = new Date();
        String userId = loginIfo4Redis.getUserId();

        NewProductRequestManage manage = newProductRequestManageService.getNewProductRequestManageById(newProductRequestId);
        if (!Optional.ofNullable(manage).isPresent()) {
            throw new BusinessException(BaseErrorConstant.NWE_PRODUCT_REQUEST_NOT_EXISTS);
        }

        if (NewProductOnlineOfflineRequestStatusConstant.ONLINE_NOT_PASS != (int) manage.getOnlineOfflineRequestStatus()) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_PASS_ONLINE_CAN_COPY);
        }

        if (!manage.getCooperatorId().equals(userId)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_ONLY_COPY_SELF);
        }

        // 获取上架商品套餐信息
        NewProductRequestOnlineOfflineComboInfo comboInfo = newProductRequestOnlineOfflineComboInfoService.getComboInfoById(comboInfoId);
        if (!Optional.ofNullable(comboInfo).isPresent()) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_ONLINE_OFFLINE_NOT_EXISTS);
        }

        if (!comboInfo.getNewProductRequestId().equals(newProductRequestId)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_SAME_ONLINE);
        }

        // 获取申请编号
        String dateToStr = DateUtils.dateToStr(date, DateUtils.DATE_FORMAT_NO_SYMBOL);
        String redisKey = RedisLockConstant.NEW_PRODUCT_ONLINE_SERIAL.concat(dateToStr);
        String requestNo = getNewOddNumber(redisKey);

        // 重新赋值一些套餐信息
        String newComboInfoId = BaseServiceUtils.getId();
        comboInfo.setId(newComboInfoId);
        comboInfo.setRequestNo(requestNo);
        comboInfo.setRequestPass(null);
        comboInfo.setCreateTime(date);
        comboInfo.setUpdateTime(date);
        newProductRequestOnlineOfflineComboInfoService.saveComboInfo(comboInfo);

        // 售后信息
        NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo
                = newProductRequestOnlineOfflineAfterSaleInfoService.getAfterSaleInfoByRequestIdAndComboInfoId(newProductRequestId, comboInfoId);
        // 重新赋值一些售后信息
        afterSaleInfo.setId(BaseServiceUtils.getId());
        afterSaleInfo.setComboInfoId(newComboInfoId);
        afterSaleInfo.setCreateTime(date);
        afterSaleInfo.setUpdateTime(date);
        newProductRequestOnlineOfflineAfterSaleInfoService.saveAfterSaleInfo(afterSaleInfo);

        // 商品素材
        List<NewProductRequestOnlineOfflineMaterial> materialList
                = newProductRequestOnlineOfflineMaterialService.getMaterialByRequestIdAndComboInfoId(newProductRequestId, comboInfoId);
        // 重新赋值一些商品素材
        materialList.stream().forEach(material -> {
            material.setId(BaseServiceUtils.getId());
            material.setComboInfoId(newComboInfoId);
            material.setCreateTime(date);
        });
        newProductRequestOnlineOfflineMaterialService.batchSaveMaterial(materialList);

        // 商务相关
        List<NewProductRequestOnlineOfflineBusiness> businessList
                = newProductRequestOnlineOfflineBusinessService.getBusinessByRequestIdAndComboInfoId(newProductRequestId, comboInfoId);
        businessList.stream().forEach(business -> {
            business.setId(BaseServiceUtils.getId());
            business.setComboInfoId(newComboInfoId);
            business.setCreateTime(date);
        });
        newProductRequestOnlineOfflineBusinessService.batchSaveBusiness(businessList);

        // 获取审批记录的第一条记录
        NewProductRequestHandlerInfoVO handlerInfoVO
                = newProductRequestHandlerInfoService.listNewProductRequestHandlerInfo(comboInfoId, NewProductRequestFlowTypeConstant.PRODUCT_ONLINE_OFFLINE).get(0);

        String nextHandlerUserId = handlerInfoVO.getNextHandlerUserId();
        // 申请历史记录
        saveHandleInfo(BaseServiceUtils.getId(), newComboInfoId,
                handlerInfoVO.getRequestLink(),
                userId,
                loginIfo4Redis.getUserName(),
                nextHandlerUserId,
                handlerInfoVO.getNextHandlerUserName(),
                NewProductRequestFlowHandlerStatusConstant.INITIATE_PROCESS,
                "",
                NewProductRequestFlowOnlineStatusConstant.ONLINE,
                date, date);


        // 申请表上下架状态的改变及当前处理人
        newProductRequestManageService.updateOnlineRelatedInfo(newProductRequestId,
                NewProductOnlineStatusConstant.COPY_ONLINE,
                NewProductOnlineOfflineRequestStatusConstant.NEW_ADD,
                userId);

        // 进行上架申请时保存当前的上下架状态60天
        String onlineStatusRedisKey = RedisLockConstant.NEW_PRODUCT_ONLINE_STATUS.concat("_")
                .concat(manage.getRequestNo());
        redisTemplate.opsForValue().set(onlineStatusRedisKey, manage.getOnlineStatus(), 60, TimeUnit.DAYS);

        // 短信发送
        /*Map<String, String> message = new HashMap<>();
        message.put("requestNo", requestNo);
        message.put("spuOfferingName", manage.getSpuOfferingName());
        message.put("skuOfferingName", manage.getSkuOfferingName());
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(nextHandlerUserId);
        List<String> phoneList = new ArrayList<>();
        phoneList.add(data4UserBaseAnswer.getData().getPhone());
        sendPhoneMessage(newProductOnlineInitiateTemplateId, message, phoneList);*/

    }

    @Override
    public String getNewOddNumber(String redisKey) {
        return redisUtil.smartLock(redisKey.concat("Lock"), () -> {
                    ValueOperations<String, Object> oddValue = redisTemplate.opsForValue();
                    String requestNoNew;
                    if (oddValue != null) {
                        String requestNoOld = (String) oddValue.get(redisKey);
                        if (StringUtils.isEmpty(requestNoOld)) {
                            requestNoNew = GenerateOddNumbersUtil.getOddNumbers(null);
                        } else {
                            requestNoNew = GenerateOddNumbersUtil.getOddNumbers(requestNoOld);
                        }
                    } else {
                        requestNoNew = GenerateOddNumbersUtil.getOddNumbers(null);
                    }

                    // 最新编码存入redis
                    redisTemplate.opsForValue().set(redisKey, requestNoNew, 24, TimeUnit.HOURS);
                    return requestNoNew;
                }
        );
    }

    // 数据权限-上下架管理
    @Override
    public PageData<NewProductOnlineOfflineVO> pageNewProductOnlineOffline(NewProductOnlineOfflineParam onlineOfflineParam,
                                                                           LoginIfo4Redis loginIfo4Redis) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ONLINE_OFFLINE_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ONLINE_OFFLINE_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ONLINE_OFFLINE_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }
        // 商品名称搜索支持反斜杠适配
        if(onlineOfflineParam.getSkuOfferingName() != null){
            onlineOfflineParam.setSkuOfferingName(onlineOfflineParam.getSkuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        if(onlineOfflineParam.getSpuOfferingName() != null){
            onlineOfflineParam.setSpuOfferingName(onlineOfflineParam.getSpuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        PageData<NewProductOnlineOfflineVO> pageData = new PageData<>();
        Integer pageNum = onlineOfflineParam.getPageNum();
        Integer pageSize = onlineOfflineParam.getPageSize();

        List<NewProductOnlineOfflineVO> onlineOfflineVOList;
        Page<NewProductOnlineOfflineVO> page = new Page<>(pageNum, pageSize);
        String roleType = loginIfo4Redis.getRoleType();
        // 超管可以看所有，其他人不管角色都只能看自己的
        if (loginIfo4Redis.getIsAdmin()) {
            onlineOfflineVOList = newProductRequestOnlineOfflineComboInfoService.listNewProductOnlineOfflineToSuperAdmin(page, onlineOfflineParam);
        } else {
            onlineOfflineParam.setCurrentUserId(loginIfo4Redis.getUserId());
            onlineOfflineVOList = newProductRequestOnlineOfflineComboInfoService.listNewProductOnlineOfflineToJudgeUser(page, onlineOfflineParam);
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(onlineOfflineVOList);

        return pageData;
    }

    @Override
    public OnlineRequestVO getOnlineRequestDetail(NewProductOnlineOfflineDetailParam detailParam,
                                                  LoginIfo4Redis loginIfo4Redis) {
        OnlineRequestVO onlineRequestVO = new OnlineRequestVO();
        String newProductRequestId = detailParam.getNewProductRequestId();
        String comboInfoId = detailParam.getComboInfoId();
        String userId = loginIfo4Redis.getUserId();

        onlineRequestVO.setNewProductRequestId(newProductRequestId);
        onlineRequestVO.setComboInfoId(comboInfoId);

        // 获取商品引入申请信息
        NewProductRequestManage manage = newProductRequestManageService.getNewProductRequestManageById(newProductRequestId);
        if (!Optional.ofNullable(manage).isPresent()) {
            throw new BusinessException(BaseErrorConstant.NWE_PRODUCT_REQUEST_NOT_EXISTS);
        }


        // 获取上架商品套餐信息
        NewProductRequestOnlineOfflineComboInfo comboInfo = newProductRequestOnlineOfflineComboInfoService.getComboInfoById(comboInfoId);
        if (!Optional.ofNullable(comboInfo).isPresent()) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_ONLINE_OFFLINE_NOT_EXISTS);
        }
        onlineRequestVO.setCreator(comboInfo.getCreator());
        onlineRequestVO.setRequestOfflineUserId(comboInfo.getRequestOfflineUserId());

        if (!comboInfo.getNewProductRequestId().equals(newProductRequestId)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_SAME_ONLINE);
        }

        NewProductManageDetailsVO manageDetailsVO = newProductRequestManageService.getProductManageDetails(newProductRequestId);
        BeanUtils.copyProperties(manageDetailsVO, onlineRequestVO);

        // 新产品上线请求商品套餐信息
        NewProductOnlineRequestComboInfoVO comboInfoVO = new NewProductOnlineRequestComboInfoVO();
        BeanUtils.copyProperties(comboInfo, comboInfoVO);
//        BeanUtils.copyProperties(manageDetailsVO,comboInfoVO);
        onlineRequestVO.setComboInfoVO(comboInfoVO);

        // 新产品上线请求售后信息
        NewProductOnlineRequestAfterSaleInfoParam afterSaleInfoParam = new NewProductOnlineRequestAfterSaleInfoParam();
        NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo
                = newProductRequestOnlineOfflineAfterSaleInfoService.getAfterSaleInfoByRequestIdAndComboInfoId(newProductRequestId, comboInfoId);
        if (Optional.ofNullable(afterSaleInfo).isPresent()) {
            BeanUtils.copyProperties(afterSaleInfo, afterSaleInfoParam);
            onlineRequestVO.setAfterSaleInfoParam(afterSaleInfoParam);
        }

        // 商品素材信息
        List<NewProductRequestOnlineOfflineMaterial> materialList
                = newProductRequestOnlineOfflineMaterialService.getMaterialByRequestIdAndComboInfoId(newProductRequestId, comboInfoId);
        if (CollectionUtils.isEmpty(materialList)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_ONLINE_OFFLINE_MATERIAL_NOT_EXISTS);
        }
        // 商品素材头图
        NewProductOnlineRequestMaterialParam headerMaterial = new NewProductOnlineRequestMaterialParam();
        // 商品素材轮播图
        List<NewProductOnlineRequestMaterialParam> slideshowMaterialList = new ArrayList<>();
        // 主图视频
        NewProductOnlineRequestMaterialParam videoMaterial = new NewProductOnlineRequestMaterialParam();
        // 商品素材商品详情图-PC端
        List<NewProductOnlineRequestMaterialParam> productDetailPCMaterialList = new ArrayList<>();
        // 商品素材商品详情图-移动端
        List<NewProductOnlineRequestMaterialParam> productDetailMobileMaterialList = new ArrayList<>();
        // 硬件商品图片（白底）
        NewProductOnlineRequestMaterialParam hardwareProductWhiteMaterial = new NewProductOnlineRequestMaterialParam();
        // 商品logo图片（白底）
        NewProductOnlineRequestMaterialParam productLogoWhiteMaterial = new NewProductOnlineRequestMaterialParam();
        // 产品功能介绍资料
        List<NewProductOnlineRequestMaterialParam> productFunctionDataMaterialList = new ArrayList<>();
        // 产品性能参数资料
        List<NewProductOnlineRequestMaterialParam> productPerformanceDataMaterialList = new ArrayList<>();
        // 产品说明书，产品操作视频
        List<NewProductOnlineRequestMaterialParam> productSpecificationMaterialList = new ArrayList<>();
        // 产品安装指导书，指导视频
        List<NewProductOnlineRequestMaterialParam> productInstallMaterialList = new ArrayList<>();
        // 软件、APP安装、操作指导书、指导视频
        List<NewProductOnlineRequestMaterialParam> relatedVideoMaterialList = new ArrayList<>();
        // 售前营销话术资料FAQ
        List<NewProductOnlineRequestMaterialParam> preSaleMarketDataMaterialList = new ArrayList<>();
        // 产品常见问题咨询点资料FAQ
        List<NewProductOnlineRequestMaterialParam> productProblemDataMaterialList = new ArrayList<>();
        materialList.stream().forEach(material -> {
            String fileType = material.getFileType();
            // 头图
            if (HEADER_MATERIAL.equals(fileType)) {
                BeanUtils.copyProperties(material, headerMaterial);
                onlineRequestVO.setHeaderMaterial(headerMaterial);
            } else if (SLIDE_SHOW_MATERIAL.equals(fileType)) {
                // 轮播图
                NewProductOnlineRequestMaterialParam slideshowMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, slideshowMaterial);
                slideshowMaterialList.add(slideshowMaterial);
            } else if (VIDEO_MATERIAL.equals(fileType)) {
                // 主图视频
                BeanUtils.copyProperties(material, videoMaterial);
                onlineRequestVO.setVideoMaterial(videoMaterial);
            } else if (PRODUCT_DETAIL_PC_MATERIAL.equals(fileType)) {
                // 商品详情图-PC端
                NewProductOnlineRequestMaterialParam productDetailPCMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, productDetailPCMaterial);
                productDetailPCMaterialList.add(productDetailPCMaterial);
            } else if (PRODUCT_DETAIL_MOBILE_MATERIAL.equals(fileType)) {
                // 商品详情图-移动端
                NewProductOnlineRequestMaterialParam productDetailMobileMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, productDetailMobileMaterial);
                productDetailMobileMaterialList.add(productDetailMobileMaterial);
            } else if (HARDWARE_PRODUCT_WHITE_MATERIAL.equals(fileType)) {
                // 硬件商品图片（白底）
                BeanUtils.copyProperties(material, hardwareProductWhiteMaterial);
                onlineRequestVO.setHardwareProductWhiteMaterial(hardwareProductWhiteMaterial);
            } else if (PRODUCT_LOGO_WHITE_MATERIAL.equals(fileType)) {
                // 商品logo图片（白底）
                BeanUtils.copyProperties(material, productLogoWhiteMaterial);
                onlineRequestVO.setProductLogoWhiteMaterial(productLogoWhiteMaterial);
            } else if (PRODUCT_FUNCTION_DATA_MATERIAL.equals(fileType)) {
                // 产品功能介绍资料
                NewProductOnlineRequestMaterialParam productFunctionDataMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, productFunctionDataMaterial);
                productFunctionDataMaterialList.add(productFunctionDataMaterial);
            } else if (PRODUCT_PERFORMANCE_DATA_MATERIAL.equals(fileType)) {
                // 产品性能参数资料
                NewProductOnlineRequestMaterialParam productPerformanceDataMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, productPerformanceDataMaterial);
                productPerformanceDataMaterialList.add(productPerformanceDataMaterial);
            } else if (PRODUCT_SPECIFICATION_MATERIAL.equals(fileType)) {
                // 产品说明书，产品操作视频
                NewProductOnlineRequestMaterialParam productSpecificationMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, productSpecificationMaterial);
                productSpecificationMaterialList.add(productSpecificationMaterial);
            } else if (PRODUCT_INSTALL_MATERIAL.equals(fileType)) {
                // 产品安装指导书，指导视频
                NewProductOnlineRequestMaterialParam productInstallMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, productInstallMaterial);
                productInstallMaterialList.add(productInstallMaterial);
            } else if (RELATED_VIDEO_MATERIAL.equals(fileType)) {
                // 软件、APP安装、操作指导书、指导视频
                NewProductOnlineRequestMaterialParam relatedVideoMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, relatedVideoMaterial);
                relatedVideoMaterialList.add(relatedVideoMaterial);
            } else if (PRE_SALE_MARKET_DATA_MATERIAL.equals(fileType)) {
                // 售前营销话术资料FAQ
                NewProductOnlineRequestMaterialParam preSaleMarketDataMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, preSaleMarketDataMaterial);
                preSaleMarketDataMaterialList.add(preSaleMarketDataMaterial);
            } else if (PRODUCT_PROBLEM_DATA_MATERIAL.equals(fileType)) {
                // 产品常见问题咨询点资料FAQ
                NewProductOnlineRequestMaterialParam productProblemDataMaterial = new NewProductOnlineRequestMaterialParam();
                BeanUtils.copyProperties(material, productProblemDataMaterial);
                productProblemDataMaterialList.add(productProblemDataMaterial);
            }
        });
        // 轮播图
        onlineRequestVO.setSlideshowMaterialList(slideshowMaterialList);
        // 商品详情图-PC端
        onlineRequestVO.setProductDetailPCMaterialList(productDetailPCMaterialList);
        // 商品详情图-移动端
        onlineRequestVO.setProductDetailMobileMaterialList(productDetailMobileMaterialList);
        // 产品功能介绍资料
        onlineRequestVO.setProductFunctionDataMaterialList(productFunctionDataMaterialList);
        // 产品性能参数资料
        onlineRequestVO.setProductPerformanceDataMaterialList(productPerformanceDataMaterialList);
        // 产品说明书，产品操作视频
        onlineRequestVO.setProductSpecificationMaterialList(productSpecificationMaterialList);
        // 产品安装指导书，指导视频
        onlineRequestVO.setProductInstallMaterialList(productInstallMaterialList);
        // 软件、APP安装、操作指导书、指导视频
        onlineRequestVO.setRelatedVideoMaterialList(relatedVideoMaterialList);
        // 售前营销话术资料FAQ
        onlineRequestVO.setPreSaleMarketDataMaterialList(preSaleMarketDataMaterialList);
        // 产品常见问题咨询点资料FAQ
        onlineRequestVO.setProductProblemDataMaterialList(productProblemDataMaterialList);


        // 商务相关信息
        // 商务合同/协议
        NewProductOnlineRequestBusinessParam businessContract = new NewProductOnlineRequestBusinessParam();
        // 保证金
        NewProductOnlineRequestBusinessParam cashDeposit = new NewProductOnlineRequestBusinessParam();

        List<NewProductRequestOnlineOfflineBusiness> businessList
                = newProductRequestOnlineOfflineBusinessService.getBusinessByRequestIdAndComboInfoId(newProductRequestId, comboInfoId);
        if (CollectionUtils.isEmpty(businessList)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_ONLINE_OFFLINE_BUSINESS_NOT_EXISTS);
        }

        businessList.stream().forEach(business -> {
            String fileType = business.getFileType();
            if (BUSINESS_CONTRACT.equals(fileType)) {
                // 商务合同/协议
                BeanUtils.copyProperties(business, businessContract);
                onlineRequestVO.setBusinessContract(businessContract);
            } else if (CASH_DEPOSIT.equals(fileType)) {
                // 保证金
                BeanUtils.copyProperties(business, cashDeposit);
                onlineRequestVO.setCashDeposit(cashDeposit);
            }
        });

        // 上架申请人不能查看配置补充信息
        // 超管可以看
        if (!userId.equals(comboInfo.getCreator()) || loginIfo4Redis.getIsAdmin()) {
            NewProductRequestOnlineOfflineConfigReplenish configReplenish
                    = newProductRequestOnlineOfflineConfigReplenishService.getConfigReplenishByRequestIdAndComboInfoId(newProductRequestId, comboInfoId);
            if (Optional.ofNullable(configReplenish).isPresent()) {
                NewProductRequestOnlineOfflineConfigReplenishVO configReplenishVO = new NewProductRequestOnlineOfflineConfigReplenishVO();
                BeanUtils.copyProperties(configReplenish, configReplenishVO);
                configReplenishVO.setConfigReplenishId(configReplenish.getId());

                onlineRequestVO.setSkuOfferingCode(manage.getSkuOfferingCode());
                onlineRequestVO.setSpuOfferingCode(manage.getSpuOfferingCode());

                String storeFirstCatalog = configReplenish.getStoreFirstCatalog();
                String storeSecondCatalog = configReplenish.getStoreSecondCatalog();

                // 获取目录信息
                if (StringUtils.isNotEmpty(storeFirstCatalog)){
                    StoreCatalog firstCatalog = storeCatalogService.getStoreCatalogById(storeFirstCatalog);
                    if (Optional.ofNullable(firstCatalog).isPresent()) {
                        configReplenishVO.setStoreFirstCatalogName(firstCatalog.getName());
                    }
                }

                if (StringUtils.isNotEmpty(storeSecondCatalog)){
                    StoreCatalog secondCatalog = storeCatalogService.getStoreCatalogById(storeSecondCatalog);
                    if (Optional.ofNullable(secondCatalog).isPresent()) {
                        configReplenishVO.setStoreSecondCatalogName(secondCatalog.getName());
                    }
                }

                onlineRequestVO.setConfigReplenishVO(configReplenishVO);
            }
        }

        // 审批意见
        List<NewProductRequestHandlerInfoVO> handlerInfoVOList
                = newProductRequestHandlerInfoService.listNewProductRequestHandlerInfo(comboInfoId, NewProductRequestFlowTypeConstant.PRODUCT_ONLINE_OFFLINE);
        onlineRequestVO.setHandlerInfoVOList(handlerInfoVOList);

        return onlineRequestVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void judgeOnlineOfflineFlow(NewProductJudgeOnlineOfflineParam onlineOfflineParam,
                                       LoginIfo4Redis loginIfo4Redis) {
        if (loginIfo4Redis.getIsAdmin()) {
            throw new BusinessException(BaseErrorConstant.NOT_USER_AUDIT);
        }

        String userId = loginIfo4Redis.getUserId();
        String userName = loginIfo4Redis.getUserName();
        String roleType = loginIfo4Redis.getRoleType();
        Date date = new Date();

        String handlerStatus = onlineOfflineParam.getHandlerStatus();
        String handlerRemark = onlineOfflineParam.getHandlerRemark();
        String comboInfoId = onlineOfflineParam.getComboInfoId();
        String handlerOnlineStatus = onlineOfflineParam.getHandlerOnlineStatus();
        String newProductRequestId = onlineOfflineParam.getNewProductRequestId();
        String nextHandlerUserId = onlineOfflineParam.getNextHandlerUserId();

        // 获取当前流程的处理人
        NewProductRequestManage manage = newProductRequestManageService.getNewProductRequestManageById(newProductRequestId);
        String onlineOfflineCurrentHandlerUserId = manage.getOnlineOfflineCurrentHandlerUserId();
        if (StringUtils.isEmpty(onlineOfflineCurrentHandlerUserId)
                || (!userId.equals(onlineOfflineCurrentHandlerUserId) && !loginIfo4Redis.getIsAdmin())) {
            //当前审核人，当前审核不是选中的user或者且不是超管，则不能审批
            throw new BusinessException(BaseErrorConstant.NOT_USER_AUDIT);
        }

        // 获取上一环节的更新时间
        NewProductRequestHandlerInfoParam handlerInfoParam = new NewProductRequestHandlerInfoParam();
        handlerInfoParam.setFlowSourceId(comboInfoId);
        handlerInfoParam.setFlowType(NewProductRequestFlowTypeConstant.PRODUCT_ONLINE_OFFLINE);
        handlerInfoParam.setNextHandlerUserId(userId);
        handlerInfoParam.setOnlineStatus(handlerOnlineStatus);
        NewProductRequestHandlerInfo lastHandlerInfo = newProductRequestHandlerInfoService.getLastHandlerInfo(handlerInfoParam);
        if (lastHandlerInfo == null) {
            throw new BusinessException(BaseErrorConstant.FLOW_NOT_EXISTS);
        }

        NewProductRequestOnlineOfflineComboInfo comboInfo = newProductRequestOnlineOfflineComboInfoService.getComboInfoById(comboInfoId);
        if (!Optional.ofNullable(comboInfo).isPresent()) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_ONLINE_OFFLINE_NOT_EXISTS);
        }

        if (!comboInfo.getNewProductRequestId().equals(newProductRequestId)) {
            throw new BusinessException(BaseErrorConstant.NEW_PRODUCT_NOT_SAME_ONLINE);
        }

        boolean isPass = NewProductRequestFlowHandlerStatusConstant.PASS.equals(handlerStatus);
        // 上架还是下架
        boolean isOnline = NewProductRequestFlowOnlineStatusConstant.ONLINE.equals(handlerOnlineStatus);
        // 新产品引入申请管理中的申请上下架状态
        Integer onlineOfflineRequestStatus = -1;
        // 新产品引入申请管理中的上架状态
        String requestOnlineStatus;
        // 短信模板
        String messageTemplateId;
        // 电话发送短信列表
        List<String> phoneList = new ArrayList<>();
        // 短信发送的参数集合
        Map<String, String> messageMap = new HashMap<>();
//        // 审核通过
//        if (isPass) {
//            if (isOnline) {
//                requestOnlineStatus = NewProductOnlineStatusConstant.ONLINING;
//                messageTemplateId = newProductOnlineInitiateTemplateId;
//                // 初审
//                if (BaseConstant.PRODUCT_OPERATOR_FRAME_ROLE.equals(roleType)) {
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_RECHECK;
//                    messageMap.put("requestNo", comboInfo.getRequestNo());
//
//                    // 校验补充配置信息
//                    validConfigReplenish(onlineOfflineParam);
//
//                    String configReplenishId = onlineOfflineParam.getConfigReplenishId();
//                    if (StringUtils.isEmpty(configReplenishId)) {
//
//                    } else {
//                        // 更新配置补充信息
//                        updateConfigReplenish(onlineOfflineParam, date);
//                    }
//
//                } else if (BaseConstant.PRODUCT_OPERATOR_RECHECK_ROLE.equals(roleType)) {
//                    // 复审
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_FINAL_JUDGMENT;
//                    messageMap.put("requestNo", comboInfo.getRequestNo());
//                } else if (BaseConstant.PRODUCT_OPERATOR_ULTIMATELY_ROLE.equals(roleType)
//                        || (BaseConstant.OPERATOR_ROLE.equals(roleType) && "商品上架-终审".equals(onlineOfflineParam.getRequestLink()))) {
//                    // 终审
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_OPERATOR_JUDGMENT;
//                    messageMap.put("requestNo", comboInfo.getRequestNo());
//                } else if (BaseConstant.OPERATOR_ROLE.equals(roleType)) {
//                    // 运管审核
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE;
//                    requestOnlineStatus = NewProductOnlineStatusConstant.ONLINE;
//                    messageTemplateId = newProductOnlinePassTemplateId;
//
//                    // spu编码和sku编码更新
//                    String spuOfferingCode = onlineOfflineParam.getSpuOfferingCode();
//                    String skuOfferingCode = onlineOfflineParam.getSkuOfferingCode();
//                    if (StringUtils.isEmpty(spuOfferingCode)
//                            || StringUtils.isEmpty(skuOfferingCode)) {
//                        throw new BusinessException(BaseErrorConstant.SPU_CODE_AND_SPU_CODE_NOT_EMPTY);
//                    }
//                    NewProductUpdateSpuAndSkuCodeParam spuAndSkuCodeParam = new NewProductUpdateSpuAndSkuCodeParam();
//                    spuAndSkuCodeParam.setId(newProductRequestId);
//                    spuAndSkuCodeParam.setSpuOfferingCode(spuOfferingCode);
//                    spuAndSkuCodeParam.setSkuOfferingCode(skuOfferingCode);
//                    newProductRequestManageService.updateSpuAndSkuOfferingCode(spuAndSkuCodeParam);
//
//                    // 未通过或最终审核通过时更新上下架套餐信息上下架请求
//                    newProductRequestOnlineOfflineComboInfoService.updateRequestPassById(1, comboInfoId);
//                }
//            } else {
//                requestOnlineStatus = NewProductOnlineStatusConstant.OFFLINING;
//                messageTemplateId = newProductOfflineInitiateTemplateId;
//                // 初审
//                if (BaseConstant.PRODUCT_OPERATOR_FRAME_ROLE.equals(roleType)) {
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_RECHECK;
//                    messageMap.put("requestNo", comboInfo.getRequestNo());
//                } else if (BaseConstant.PRODUCT_OPERATOR_RECHECK_ROLE.equals(roleType)) {
//                    // 复审
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_FINAL_JUDGMENT;
//                    messageMap.put("requestNo", comboInfo.getRequestNo());
//                } else if (BaseConstant.PRODUCT_OPERATOR_ULTIMATELY_ROLE.equals(roleType)
//                        || (BaseConstant.OPERATOR_ROLE.equals(roleType) && "商品下架-终审".equals(onlineOfflineParam.getRequestLink()))) {
//                    // 终审
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_OPERATER_JUDGMENT;
//                    messageMap.put("requestNo", comboInfo.getRequestNo());
//                } else if (BaseConstant.OPERATOR_ROLE.equals(roleType)) {
//                    // 运管审核
//                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE;
//                    messageTemplateId = newProductOfflinePassTemplateId;
//                    requestOnlineStatus = NewProductOnlineStatusConstant.OFFLINE;
//
//                    // 未通过或最终审核通过时更新上下架套餐信息上下架请求
//                    newProductRequestOnlineOfflineComboInfoService.updateRequestPassById(1, comboInfoId);
//                }
//            }
//        } else {
//            // 处理意见不能为空
//            if (StringUtils.isEmpty(handlerRemark)) {
//                throw new BusinessException(BaseErrorConstant.NOT_PASS_MUST_REMARK);
//            }
//            String onlineStatusRedisKey = RedisLockConstant.NEW_PRODUCT_ONLINE_STATUS.concat("_")
//                    .concat(manage.getRequestNo());
//            // 审核不通过保存原来的上下架状态
//            requestOnlineStatus = redisUtil.get(onlineStatusRedisKey) + "";
//
//            if (isOnline) {
//                // 上架审核不通过
//                onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_NOT_PASS;
//                messageTemplateId = newProductOnlineNotPassTemplateId;
//            } else {
//                // 下架审核不通过
//                onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_NOT_PASS;
//                messageTemplateId = newProductOfflineNotPassTemplateId;
//            }
//
//            // 未通过或最终审核通过时更新上下架套餐信息上下架请求
//            newProductRequestOnlineOfflineComboInfoService.updateRequestPassById(0, comboInfoId);
//        }

        //接口入口已经校验角色是否持有审核权限，所以直接根据请求当前状态，调整下一流程状态
        Integer currentOneOffRequestStatus = manage.getOnlineOfflineRequestStatus();
        // 审核通过
        if (isPass) {
            if (isOnline) {
                requestOnlineStatus = NewProductOnlineStatusConstant.ONLINING;
                messageTemplateId = newProductOnlineInitiateTemplateId;
                // 初审
                if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.ONLINE_FIRST_TRIAL)) {
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_RECHECK;
                    messageMap.put("requestNo", comboInfo.getRequestNo());

                    // 校验补充配置信息
                    validConfigReplenish(onlineOfflineParam);

                    String configReplenishId = onlineOfflineParam.getConfigReplenishId();
                    if (StringUtils.isEmpty(configReplenishId)) {

                    } else {
                        // 更新配置补充信息
                        updateConfigReplenish(onlineOfflineParam, date);
                    }

                } else if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.ONLINE_RECHECK)) {
                    // 复审
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_FINAL_JUDGMENT;
                    messageMap.put("requestNo", comboInfo.getRequestNo());
                } else if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.ONLINE_FINAL_JUDGMENT)) {
                    // 终审
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_OPERATOR_JUDGMENT;
                    messageMap.put("requestNo", comboInfo.getRequestNo());
                } else if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.ONLINE_OPERATOR_JUDGMENT)) {
                    // 运管审核
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE;
                    requestOnlineStatus = NewProductOnlineStatusConstant.ONLINE;
                    messageTemplateId = newProductOnlinePassTemplateId;

                    // spu编码和sku编码更新
                    String spuOfferingCode = onlineOfflineParam.getSpuOfferingCode();
                    String skuOfferingCode = onlineOfflineParam.getSkuOfferingCode();
                    if (StringUtils.isEmpty(spuOfferingCode)
                            || StringUtils.isEmpty(skuOfferingCode)) {
                        throw new BusinessException(BaseErrorConstant.SPU_CODE_AND_SPU_CODE_NOT_EMPTY);
                    }
                    NewProductUpdateSpuAndSkuCodeParam spuAndSkuCodeParam = new NewProductUpdateSpuAndSkuCodeParam();
                    spuAndSkuCodeParam.setId(newProductRequestId);
                    spuAndSkuCodeParam.setSpuOfferingCode(spuOfferingCode);
                    spuAndSkuCodeParam.setSkuOfferingCode(skuOfferingCode);
                    newProductRequestManageService.updateSpuAndSkuOfferingCode(spuAndSkuCodeParam);

                    // 未通过或最终审核通过时更新上下架套餐信息上下架请求
                    newProductRequestOnlineOfflineComboInfoService.updateRequestPassById(1, comboInfoId);
                }
            } else {
                requestOnlineStatus = NewProductOnlineStatusConstant.OFFLINING;
                messageTemplateId = newProductOfflineInitiateTemplateId;
                // 初审
                if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.OFFLINE_FIRST_TRIAL)) {
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_RECHECK;
                    messageMap.put("requestNo", comboInfo.getRequestNo());
                } else if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.OFFLINE_RECHECK)) {
                    // 复审
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_FINAL_JUDGMENT;
                    messageMap.put("requestNo", comboInfo.getRequestNo());
                } else if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.OFFLINE_FINAL_JUDGMENT)) {
                    // 终审
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_OPERATER_JUDGMENT;
                    messageMap.put("requestNo", comboInfo.getRequestNo());
                } else if (currentOneOffRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.OFFLINE_OPERATER_JUDGMENT)) {
                    // 运管审核
                    onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE;
                    messageTemplateId = newProductOfflinePassTemplateId;
                    requestOnlineStatus = NewProductOnlineStatusConstant.OFFLINE;

                    // 未通过或最终审核通过时更新上下架套餐信息上下架请求
                    newProductRequestOnlineOfflineComboInfoService.updateRequestPassById(1, comboInfoId);
                }
            }
        } else {
            // 处理意见不能为空
            if (StringUtils.isEmpty(handlerRemark)) {
                throw new BusinessException(BaseErrorConstant.NOT_PASS_MUST_REMARK);
            }
            String onlineStatusRedisKey = RedisLockConstant.NEW_PRODUCT_ONLINE_STATUS.concat("_")
                    .concat(manage.getRequestNo());
            // 审核不通过保存原来的上下架状态
            requestOnlineStatus = redisUtil.get(onlineStatusRedisKey) + "";

            if (isOnline) {
                // 上架审核不通过
                onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.ONLINE_NOT_PASS;
                messageTemplateId = newProductOnlineNotPassTemplateId;
            } else {
                // 下架审核不通过
                onlineOfflineRequestStatus = NewProductOnlineOfflineRequestStatusConstant.OFFLINE_NOT_PASS;
                messageTemplateId = newProductOfflineNotPassTemplateId;
            }

            // 未通过或最终审核通过时更新上下架套餐信息上下架请求
            newProductRequestOnlineOfflineComboInfoService.updateRequestPassById(0, comboInfoId);
        }


        // 进行流程处理保存
        saveHandleInfo(BaseServiceUtils.getId(),
                comboInfoId,
                onlineOfflineParam.getRequestLink(),
                userId,
                userName,
                nextHandlerUserId,
                onlineOfflineParam.getNextHandlerUserName(),
                handlerStatus,
                handlerRemark,
                handlerOnlineStatus,
                lastHandlerInfo.getUpdateTime(),
                date);


        //  申请表上下架状态的改变及当前处理人
        newProductRequestManageService.updateOnlineRelatedInfo(newProductRequestId,
                requestOnlineStatus,
                onlineOfflineRequestStatus,
                nextHandlerUserId);

        // 短信发送 不同角色发送不同短信
        if (StringUtils.isNotEmpty(nextHandlerUserId)) {
            messageMap.put("spuOfferingName", manage.getSpuOfferingName());
            messageMap.put("skuOfferingName", manage.getSkuOfferingName());
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(nextHandlerUserId);
            phoneList.add(data4UserBaseAnswer.getData().getPhone());
            sendPhoneMessage(messageTemplateId, messageMap, phoneList);
        }

/*        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code,
                ProductManageOperateEnum.ONLINE_OFFLINE_MANAGE.code,
                IotLogUtil.judgeOnlineOfflineProductContentFromRequest(onlineOfflineParam,manage,
                        onlineOfflineRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.OFFLINE)
                                || onlineOfflineRequestStatus.equals(NewProductOnlineOfflineRequestStatusConstant.ONLINE)));*/
    }

    /**
     * 导出商品上架信息
     */
    @Override
    public void exportNewProductOnlineOfflineInfo(NewProductOnlineOfflineDetailParam detailParam,
                                                  LoginIfo4Redis loginIfo4Redis,
                                                  HttpServletResponse response) {

        NewProductRequestManage manage = newProductRequestManageService.getNewProductRequestManageById(detailParam.getNewProductRequestId());
        OnlineRequestVO onlineRequestVO = getOnlineRequestDetail(detailParam, loginIfo4Redis);

        // 商品套餐信息
//        ComboInfoExcel comboInfoExcel = new ComboInfoExcel();
//        BeanUtils.copyProperties(manage, comboInfoExcel);
        ComboInfoExcel comboInfoExcel = JSONObject.parseObject(JSONObject.toJSONString(manage),ComboInfoExcel.class);
        BeanUtils.copyProperties(onlineRequestVO.getComboInfoVO(), comboInfoExcel);
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(manage.getCooperatorId());
        if (data4UserBaseAnswer != null) {
            Data4User data4User = data4UserBaseAnswer.getData();
            if (data4User != null) {
                comboInfoExcel.setCooperatorName(data4User.getName());
            }
        }
//        comboInfoExcel.setSupplyPrice(manage.getSupplyPrice().toString());
        comboInfoExcel.setProductManagerContact(manage.getProductManagerName() + " " + manage.getProductManagerPhone() + " " + manage.getProductManagerEmail());
        List<ComboInfoExcel> comboInfoExcelList = new ArrayList<>();
        comboInfoExcelList.add(comboInfoExcel);

        // 售后信息
        NewProductOnlineRequestAfterSaleInfoParam afterSaleInfoParam = onlineRequestVO.getAfterSaleInfoParam();
        List<AfterSaleInfoExcel> afterSaleInfoExcelList = new ArrayList<>();
        if (afterSaleInfoParam != null) {
            AfterSaleInfoExcel afterSaleInfoExcel = new AfterSaleInfoExcel();
            BeanUtils.copyProperties(afterSaleInfoParam, afterSaleInfoExcel);
            afterSaleInfoExcel.setSpuOfferingName(manage.getSpuOfferingName());
            afterSaleInfoExcel.setSkuOfferingName(manage.getSkuOfferingName());
            afterSaleInfoExcel.setStoreOrderHandler(afterSaleInfoParam.getStoreOrderHandlerUserName() + " "
                    + afterSaleInfoParam.getStoreOrderHandlerUserPhone() + " "
                    + afterSaleInfoParam.getStoreOrderHandlerUserEmail());
            afterSaleInfoExcel.setStoreOrderHandlerSub(afterSaleInfoParam.getStoreOrderHandlerSubUserName() + " "
                    + afterSaleInfoParam.getStoreOrderHandlerSubUserPhone() + " "
                    + afterSaleInfoParam.getStoreOrderHandlerSubUserEmail());
            afterSaleInfoExcel.setPreSaleManager(afterSaleInfoParam.getPreSaleManagerUserName() + " "
                    + afterSaleInfoParam.getPreSaleManagerUserPhone() + " "
                    + afterSaleInfoParam.getPreSaleManagerUserEmail());
            afterSaleInfoExcel.setProductShipUser(afterSaleInfoParam.getProductShipUserName() + " "
                    + afterSaleInfoParam.getProductShipUserPhone() + " "
                    + afterSaleInfoParam.getProductShipUserEmail());
            afterSaleInfoExcel.setProductInstaller(afterSaleInfoParam.getProductInstallUserName() + " "
                    + afterSaleInfoParam.getProductInstallUserPhone() + " "
                    + afterSaleInfoParam.getProductInstallUserEmail());
            afterSaleInfoExcel.setThingsCardUser(afterSaleInfoParam.getThingsCardUserName() + " "
                    + afterSaleInfoParam.getThingsCardUserPhone() + " "
                    + afterSaleInfoParam.getThingsCardUserEmail());
            afterSaleInfoExcel.setSoftwareAuthorityUser(afterSaleInfoParam.getSoftwareAuthorityUserName() + " "
                    + afterSaleInfoParam.getSoftwareAuthorityUserPhone() + " "
                    + afterSaleInfoParam.getSoftwareAuthorityUserEmail());
            afterSaleInfoExcel.setAfterSaleUser(afterSaleInfoParam.getAfterSaleUserName() + " "
                    + afterSaleInfoParam.getAfterSaleUserPhone() + " "
                    + afterSaleInfoParam.getAfterSaleUserEmail());
            afterSaleInfoExcelList.add(afterSaleInfoExcel);
        }

        // 配置补充
        List<ConfigReplenishExcel> configReplenishExcelList = new ArrayList<>();
        if (onlineRequestVO.getConfigReplenishVO() != null) {
            ConfigReplenishExcel configReplenishExcel = JSONObject.parseObject(JSONObject.toJSONString(onlineRequestVO.getConfigReplenishVO()),ConfigReplenishExcel.class);
//            ConfigReplenishExcel configReplenishExcel = new ConfigReplenishExcel();
//            BeanUtils.copyProperties(onlineRequestVO.getConfigReplenishVO(), configReplenishExcel);
            configReplenishExcel.setSpuOfferingClass("软件功能费+硬件（代销）");
            if ("101".equals(configReplenishExcel.getProductProperty())) {
                configReplenishExcel.setProductProperty("自有");
            } else if ("102".equals(configReplenishExcel.getProductProperty())) {
                configReplenishExcel.setProductProperty("非自有");
            }
//            configReplenishExcel.setSalePrice(onlineRequestVO.getConfigReplenishVO().getSalePrice().toString());
//            configReplenishExcel.setAtomHardwareSettlePrice(onlineRequestVO.getConfigReplenishVO().getAtomHardwareSettlePrice().toString());
//            configReplenishExcel.setAtomSoftwareSalePrice(onlineRequestVO.getConfigReplenishVO().getAtomSoftwareSalePrice().toString());
//            configReplenishExcel.setAtomHardwareSalePrice(onlineRequestVO.getConfigReplenishVO().getAtomHardwareSalePrice().toString());
//            configReplenishExcel.setProvinceAccrual(onlineRequestVO.getConfigReplenishVO().getProvinceAccrual().toString());
            configReplenishExcelList.add(configReplenishExcel);
        }

        try {
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .build();

            // 写入商品套餐信息
            WriteSheet comboInfoSheetWirter = EasyExcel.writerSheet(0, "商品套餐信息").head(ComboInfoExcel.class).build();
            excelWriter.write(comboInfoExcelList, comboInfoSheetWirter);

            // 写入售后信息
            WriteSheet afterSaleInfoSheetWirter = EasyExcel.writerSheet(1, "售后信息").head(AfterSaleInfoExcel.class).build();
            excelWriter.write(afterSaleInfoExcelList, afterSaleInfoSheetWirter);

            // 写入售后信息
            WriteSheet configReplenishSheetWirter = EasyExcel.writerSheet(2, "配置补充").head(ConfigReplenishExcel.class).build();
            excelWriter.write(configReplenishExcelList, configReplenishSheetWirter);

            String fileName = "商品上架信息"
                    + DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATE_FORMAT)
                    + ".xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
//            response.setHeader("content-type", "application/octet-stream;utf-8");
            response.setHeader("Access-Control-Expose-Headers", "content-disposition");
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            excelWriter.finish();

            //记录日志
/*            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.ONLINE_OFFLINE_MANAGE.code,
                    IotLogUtil.exportProductOnlineOfflineContentFromRequest(manage));*/
        } catch (IOException e) {
            log.error("生成商品上架信息excel输出流失败！,内容：{}", e.getMessage());
            throw new BusinessException(StatusConstant.EXPORT_PRODUCT_ONLINE_INFO_ERROR);
        }
    }

    /**
     * 发送短信
     *
     * @param templateId
     * @param message
     * @param phoneList
     */
    private void sendPhoneMessage(String templateId,
                                  Map<String, String> message,
                                  List<String> phoneList) {
        Msg4Request msg4Request = new Msg4Request();
        msg4Request.setTemplateId(templateId);
        msg4Request.setMessage(message);
        msg4Request.setMobiles(phoneList);
        smsFeignClient.asySendMessage(msg4Request);
    }

    /**
     * 保存申请的历史记录
     *
     * @param id
     * @param flowSourceId
     * @param requestLink
     * @param currentHandlerUserId
     * @param currentHandlerUserName
     * @param nextHandlerUserId
     * @param nextHandlerUserName
     * @param handlerStatus
     * @param handlerRemark
     * @param onlineStatus
     * @param createTime
     * @param updateTime
     */
    private void saveHandleInfo(String id,
                                String flowSourceId,
                                String requestLink,
                                String currentHandlerUserId,
                                String currentHandlerUserName,
                                String nextHandlerUserId,
                                String nextHandlerUserName,
                                String handlerStatus,
                                String handlerRemark,
                                String onlineStatus,
                                Date createTime,
                                Date updateTime) {
        NewProductRequestHandlerInfo handlerInfo = new NewProductRequestHandlerInfo();
        handlerInfo.setId(id);
        handlerInfo.setFlowSourceId(flowSourceId);
        handlerInfo.setFlowType(NewProductRequestFlowTypeConstant.PRODUCT_ONLINE_OFFLINE);
        handlerInfo.setRequestLink(requestLink);
        handlerInfo.setCurrentHandlerUserId(currentHandlerUserId);
        handlerInfo.setCurrentHandlerUserName(currentHandlerUserName);
        handlerInfo.setNextHandlerUserId(nextHandlerUserId);
        handlerInfo.setNextHandlerUserName(nextHandlerUserName);
        handlerInfo.setHandlerStatus(handlerStatus);
        handlerInfo.setHandlerRemark(handlerRemark);
        handlerInfo.setOnlineStatus(onlineStatus);
        handlerInfo.setCreateTime(createTime);
        handlerInfo.setUpdateTime(updateTime);
        newProductRequestHandlerInfoService.saveHandlerInfo(handlerInfo);
    }

    /**
     * 校验补充配置参数
     *
     * @param onlineOfflineParam
     */
    private void validConfigReplenish(NewProductJudgeOnlineOfflineParam onlineOfflineParam) {
        String storeFirstCatalog = onlineOfflineParam.getStoreFirstCatalog();
        String storeSecondCatalog = onlineOfflineParam.getStoreSecondCatalog();
        String atomOfferingName = onlineOfflineParam.getAtomOfferingName();
        Integer departmentId = onlineOfflineParam.getDepartmentId();
        String departmentName = onlineOfflineParam.getDepartmentName();
        String productProperty = onlineOfflineParam.getProductProperty();
        if (StringUtils.isEmpty(storeFirstCatalog)) {
            throw new BusinessException("10008", "商城展示一级类目不能为空");
        }

        if (StringUtils.isEmpty(storeSecondCatalog)) {
            throw new BusinessException("10008", "商城展示二级类目不能为空");
        }

        if (StringUtils.isEmpty(atomOfferingName)) {
            throw new BusinessException("10008", "原子商品名称不能为空");
        }

        if (departmentId == null) {
            throw new BusinessException("10008", "产品归属部门Id不能为空");
        }

        if (StringUtils.isEmpty(departmentName)) {
            throw new BusinessException("10008", "产品归属部门不能为空");
        }

        if (StringUtils.isEmpty(productProperty)) {
            throw new BusinessException("10008", "产品属性不能为空");
        }
    }

    /**
     * 保存配置补充
     *
     * @param newProductRequestId
     * @param comboInfoId
     * @param salePrice
     * @param supplyPrice
     * @param date
     */
    private void saveConfigReplenish(String newProductRequestId,
                                     String comboInfoId,
                                     BigDecimal salePrice,
                                     BigDecimal supplyPrice,
                                     Date date) {
        NewProductRequestOnlineOfflineConfigReplenish configReplenish = new NewProductRequestOnlineOfflineConfigReplenish();
        configReplenish.setId(BaseServiceUtils.getId());
        configReplenish.setNewProductRequestId(newProductRequestId);
        configReplenish.setComboInfoId(comboInfoId);
        configReplenish.setSalePrice(salePrice);
        BigDecimal atomHardwareSettlePrice = supplyPrice.multiply(new BigDecimal(0.98))
                .divide(new BigDecimal(1), 2, RoundingMode.HALF_UP);
        configReplenish.setAtomHardwareSettlePrice(atomHardwareSettlePrice);
        configReplenish.setAtomHardwareSalePrice(supplyPrice);
        BigDecimal atomSoftwareSalePrice = salePrice.add(atomHardwareSettlePrice.multiply(new BigDecimal(-1)));
        configReplenish.setAtomSoftwareSalePrice(atomSoftwareSalePrice);
        configReplenish.setProvinceAccrual(atomSoftwareSalePrice);
        configReplenish.setCreateTime(date);
        configReplenish.setUpdateTime(date);
        newProductRequestOnlineOfflineConfigReplenishService.saveConfigReplenish(configReplenish);
    }


    /**
     * 更新配置补充信息
     *
     * @param onlineOfflineParam
     * @param date
     */
    private void updateConfigReplenish(NewProductJudgeOnlineOfflineParam onlineOfflineParam,
                                       Date date) {
        NewProductRequestOnlineOfflineConfigReplenish configReplenish = new NewProductRequestOnlineOfflineConfigReplenish();
        configReplenish.setId(onlineOfflineParam.getConfigReplenishId());
        configReplenish.setStoreFirstCatalog(onlineOfflineParam.getStoreFirstCatalog());
        configReplenish.setStoreSecondCatalog(onlineOfflineParam.getStoreSecondCatalog());
        configReplenish.setSpuOfferingClass(SPUOfferingClassEnum.A06.getSpuOfferingClass());
        configReplenish.setAtomOfferingName(onlineOfflineParam.getAtomOfferingName());

        configReplenish.setDepartmentId(onlineOfflineParam.getDepartmentId());
        configReplenish.setDepartmentName(onlineOfflineParam.getDepartmentName());
        configReplenish.setProductProperty(onlineOfflineParam.getProductProperty());
        configReplenish.setUpdateTime(date);
        newProductRequestOnlineOfflineConfigReplenishService.updateConfigReplenishById(configReplenish);
    }

    /**
     * 设置全部商务相关
     *
     * @param onlineRequestParam
     * @param comboInfoId
     * @param newProductRequestId
     * @param date
     */
    private void saveBusiness(OnlineRequestParam onlineRequestParam,
                              String comboInfoId,
                              String newProductRequestId,
                              Date date) {
        List<NewProductRequestOnlineOfflineBusiness> businessList = new ArrayList<>();
        // 1.商务合同/协议
        NewProductOnlineRequestBusinessParam businessContract = onlineRequestParam.getBusinessContract();
        if (!businessContract.getFileType().equals(BUSINESS_CONTRACT)) {
            throw new BusinessException("10020", "商务合同/协议文件类型错误");
        }
        setBusiness(businessContract, businessList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);

        // 2.保证金
        NewProductOnlineRequestBusinessParam cashDeposit = onlineRequestParam.getCashDeposit();
        if (!cashDeposit.getFileType().equals(CASH_DEPOSIT)) {
            throw new BusinessException("10020", "保证金文件类型错误");
        }
        setBusiness(cashDeposit, businessList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);

        newProductRequestOnlineOfflineBusinessService.batchSaveBusiness(businessList);
    }

    /**
     * 设置单独商务相关
     *
     * @param businessParam
     * @param businessList
     * @param id
     * @param comboInfoId
     * @param newProductRequestId
     * @param date
     */
    private void setBusiness(NewProductOnlineRequestBusinessParam businessParam,
                             List<NewProductRequestOnlineOfflineBusiness> businessList,
                             String id,
                             String comboInfoId,
                             String newProductRequestId,
                             Date date) {
        NewProductRequestOnlineOfflineBusiness business = new NewProductRequestOnlineOfflineBusiness();
        business.setId(id);
        business.setNewProductRequestId(newProductRequestId);
        business.setComboInfoId(comboInfoId);
        business.setCreateTime(date);
        BeanUtils.copyProperties(businessParam, business);
        businessList.add(business);
    }

    /**
     * 设置全部商品素材
     *
     * @param onlineRequestParam
     * @param newProductRequestId
     * @param date
     */
    private void saveMaterial(OnlineRequestParam onlineRequestParam,
                              String comboInfoId,
                              String newProductRequestId,
                              Date date) {
        List<NewProductRequestOnlineOfflineMaterial> materialList = new ArrayList<>();

        // 1.头图
        NewProductOnlineRequestMaterialParam headerMaterial = onlineRequestParam.getHeaderMaterial();
        if (!headerMaterial.getFileType().equals(HEADER_MATERIAL)) {
            throw new BusinessException("10020", "头图文件类型错误");
        }
        setMaterial(headerMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);

        // 2.轮播图
        List<NewProductOnlineRequestMaterialParam> slideshowMaterialList = onlineRequestParam.getSlideshowMaterialList();
        slideshowMaterialList.forEach(slideshowMaterial -> {
            if (!slideshowMaterial.getFileType().equals(SLIDE_SHOW_MATERIAL)) {
                throw new BusinessException("10020", "轮播图文件类型错误");
            }
            setMaterial(slideshowMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
        });

        // 3.主图视频
        NewProductOnlineRequestMaterialParam videoMaterial = onlineRequestParam.getVideoMaterial();
        if (Optional.ofNullable(videoMaterial).isPresent()) {
            if (!videoMaterial.getFileType().equals(VIDEO_MATERIAL)) {
                throw new BusinessException("10020", "主图视频文件类型错误");
            }
            setMaterial(videoMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
        }

        // 4.商品详情图-PC端
        List<NewProductOnlineRequestMaterialParam> productDetailPCMaterialList = onlineRequestParam.getProductDetailPCMaterialList();
        productDetailPCMaterialList.forEach(productDetailPCMaterial -> {
            if (!productDetailPCMaterial.getFileType().equals(PRODUCT_DETAIL_PC_MATERIAL)) {
                throw new BusinessException("10020", "商品详情图-PC端文件类型错误");
            }
            setMaterial(productDetailPCMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
        });

        // 5.商品详情图-移动端
        List<NewProductOnlineRequestMaterialParam> productDetailMobileMaterialList = onlineRequestParam.getProductDetailMobileMaterialList();
        productDetailMobileMaterialList.forEach(productDetailMobileMaterial -> {
            if (!productDetailMobileMaterial.getFileType().equals(PRODUCT_DETAIL_MOBILE_MATERIAL)) {
                throw new BusinessException("10020", "商品详情图-移动端文件类型错误");
            }
            setMaterial(productDetailMobileMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
        });

        // 6.硬件商品图片（白底）
        NewProductOnlineRequestMaterialParam hardwareProductWhiteMaterial = onlineRequestParam.getHardwareProductWhiteMaterial();
        if (!hardwareProductWhiteMaterial.getFileType().equals(HARDWARE_PRODUCT_WHITE_MATERIAL)) {
            throw new BusinessException("10020", "硬件商品图片（白底）文件类型错误");
        }
        setMaterial(hardwareProductWhiteMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);

        // 7.商品logo图片（白底）
        NewProductOnlineRequestMaterialParam productLogoWhiteMaterial = onlineRequestParam.getProductLogoWhiteMaterial();
        if (!productLogoWhiteMaterial.getFileType().equals(PRODUCT_LOGO_WHITE_MATERIAL)) {
            throw new BusinessException("10020", "商品logo图片（白底）文件类型错误");
        }
        setMaterial(productLogoWhiteMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);

        // 8.产品功能介绍资料
        List<NewProductOnlineRequestMaterialParam> productFunctionDataMaterialList = onlineRequestParam.getProductFunctionDataMaterialList();
        if (CollectionUtils.isNotEmpty(productFunctionDataMaterialList)) {
            productFunctionDataMaterialList.forEach(productFunctionDataMaterial -> {
                if (!productFunctionDataMaterial.getFileType().equals(PRODUCT_FUNCTION_DATA_MATERIAL)) {
                    throw new BusinessException("10020", "产品功能介绍资料文件类型错误");
                }
                setMaterial(productFunctionDataMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
            });
        }

        // 9.产品性能参数资料
        List<NewProductOnlineRequestMaterialParam> productPerformanceDataMaterialList = onlineRequestParam.getProductPerformanceDataMaterialList();
        if (CollectionUtils.isNotEmpty(productPerformanceDataMaterialList)) {
            productPerformanceDataMaterialList.forEach(productPerformanceDataMaterial -> {
                if (!productPerformanceDataMaterial.getFileType().equals(PRODUCT_PERFORMANCE_DATA_MATERIAL)) {
                    throw new BusinessException("10020", "产品性能参数资料文件类型错误");
                }
                setMaterial(productPerformanceDataMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
            });
        }

        // 10.产品说明书，产品操作视频
        List<NewProductOnlineRequestMaterialParam> productSpecificationMaterialList = onlineRequestParam.getProductSpecificationMaterialList();
        if (CollectionUtils.isNotEmpty(productSpecificationMaterialList)) {
            productSpecificationMaterialList.forEach(productSpecificationMaterial -> {
                if (!productSpecificationMaterial.getFileType().equals(PRODUCT_SPECIFICATION_MATERIAL)) {
                    throw new BusinessException("10020", "产品说明书，产品操作视频文件类型错误");
                }
                setMaterial(productSpecificationMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
            });
        }

        // 11.产品安装指导书，指导视频
        List<NewProductOnlineRequestMaterialParam> productInstallMaterialList = onlineRequestParam.getProductInstallMaterialList();
        if (CollectionUtils.isNotEmpty(productInstallMaterialList)) {
            productInstallMaterialList.forEach(productInstallMaterial -> {
                if (Optional.ofNullable(productInstallMaterial).isPresent()) {
                    if (!productInstallMaterial.getFileType().equals(PRODUCT_INSTALL_MATERIAL)) {
                        throw new BusinessException("10020", "产品安装指导书，指导视频文件类型错误");
                    }
                    setMaterial(productInstallMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
                }
            });
        }

        // 12.软件、APP安装、操作指导书、指导视频
        List<NewProductOnlineRequestMaterialParam> relatedVideoMaterialList = onlineRequestParam.getRelatedVideoMaterialList();
        if (CollectionUtils.isNotEmpty(relatedVideoMaterialList)) {
            relatedVideoMaterialList.forEach(relatedVideoMaterial -> {
                if (Optional.ofNullable(relatedVideoMaterial).isPresent()) {
                    if (!relatedVideoMaterial.getFileType().equals(RELATED_VIDEO_MATERIAL)) {
                        throw new BusinessException("10020", "软件、APP安装、操作指导书、指导视频文件类型错误");
                    }
                    setMaterial(relatedVideoMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
                }
            });
        }

        // 13.售前营销话术资料FAQ
        List<NewProductOnlineRequestMaterialParam> preSaleMarketDataMaterialList = onlineRequestParam.getPreSaleMarketDataMaterialList();
        if (CollectionUtils.isNotEmpty(preSaleMarketDataMaterialList)) {
            preSaleMarketDataMaterialList.forEach(preSaleMarketDataMaterial -> {
                if (Optional.ofNullable(preSaleMarketDataMaterial).isPresent()) {
                    if (!preSaleMarketDataMaterial.getFileType().equals(PRE_SALE_MARKET_DATA_MATERIAL)) {
                        throw new BusinessException("10020", "售前营销话术资料FAQ文件类型错误");
                    }
                    setMaterial(preSaleMarketDataMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
                }
            });
        }

        // 14.产品常见问题咨询点资料FAQ
        List<NewProductOnlineRequestMaterialParam> productProblemDataMaterialList = onlineRequestParam.getProductProblemDataMaterialList();
        if (CollectionUtils.isNotEmpty(productProblemDataMaterialList)) {
            productProblemDataMaterialList.forEach(productProblemDataMaterial -> {
                if (Optional.ofNullable(productProblemDataMaterial).isPresent()) {
                    if (!productProblemDataMaterial.getFileType().equals(PRODUCT_PROBLEM_DATA_MATERIAL)) {
                        throw new BusinessException("10020", "产品常见问题咨询点资料FAQ文件类型错误");
                    }
                    setMaterial(productProblemDataMaterial, materialList, BaseServiceUtils.getId(), comboInfoId, newProductRequestId, date);
                }
            });
        }

        newProductRequestOnlineOfflineMaterialService.batchSaveMaterial(materialList);
    }

    /**
     * 设置单独商品素材
     *
     * @param materialParam
     * @param materialList
     * @param id
     * @param comboInfoId
     * @param newProductRequestId
     * @param date
     */
    private void setMaterial(NewProductOnlineRequestMaterialParam materialParam,
                             List<NewProductRequestOnlineOfflineMaterial> materialList,
                             String id,
                             String comboInfoId,
                             String newProductRequestId,
                             Date date) {
        NewProductRequestOnlineOfflineMaterial material = new NewProductRequestOnlineOfflineMaterial();
        material.setId(id);
        material.setNewProductRequestId(newProductRequestId);
        material.setComboInfoId(comboInfoId);
        material.setCreateTime(date);
        BeanUtils.copyProperties(materialParam, material);
        materialList.add(material);
    }

    /**
     * 售后信息
     *
     * @param onlineRequestParam
     * @param comboInfoId
     * @param newProductRequestId
     * @param date
     */
    private void saveAfterSaleInfo(OnlineRequestParam onlineRequestParam,
                                   String comboInfoId,
                                   String newProductRequestId,
                                   Date date) {
        NewProductOnlineRequestAfterSaleInfoParam afterSaleInfoParam = onlineRequestParam.getAfterSaleInfoParam();
        NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo = new NewProductRequestOnlineOfflineAfterSaleInfo();
        afterSaleInfo.setId(BaseServiceUtils.getId());
        afterSaleInfo.setComboInfoId(comboInfoId);
        afterSaleInfo.setNewProductRequestId(newProductRequestId);
        afterSaleInfo.setCreateTime(date);
        afterSaleInfo.setUpdateTime(date);
        BeanUtils.copyProperties(afterSaleInfoParam, afterSaleInfo);
        newProductRequestOnlineOfflineAfterSaleInfoService.saveAfterSaleInfo(afterSaleInfo);
    }

    /**
     * 套餐信息
     *
     * @param onlineRequestParam
     * @param id
     * @param newProductRequestId
     * @param date
     */
    private String saveComboInfo(OnlineRequestParam onlineRequestParam,
                                 String userId,
                                 String id,
                                 String newProductRequestId,
                                 String redisKey,
                                 Date date) {
        NewProductOnlineRequestComboInfoParam comboInfoParam = onlineRequestParam.getComboInfoParam();
        NewProductRequestOnlineOfflineComboInfo comboInfo = new NewProductRequestOnlineOfflineComboInfo();

        comboInfo.setId(id);
        String requestNo = getNewOddNumber(redisKey);
        comboInfo.setRequestNo(requestNo);
        comboInfo.setNewProductRequestId(newProductRequestId);
        comboInfo.setCreator(userId);
        comboInfo.setCreateTime(date);
        comboInfo.setUpdateTime(date);
        BeanUtils.copyProperties(comboInfoParam, comboInfo);
        newProductRequestOnlineOfflineComboInfoService.saveComboInfo(comboInfo);

        return requestNo;
    }


    @Override
    public BaseAnswer<Void> downLoadSingleFile() {
        return null;
    }

    @Override
    public BaseAnswer<Void> downLoadFilePack() {
        return null;
    }

    @Override
    public BaseAnswer<Void> deleteFileByKey(NewProductOnlineOfflineDeleteParam param) {
        log.info("deleteFileByKey Enter, param = {}",param);
        List<String> deleteFiles = param.getDeleteFiles();
        if (CollectionUtils.isEmpty(deleteFiles)) {
            throw new BusinessException(StatusConstant.UNEXIST_DELFILE_ERROR);
        }
        try {
            for (String key : deleteFiles) {
                log.info("deleteFileByKey for, key = {}",key);
                BaseAnswer<DelResult> data = storageService.delete(key);
                DelResult delResult = data.getData();
                List<String> failedKeys = delResult.getFailKeys();
                if (!CollectionUtils.isEmpty(failedKeys)) {
                    throw new BusinessException(StatusConstant.OSS_DEL_OR_SETEXPIRED_ERROR);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusConstant.SERVER_INTERNAL_ERROR,"删除文件出错");
        }
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<ProOnlineUploadVO> uploadFile(MultipartFile upfile, String fileType) {
        File file = null;
        ProOnlineUploadVO proOnlineUploadVo = new ProOnlineUploadVO();
        try {
            String name = upfile.getOriginalFilename();
            log.info("P online orgname = {}", name);
            file = multipartFileToFile(upfile);
            log.info("toFile = {}", file.getAbsolutePath());
//            String filePathName = String.format("%s/%s", "testOs",name);
            String prefix = System.currentTimeMillis() + "";
            String filePathName = String.format("%s/%s", OSS_DIR, prefix + name);
            BaseAnswer<UpResult> data = storageService.uploadFile(file, filePathName
                    , InvoiceConstant.IS_COVER, InvoiceConstant.EXPIRED_DAY);
            UpResult upResult = data.getData();
            String outerUrl = upResult.getOuterUrl();
            String innerUrl = upResult.getInnerUrl();
            String key = upResult.getKey();
            log.info("P online outUrl = {}; \r\n innerUrl = {}; \r\n key = {}", outerUrl, innerUrl, key);
            proOnlineUploadVo.setFileInnerUrl(innerUrl);
            proOnlineUploadVo.setFileOuterUrl(outerUrl);
            proOnlineUploadVo.setFileKey(key);
            proOnlineUploadVo.setFileType(fileType);
            proOnlineUploadVo.setFileName(name);
        } catch (Exception e) {
            log.error("OS系统上传对象存储失败！", e.getMessage());
            e.printStackTrace();
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERROR);
        }

        file.delete();
        return new BaseAnswer<ProOnlineUploadVO>().setData(proOnlineUploadVo);
    }

    @Override
    public BaseAnswer<String> uploadBackendFile(MultipartFile upfile, String dir) {
        File file = null;
        String retUrl = "";
        try {
            String name = upfile.getOriginalFilename();
            log.info("uploadBackendFile orgname = {}", name);
            file = multipartFileToFile(upfile);
            log.info("toFile = {}", file.getAbsolutePath());
            String filePathName = String.format("%s/%s", dir, name);
            BaseAnswer<UpResult> data = storageService.uploadFile(file, filePathName
                    , InvoiceConstant.IS_COVER, InvoiceConstant.EXPIRED_DAY);
            UpResult upResult = data.getData();
            retUrl = upResult.getOuterUrl();
        } catch (Exception e) {
            log.error("OS系统上传对象存储失败！", e.getMessage());
            e.printStackTrace();
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERROR);
        }
        file.delete();
        return new BaseAnswer<String>().setData(retUrl);
    }

    @Override
    public BaseAnswer<ProOnlineDownloadVO> downLoadFile(NewProductOnlineOfflineDownloadParam param) {
        log.info("downLoadFile param = {}",param);
        List<NewProductOnlineOfflineDownloadParam.DownLoadParam> upUrlList = param.getDownloadUrl();
        log.info("upUrlList = {}",upUrlList);
        if (CollectionUtils.isEmpty(upUrlList)) {
            throw new BusinessException(StatusConstant.NO_UPLOAD_FILE);
        }

        BaseAnswer<ProOnlineDownloadVO> answer = new BaseAnswer<>();

        if(upUrlList.size()==1){
            //如果只有一个，直接返回
            ProOnlineDownloadVO proOnlineDownloadVo = new ProOnlineDownloadVO();
            String fileKey = upUrlList.get(0).getFileKey();
            String outerUrl = storageService.getStorageDomain(fileKey);
            proOnlineDownloadVo.setFileOuterUrl(outerUrl);
            proOnlineDownloadVo.setCacheDir(outerUrl + "/");
            proOnlineDownloadVo.setFileKey(fileKey);
            answer.setData(proOnlineDownloadVo);
            return answer;
        }



        String zipName = param.getZipName();
        String cacheName = System.currentTimeMillis() + LOCAL_CACHEDIR;

        try {
            File path = new File(ResourceUtils.getURL("classpath:").getPath());
            log.info("file class path = {}", path.getAbsolutePath());    //D:\CompanyProject\supply-chain\iot-service\target\classes

            path = new File("");
            log.info("file class path2 = {}", path.getAbsolutePath());   //D:\CompanyProject\supply-chain
            File fileCache = new File(path.getAbsolutePath(), cacheName);
            log.info("file1 path = {}", fileCache.getAbsolutePath());
            //       file1.mkdir();
            fileCache.mkdirs();
            log.info("bp1");

            for (NewProductOnlineOfflineDownloadParam.DownLoadParam item : upUrlList) {
                File downloadFile = new File(path.getAbsolutePath() + "/" + cacheName, item.getFileName());
                storageService.downloadFile(item.getFileKey(), downloadFile.getAbsolutePath());

            }
            //打包文件夹
            File zipFile = new File(path.getAbsolutePath() + "/" + zipName);
            File[] srcFile = fileCache.listFiles();
            byte[] buffer = new byte[1024];
            try {
                ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFile));
                for (int i = 0; i < srcFile.length; i++) {
                    FileInputStream fileInputStream = new FileInputStream(srcFile[i]);
                    out.putNextEntry(new ZipEntry(srcFile[i].getName()));
                    int length;
                    while ((length = fileInputStream.read(buffer)) > 0) {
                        out.write(buffer, 0, length);
                    }
                    out.closeEntry();
                    fileInputStream.close();
                }
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException(StatusConstant.SERVER_INTERNAL_ERROR,"下载文件读取出错");
            }

            //上传zip到对象存储，产出本地zip和cache
            String storageDir = OSS_DIR + "/" + OSS_CACHEDIR + "/" + System.currentTimeMillis() + ZIPDIR_SUFFIX;
            String filePathName = String.format("%s/%s", storageDir, zipName);
            BaseAnswer<UpResult> data = storageService.uploadFile(zipFile, filePathName
                    , InvoiceConstant.IS_COVER, InvoiceConstant.TEMP_EXPIRED_DAY);
            UpResult upResult = data.getData();
            ProOnlineDownloadVO proOnlineDownloadVo = new ProOnlineDownloadVO();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
//            String fileName = upResult.getFileName();
            proOnlineDownloadVo.setFileOuterUrl(outerUrl);
//            proOnlineDownloadVo.setFileName(fileName);
            proOnlineDownloadVo.setCacheDir(storageDir + "/");
            proOnlineDownloadVo.setFileKey(fileKey);
            answer.setData(proOnlineDownloadVo);
//            log.info("P online outUrl = {}; \r\n innerUrl = {}; \r\n key = {}",outerUrl, innerUrl,key);
            //删除本地缓存目录
            zipFile.delete();
            deleteFile(fileCache);
            //待掉接口删除
//            storageService.deleteCacheDir(storageDir+"/");
            //删除对象存储目录

        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusConstant.SERVER_INTERNAL_ERROR,"下载文件出错");
        }
        return answer;

    }

    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private boolean deleteFile(File dirFile) {
        if (!dirFile.exists()) {
            return false;
        }
        if (dirFile.isFile()) {
            return dirFile.delete();
        } else {
            for (File file : dirFile.listFiles()) {
                deleteFile(file);
            }
        }
        return dirFile.delete();
    }

    @Override
    public BaseAnswer<Void> deleteDir(String dirPath) {
        log.info("NPService deleteDir dirPath = {}",dirPath);
        try {
//            storageService.deleteDir(bucket, path);
            storageService.deleteCacheDir(dirPath);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(StatusConstant.OSS_DEL_ERROR);
        }
        return new BaseAnswer<>();
    }

    private HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为白色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.index);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //边框
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //自动换行
        headWriteCellStyle.setWrapped(true);
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteFont.setFontName("宋体");
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // 背景绿色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        //自动换行
        contentWriteCellStyle.setWrapped(true);
        //文字
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteFont.setFontName("宋体");
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
