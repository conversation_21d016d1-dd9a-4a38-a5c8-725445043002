package com.chinamobile.iot.sc.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30
 * @description 授权额度/额度释放同步
 */
@Data
public class LimitGetRequest {

    /**
     * 交易流水，交易的唯一标识，发起方保证唯一
     */

    private String transID;

    /**
     * 省代码，参见附录4.1省代码
     */

    private String companyID;

    /**
     * 服务包编码，参考4.15服务列表
     */

    private String serviceCode;



}
