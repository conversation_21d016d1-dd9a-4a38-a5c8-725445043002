package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 订单数据割接增加支付/退款时间
 *
 */
@Data
public class OrderInfoTime {

    @ExcelProperty(value="ORDERID",index=0)
    private String orderId;

    /**
     * 支付时间
     */
    @ExcelProperty(value="PAYTIME",index=1)
    private String payTime;

    /**
     * 退款时间
     */
    @ExcelProperty(value="REFUNDTIME",index=2)
    private String refundTime;

}
