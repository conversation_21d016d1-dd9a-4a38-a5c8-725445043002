package com.chinamobile.iot.sc.request;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023/1/10 14:50
 * @description: 商城向os系统同步特殊退货退款状态结果
 **/
@Data
public class SpecialReturnRefundResultRequest {
    /**
     * 业务订单流水号
     */
    private String orderId;

    /**
     * 1：待退款
     * 2：退款中
     * 3：退款成功
     * 4：退款取消
     */
    private String status;

    /**
     * 	statusTime	1	String	F14	状态时间	格式：yyyyMMddhhmmss
     * 状态变更的时间
     */
    private String statusTime;

    /**
     * yyyyMMddhhmmss
     * 开启特殊退货退款+7天，即特殊退货退款入口关闭的时间
     */
    private String latestTime;
    /**
     * 	特殊退款数量
     */
    private String refundsNumber;
}
