package com.chinamobile.iot.sc.constant;

/**
 * @Author: YSC
 * @Date: 2021/12/8 14:10
 * @Description: 和IOT商城订单状态枚举类 暂未使用，仅作为查阅使用
 */
public enum OrderStatusIOTEnum {
    // 订单状态
    ORDER_CREATE(0, "订单创建"),
    ORDER_RECEIVE(1, "订单验收"),
    ORDER_COLLECT(3, "订单计收"),
    ORDER_REFUND(4, "订单退款完成"),
    //预受理单（待客下单订单待合作伙伴接单时，同步本状态,可能会被驳回后多次同步本状态）
    VALET_ORDER_PREPARED(9, "预受理单"),
    /**
     * 个人客户物联网公司退款成功时，同步本状态
     */
    ORDER_CMIOT_REFUND(5, "订单部分退款完成"),
    /**
     * 个人客户省公司退款成功时，同步本状态
     */
    ORDER_PROVINCE_REFUND(6, "订单部分退款完成"),
    /**
     * 个人客户省公司退款成功时，同步本状态
     */
    ORDER_READY_SEND_GOODS(10, "待发货"),
    /**
     * 订单部分退款完成
     */
    ORDER_PART_SUCCESS(11, "订单部分退款完成"),
    /**
     * 待收货/待客户经理确认交付
     */
    ORDER_WAIT_RECEIVE(12, "待收货/待客户经理确认交付");

    /**
     * 订单状态码
     */
    private final Integer status;
    /**
     * 订单状态信息
     */
    private final String message;

    OrderStatusIOTEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 获取订单状态信息
     * @param status
     * @return
     */
    public static String getOrderStatusMessage(Integer status){
        OrderStatusIOTEnum[] values = OrderStatusIOTEnum.values();
        for (OrderStatusIOTEnum value : values) {
            if (value.getStatus().equals(status)){
                return value.getMessage();
            }
        }
        return "";
    }
}
