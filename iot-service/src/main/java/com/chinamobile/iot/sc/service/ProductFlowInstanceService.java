package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.ProductFlow;
import com.chinamobile.iot.sc.pojo.ProductFlowInstance;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.pojo.vo.productFlowInfo.ShelfSpuDetailVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ProductFlowInstanceService {
    BaseAnswer<List<ShelfCategoryListVO>> getShelfCategoryList();

    BaseAnswer<List<ShelfCategoryListVO>> getCmiotCostListByCategoryId(String categoryId);

    BaseAnswer<List<NavigationListVO>> getFirstNavigationList();

    BaseAnswer<List<NavigationListVO>> getSecondNavigationList(List<String> parentId);

    BaseAnswer uploadAttachment(MultipartFile file, String flowInstanceId, Integer type);

    BaseAnswer<List<AttachmentListVO>> getAttachmentList(String flowInstanceId);

    BaseAnswer<PageData<ProductFlowInstanceListVO>> getFlowInstanceList(LoginIfo4Redis loginIfo4Redis, FlowInstanceListParam param);

    BaseAnswer cancelFlow(String flowInstanceId, LoginIfo4Redis loginIfo4Redis,Integer subModule);

    BaseAnswer auditProductFlow(AuditProductFlowParam param, LoginIfo4Redis loginIfo4Redis, Integer subModule,String ip);



    BaseAnswer spuShelf(Integer productStandard, Integer productType, MultipartFile file, LoginIfo4Redis loginIfo4Redis) throws IOException;

    BaseAnswer skuShelf(String spuCode, String existSkuCode, MultipartFile file, LoginIfo4Redis loginIfo4Redis) throws IOException;

    /**
     * 产品流程变更流程
     *
     * @param skuInfoUpdateParam
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer skuInfoUpdate(SkuInfoUpdateParam skuInfoUpdateParam, LoginIfo4Redis loginIfo4Redis);


    BaseAnswer skuDelist(SkuInfoDelistParam skuInfoDelistParam, LoginIfo4Redis loginIfo4Redis,String ip);
    BaseAnswer<List<ProductStandardListVO>> getProductStandardList();

    BaseAnswer<List<ProductTypeListVO>> getProductTypeList(Integer productStandardCode);
    BaseAnswer<List<ProductTypeListVO>> getProductOperateList();

    BaseAnswer<PageData<ShelfSpuInfoVO>> getShelfSpuList(LoginIfo4Redis loginIfo4Redis, String userid, FlowInstanceSpuListParam param);

    BaseAnswer<List<ProductFlowInstanceSku>> getShelfSkuListBySpu(String spuCode);
    BaseAnswer<List<ProductFlowInstanceSku>> getShelfSkuNewListBySpu(String spuCode);
    BaseAnswer<ProductFlowInstanceDetailVO> getInstanceDetail(String flowInstanceId, LoginIfo4Redis loginIfo4Redis, Integer subModule);


    BaseAnswer<ShelfSpuDetailVO> getShelfSpuDeatail(String flowInstanceId,String spuCode,String skuCode);
    BaseAnswer shelfSpuDetailRecord(ShelfSpuDetailRecordParam param);

    void skuExport(LoginIfo4Redis loginIfo4Redis,String flowInstanceId,String spuCode,String skuCode,String ip);
    void skuExportAll(LoginIfo4Redis loginIfo4Redis,String ip);

    BaseAnswer editFlow(ProductFlowInstanceEditParam param, LoginIfo4Redis loginIfo4Redis, boolean addLog, ProductFlowInstanceSpu flowInstanceSpu, ProductFlowInstanceSku flowInstanceSku, ProductFlowInstance flowInstance, ProductFlow productFlow,String ip,Boolean specialEdit);
    BaseAnswer editDelFlow(ProductFlowInstanceEditParam param, LoginIfo4Redis loginIfo4Redis,boolean addLog);


    String getLogContent(String title, String flowInstanceNumber, String spuName, String skuName, String resultDesc, String downloadAttachmentList, String url, String spuCode, String skuCode,String oldFlowNumber,String newFlowNumber);

    BaseAnswer<Boolean> hasRunningFlowInstance(String userId);

    BaseAnswer addDetaiAttachmentlLog(FlowInstanceAddDetaiAttachmentlLogParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<ShelfSpuSimpleListVO>> getShelfSpuSimpleList(LoginIfo4Redis loginIfo4Redis, ShelfSpuSimpleListParam param);

    BaseAnswer deleteAttachment(String id);

    BaseAnswer<List<ProductFlowAuditStepListVO>> getAuditStepList(String flowInstanceId);

    Integer getOperateType(Integer productType);

    BaseAnswer specialEdit(ProductFlowInstanceEditParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer importAttachment(MultipartFile file);

    public String getShelfCmiotCostIdByName(String costName, String shelfCatagoryId);

    public String getNavigationIdByName(String firstDirectoryName,String parentId);

    BaseAnswer dealProductFlowInstanceDirectory();

    void updateProductFlowInstanceDirectoryName();

    void updateProductFlowInstanceDirectoryNameTest();

    BaseAnswer<List<ProductFlowInstanceDetailVO.NavigationDirectory>> navigationData();
}
