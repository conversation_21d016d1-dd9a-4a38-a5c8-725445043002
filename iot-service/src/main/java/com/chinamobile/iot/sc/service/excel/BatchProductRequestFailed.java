package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/9/26 10:51
 * @description: 批量新增产品申请失败信息
 **/
@Data
public class BatchProductRequestFailed {

    @ExcelProperty(value = "商品名称SPU")
    private String spuOfferingName;

    /**
     * 商品名称SKU
     */
    @ExcelProperty(value = "商品名称SKU")
    private String skuOfferingName;


    @ExcelProperty(value="失败原因")
    private String failedReason;

}
