package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.request.sms.Msg4Request;
import com.chinamobile.iot.sc.response.iot.IOTSmsResponse;

public interface IOTSmsService {
    /**
     * 发送短信
     * @param baseRequest
     * @return
     */
    IOTAnswer<JSONObject> sendSms(IOTRequest baseRequest);
}
