package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.Order2cAttachmentMapper;
import com.chinamobile.iot.sc.pojo.Order2cAttachment;
import com.chinamobile.iot.sc.pojo.Order2cAttachmentExample;
import com.chinamobile.iot.sc.pojo.vo.OrderAttachmentVO;
import com.chinamobile.iot.sc.service.Order2cAttachmentService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2024/10/10 16:13
 */
@Service
public class Order2cAttachmentServiceImpl implements Order2cAttachmentService {


    @Resource
    private Order2cAttachmentMapper order2cAttachmentMapper;


    @Override
    public List<OrderAttachmentVO> findOrderAttachment(String orderId) {
        //查询附件
        Order2cAttachmentExample attachmentExample = new Order2cAttachmentExample().createCriteria().andOrderIdEqualTo(orderId).example();
        List<Order2cAttachment> order2cAttachmentList = order2cAttachmentMapper.selectByExample(attachmentExample);
        if(CollectionUtils.isNotEmpty(order2cAttachmentList)){
            List<OrderAttachmentVO> collect = order2cAttachmentList.stream().map(o -> {
                OrderAttachmentVO vo = new OrderAttachmentVO();
                BeanUtils.copyProperties(o, vo);
                return vo;
            }).collect(Collectors.toList());
            return collect;
        }
        return new ArrayList<>();
    }
}
