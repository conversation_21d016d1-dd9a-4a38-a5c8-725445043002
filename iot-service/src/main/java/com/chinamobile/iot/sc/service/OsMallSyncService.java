package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.CardInfo;
import com.chinamobile.iot.sc.response.iot.LimitSyncInfoResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/2
 * @description OS与商城同步数据的service接口类
 */
public interface OsMallSyncService {

    /**
     * 3.3.4号卡信息同步
     * @param baseRequest
     * @return
     */
    IOTAnswer<Void> syncNumberCardInfos(IOTRequest baseRequest);


    /**
     *
     * @param cardInfo
     */
    void handleCardInfoFile(CardInfo cardInfo);

    void updateCardInventory(String id,Integer currentInventory ,Integer totalInventory);
}
