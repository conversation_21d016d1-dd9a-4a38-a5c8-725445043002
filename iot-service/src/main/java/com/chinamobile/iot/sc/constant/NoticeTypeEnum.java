package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22
 * @description 短信通知类型枚举类
 */
public enum NoticeTypeEnum {

    NOTICE_TYPE_KX(1,"卡+x退换短信通知"),
    NOTICE_TYPE_YSX_SERVICE(2,"代销云视讯服务开通失败短信通知"),
    NOTICE_TYPE_CAR_SECURITY_SERVICE(3,"行车卫士服务开通失败短信通知"),
    NOTICE_TYPE_INVENTORY_ALARM(4,"库存警报短信通知"),
    NOTICE_TYPE_QLY_ALARM(5,"千里眼软件服务开通失败/退订失败短信通知"),
    NOTICE_TYPE_HYC_ALARM(6,"和易充软件服务开通失败/退订失败短信通知"),
    NOTICE_TYPE_XCWS_ALARM(7,"行车卫士短信包软件服务开通失败/退订失败短信通知"),
    NOTICE_TYPE_ONENET_ALARM(8,"OneNET公有云软件服务开通失败/退订失败短信通知"),
    NOTICE_TYPE_HM_ALARM(9,"和目业务平台软件服务开通失败/退订失败短信通知"),
    NOTICE_TYPE_SERVICE_INSUFFICIENT_INVENTORY_ALARM(10,"服务类商品库存不足通知"),
    NOTICE_TYPE_SERVICE_INVENTORY_ALARM_ALARM(11,"服务类商品库存警报通知"),
    NOTICE_TYPE_ONECYBER_ALARM(12,"OneCyber标品软件服务开通失败短信通知");

    /**
     * 通知类型
     */
    private Integer type;

    /**
     * 通知描述
     */
    private String desc;

    NoticeTypeEnum(Integer type,String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(Integer type) {
        for (NoticeTypeEnum value : NoticeTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }
}
