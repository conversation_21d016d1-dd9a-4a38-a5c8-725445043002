package com.chinamobile.iot.sc.request.exhandle;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @package: com.chinamobile.iot.sc.request.exhandle
 * @ClassName: Request4ExHandleAdd
 * @description: 异常处理信息新增请求
 * @author: zyj
 * @create: 2022/1/28 14:58
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class Request4ExHandleAdd {
    @NotBlank(message = "业务订单号不能为空！")
    private String orderId;
    @NotBlank(message = "原子订单号不能为空！")
    private String atomOrderId;
    //退款订单请求流水号
    private String refundOrderId;
    @NotBlank(message = "异常类型不能为空！")
    private String expectType;
    @NotBlank(message = "处理选项不能为空！")
    private String handleType;
    @NotBlank(message = "订单类型不能为空！")
    private String orderType;
    @NotBlank(message = "详细说明不能为空！")
    @Length(min = 1, max = 500, message = "详细说明不超过500字！")
    private String reason;
}
