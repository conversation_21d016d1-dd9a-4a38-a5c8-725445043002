package com.chinamobile.iot.sc.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.dao.ext.OpenOrderMapperExt;
import com.chinamobile.iot.sc.dao.ext.ShopCustomerInfoMapperExt;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.OpenAbilityOrganizationRO;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.CustCodeCustIdDTO;
import com.chinamobile.iot.sc.pojo.dto.CyzqListDTO;
import com.chinamobile.iot.sc.pojo.param.CyzqListParam;
import com.chinamobile.iot.sc.pojo.vo.CyzqListVO;
import com.chinamobile.iot.sc.service.OpenOrderService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/21
 * @description 订单数据对外接口service实现类
 */
@Service
public class OpenOrderServiceImpl  implements OpenOrderService {

    @Resource
    private OpenOrderMapperExt openOrderMapperExt;

    @Resource
    private ShopCustomerInfoMapperExt shopCustomerInfoMapperExt;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;
    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Override
    public PageData<CyzqListVO> pageCyzqList(CyzqListParam cyzqListParam,
                                             OpenAbilityOrganizationRO organizationRO) {

        List<CyzqListVO> cyzqListVOList = new ArrayList<>();

        // 时间格式转换
        String startTime = cyzqListParam.getStartTime();
        String endTime = cyzqListParam.getEndTime();
        if (StringUtils.isNotEmpty(startTime)){
            startTime = DateUtils.toStringDate(startTime,DateUtils.DEFAULT_DATETIME_FORMAT,DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            cyzqListParam.setStartTime(startTime);
        }

        if (StringUtils.isNotEmpty(endTime)){
            endTime = DateUtils.toStringDate(endTime,DateUtils.DEFAULT_DATETIME_FORMAT,DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            cyzqListParam.setEndTime(endTime);
        }

        PageData<CyzqListVO> pageData = new PageData<>();
        Integer pageSize = cyzqListParam.getNum();
        Integer pageNum = cyzqListParam.getPage();

        Page<CyzqListDTO> page = new Page<>(pageNum, pageSize);
        List<CyzqListDTO> cyzqListDTOList = openOrderMapperExt.listCyzq(page, cyzqListParam);
        if (CollectionUtils.isNotEmpty(cyzqListDTOList)){
            // 获取custId数据
            Map<String,String> custCodeCustIdMap = new HashMap<>();
            Set<String> custCodeSet = cyzqListDTOList.stream().map(e -> {
                //解密客户编码
                String custCode = e.getCustCode();
                if (StringUtils.isNotEmpty(custCode)){
                    custCode = IOTEncodeUtils.decryptSM4(custCode, iotSm4Key,iotSm4Iv);
                    e.setCustCode(custCode);
                }
                return custCode;
            }).collect(Collectors.toSet());
            List<CustCodeCustIdDTO> custCodeCustIdList = shopCustomerInfoMapperExt.custCodeCustIdList(new ArrayList<>(custCodeSet));
            if(CollectionUtils.isNotEmpty(custCodeCustIdList)){
                custCodeCustIdList.forEach(c -> {
                    custCodeCustIdMap.put(c.getCustCode(),c.getCustId());
                });
                for (CyzqListDTO cyzqListDTO : cyzqListDTOList) {
                    cyzqListDTO.setCustId(custCodeCustIdMap.get(cyzqListDTO.getCustCode()));
                }
            }


            for (int i = 0; i < cyzqListDTOList.size(); i++) {
                CyzqListDTO cyzqListDTO = cyzqListDTOList.get(i);
                CyzqListVO cyzqListVO = new CyzqListVO();
                BeanUtils.copyProperties(cyzqListDTO,cyzqListVO);
                try {
                    // 个人账号（客户手机号，加密传输）
                    String custId = cyzqListDTO.getCustId();
                    if (StringUtils.isNotEmpty(custId)){
                        cyzqListVO.setCustphone(BaseUtils.aesEncrypt(custId,organizationRO.getSecret()));
                    }

                    //解密收货人电话
                    String contactPhone = IOTEncodeUtils.decryptSM4(cyzqListDTO.getContactPhone(), iotSm4Key,iotSm4Iv);
                    cyzqListVO.setContactPhone(BaseUtils.aesEncrypt(contactPhone,organizationRO.getSecret()));
                    
                    //解密收货人姓名
                    String contactPersonName = IOTEncodeUtils.decryptSM4(cyzqListDTO.getContactPersonName(), iotSm4Key,iotSm4Iv);
                    cyzqListVO.setContactPersonName(BaseUtils.aesEncrypt(contactPersonName,organizationRO.getSecret()));

                    //解析加密的省市区镇，获得收货地址
                    String decryptedAddr1 = IOTEncodeUtils.decryptSM4(cyzqListDTO.getAddr1(), iotSm4Key,iotSm4Iv);
                    String decryptedAddr2 = IOTEncodeUtils.decryptSM4(cyzqListDTO.getAddr2(), iotSm4Key,iotSm4Iv);
                    String decryptedAddr3 = IOTEncodeUtils.decryptSM4(cyzqListDTO.getAddr3(), iotSm4Key,iotSm4Iv);
                    String decryptedAddr4 = IOTEncodeUtils.decryptSM4(cyzqListDTO.getAddr4(), iotSm4Key,iotSm4Iv);
                    String decryptedUsaddr = IOTEncodeUtils.decryptSM4(cyzqListDTO.getUsaddr(), iotSm4Key,iotSm4Iv);
                    String address = (StringUtils.isEmpty(decryptedAddr1) ? "" : decryptedAddr1) +
                            (StringUtils.isEmpty(decryptedAddr2) ? "" : decryptedAddr2) +
                            (StringUtils.isEmpty(decryptedAddr3) ? "" : decryptedAddr3) +
                            (StringUtils.isEmpty(decryptedAddr4) ? "" : decryptedAddr4) +
                            (StringUtils.isEmpty(decryptedUsaddr) ? "" : decryptedUsaddr);
                    if (StringUtils.isNotEmpty(address)){
                        cyzqListVO.setContactAddr(BaseUtils.aesEncrypt(address,organizationRO.getSecret()));
                    }
                } catch (Exception e) {
                    throw new BusinessException("10001","加密数据失败");
                }
                cyzqListVOList.add(cyzqListVO);
            }
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(cyzqListVOList);

        return pageData;
    }
}
