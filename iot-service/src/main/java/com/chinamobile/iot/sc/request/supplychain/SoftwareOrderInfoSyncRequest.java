package com.chinamobile.iot.sc.request.supplychain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 软件订单信息同步request
 **/
@Data
@Accessors(chain = true, fluent = false )
public class SoftwareOrderInfoSyncRequest {
    @JSONField(name = "ROOT")
    private ROOT ROOT;

    @Data
    @Accessors(chain = true, fluent = false )
    public static class ROOT {
        @JSONField(name = "BODY")
        private BODY BODY;

        @JSONField(name = "HEADER")
        private HEADER HEADER;
    }

    @Data
    @Accessors(chain = true, fluent = false )
    public static class HEADER {
    }
    @Data
    @Accessors(chain = true, fluent = false )
    public static class BODY {
        @JSONField(name = "AUTH_INFO")
        private AuthInfo AUTH_INFO;
        @JSONField(name = "BUSI_INFO")

        private BusiInfo BUSI_INFO;
    }
    @Data
    @Accessors(chain = true, fluent = false )
    public static class AuthInfo {
        @J<PERSON>NField(name = "USER_ID")
        private String USER_ID;
        @J<PERSON>NField(name = "SIGN")
        private String SIGN;
        @JSONField(name = "POST_DATE")
        private String POST_DATE;
    }

    @Data
    @Accessors(chain = true, fluent = false )
    public static class BusiInfo {
        @JSONField(name = "RECEIVABLE_INFO_LIST")
        private List<OrderHeaderInfo> RECEIVABLE_INFO_LIST;
    }


    @Data
    @Accessors(chain = true, fluent = false )
    public static class OrderHeaderInfo {

        //应收单号 生成规则：ARM+8位数字
        @JSONField(name = "BILL_NO")
        private String BILL_NO;

        //业务日期订单归档时间
        @JSONField(name = "BUSINESS_DATE")
        private String BUSINESS_DATE;

        //结算组织
        @JSONField(name = "COMPANY")
        private String COMPANY;

        //结算组织名称
        @JSONField(name = "COMPANY_NAME")
        private String COMPANY_NAME;

        @JSONField(name = "CURRENCY")
        //币别 传入币别代码，币别规则如下人民币 PRE001,港元PRE002,欧元PRE003,日元PRE004,美元PRE007
        private String CURRENCY;
        @JSONField(name = "BILL_TYPE_ID")
        //单据类型标准应收单，默认标准应收单
        private String BILL_TYPE_ID;
        @JSONField(name = "APPROVE_DATE")
        //审核日期，取生成应收单时间
        private String APPROVE_DATE;
        @JSONField(name = "APPROVE_ID")
        //销售员工号
        private String APPROVE_ID;
        @JSONField(name = "VALOREM_TOTAL")
        //价税合计，销售订单含税总金额
        private BigDecimal VALOREM_TOTAL;
        @JSONField(name = "CLIENT_NAME")
        //客户名称，默认：IoT应用商城
        private String CLIENT_NAME;
        @JSONField(name = "CLIENT")
        //客户编码, 默认*********
        private String CLIENT;
        @JSONField(name = "SALE_ORG_ID")
        //销售组织, 销售部门的编码
        //0390开头
        private String SALE_ORG_ID;
        @JSONField(name = "SALE_ORG_NAME")
        //销售组织名称
        private String SALE_ORG_NAME;
        @JSONField(name = "COST_CENTER_CODE")
        //销售部门成本中心销售部门
        private String COST_CENTER_CODE;
        @JSONField(name = "COST_CENTER_NAME")
        //销售部门成本中心名称
        private String COST_CENTER_NAME;
        @JSONField(name = "SALE_GROUP_ID")
        //销售组
        private String SALE_GROUP_ID;
        @JSONField(name = "SALE_GROUP_NAME")
        //销售组名称
        private String SALE_GROUP_NAME;
        @JSONField(name = "SALES_MAN")
        //销售员
        private String SALES_MAN;
        @JSONField(name = "SALES_MAN_NAME")
        //销售员名称
        private String SALES_MAN_NAME;

        @JSONField(name = "PAY_ORG_ID")
        //收款组织
        private String PAY_ORG_ID;
        @JSONField(name = "PAY_ORG_NAME")
        //收款组织名称
        private String PAY_ORG_NAME;
        @JSONField(name = "AR_REMARK")
        //备注
        private String AR_REMARK;
        @JSONField(name = "RED_BLUE")
        //红蓝字红 / 蓝 ，红字应收单金额为负数，蓝字为正数 1 蓝字 0 红字
        private String RED_BLUE;
        @JSONField(name = "DOCUMENT_STATUS")
        //单据状态 默认传A  A已审核 C已保存
        private String DOCUMENT_STATUS;
        @JSONField(name = "YES_OR_NO_SERVICE")
        //是否服务类 传入true或false  默认为false
        private String YES_OR_NO_SERVICE;
        @JSONField(name = "PAEZ_TEXT3")
        //服务类编码头
        private String PAEZ_TEXT3;
        @JSONField(name = "CONTRACT")
        //合同号，销售合同编号
        private String CONTRACT;
        @JSONField(name = "PROVINCE")
        //省份
        private String PROVINCE;
        @JSONField(name = "CITY")
        //城市
        private String CITY;
        @JSONField(name = "BUSINESS_CATEGORY")
        //线上线下U:线上 D:线下 商城OS为线上，其他默认为线下
        private String BUSINESS_CATEGORY;
        @JSONField(name = "RESPONSIBLE_PERSON")
        //回款责任人 传入员工号，取销售员
        private String RESPONSIBLE_PERSON;
        @JSONField(name = "SAL_ORDER_DATE")
        //销售订单日期订单创建日期
        private String SAL_ORDER_DATE;
        @JSONField(name = "PAEZ_TEXT6")
        //客户订单编号
        private String PAEZ_TEXT6;
        @JSONField(name = "TAX_AMOUNT_FOR")
        //税额
        private String TAX_AMOUNT_FOR;
        @JSONField(name = "NO_TAX_AMOUNT_FOR")
        //不含税金额去掉税额之后的金额
        private String NO_TAX_AMOUNT_FOR;
        @JSONField(name = "EXCHANGE_RATE")
        //人民币为1，其他外币为小数 例：1.2
        private String EXCHANGE_RATE;

        @JSONField(name = "SALOUT_BILLNO")
        //销售出库单号蓝字对应出库单号，红字退货单号
        private String SALOUT_BILLNO;

        @JSONField(name = "ACCOUNT_PERIOD")
        //账期业务日期的月份
        private String ACCOUNT_PERIOD;

        @JSONField(name = "ACCOUNT_CONTROL_INDICATORS")
        //入账控制标识 入账控制标识 N-----------按接收方入账 Y------------各自入账 S------------按发起方入账 默认：N
        private String ACCOUNT_CONTROL_INDICATORS;

        @JSONField(name = "INTERNAL_TRANSACTION_SMALL_CLASS")
        //内部交易小类订单创建时，下拉选择
        private String INTERNAL_TRANSACTION_SMALL_CLASS;

        @JSONField(name = "INTERNAL_TRANSACTION_BIG_CLASS")
        //内部交易大类订单创建时，下拉选择
        private String INTERNAL_TRANSACTION_BIG_CLASS;

        @JSONField(name = "ACCOUNT_CONTROL_IDENTIFICATION")
        //备注
        private String ACCOUNT_CONTROL_IDENTIFICATION;

        @JSONField(name = "BUSINESS_MODE")
        //业务模式默认为standard
        private String BUSINESS_MODE;

        @JSONField(name = "IS_INTERNAL_TRANSACTION")
        //是否内部关联交易是  填写大类/小类
        private String IS_INTERNAL_TRANSACTION;

        @JSONField(name = "SALE_DEPT_ID")
        //销售部门编码
        private String SALE_DEPT_ID;

        @JSONField(name = "F_QCWB_QZYWDJBM")
        //前置业务单据编码"正数（蓝字）应收单前置业务单据编码为空 负数（红字）应收单前置业务单据编码为销售出库单号
        private String F_QCWB_QZYWDJBM;

        @JSONField(name = "F_QCWB_YWDJBM")
        //业务单据编码 正数应收单业务单据编码传销售出库单 负数应收单业务单据编码传销售退货单号
        private String F_QCWB_YWDJBM;

        @JSONField(name = "MALL_ORDER_TYPE")
        //订单类型 传1
        private String MALL_ORDER_TYPE;


        @JSONField(name = "MATERIAL_LIST")
        private List<SaleLineInfo> MATERIAL_LIST;

    }

    @Data
    @Accessors(chain = true, fluent = false )
    public static class SaleLineInfo {
        @JSONField(name = "BIZ_ID")
        //单据内码默认为空
        private String BIZ_ID;
        @JSONField(name = "PRODUCT_NUMBER")
        //物料编码
        private String PRODUCT_NUMBER;
        @JSONField(name = "PRODUCT_NAME")
        //物料名称
        private String PRODUCT_NAME;
        @JSONField(name = "MODEL")
        //规格型号
        private String MODEL;
        @JSONField(name = "TAX_PRICE")
        //含税单价含税
        private BigDecimal TAX_PRICE;
        @JSONField(name = "AMOUNT")
        //计价数量销售数量
        private BigDecimal AMOUNT;
        @JSONField(name = "DIS_COUNT_AMOUNT_FOR")
        //折扣额为0
        private BigDecimal DIS_COUNT_AMOUNT_FOR;
        @JSONField(name = "ENTRY_DISCOUNT_RATE")
        //折扣率(%)为0
        private BigDecimal ENTRY_DISCOUNT_RATE;
        @JSONField(name = "NO_TAX_PRICE")
        //不含税金额不含税
        private BigDecimal NO_TAX_PRICE;
        @JSONField(name = "TAX_RATE")
        //税率(%)税率
        private BigDecimal TAX_RATE;
        @JSONField(name = "TAX")
        //税额税额
        private BigDecimal TAX;
        @JSONField(name = "VALOREM_TOTAL")
        //价税合计
        private BigDecimal VALOREM_TOTAL;
        @JSONField(name = "ORDER_NUMBER")
        //销售订单号项目订单号
        private String ORDER_NUMBER;
        @JSONField(name = "UNIT")
        //计价单位销售单位
        private String UNIT;
        @JSONField(name = "PROJECT")
        //项目编码合同编号
        private String PROJECT;
        @JSONField(name = "PROJECT_NAME")
        //项目名称合同名称
        private String PROJECT_NAME;
        @JSONField(name = "COST_AMOUNT")
        //成本单价生产供应链回传对应出库单的【单价】 0
        private BigDecimal COST_AMOUNT;
        @JSONField(name = "PRICE")
        //不含税单价
        private BigDecimal PRICE;
        @JSONField(name = "FYZH")
        //费用账户成本科目，生产供应链回传
        private String FYZH;
        @JSONField(name = "CHILD_PROJECT_CODE")
        //子项目编码订单编码
        private String CHILD_PROJECT_CODE;
        @JSONField(name = "CHILD_PROJECT_NAME")
        //子项目名称订单名称
        private String CHILD_PROJECT_NAME;

        @JSONField(name = "F_QCWB_ISHAVECOST")
        //是否有成本 生产供应链回传
        private String F_QCWB_ISHAVECOST;
        @JSONField(name = "F_QCWB_YESERP")
        //是否已读取过成本生产供应链回传，有值为是
        private String F_QCWB_YESERP;

        private BigDecimal totalPrice;

        private String contractNumber;

        private String orderStatusTime;

        private String labelTax;

        private String createTime;

        private String billNoNumber;

    }
}
