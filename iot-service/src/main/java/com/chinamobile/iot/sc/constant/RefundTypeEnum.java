package com.chinamobile.iot.sc.constant;

/**
 * @Author: YSC
 * @Date: 2021/12/20 08:38
 * @Description: IOT同步OS系统状态描述
 */
public enum RefundTypeEnum {

    REFUND("1", "仅退款"),
    RETURN("2", "退货退款"),
    CHANGE("3", "换货"),
    CANCEL("4", "取消申请");

    private final String refundType;
    private final String describe;

    RefundTypeEnum(String refundType, String describe) {
        this.refundType = refundType;
        this.describe = describe;
    }

    public String getRefundType() {
        return refundType;
    }

    public String getDescribe() {
        return describe;
    }
}
