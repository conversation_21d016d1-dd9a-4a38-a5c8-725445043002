package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.param.GetOrderUpdateLogisticsParam;
import com.chinamobile.iot.sc.request.IotLogisticRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/7
 * @description OS同步到商城的service接口类
 */
public interface Order2IOTService {

    /**
     * 订购结果反馈接口
     *
     * @param baseRequest
     * @return
     */
    IOTAnswer<Void> syncOrderingResult(IOTRequest baseRequest);

    /**
     * 接单更新物流信息
     *
     * @param getOrderUpdateLogisticsParam
     * @return
     */
    BaseAnswer syncUpdateLogistics(GetOrderUpdateLogisticsParam getOrderUpdateLogisticsParam);

    /**
     * 同步物流信息到商城
     *
     * @param logisInfoList
     * @param orderId
     * @param order2cAtomInfo
     */
    void synLogisticToIOT(List<IotLogisticRequest.LogisInfo> logisInfoList,
                                 String orderId,
                                 Order2cAtomInfo order2cAtomInfo);

    /**
     * 续费操作信息同步接口
     * @param baseRequest
     * @return
     */
    IOTAnswer<Void> synchronizeRenewalInformation(IOTRequest baseRequest);
}
