package com.chinamobile.iot.sc.request.supplychain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description: 软件订单信息同步B2Brequest
 **/
@Data
@Accessors(chain = true, fluent = false )
public class SoftwareOrderInfoB2BSyncRequest {
    @JSONField(name = "ROOT")
    private ROOT ROOT;

    @Data
    @Accessors(chain = true, fluent = false )
    public static class ROOT {
        @JSONField(name = "BODY")
        private BODY BODY;

        @JSONField(name = "HEADER")
        private HEADER HEADER;
    }

    @Data
    @Accessors(chain = true, fluent = false )
    public static class HEADER {
    }
    @Data
    @Accessors(chain = true, fluent = false )
    public static class BODY {
        @JSONField(name = "AUTH_INFO")
        private AuthInfo AUTH_INFO;
        @JSONField(name = "BUSI_INFO")

        private BusiInfo BUSI_INFO;
    }
    @Data
    @Accessors(chain = true, fluent = false )
    public static class AuthInfo {
        @JSONField(name = "USER_ID")
        private String USER_ID;
        @JSONField(name = "SIGN")
        private String SIGN;
        @JSONField(name = "POST_DATE")
        private String POST_DATE;
    }

    @Data
    @Accessors(chain = true, fluent = false )
    public static class BusiInfo {
        @JSONField(name = "HEADER_INFO_LIST")
        private List<OrderHeaderInfo> HEADER_INFO_LIST;
    }


    @Data
    @Accessors(chain = true, fluent = false )
    public static class OrderHeaderInfo {
        //公司名称
        @JSONField(name = "CORPORATE_NAME")
        private String CORPORATE_NAME;
        //公司编码
        @JSONField(name = "CORPORATE_CODE")
        private String CORPORATE_CODE;
        //接口人名称
        @JSONField(name = "ACCEPTER")
        private String ACCEPTER;
        //接口人编码
        @JSONField(name = "ACCEPTER_CODE")
        private String ACCEPTER_CODE;

        //销售合同编码
        @JSONField(name = "SALE_CONTRACT_NO")
        private String SALE_CONTRACT_NO;
        @JSONField(name = "SALE_CONTRACT_AMOUNT")
        //销售合同金额
        private String SALE_CONTRACT_AMOUNT;

        @JSONField(name = "SALE_DEPARTMENT_NAME")
        //销售部门名称
        private String SALE_DEPARTMENT_NAME;
        @JSONField(name = "SALE_DEPARTMENT_CODE")
        //销售部门编码
        private String SALE_DEPARTMENT_CODE;
        @JSONField(name = "SALE_TEAM_NAME")
        //销售团队名称
        private String SALE_TEAM_NAME;
        @JSONField(name = "SALE_TEAM_CODE")
        //销售团队编码
        private String SALE_TEAM_CODE;

        //销售订单编号
        @JSONField(name = "SALE_ORDER_CODE")
        private String SALE_ORDER_CODE;
        //销售订单状态
        @JSONField(name = "SALE_ORDER_STATUS")
        private String SALE_ORDER_STATUS;
        //销售订单类型
        @JSONField(name = "SALE_ORDER_TYPE")
        private String SALE_ORDER_TYPE;
        //签约客户编码,社会统一信用代码
        @JSONField(name = "SIGN_CLIENT_CODE")
        private String SIGN_CLIENT_CODE;
        //签约客户名称
        @JSONField(name = "SIGN_CLIENT_NAME")
        private String SIGN_CLIENT_NAME;
        //签约客户类别,0:体系内,1:体系外
        @JSONField(name = "SIGN_CLIENT_TYPE")
        private String SIGN_CLIENT_TYPE;

        @JSONField(name = "CURRENCY")
        //币种名称
        private String CURRENCY;
        @JSONField(name = "COLLABORATIVE_CODE")
        //协作人员编码
        private String COLLABORATIVE_CODE;
        @JSONField(name = "COLLABORATIVE_NAME")
        //协作人员名称
        private String COLLABORATIVE_NAME;
        @JSONField(name = "COLLABORATIVE_DEPARTMENT_NAME")
        //协作人员部门名称
        private String COLLABORATIVE_DEPARTMENT_NAME;
        @JSONField(name = "COLLABORATIVE_DEPARTMENT_CODE")
        //协作人员部门编码
        private String COLLABORATIVE_DEPARTMENT_CODE;
        @JSONField(name = "COLLABORATIVE_TEAM_NAME")
        //协作人员团队名称
        private String COLLABORATIVE_TEAM_NAME;
        @JSONField(name = "COLLABORATIVE_TEAM_CODE")
        //协作人员团队编码
        private String COLLABORATIVE_TEAM_CODE;
        @JSONField(name = "PROJECT_NO")
        //项目编码
        private String PROJECT_NO;
        @JSONField(name = "PROJECT_NAME")
        //项目名称
        private String PROJECT_NAME;
        @JSONField(name = "PROJECT_PROGRESS")
        //项目进度
        private String PROJECT_PROGRESS;
        @JSONField(name = "COST_CENTER_NAME")
        //成本中心
        private String COST_CENTER_NAME;

        @JSONField(name = "COST_CENTER_CODE")
        //成本中心编码
        private String COST_CENTER_CODE;
        @JSONField(name = "ORDER_TYPE")
        //单据类型
        private String ORDER_TYPE;
        @JSONField(name = "MAIN_PROJECT_NAME")
        //主项目名称
        private String MAIN_PROJECT_NAME;
        @JSONField(name = "CLIENT_NAME")
        //订购客户名称
        private String CLIENT_NAME;

        @JSONField(name = "CLIENT_CODE")
        //订购客户编码
        private String CLIENT_CODE;
        @JSONField(name = "CITY_NAME")
        //城市名称
        private String CITY_NAME;

        @JSONField(name = "PROVINCE_NAME")
        //省份名称
        private String PROVINCE_NAME;

        @JSONField(name = "CLIENT_CONTACTS")
        //客户联系人
        private String CLIENT_CONTACTS;
        @JSONField(name = "CLIENT_CONTACTS_PHONE")
        //客户联系人电话
        private String CLIENT_CONTACTS_PHONE;

        @JSONField(name = "SETTLEMENT_TYPE")
        //结算类型
        private String SETTLEMENT_TYPE;
        @JSONField(name = "BUSINESS_TYPE")
        //业务类型
        private String BUSINESS_TYPE;
        @JSONField(name = "INVOICING_METHOD")
        //开票方式
        private String INVOICING_METHOD;
        @JSONField(name = "SALE_TOTAL_AMOUNT_HAS_TAX")
        //销售总金额含税
        private String SALE_TOTAL_AMOUNT_HAS_TAX;
        @JSONField(name = "SALE_TOTAL_AMOUNT_NO_TAX")
        //销售总金额不含税
        private String SALE_TOTAL_AMOUNT_NO_TAX;
        @JSONField(name = "SALE_TAX_AMOUNT")
        //销售总税额
        private String SALE_TAX_AMOUNT;
        @JSONField(name = "PURCHASE_TOTAL_AMOUNT_HAS_TAX")
        //采购总金额含税
        private String PURCHASE_TOTAL_AMOUNT_HAS_TAX;
        @JSONField(name = "PURCHASE_TOTAL_AMOUNT_NO_TAX")
        //采购总金额不含税
        private String PURCHASE_TOTAL_AMOUNT_NO_TAX;
        @JSONField(name = "PURCHASE_TAX_AMOUNT")
        //采购总税额
        private String PURCHASE_TAX_AMOUNT;
        @JSONField(name = "ACCUMULATED_COLLECTION_RATIO")
        //累计计收比例
        private String ACCUMULATED_COLLECTION_RATIO;
        @JSONField(name = "REMARKS")
        //备注
        private String REMARKS;

        @JSONField(name = "POSTING_DATE")
        //入账日期(业务日期),格式:yyyy-MM-dd
        private String POSTING_DATE;

        @JSONField(name = "INTERNAL_TRANSACTION_SUBCATEGORY")
        //内部交易小类
        private String INTERNAL_TRANSACTION_SUBCATEGORY;

        @JSONField(name = "INTERNAL_TRANSACTION_CATEGORY")
        //内部交易大类
        private String INTERNAL_TRANSACTION_CATEGORY;

        @JSONField(name = "BUSINESS_MODEL")
        //业务模式
        private String BUSINESS_MODEL;

        @JSONField(name = "WHOLE_NETWORK_SETTLEMENT")
        //是否全网结算
        private String WHOLE_NETWORK_SETTLEMENT;

        @JSONField(name = "ACCOUNT_CONTROL_IDENTIFICATION")
        //入账控制标识
        private String ACCOUNT_CONTROL_IDENTIFICATION;

        @JSONField(name = "SALE_LINE_INFO")
        private List<SaleLineInfo> SALE_LINE_INFO;

    }

    @Data
    @Accessors(chain = true, fluent = false )
    public static class SaleLineInfo {
        @JSONField(name = "LINE_NUM")
        //行号
        private String LINE_NUM;
        @JSONField(name = "MATERIAL_CODE")
        //物料编码
        private String MATERIAL_CODE;
        @JSONField(name = "MATERIAL_NAME")
        //物料名称
        private String MATERIAL_NAME;
        @JSONField(name = "PRODUCT_DEPARTMENT_NAME")
        //产品归属部门名称
        private String PRODUCT_DEPARTMENT_NAME;
        @JSONField(name = "PRODUCT_DEPARTMENT_CODE")
        //产品归属部门编码
        private String PRODUCT_DEPARTMENT_CODE;
        @JSONField(name = "PRODUCT_COLLABORATIVE_CODE")
        //产品部门协作人员账号
        private String PRODUCT_COLLABORATIVE_CODE;
        @JSONField(name = "PRODUCT_COLLABORATIVE_NAME")
        //产品部门协作人员名称
        private String PRODUCT_COLLABORATIVE_NAME;
        @JSONField(name = "SPECIFICATION_MODEL")
        //规格型号
        private String SPECIFICATION_MODEL;
        @JSONField(name = "UNIT")
        //单位
        private String UNIT;
        @JSONField(name = "SALES_VOLUMES")
        //销售数量
        private String SALES_VOLUMES;
        @JSONField(name = "UNIT_PRICE_HAS_TAX")
        //含税单价
        private String UNIT_PRICE_HAS_TAX;
        @JSONField(name = "UNIT_PRICE_NO_TAX")
        //不含税单价
        private String UNIT_PRICE_NO_TAX;
        @JSONField(name = "OUTPUT_TAX_RATE")
        //销项税率
        private String OUTPUT_TAX_RATE;
        @JSONField(name = "SALE_AMOUNT_HAS_TAX")
        //销售金额含税
        private String SALE_AMOUNT_HAS_TAX;
        @JSONField(name = "SALE_AMOUNT_NO_TAX")
        //销售金额不含税
        private String SALE_AMOUNT_NO_TAX;
        @JSONField(name = "TAX_AMOUNT")
        //税额
        private String TAX_AMOUNT;
        @JSONField(name = "INCOME_SUBJECT_CODE")
        //收入科目编码
        private String INCOME_SUBJECT_CODE;
        @JSONField(name = "INCOME_SUBJECT_NAME")
        //收入科目名称
        private String INCOME_SUBJECT_NAME;
        @JSONField(name = "GROUP_COMMITTEE_MARKET_NAME")
        //集团管会市场名称
        private String GROUP_COMMITTEE_MARKET_NAME;
        @JSONField(name = "GROUP_COMMITTEE_PRODUCT_NAME")
        //集团管会产品名称
        private String GROUP_COMMITTEE_PRODUCT_NAME;
        @JSONField(name = "GROUP_COMMITTEE_ACTIVITY_NAME")
        //集团管会活动名称
        private String GROUP_COMMITTEE_ACTIVITY_NAME;

        @JSONField(name = "PURCHASE_ORDER_ID")
        //采购订单编码
        private String PURCHASE_ORDER_ID;

        @JSONField(name = "PURCHASE_ORDER_NAME")
        //采购订单名称
        private String PURCHASE_ORDER_NAME;

        private String totalPrice;

        private String contractNumber;

        private String orderStatus;

        private String orderType;

        private String orgName;

        @JSONField(name = "ACCEPTER")
        private String ACCEPTER;
        //接口人编码
        @JSONField(name = "ACCEPTER_CODE")
        private String ACCEPTER_CODE;

        //销售合同编码
        @JSONField(name = "SALE_CONTRACT_NO")
        private String SALE_CONTRACT_NO;

        @JSONField(name = "SALE_DEPARTMENT_NAME")
        //销售部门名称
        private String SALE_DEPARTMENT_NAME;
        @JSONField(name = "SALE_DEPARTMENT_CODE")
        //销售部门编码
        private String SALE_DEPARTMENT_CODE;
        @JSONField(name = "SALE_TEAM_NAME")
        //销售团队名称
        private String SALE_TEAM_NAME;
        @JSONField(name = "SALE_TEAM_CODE")
        //销售团队编码
        private String SALE_TEAM_CODE;

        @JSONField(name = "COST_CENTER_NAME")
        //成本中心
        private String COST_CENTER_NAME;

        @JSONField(name = "COST_CENTER_CODE")
        //成本中心编码
        private String COST_CENTER_CODE;

        @JSONField(name = "BUSINESS_TYPE")
        //业务类型
        private String BUSINESS_TYPE;

        @JSONField(name = "SALE_ORDER_CODE")
        private String SALE_ORDER_CODE;
        //销售订单状态
        @JSONField(name = "SALE_ORDER_STATUS")
        private String SALE_ORDER_STATUS;
        //销售订单类型
        @JSONField(name = "SALE_ORDER_TYPE")
        private String SALE_ORDER_TYPE;

        @JSONField(name = "SIGN_CLIENT_CODE")
        private String SIGN_CLIENT_CODE;
        @JSONField(name = "SIGN_CLIENT_NAME")
        private String SIGN_CLIENT_NAME;
        //签约客户类别,0:体系内,1:体系外
        @JSONField(name = "SIGN_CLIENT_TYPE")
        private String SIGN_CLIENT_TYPE;

        @JSONField(name = "CITY_NAME")
        //城市名称
        private String CITY_NAME;

        @JSONField(name = "PROVINCE_NAME")
        //省份名称
        private String PROVINCE_NAME;

        @JSONField(name = "CLIENT_NAME")
        //订购客户名称
        private String CLIENT_NAME;

        @JSONField(name = "CLIENT_CODE")
        //订购客户编码
        private String CLIENT_CODE;

    }
}
