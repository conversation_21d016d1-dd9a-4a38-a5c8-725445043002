package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.CardChooseDelivery;
import com.chinamobile.iot.sc.pojo.entity.CardChooseDeliveryExample;
import com.chinamobile.iot.sc.pojo.vo.CardChooseDeliveryVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/9
 * @description 批量导入交付卡信息service接口类
 */
public interface CardChooseDeliveryService {

    /**
     * 批量新增交付卡信息
     * @param cardChooseDeliveryList
     */
    void batchAddCardChooseDelivery(List<CardChooseDelivery> cardChooseDeliveryList);

    /**
     * 根据需要删除指定数据
     * @param cardChooseDeliveryExample
     */
    void deleteCardChooseDeliveryByNeed(CardChooseDeliveryExample cardChooseDeliveryExample);

    /**
     * 根据需要获取指定数据
     * @param cardChooseDeliveryExample
     * @return
     */
    List<CardChooseDelivery> listCardChooseDeliveryByNeed(CardChooseDeliveryExample cardChooseDeliveryExample);

    /**
     * 根据原子订单id获取指定数据
     * @param atomOrderIdList
     * @return
     */
    List<CardChooseDeliveryVO> listCardChooseDeliveryByAtomOrderId(List<String> atomOrderIdList);

    /**
     * 根据订单id获取指定数据
     * @param orderIdList
     * @return
     */
    List<CardChooseDeliveryVO> listCardChooseDeliveryByOrderId(List<String> orderIdList);

    /**
     * 根据主键id删除数据
     * @param id
     */
    void deleteCardChooseDeliveryById(String id);

    /**
     * 根据需要更新表数据
     * @param cardChooseDelivery
     * @param cardChooseDeliveryExample
     */
    void updateCardChooseDeliveryByNeed(CardChooseDelivery cardChooseDelivery,
                                        CardChooseDeliveryExample cardChooseDeliveryExample);
}
