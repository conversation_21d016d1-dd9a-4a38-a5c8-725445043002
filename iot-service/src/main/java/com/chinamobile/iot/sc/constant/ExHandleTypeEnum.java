package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/26
 * @description 异常处理信息处理选项枚举类
 */
public enum ExHandleTypeEnum {

    ZERO("0","结束订单至交易失败状态");

    /**
     * 处理选项
     */
    private String type;

    /**
     * 处理选项描述
     */
    private String desc;

    ExHandleTypeEnum(String type,String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type) {
        for (ExHandleTypeEnum value : ExHandleTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

}
