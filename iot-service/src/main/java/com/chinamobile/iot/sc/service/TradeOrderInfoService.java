package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.TradeOrderInfo;
import com.chinamobile.iot.sc.pojo.param.TradeOrderInfoParam;
import com.chinamobile.iot.sc.pojo.vo.TradeOrderInfoVO;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import org.springframework.web.bind.annotation.RequestAttribute;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13
 * @description 贸易订单信息service接口类
 */
public interface TradeOrderInfoService {

    /**
     * 根据合同编号获取贸易订单信息
     *
     * @param contractNum
     * @return
     */
    List<TradeOrderInfo> listTradeOrderInfoByContract(String contractNum);

    /**
     * 批量新增贸易订单
     *
     * @param tradeOrderInfoList
     */
    void batchInsert(List<TradeOrderInfo> tradeOrderInfoList);

    /**
     * 分页查询贸易订单列表
     *
     * @param tradeOrderInfoParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<TradeOrderInfoVO> pageTradeOrderInfo(TradeOrderInfoParam tradeOrderInfoParam,
                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis);

    /**
     * 导出贸易订单明细
     *
     * @param tradeNo
     */
    void exportTradeOrderDetail(String tradeNo, LoginIfo4Redis loginIfo4Redis) throws Exception;

    /**
     * 获取保理状态下拉值
     * @return
     */
    BaseAnswer<List<SimpleItemDTO>> getBaoliStatusList();

    /**
     * 导出财务台账详情
     *
     * @param tradeNo
     */
    void exportFinancingBillDetail(String tradeNo, LoginIfo4Redis loginIfo4Redis) throws Exception;
}
