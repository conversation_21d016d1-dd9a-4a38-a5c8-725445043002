package com.chinamobile.iot.sc.request.supplychain;

import com.chinamobile.iot.sc.pojo.mapper.SupplyChainIotLineInfoDO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 导入供应链订单草稿
 **/
@Data
public class SupplyChainImportPoDraftRequest {

    /**
     * 主数据统一员工名称
     */
    private String applyName;

    /**
     * 主数据统一员工编码
     */
    private String applyNum;

    /**
     * 申请人所在公司代码，主数据统一公司代码
     */
    private String companyCode;

    /**
     * 申请人所在公司名称，主数据统一公司名称
     */
    private String companyName;

    /**
     * 集中化合同系统合同名称
     */
    private String contractName;

    /**
     * 集中化合同系统合同编号
     */
    private String contractNo;

    /**
     * 主数据统一部门编码
     */
    private String deptCode;

    /**
     * 主数据统一部门名称
     */
    private String deptName;

    /**
     * SUM(行不含税金额)
     */
    private BigDecimal poAmount;

    /**
     * 含税总金额=SUM(行不含税金 额)+SUM（行税金）
     */
    private BigDecimal poAmountTax;

    /**
     * 商城单据号
     */
    private String recordNumber;

    /**
     * 头税金=行税金汇总
     */
    private BigDecimal taxSum;

    /**
     * 主数据统一供应商编码
     */
    private String vendorCode;

    /**
     * 主数据统一供应商名称
     */
    private String vendorName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 更新日期
     */
    private Date lastUpdateDate;


    /**
     * IOT商城汇总结算单行信息
     */
    private List<SupplyChainIotLineInfoDO> scmIotPoLineInfo;

}
