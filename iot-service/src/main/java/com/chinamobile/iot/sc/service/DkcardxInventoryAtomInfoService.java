package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfo;
import com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfoExample;
import com.chinamobile.iot.sc.pojo.dto.AtomDetailInventoryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/23
 * @description 卡+X终端库存原子详情service接口类
 */
public interface DkcardxInventoryAtomInfoService {

    /**
     * 根据需要删除指定数据
     * @param inventoryAtomInfoExample
     */
    void deleteInventoryAtomInfoByExample(DkcardxInventoryAtomInfoExample inventoryAtomInfoExample);

    /**
     * 查询需要的指定数据
     * @param inventoryAtomInfoExample
     * @return
     */
    List<DkcardxInventoryAtomInfo> getInventoryAtomInfoByExample(DkcardxInventoryAtomInfoExample inventoryAtomInfoExample);

    /**
     * 批量新增卡+X终端库存原子详情
     * @param inventoryAtomInfoList
     */
    void batchAddInventoryAtomInfo(List<DkcardxInventoryAtomInfo> inventoryAtomInfoList);

    /**
     * 获取原子商品绑定的库存个数列表
     * @return
     */
    List<AtomDetailInventoryDTO> listAtomDetailInventory();

}
