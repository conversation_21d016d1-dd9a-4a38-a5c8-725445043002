package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * spu割接
 */
@Data
public class SpuCutOver {

    @ExcelProperty(value="后台一级管理目录",index=0)
    private String level1DirCode;

    @ExcelProperty(value="后台二级管理目录",index=1)
    private String level2DirCode;

    @ExcelProperty(value="商品编码",index=2)
    private String spuCode;
    
    @ExcelProperty(value="商品名称",index=3)
    private String spuName;

    @ExcelProperty(value="商品关键字",index=4)
    private String keyWords;

    @ExcelProperty(value="销售标签",index=5)
    private String tag;

    @ExcelProperty(value="是否隐秘上架",index=6)
    private String isHidden;

    @ExcelProperty(value="SPU主图文件名",index=7)
    private String headImage;

    @ExcelProperty(value="产品详情文件名",index=8)
    private String detailImage;

    @ExcelProperty(value="售后规则文件名",index=9)
    private String afterSaleImage;
}
