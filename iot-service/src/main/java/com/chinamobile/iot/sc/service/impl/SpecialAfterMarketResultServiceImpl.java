package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.SpecialAfterMarketResultMapper;
import com.chinamobile.iot.sc.pojo.entity.SpecialAfterMarketResult;
import com.chinamobile.iot.sc.pojo.entity.SpecialAfterMarketResultExample;
import com.chinamobile.iot.sc.pojo.vo.SpecialAfterMarketResultVO;
import com.chinamobile.iot.sc.service.SpecialAfterMarketResultService;
import com.chinamobile.iot.sc.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/11
 * @description 特殊售后服务历史记录service实现类
 */
@Service
public class SpecialAfterMarketResultServiceImpl implements SpecialAfterMarketResultService {

    @Resource
    private SpecialAfterMarketResultMapper specialAfterMarketResultMapper;


    @Override
    public void addSpecialAfterMarketResult(SpecialAfterMarketResult specialAfterMarketResult) {
        specialAfterMarketResultMapper.insert(specialAfterMarketResult);
    }

    @Override
    public List<SpecialAfterMarketResultVO> listSpecialAfterMarketResult(String orderId) {
        SpecialAfterMarketResultExample example = new SpecialAfterMarketResultExample();
        example.createCriteria()
                .andOrderIdEqualTo(orderId);
        example.orderBy("create_time asc");
        List<SpecialAfterMarketResult> specialAfterMarketResultList = specialAfterMarketResultMapper.selectByExample(example);
        List<SpecialAfterMarketResultVO> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(specialAfterMarketResultList)){
            specialAfterMarketResultList.stream().forEach(specialAfterMarketResult -> {
                SpecialAfterMarketResultVO resultVO = new SpecialAfterMarketResultVO();
                BeanUtils.copyProperties(specialAfterMarketResult,resultVO);
                resultVO.setCreateTimeStr(DateUtils.dateToStr(specialAfterMarketResult.getCreateTime(),DateUtils.DEFAULT_DATETIME_FORMAT));
                voList.add(resultVO);
            });
        }
        return voList;
    }
}
