package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineBusiness;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架商务相关service接口类
 */
public interface NewProductRequestOnlineOfflineBusinessService {

    /**
     * 批量新增商务相关信息
     * @param businessList
     */
    void batchSaveBusiness(List<NewProductRequestOnlineOfflineBusiness> businessList);

    /**
     * 删除商务相关信息
     * @param comboInfoId
     * @param requestId
     */
    void deleteBusinessByRequestIdAndComboInfoId(String comboInfoId,
                                                 String requestId);

    /**
     * 批量更新商务相关信息
     * @param businessList
     */
    void batchUpdateBusinessByRequestIdAndComboInfoIdAndFileType(List<NewProductRequestOnlineOfflineBusiness> businessList);

    /**
     * 根据商品申请id和套餐信息id获取商务相关信息
     * @param newProductRequestId
     * @param comboInfoId
     * @return
     */
    List<NewProductRequestOnlineOfflineBusiness> getBusinessByRequestIdAndComboInfoId(String newProductRequestId,
                                                                                String comboInfoId);
}
