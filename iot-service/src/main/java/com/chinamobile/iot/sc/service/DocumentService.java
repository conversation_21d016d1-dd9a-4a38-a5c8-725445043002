package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Document;
import com.chinamobile.iot.sc.pojo.param.AnnouncementListParam;
import com.chinamobile.iot.sc.pojo.param.DocumentListParam;
import com.chinamobile.iot.sc.pojo.param.SaveAnnouncementParam;
import com.chinamobile.iot.sc.pojo.param.SaveDocumentParam;
import com.chinamobile.iot.sc.pojo.vo.AnnouncementVO;
import com.chinamobile.iot.sc.pojo.vo.DocumentListVO;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

public interface DocumentService {

    /**
     * 创建文档
     * @param file
     * @param name
     * @param visibleRange
     * @param isRepetitionName
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer saveDocument(MultipartFile file,String name,String visibleRange,Boolean isRepetitionName,LoginIfo4Redis loginIfo4Redis);

    /**
     * 更新文档
     * @param id
     * @param file
     * @param name
     * @param visibleRange
     * @param isRepetitionName
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer updateDocument(String id,MultipartFile file,String name,String visibleRange,Boolean isRepetitionName,LoginIfo4Redis loginIfo4Redis);


    /**
     * 删除文档
     * @param id
     * @return
     */
    BaseAnswer deleteDocument(String id );

    /**
     * 查询详情
     * @param id
     * @return
     */
    BaseAnswer<Document> selectDocumentDetail(String id );

    /**
     * 下载文件
     * @param id
     * @return
     */
    BaseAnswer downloadDocumentFile(String id,String operate,LoginIfo4Redis loginIfo4Redis);


    /**
     * 分页查询
     * @param param
     * @return
     */
    BaseAnswer<PageData<DocumentListVO>> getDocumentList(DocumentListParam param);

    /**
     * 首页分页查询用户
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer<PageData<DocumentListVO>> getDocumentHomeList(DocumentListParam param, LoginIfo4Redis loginIfo4Redis);
}
