package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/23
 * @description 商品开卡模板编码数据割接
 */
@Data
public class SyncSkuTemplateIdCutOver {

    @ExcelProperty(value="开卡模板编码（templateId）",index=0)
    private String templateId;

    @ExcelProperty(value="商品组/销售编码（spuOfferingInfo）",index=1)
    private String spuOfferingInfo;

    @ExcelProperty(value="规格商品编码（offeringCode）",index=2)
    private String offeringCode;
}
