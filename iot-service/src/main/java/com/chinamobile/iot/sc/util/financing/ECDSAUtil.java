package com.chinamobile.iot.sc.util.financing;

import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.interfaces.ECPrivateKey;
import java.security.interfaces.ECPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.chinamobile.iot.sc.pojo.param.AuthenticationAndActivationStatusParam;
import com.chinamobile.iot.sc.pojo.param.OrderFinancingUrlParam;

/**
 * @Type EcdsaUtil.java
 * @Desc Ecdsa椭圆曲线算法数字签名
 * @version
 */
public class ECDSAUtil {

	/**
	 * 生成一对公私钥
	 * 
	 * @return
	 * @throws Exception
	 */
	public static Map<String, String> generatePairKey() {

		Map<String, String> pairKey = new HashMap<String, String>();
		try {
			KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC");
			keyPairGenerator.initialize(256);
			KeyPair keyPair = keyPairGenerator.generateKeyPair();
			ECPublicKey ecpublicKey = (ECPublicKey) keyPair.getPublic();
			ECPrivateKey ecprivateKey = (ECPrivateKey) keyPair.getPrivate();
			pairKey.put("privateKey", new BASE64Encoder().encode(ecprivateKey.getEncoded()));
			pairKey.put("publicKey", new BASE64Encoder().encode(ecpublicKey.getEncoded()));
		} catch (Exception e) {
			// TODO: handle exception
		}
		return pairKey;
	}

	/**
	 * 加签数字签名
	 * 
	 * @param privateKey
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public static String signString(String privateKey, String data) {
		String result = "";
		try {
			Signature signature = Signature.getInstance("SHA256withECDSA");
			PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(new BASE64Encoder().decode(privateKey));
			KeyFactory keyFactory = KeyFactory.getInstance("EC");
			PrivateKey key = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
			signature.initSign(key);
			signature.update(data.getBytes());
			result = encryptBASE64(signature.sign());
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			return null;
		}
		return result;
	}

	/**
	 * 核数字签名
	 * 
	 * @param publicKey
	 * @return
	 * @throws Exception
	 */
	public static boolean verifyString(String publicKey, String srcStr, String signedStr) {
		boolean result = true;
		try {
			X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(new BASE64Encoder().decode(publicKey));
			KeyFactory keyFactory = KeyFactory.getInstance("EC");
			PublicKey key = keyFactory.generatePublic(x509EncodedKeySpec);
			Signature signature = Signature.getInstance("SHA256withECDSA");
			signature.initVerify(key);
			signature.update(srcStr.getBytes());
			result = signature.verify(decryptBASE64(signedStr));
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			return false;
		}
		return result;
	}

	/**
	 * BASE64解码
	 * 
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static final byte[] decryptBASE64(String key) {
		try {
			return new BASE64Encoder().decode(key);
		} catch (Exception e) {
			throw new RuntimeException("解密错误，错误信息：", e);
		}
	}

	/**
	 * BASE64编码
	 * 
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static final String encryptBASE64(byte[] key) {
		try {
			return new BASE64Encoder().encode(key);
		} catch (Exception e) {
			throw new RuntimeException("加密错误，错误信息：", e);
		}
	}

//	public static void main(String[] args) {
//		/*Map<String, String> map = generatePairKey();
//		System.out.println("私钥:"+map.get("privateKey"));
//		System.out.println("公钥:"+map.get("publicKey"));*/
//
//
//		try {
//			// 加签
////			String signedStr = signString(
////					"meecaqaMeMyhkEziP0QcaqyikEziP0Qdaq3ejPaBa7ebbcbAnwqRdcE8DlyJlT9f3aOcsInBzegdFB3hcTGHJ5Ts5a==",
////					"xxxxxxxx");
//
//			// 验签
////			boolean isVerify = verifyString(
////					"mfAMeMyhkEziP0QcaqyikEziP0Qdaq3dq7aeVQE9JAAF0F1DgGJs47/E8izcWKv1pLN+gQ256hV2ov+dLU86+mOQAumLpbFP87kMBb5ij06W2GwyfQ6a2OjTjM==",
////					"解放军在台岛以东等海空域实兵演练",
////					"meuciqcdhpCW/lPMRhr5f6SmWPpgvgPWU+EBSc+RUMajNzNSZai7jYewIRZS0nY1GBFZnDYOvW6OndfWfqdyI+yeolj+6By=");
////			System.out.print(isVerify);
//
////			Map<String, String> stringStringMap = generatePairKey();
////			System.out.println(stringStringMap.toString());
//			/*String data = JSON.toJSONString("{\n" +
//					"\t\"sign\":\"meucickAtAaGOp/Kl3l7A8kjc7gydOIvHB3QkkosBpL40FWma9eaTM3Bzi3wkhuzckShdqAp08wz4cdGe8lu+xn9HtL7AUu=\",\n" +
//					"\t\"companyName\": \"中国移动通信集团财务有限公司\",\n" +
//					"\t\"certNo\": \"913206210009491709\",\n" +
//					"\t\"personName\": \"王五\",\n" +
//					"\t\"personPhone\": \"***********\",\n" +
//					"\t\"orderList\": [{\n" +
//					"\t\t\"orderType\": \"2\",\n" +
//					"\t\t\"filePath\": \"/aaa/bbb.excel\",\n" +
//					"\t\t\"orderCode\": \"*************\",\n" +
//					"\t\t\"provinceContractNo\": \"************\",\n" +
//					"\t\t\"provinceName\": \"中国移动通信集团湖北有限公司\",\n" +
//					"\t\t\"totalAmount\": \"200000\",\n" +
//					"\t\t\"applyAmount\": \"2000\",\n" +
//					"\t\t\"targetBank\": \"中信银行\",\n" +
//					"\t\t\"estimateReceiptTime\": \"2023-07-20 00:00:00\",\n" +
//					"\t\t\"invoiceNo\": \"***********\",\n" +
//					"\t\t\"invoiceIncludeTaxAmount\": \"100\"\n" +
//					"\t}]\n" +
//					"}");*/
//
//
//			String data = "{\"certNo\":\"0123456789ABCDEFGH\",\"companyName\":\"何文丰合作主\",\"orderList\":[{\"applyAmount\":\"100\",\"estimateReceiptTime\":\"2023-10-10 00:00:00\",\"filePath\":\"/iotmall/files/orderList_IOTMALL20230720000008.xlsx\",\"invoiceIncludeTaxAmount\":\"110\",\"invoiceNo\":\"123456\",\"orderCode\":\"IOTMALL20230720000008\",\"orderType\":\"1\",\"provinceContractNo\":\"CMIOT-*********\",\"provinceName\":\"中移物联网有限公司\",\"targetBank\":\"中信银行\",\"totalAmount\":\"363.0\"}],\"personName\":\"yongfactoring\",\"personPhone\":\"***********\"}";
//            String s = signObject("meecaqaMeMyhkEziP0QcaqyikEziP0Qdaq3ejPaBa7ebbcdkzDlZp0wmampVQz5CM5u29GYrrp3173X5zuYLL/r0/7==", JSONObject.parseObject(data));
//
//            boolean b1 = verifyObject("mfAMeMyhkEziP0QcaqyikEziP0Qdaq3dq7aesSmWZP6bL4Z/uRHRJttEnzvWYF4gO9eRLE0JtQfgEP94ZHkHUCQDE/v8JpbUCoKealsIaoGWjIvmSV7xL3K82q==", JSONObject.parseObject(data), s);
//            System.out.println("验签结果"+b1);
//
//
//			/*HashMap<String, Object> map = new HashMap<>();
//			map.put("personName", "刘祥保理");
//			map.put("personPhone", "***********");
//			map.put("certNo", "0123456789ABCDEFGI");
//			map.put("companyId","1686189281719808002");
//			map.put("advice","");
//			map.put("companyName","新正合作伙伴");
//			map.put("sign","meqcibuYTRb5Zs9bt6shGl5zQSpMDMKXbeMNP1cf1bdLXDN/a9bEbopsFKJabh5O8XfALMpzz1jXbNOGvbYrjYQXbh6eva==");
//			map.put("status","init");
//			String data = sortByASCIIToJson(map);*/
//
////			String s = signObject("meecaqaMeMyhkEziP0QcaqyikEziP0Qdaq3ejPaBa7ebbcdkzDlZp0wmampVQz5CM5u29GYrrp3173X5zuYLL/r0/7==", JSONObject.parseObject(data));
////			String s1 = signString("meecaqaMeMyhkEziP0QcaqyikEziP0Qdaq3ejPaBa7ebbcdkzDlZp0wmampVQz5CM5u29GYrrp3173X5zuYLL/r0/7==", data);
////			System.out.println("签名:"+s);
////			map.remove("sign");
////			data = sortByASCIIToJson(map);
//			boolean b = verifyObject("mfAMeMyhkEziP0QcaqyikEziP0Qdaq3dq7aeFPQrg21YqBO9rcS3VDSZVb0tkHr9gkeBkQbEmzIHkY+5ZH2eugLGEZZU0vRcePxmjnc3Sdg8UHFGVo+65wCGoq==", JSONObject.parseObject(data), "meucie0tPpFiyYWPoqy3OBYeykE1weZuqVZVnEUM2Wb1whlia9ea9imVxyclBhBUqjSkWhooUqy7RHy2CjBcVyMzxc+k+yQ=");
//			System.out.println(b);
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}

    public static void main(String[] args) {
            String data = "{\"certNo\":\"0123456789ABCDEFGH\",\"companyName\":\"何文丰合作主\",\"orderList\":[{\"applyAmount\":\"100\",\"estimateReceiptTime\":\"2023-10-10 00:00:00\",\"filePath\":\"/iotmall/files/orderList_IOTMALL20230720000008.xlsx\",\"invoiceIncludeTaxAmount\":\"110\",\"invoiceNo\":\"123456\",\"orderCode\":\"IOTMALL20230720000008\",\"orderType\":\"1\",\"provinceContractNo\":\"CMIOT-*********\",\"provinceName\":\"中移物联网有限公司\",\"targetBank\":\"中信银行\",\"totalAmount\":\"363.0\"}],\"personName\":\"yongfactoring\",\"personPhone\":\"***********\",\"sign\":\"meuciaPnDsHWSADKr1Me+IIHfrPkwXD1IZl9rE8u0LR2Qy2Ta9eaJDzok/48ePYPau0QVMCMoZaKiZ7irkoh4A9graykLU7=\"}";

            JSONObject jsonObject = JSONObject.parseObject(data);

            boolean b = verifyObject("mfAMeMyhkEziP0QcaqyikEziP0Qdaq3dq7aesSmWZP6bL4Z/uRHRJttEnzvWYF4gO9eRLE0JtQfgEP94ZHkHUCQDE/v8JpbUCoKealsIaoGWjIvmSV7xL3K82q==", jsonObject, "meuciaPnDsHWSADKr1Me+IIHfrPkwXD1IZl9rE8u0LR2Qy2Ta9eaJDzok/48ePYPau0QVMCMoZaKiZ7irkoh4A9graykLU7=");
            System.out.println(b);

        }

	public static String signObject(String privateKey, Object data) {
		JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(data));
		jsonObject.remove("sign");
		String preparedSignStr = sortByASCIIToJson(jsonObject);
		String s = signString(privateKey, preparedSignStr);
		return s;
	}

	/**
	 * 核数字签名(参数是响应对象)
	 */
	public static boolean verifyObject(String publicKey, Object data, String signedStr) {
		JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(data));
		jsonObject.remove("sign");
		String preparedSignStr = sortByASCIIToJson(jsonObject);
		return verifyString(publicKey,preparedSignStr,signedStr);
	}

	public static String sortByASCIIToJson(Object obj) {
		String json = JSONObject.toJSONString(obj, new SerializerFeature[]{SerializerFeature.SortField, SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.MapSortField});
		return json;
	}
}
