package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/29 15:10
 *
 * 订单数据割接
 */
@Data
public class OrderInfoCutOver {

    @ExcelProperty(value="ORDERID",index=0)
    private String orderId;

    @ExcelProperty(value="CREATEOPERCODE",index=1)
    private String createOperCode;

    @ExcelProperty(value="EMPLOYEENUM",index=2)
    private String employeeNum;

    @ExcelProperty(value="CUSTOMERMANAGERNAME",index=3)
    private String customerManagerName;

    @ExcelProperty(value="CUSTOMERMANAGERPHONE",index=4)
    private String customerManagerPhone;
}
