package com.chinamobile.iot.sc.request.jiangsuzw;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/1/11 15:27
 */
@Data
public class JiangSuZWHeader {

    @J<PERSON><PERSON>ield(name = "ROUTING")
    private Routing ROUTING;
    //操作流水
    @J<PERSON><PERSON><PERSON>(name = "TRACE_ID")
    private String TRACE_ID;
    //租户标识
    @JSONField(name = "TENANT_ID")
    private String TENANT_ID;
    //渠道标识
    @J<PERSON>NField(name = "CHANNEL_ID")
    private String CHANNEL_ID;

    @Data
    public static class Routing{

        @J<PERSON><PERSON>ield(name = "ROUTE_KEY")
        private String ROUTE_KEY;

        @J<PERSON>NField(name = "ROUTE_VALUE")
        private String ROUTE_VALUE;

    }

}
