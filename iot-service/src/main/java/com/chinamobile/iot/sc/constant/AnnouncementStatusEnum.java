package com.chinamobile.iot.sc.constant;

public enum AnnouncementStatusEnum {

    DRAFT(0,"待审核"),
    PUBLISHED(1,"已发布"),
    REJECTED(2,"已驳回"),
    INVISIBLE(3,"隐藏"),
    DISPLAY(4,"显示"),
    ;

    public final Integer code;
    public final String name;

    AnnouncementStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean contain(Integer code) {
        if(code == null){
            return false;
        }
        for (AnnouncementStatusEnum status : values()) {
            if (status.code.intValue() == code.intValue()) {
                return true;
            }
        }
        return false;
    }

    public AnnouncementStatusEnum fromCode(Integer code){
        for (AnnouncementStatusEnum status : values()) {
            if (status.code.intValue() == code.intValue()) {
                return status;
            }
        }
        return null;
    }

}
