package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.constant.CardStatusEnum;
import com.chinamobile.iot.sc.dao.CardInventoryMainInfoMapper;
import com.chinamobile.iot.sc.entity.user.UserPartner;
import com.chinamobile.iot.sc.dao.ServicePackLimitSyncMapper;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.CardInfo;
import com.chinamobile.iot.sc.pojo.CardInfoExample;
import com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfoExample;
import com.chinamobile.iot.sc.pojo.entity.CardMallSync;
import com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSync;
import com.chinamobile.iot.sc.request.CardInfoRequest;
import com.chinamobile.iot.sc.response.iot.LimitSyncInfoResponse;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.service.CardMallSyncService;
import com.chinamobile.iot.sc.service.OsMallSyncService;
import com.chinamobile.iot.sc.request.LimitInfoRequest;
import com.chinamobile.iot.sc.util.SFTPUtil;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.chinamobile.iot.sc.constant.RedisLockConstant.MALL_SYNC_LOCK;
import static com.chinamobile.iot.sc.constant.RedisLockConstant.ORDER_LOCK;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/2
 * @description OS与商城同步数据的service实现类
 */
@Service
@Slf4j
public class OsMallSyncServiceImpl implements OsMallSyncService {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CardInfoService cardInfoService;
    @Resource
    ServicePackLimitSyncMapper servicePackLimitSyncMapper;

    @Resource
    private CardMallSyncService cardMallSyncService;

    @Resource
    private CardInventoryMainInfoMapper cardInventoryMainInfoMapper;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Value("${iot.ftp.name}")
    private String sftpUserName;
    @Value("${iot.ftp.password}")
    private String sftpPassword;
    @Value("${iot.ftp.host}")
    private String sftpHost;
    @Value("${iot.ftp.port}")
    private Integer sftpPort;
    @Value("${iot.ftp.sftpCardInfoPath}")
    private String sftpCardInfoPath;


    @Override
    public IOTAnswer<Void> syncNumberCardInfos(IOTRequest baseRequest) {
        String cardJson = JSON.toJSONString(baseRequest);
        log.info("号卡信息同步请求:{}", cardJson);
        Date now = new Date();
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        CardInfoRequest cardInfoRequest;
        try {
            cardInfoRequest = JSON.parseObject(baseRequest.getContent(), CardInfoRequest.class);
        } catch (Exception e) {
            log.error("号卡信息同步请求解析异常:{}", e);
            throw new IOTException(iotAnswer, "数据解析异常");
        }
        String reqFileName = cardInfoRequest.getReqFileName();
        if (StringUtils.isEmpty(reqFileName)) {
            log.error("号卡信息同步请求文件名不存在");
            throw new IOTException(iotAnswer, "号卡信息同步请求文件名不存在");
        }
        String beId = baseRequest.getBeId();
        String fileName = reqFileName.split("[.]")[0];
        return redisUtil.smartLock(ORDER_LOCK + fileName, () -> {
            String templateId = cardInfoRequest.getTemplateId();
            String regionId = cardInfoRequest.getRegionId();
            String custCode = cardInfoRequest.getCustCode();
            String projectId = cardInfoRequest.getProjectId();

            CardInfoExample cardInfoExample = new CardInfoExample();
            cardInfoExample.createCriteria()
                    .andTemplateIdEqualTo(templateId)
                    .andRegionIdEqualTo(regionId)
                    .andCustCodeEqualTo(custCode)
                    .andProjectIdEqualTo(projectId)
                    .andReqFileNameEqualTo(reqFileName);
            List<CardInfo> cardInfoList = cardInfoService.listCardInfoByNeed(cardInfoExample);
            if (CollectionUtils.isNotEmpty(cardInfoList)) {
                log.error("号卡信息同步请求文件已经存在:{}", cardJson);
                throw new IOTException(iotAnswer, "号卡信息同步请求已经存在:".concat(cardJson));
            }

            CardInfo cardInfo = new CardInfo();
            cardInfo.setId(BaseServiceUtils.getId());
            cardInfo.setCreateTime(now);
            cardInfo.setUpdateTime(now);
            BeanUtils.copyProperties(cardInfoRequest, cardInfo);
            String[] fileNameSplit = reqFileName.split("_");
            cardInfo.setOpenCardTime(fileNameSplit[1]);
            cardInfo.setBeId(beId);
            cardInfoService.addCardInfo(cardInfo);

            new Thread(() -> {
                try {
                    // 休眠1分钟后去取文件，防止文件未上传   随机休眠，30秒到一分钟
                    Random random = new Random();
                    Thread.sleep(random.nextInt(60000)+30000);
                    /*Thread.sleep(60000);*/
                    handleCardInfoFile(cardInfo);
                } catch (InterruptedException e) {
                    log.error("号卡信息同步请求线程休眠失败");
                }
            }).start();

            return iotAnswer;
        });
    }




    @Override
    public void handleCardInfoFile(CardInfo cardInfo) {
        String reqFileName = cardInfo.getReqFileName();
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("解析号卡信息同步FTP信息，host：{}，port：{}，name：{}，reqFileName：{}", sftpHost, sftpPort, sftpUserName, reqFileName);
        try {
            if (!sftpUtil.login()) {
                log.info("解析号卡信息同步FTP登录失败！");
                return;
            }
            log.info("解析号卡信息同步FTP登录成功！");

            Vector files = sftpUtil.listFiles(sftpCardInfoPath);
            if (files == null || files.isEmpty()) {
                log.info("解析号卡信息同步FTP没有文件信息！");
                return;
            }

            List<CardMallSync> cardMallSyncArrayList = new ArrayList<>();
            Date date = new Date();
            String cardInfoId = cardInfo.getId();
            String custCode = cardInfo.getCustCode();
            String templateId = cardInfo.getTemplateId();
            redisUtil.smartLock(MALL_SYNC_LOCK + custCode+templateId, () -> {
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            List<CardInventoryMainInfo> cardInventoryMainInfos = cardInventoryMainInfoMapper.selectByExample(new CardInventoryMainInfoExample().createCriteria().andTemplateIdEqualTo(cardInfo.getTemplateId())
                    .andCustCodeEqualTo(cardInfo.getCustCode()).example());
                String mainId = "";
                String cardType="";
               if (CollectionUtils.isEmpty(cardInventoryMainInfos)){
                   mainId = BaseServiceUtils.getId();
               }else {
                   CardInventoryMainInfo cardInventoryMainInfo = cardInventoryMainInfos.get(0);
                   mainId = cardInventoryMainInfo.getId();
               }
                try {
                    for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext(); ) {
                        ChannelSftp.LsEntry str = it.next();
                        String filename = str.getFilename();
                        // 不是当前文件跳过继续遍历
                        if (filename.equals(".")
                                || filename.equals("..")
                                || !filename.equals(reqFileName)) {
                            continue;
                        }

                        InputStream is = sftpUtil.getInputStream(sftpCardInfoPath, reqFileName);
                        BufferedReader br = new BufferedReader(new InputStreamReader(is));
                        String readStr;
                        while ((readStr = br.readLine()) != null) {
                            String[] parts = readStr.split("\\|", -1);
                            cardType =parts[2];
                            CardMallSync cardMallSync = new CardMallSync();
                            cardMallSync.setId(BaseServiceUtils.getId());
                            cardMallSync.setCardInventoryMainId(mainId);
                            cardMallSync.setMsisdn(parts[0]);
                            cardMallSync.setIccid(parts[1]);
                            cardMallSync.setCardInfoId(cardInfoId);
                            cardMallSync.setCardType(parts[2]);
                            cardMallSync.setCardStatus(CardStatusEnum.NOT_SELL.getType());
                            cardMallSync.setCreateTime(date);
                            cardMallSync.setUpdateTime(date);
                            cardMallSyncArrayList.add(cardMallSync);
                        }
                       /* log.info("删除文件:{}", reqFileName);
                        sftpUtil.delete(sftpCardInfoPath, reqFileName);
                        log.info("删除成功");*/
                        // 不用在遍历其他文件
                        break;
                    }
                } catch (SftpException e) {
                    throw new RuntimeException(e);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                if (CollectionUtils.isEmpty(cardMallSyncArrayList)) {
                log.info("解析号卡信息同步FTP文件信息为空:{}", reqFileName);
                return null;
            }
            cardMallSyncService.batchInsertCardMallSync(cardMallSyncArrayList);
            /*String finalMainId = mainId;
            String finalCardType = cardType;*/
                //封装码号库存信息 先查下码号库存是否存在。
                if (CollectionUtils.isEmpty(cardInventoryMainInfos)){
                    //码号库存新的 初始化新的码号库存
                    CardInventoryMainInfo cardInventoryMainInfo = new CardInventoryMainInfo();

                    cardInventoryMainInfo.setId(mainId);
                    cardInventoryMainInfo.setCustCode(cardInfo.getCustCode());
                    cardInventoryMainInfo.setCustName(cardInfo.getCustName());
                    cardInventoryMainInfo.setTemplateId(cardInfo.getTemplateId());
                    cardInventoryMainInfo.setTemplateName(cardInfo.getTemplateName());
                    cardInventoryMainInfo.setBeId(cardInfo.getBeId());
                    cardInventoryMainInfo.setProviceName((String) provinceCodeNameMap.get(cardInfo.getBeId()));
                    cardInventoryMainInfo.setRegionId(cardInfo.getRegionId());
                    cardInventoryMainInfo.setRegionName((String) locationCodeNameMap.get(cardInfo.getRegionId()));
                    //查询号卡关联的码号的卡片类型，按照现在来说 相同号卡的码号卡片类型是相同的 任取一个就行。。。。。、

                    cardInventoryMainInfo.setCardType(cardType);
                    //TODO 计算初始化历史码号库存
                    cardInventoryMainInfo.setReserveQuatity(0);
                    cardInventoryMainInfo.setInventoryThreshold(0);
                    cardInventoryMainInfo.setIsNotice(true);
                    cardInventoryMainInfo.setInventoryStatus("1");
                    cardInventoryMainInfo.setCreateTime(date);
                    cardInventoryMainInfo.setUpdateTime(date);
                    cardInventoryMainInfo.setCurrentInventory(cardMallSyncArrayList.size());
                    cardInventoryMainInfo.setTotalInventory(cardMallSyncArrayList.size());
                    //TODO 查询当前省份是否存在省公司合作伙伴
              /*  BaseAnswer<List<UserPartner>> userPartnerByProvinceList = userFeignClient.getUserPartnerByProvinceList(cardInfo.getBeId());
                List<UserPartner> data = userPartnerByProvinceList.getData();
                if (CollectionUtils.isEmpty(data)){
                    cardInventoryMainInfo.setIsProvicePartner("1");
                }else {
                    cardInventoryMainInfo.setIsProvicePartner("0");
                }*/
                    //筛选过滤确定码号是未销售及销售中
                    try {
                        cardInventoryMainInfoMapper.insert(cardInventoryMainInfo);
                        log.info("新增码号库存数据CustCode：{}，TemplateId：{}，条数cardMallSyncArrayList：{}",custCode,templateId,cardMallSyncArrayList.size());
                    } catch (DataIntegrityViolationException e) {
                        log.info("新增码号库存并发新增唯一索引异常处理：DataIntegrityViolationException:{}",e.getMessage());
                        //唯一索引异常，重新查询进行更新操作
                        List<CardInventoryMainInfo> cardInventoryMainInfosIndex = cardInventoryMainInfoMapper.selectByExample(new CardInventoryMainInfoExample().createCriteria().andTemplateIdEqualTo(cardInfo.getTemplateId())
                                .andCustCodeEqualTo(cardInfo.getCustCode()).example());
                        CardInventoryMainInfo cardInventoryMainInfoIndex = cardInventoryMainInfosIndex.get(0);
                        CardInventoryMainInfo cardInventoryMainInfoEn =new CardInventoryMainInfo();
                        cardInventoryMainInfoEn.setId(cardInventoryMainInfoIndex.getId());
                        cardInventoryMainInfoEn.setCurrentInventory(cardInventoryMainInfoIndex.getCurrentInventory()+cardMallSyncArrayList.size());
                        cardInventoryMainInfoEn.setTotalInventory(cardInventoryMainInfoIndex.getTotalInventory()+cardMallSyncArrayList.size());
                        cardInventoryMainInfoEn.setUpdateTime(date);
                        cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);
                        log.info("索引异常修改码号库存数据CustCode：{}，TemplateId：{}，条数cardMallSyncArrayList：{}",custCode,templateId,cardMallSyncArrayList.size());
                    }
                }else {
                    //码号库存不为空 更新码号库存  服务编码和模板编码确定唯一码号库存
                    List<CardInventoryMainInfo> cardInventoryMainInfoUpdate = cardInventoryMainInfoMapper.selectByExample(new CardInventoryMainInfoExample().createCriteria().andTemplateIdEqualTo(cardInfo.getTemplateId())
                            .andCustCodeEqualTo(cardInfo.getCustCode()).example());
                    CardInventoryMainInfo cardInventoryMainInfo = cardInventoryMainInfoUpdate.get(0);
                    CardInventoryMainInfo cardInventoryMainInfoEn =new CardInventoryMainInfo();
                    cardInventoryMainInfoEn.setId(cardInventoryMainInfo.getId());
                    cardInventoryMainInfoEn.setCurrentInventory(cardInventoryMainInfo.getCurrentInventory()+cardMallSyncArrayList.size());
                    cardInventoryMainInfoEn.setTotalInventory(cardInventoryMainInfo.getTotalInventory()+cardMallSyncArrayList.size());
                    cardInventoryMainInfoEn.setUpdateTime(date);
                    cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);
                    log.info("修改码号库存数据CustCode：{}，TemplateId：{}，条数cardMallSyncArrayList：{}",custCode,templateId,cardMallSyncArrayList.size());
                }
              //执行完所有任务在执行删除
                log.info("删除文件:{}", reqFileName);
                try {
                    sftpUtil.delete(sftpCardInfoPath, reqFileName);
                } catch (SftpException e) {
                    throw new RuntimeException(e);
                }
                log.info("删除成功");
                return null;
            });

        } catch (Exception e) {
            log.error("解析号卡信息同步FTP文件{}信息错误:{}", reqFileName, e);
        }
    }

    @Override
    public void updateCardInventory(String id, Integer currentInventory, Integer totalInventory) {
        CardInventoryMainInfo cardInventoryMainInfoEn = new CardInventoryMainInfo();
        cardInventoryMainInfoEn.setId(id);
        cardInventoryMainInfoEn.setCurrentInventory(currentInventory);
        cardInventoryMainInfoEn.setTotalInventory(totalInventory);
        cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);
    }
}
