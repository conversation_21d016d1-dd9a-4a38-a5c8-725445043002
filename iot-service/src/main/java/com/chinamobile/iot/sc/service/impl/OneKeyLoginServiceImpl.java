package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.request.OneKeyLoginRequest;
import com.chinamobile.iot.sc.response.iot.OneKeyLoginResponse;
import com.chinamobile.iot.sc.service.OneKeyLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.lang3.StringUtils;
@Slf4j
@Service
public class OneKeyLoginServiceImpl implements OneKeyLoginService {

    @Value("${oneKey.loginUrl}")
    private String oneKeyLoginUrl;

    @Value("${oneKey.originUrl}")
    private String originUrl;



    /**
     * 转发商城请求移动认证一键登录接口
     * @param request
     * @return
     */
    @Override
    public IOTAnswer<OneKeyLoginResponse> tokenValidate(IOTRequest request) {
        log.debug("IoT商城一键登录信息, data:{}", JSON.toJSONString(request));
        OneKeyLoginRequest oneKeyLoginRequest = JSON.parseObject(request.getContent(), OneKeyLoginRequest.class);
        OneKeyLoginRequest.Header requestHeader = oneKeyLoginRequest.getHeader();
        HttpHeaders headers = new HttpHeaders();

        headers.add("content-type", "application/json;charset=utf-8");
        headers.add("interfaceVersion", requestHeader.getInterfaceVersion());
        headers.add("appId", requestHeader.getAppId());
        headers.add("traceId", requestHeader.getTraceId());
        headers.add("timestamp", requestHeader.getTimestamp());
        headers.add("businessType", requestHeader.getBusinessType());

        if(StringUtils.isNotEmpty(originUrl)){
            headers.add("origin", originUrl);
        }
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(oneKeyLoginRequest.getBody()), headers);
        try {
            log.debug("一键登录token校验接口request:{}",JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<OneKeyLoginResponse.Body> response = restTemplateHttps.postForEntity(oneKeyLoginUrl, requestEntity, OneKeyLoginResponse.Body.class);

            log.debug("一键登录token校验接口response:{}",JSON.toJSONString(response));
            OneKeyLoginResponse.Body oneKeyLoginResponseBody = response.getBody();

            OneKeyLoginResponse.Header oneKeyLoginResponseHeader = new OneKeyLoginResponse.Header();
            HttpHeaders oneKeyLoginResponseHeaderRet = response.getHeaders();
            oneKeyLoginResponseHeader.setTraceId(oneKeyLoginResponseHeaderRet.get("traceId").get(0));
            oneKeyLoginResponseHeader.setAppId(oneKeyLoginResponseHeaderRet.get("appId").get(0));
            oneKeyLoginResponseHeader.setTimestamp(oneKeyLoginResponseHeaderRet.get("timestamp").get(0));

            OneKeyLoginResponse OneKeyLoginResponse = new OneKeyLoginResponse();
            OneKeyLoginResponse.setBody(oneKeyLoginResponseBody);
            OneKeyLoginResponse.setHeader(oneKeyLoginResponseHeader);

            IOTAnswer<OneKeyLoginResponse> iotAnswer = new IOTAnswer<>();
            iotAnswer.setContent(OneKeyLoginResponse);
            return iotAnswer;
        } catch (Exception e) {
            log.error("一键登录失败:" + e);
            throw new BusinessException(StatusConstant.ONE_KEY_LOGIN_FAILED);
        }
    }


}
