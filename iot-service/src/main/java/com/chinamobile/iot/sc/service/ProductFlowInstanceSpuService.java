package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu;
import com.chinamobile.iot.sc.pojo.param.ProductFlowSkuParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/8
 * @description 产品流程spu service接口类
 */
public interface ProductFlowInstanceSpuService {

    /**
     * 获取流程实例的spu列表
     *
     * @return
     */
    BaseAnswer<List<ProductFlowInstanceSpu>> listProductFlowInstanceSpu(String spuInfo);
}
