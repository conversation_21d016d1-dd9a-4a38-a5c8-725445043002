package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.AddMessageParam;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.MessageListParam;
import com.chinamobile.iot.sc.pojo.vo.MessageDetailVO;
import com.chinamobile.iot.sc.pojo.vo.MessageListVO;

public interface MessageCenterService {
    BaseAnswer<PageData<MessageListVO>> getMessageList(MessageListParam param, String userId);

    BaseAnswer<MessageDetailVO> getMessageDetail(String id);

    BaseAnswer<Integer> getUnReadMsgCount(String userId, Integer source);

    BaseAnswer<Void> addMessage(AddMessageParam param);

    BaseAnswer<Void> deleteMessage(String id,String userId);

    BaseAnswer initModule();
}
