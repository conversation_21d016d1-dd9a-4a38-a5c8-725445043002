package com.chinamobile.iot.sc.service;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.request.supplychain.SupplyChainImportPoDraftIOTRequest;
import com.chinamobile.iot.sc.request.sync.SyncCommonRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface SupplyChainSyncService {

    BaseAnswer<Void> claimMaterials(SyncCommonRequest request);

    BaseAnswer<Void> paymentStatus(SyncCommonRequest request);

    BaseAnswer<Void> importPoDraft(SupplyChainImportPoDraftIOTRequest request);

    BaseAnswer<String> getMaterial(String contractId,  String segment1);

    BaseAnswer<Void> syncOrderToMarketSystem(List<String> OrderId);

    BaseAnswer<Void> syncOrderToMarketSystemB2B(String OrderId);

    BaseAnswer<Void> orderStatusFeedback(SyncCommonRequest syncCommonRequest);

    BaseAnswer syncOrderToMarketSystemCutOver(MultipartFile excel);

    void syncOrderToMarketSystemLastMonth();


    BaseAnswer syncOrderToMarketSystemCutOverClear(MultipartFile excel);

    BaseAnswer<Void> syncOrderToMarketSystemB2BMulti(List<String> orderIds);
}
