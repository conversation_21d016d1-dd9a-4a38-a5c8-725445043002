package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22
 * @description 卡片类型枚举类
 */
public enum CardTypeEnum {

    CHA_BO_CARD("0","插拔卡"),
    TIE_PIAN_CARD("1","贴片卡"),
//    M2M_NOT_NULL_CARD("2","M2M芯片非空写卡"),
    M2M_NULL_CARD("3","M2M芯片空写卡"),
    NO_INCLUDED_CARD("51","不含卡");

    /**
     * 卡片类型
     */
    private String type;

    /**
     * 卡片描述
     */
    private String desc;

    CardTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type) {
        for (CardTypeEnum value : CardTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public static Boolean containDesc(String desc) {
        for (CardTypeEnum value : CardTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
