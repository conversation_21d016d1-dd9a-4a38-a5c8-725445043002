package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.dao.AtomOfferingCooperatorRelationMapper;
import com.chinamobile.iot.sc.dao.ext.AtomOfferingCooperatorRelationMapperExt;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.pojo.dto.AtomCooperatorInfoByGroupDTO;
import com.chinamobile.iot.sc.pojo.dto.AtomCooperatorInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.AtomOfferingCooperatorRelation;
import com.chinamobile.iot.sc.pojo.entity.AtomOfferingCooperatorRelationExample;
import com.chinamobile.iot.sc.pojo.param.AtomCooperatorInfoByGroupParam;
import com.chinamobile.iot.sc.pojo.param.AtomCooperatorInfoParam;
import com.chinamobile.iot.sc.service.AtomOfferingCooperatorRelationService;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/26
 * @description 商品和从合作伙伴的关系service实现类
 */
@Service
public class AtomOfferingCooperatorRelationServiceImpl implements AtomOfferingCooperatorRelationService {

    @Resource
    private AtomOfferingCooperatorRelationMapper atomOfferingCooperatorRelationMapper;

    @Resource
    private AtomOfferingCooperatorRelationMapperExt atomOfferingCooperatorRelationMapperExt;

    @Resource
    private UserFeignClient userFeignClient;

    @Override
    public void batchAddAtomOfferingCooperatorRelation(List<AtomOfferingCooperatorRelation> relationList) {
        atomOfferingCooperatorRelationMapper.batchInsert(relationList);
    }

    @Override
    public void deleteAtomOfferingCooperatorRelationByExample(AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample) {
        atomOfferingCooperatorRelationMapper.deleteByExample(atomOfferingCooperatorRelationExample);
    }

    @Override
    public List<AtomOfferingCooperatorRelation> listAtomOfferingCooperatorRelationByExample(AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample) {
        return atomOfferingCooperatorRelationMapper.selectByExample(atomOfferingCooperatorRelationExample);
    }

    @Override
    public List<AtomCooperatorInfoByGroupDTO> listCooperatorInfoByGroup(AtomCooperatorInfoByGroupParam atomCooperatorInfoByGroupParam) {
        return atomOfferingCooperatorRelationMapperExt.listCooperatorInfoByGroup(atomCooperatorInfoByGroupParam);
    }

    @Override
    public List<AtomCooperatorInfoDTO> listCooperatorInfo(AtomCooperatorInfoParam atomCooperatorInfoParam) {
        return atomOfferingCooperatorRelationMapperExt.listCooperatorInfo(atomCooperatorInfoParam);
    }

    @Override
    public List<Data4User> listCooperatorUserInfo(String atomOfferingId) {
        AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample = new AtomOfferingCooperatorRelationExample();
        atomOfferingCooperatorRelationExample.createCriteria()
                .andAtomOfferingIdEqualTo(atomOfferingId);
        List<AtomOfferingCooperatorRelation> atomOfferingCooperatorRelationList = listAtomOfferingCooperatorRelationByExample(atomOfferingCooperatorRelationExample);
        if (CollectionUtils.isEmpty(atomOfferingCooperatorRelationList)){
            throw new RuntimeException("未找到原子商品id为"+atomOfferingId+"的从合作伙伴信息");
        }

        List<String> cooperatorIdList = atomOfferingCooperatorRelationList.stream()
                .map(AtomOfferingCooperatorRelation::getCooperatorId)
                .distinct()
                .collect(Collectors.toList());
        BaseAnswer<List<Data4User>> cooperatorBaseAnswer = userFeignClient.partnerInfoByIds(cooperatorIdList);
        cooperatorBaseAnswer.getData();

        if (cooperatorBaseAnswer == null
                || !cooperatorBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                || cooperatorBaseAnswer.getData() == null) {
            throw new RuntimeException("调用获取从合作伙伴信息失败。从合作伙伴ID:" + String.join(",",cooperatorIdList));
        }

        return cooperatorBaseAnswer.getData();
    }
}
