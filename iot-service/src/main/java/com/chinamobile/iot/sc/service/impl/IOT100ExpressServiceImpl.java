package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.dao.LogisticsInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cInfoMapper;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.LogisticsInfo;
import com.chinamobile.iot.sc.pojo.LogisticsInfoExample;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.request.express.QueryTrackReq;
import com.chinamobile.iot.sc.request.express.SubscribeExpressParam;
import com.chinamobile.iot.sc.request.express.SubscribeReq;
import com.chinamobile.iot.sc.response.iot.express.*;
import com.chinamobile.iot.sc.service.IOT100ExpressService;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.express.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> xiemaohua
 * @date : 2022/4/13 16:40
 * @description: 快递100接口实现类
 **/
@Service
@Slf4j
public class IOT100ExpressServiceImpl implements IOT100ExpressService {

    /**
     * 查询url
     */
    public static final String QUERY_URL = "https://poll.kuaidi100.com/poll/query.do";

    /**快递100授权码*/
    @Value("${logistics.key}")
    private String logisticsKey;

    /**快递100订阅地址*/
    @Value("${logistics.subscribeUrl}")
    private String subscribeUrl;

    /**快递100回调地址*/
    @Value("${logistics.callbackUrl}")
    private String callbackUrl;

    @Resource
    private LogisticsInfoMapper logisticsInfoMapper;

    @Resource
    private IOrder2CService order2CService;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Override
    public IOTAnswer<JSONObject> queryRealTimeExpress(IOTRequest baseRequest) {
        log.info("接受到查询快递100请求,data:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<JSONObject> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        QueryTrackReq queryTrackReq = null;
        try {
            queryTrackReq = JSON.parseObject(baseRequest.getContent(), QueryTrackReq.class);
            log.info("封装传入的json字符串：{}",baseRequest.getContent());
        } catch (Exception e) {
            log.error("解析异常:" + e);
            throw new IOTException(iotAnswer, "数据解析异常");
        }
   /*     QueryTrackParam param = queryTrackReq.getParam();
        String jsonParam = JSON.toJSONString(param);
        QueryTrackParamReq queryTrackParamReq =new QueryTrackParamReq();
        queryTrackParamReq.setCustomer(queryTrackReq.getCustomer());
        queryTrackParamReq.setSign(queryTrackReq.getSign());
        queryTrackParamReq.setParam(jsonParam);*/
        HttpResult httpResult = HttpUtils.doPost(QUERY_URL, queryTrackReq, 3000, 3000);
        if (httpResult.getStatus() != HttpStatus.SC_OK && StringUtils.isBlank(httpResult.getBody())) {
            log.error("查询快递数据失败：{}", httpResult);
            throw new IOTException(iotAnswer,String.valueOf(httpResult.getStatus()), httpResult.getError());
        }
        QueryTrackResp queryTrackResp = JSON.parseObject(httpResult.getBody(), QueryTrackResp.class);
        JSONObject jsonObject = (JSONObject) JSON.toJSON(queryTrackResp);
        log.info("快递查询服务返回数据：{}",jsonObject.toJSONString());
        iotAnswer.setContent(jsonObject);
        return iotAnswer;
    }

    @Override
    public BaseKD100Resp syncByKD100(KD100SyncParamWithSign kd100param) {
        BaseKD100Resp resp = new BaseKD100Resp();
        log.info("快递100物流信息推送,data:{}", JSON.toJSONString(kd100param));
        KD100SyncParam param = kd100param.getParam();
        if (param == null || param.getLastResult() == null) {
            String msg = "快递100物流信息推送数据为空";
            log.error(msg);
            resp.setResult(false);
            resp.setReturnCode("201");
            resp.setMessage(msg);
            return resp;
        }

        if (StringUtils.isBlank(param.getLastResult().getCom()) || StringUtils.isBlank(param.getLastResult().getNu())) {
            String msg = "物流单号异常";
            log.error(msg);
            resp.setResult(false);
            resp.setReturnCode("202");
            resp.setMessage(msg);
            return resp;
        }

        List<LogisticsInfo> logisticsInfos = logisticsInfoMapper.selectByExample(new LogisticsInfoExample().createCriteria()
                .andSupplierNameEqualTo(param.getLastResult().getCom()).andLogisCodeEqualTo(param.getLastResult().getNu())
                .example());
        if (CollectionUtils.isNotEmpty(logisticsInfos)) {
            //物流单唯一
            LogisticsInfo logisticsInfo = logisticsInfos.get(0);
            Integer state = StringUtils.isNotBlank(param.getLastResult().getState()) ?  Integer.parseInt(param.getLastResult().getState()) : 0;
            logisticsInfo.setLogisticsState(state);
            logisticsInfoMapper.updateByPrimaryKeySelective(logisticsInfo);

            order2CService.asyncDealServiceOrder(logisticsInfo.getOrderId());
        }



        return resp;
    }

    @Override
    public BaseAnswer<Void> subscribe(SubscribeExpressParam param) {
        log.info("订阅快递100物流信息,data:{}", JSON.toJSONString(param));

        SubscribeReq subscribeReq = new SubscribeReq();
        subscribeReq.setCompany(param.getCompany());
        subscribeReq.setNumber(param.getNumber());
        subscribeReq.setKey(logisticsKey);
        SubscribeReq.Parameters parameters = new SubscribeReq.Parameters();
        parameters.setResultv2("4");
        parameters.setCallbackurl(callbackUrl);
        if (StringUtils.isNoneBlank(param.getOrderId())) {
            Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(param.getOrderId());
            parameters.setPhone(IOTEncodeUtils.decryptSM4(order2cInfo.getContactPhone(),iotSm4Key, iotSm4Iv));
        }
        subscribeReq.setParameters(parameters);

        QueryTrackReq paramReq = new QueryTrackReq();
        paramReq.setParam(JSON.toJSONString(subscribeReq));
        log.info("订阅快递100物流信息,订阅参数:{}", JSON.toJSONString(paramReq));
        HttpResult httpResult = HttpUtils.doPost(subscribeUrl, paramReq, 3000, 3000);
        log.info("订阅快递100物流信息,订阅结果:{}", JSON.toJSONString(httpResult));
        if (httpResult.getStatus() != HttpStatus.SC_OK && StringUtils.isBlank(httpResult.getBody())) {
            log.error("订阅快递100物流信息失败：{}", httpResult);
            throw new BusinessException(String.valueOf(httpResult.getStatus()), httpResult.getError());
        }

        BaseKD100Resp queryTrackResp = JSON.parseObject(httpResult.getBody(), BaseKD100Resp.class);
        if (!queryTrackResp.getResult()) {
            log.error("订阅快递100物流信息失败：{}", queryTrackResp);
            throw new BusinessException(BaseErrorConstant.KD_100_SUBSCRIBE_ERROR,queryTrackResp.getMessage());
        }

        return BaseAnswer.success(null);
    }
}
