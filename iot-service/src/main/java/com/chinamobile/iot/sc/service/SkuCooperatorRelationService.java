package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.SkuCooperatorRelation;
import com.chinamobile.iot.sc.pojo.entity.SkuCooperatorRelationExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 * @description  sku商品和从合作伙伴关系service接口类
 */
public interface SkuCooperatorRelationService {

    /**
     * 批量新增sku商品和从合作伙伴关系
     * @param skuCooperatorRelationList
     */
    void batchAddSkuCooperatorRelation(List<SkuCooperatorRelation> skuCooperatorRelationList);

    /**
     * 根据需要删除sku商品和从合作伙伴关系
     * @param skuCooperatorRelationExample
     */
    void deleteSkuCooperatorRelationByNeed(SkuCooperatorRelationExample skuCooperatorRelationExample);

    /**
     * 根据需要获取sku商品和从合作伙伴关系
     * @param skuCooperatorRelationExample
     * @return
     */
    List<SkuCooperatorRelation> listSkuCooperatorRelationByNeed(SkuCooperatorRelationExample skuCooperatorRelationExample);
}
