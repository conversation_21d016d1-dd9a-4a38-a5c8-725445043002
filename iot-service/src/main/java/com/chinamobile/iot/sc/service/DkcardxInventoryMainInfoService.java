package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfoExample;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryCardDetailParam;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryMainInfoParam;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailInfoDTO;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailLocationDTO;
import com.chinamobile.iot.sc.pojo.vo.DkcardxInventoryMainInfoVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16
 * @description 卡+X终端库存主要信息service接口类
 */
public interface DkcardxInventoryMainInfoService {

    /**
     * 批量新增卡+X终端库存主要信息
     *
     * @param mainInfoList
     */
    void batchInsertInventoryMainInfo(List<DkcardxInventoryMainInfo> mainInfoList);

    /**
     * 根据需要获取卡+X终端库存主要信息
     *
     * @param mainInfoExample
     * @return
     */
    List<DkcardxInventoryMainInfo> listDkcardxInventoryMainInfoByNeed(DkcardxInventoryMainInfoExample mainInfoExample);

    /**
     * 分页查询卡+X终端库存主要信息
     *
     * @param dkcardxInventoryMainInfoParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<DkcardxInventoryMainInfoVO> pageCardMainInfo(DkcardxInventoryMainInfoParam dkcardxInventoryMainInfoParam,
                                                          LoginIfo4Redis loginIfo4Redis);

    /**
     * 导出卡+X库存信息
     * @param dkcardxInventoryMainInfoParam
     * @param loginIfo4Redis
     * @param response
     * @throws Exception
     */
    void exportDxInventoryMainAndDetail(DkcardxInventoryMainInfoParam dkcardxInventoryMainInfoParam,
                                        LoginIfo4Redis loginIfo4Redis,
                                        HttpServletResponse response) throws Exception;

    /**
     * 根据库存信息获取卡+X详情列表
     * @param cardDetailParam
     * @return
     */
    List<DkcardxInventoryCardDetailInfoDTO> listCardRelationByInventory(DkcardxInventoryCardDetailParam cardDetailParam);

    /**
     * 根据库存信息分页获取卡+X详情列表
     * @param cardDetailParam
     * @return
     */
    PageData<DkcardxInventoryCardDetailInfoDTO> pageCardRelationByInventory(DkcardxInventoryCardDetailParam cardDetailParam);

    /**
     * 卡+X库存详情终端明细地市下拉列表
     * @param inventoryMainId
     * @return
     */
    List<DkcardxInventoryCardDetailLocationDTO> listXDetailLocation(String inventoryMainId);
}
