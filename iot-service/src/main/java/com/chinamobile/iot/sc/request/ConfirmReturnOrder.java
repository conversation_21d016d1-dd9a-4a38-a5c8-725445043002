package com.chinamobile.iot.sc.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: YSC
 * @Date: 2022/1/6 14:10
 * @Description:
 */
@Data
public class ConfirmReturnOrder {
    /**
     * 退货订单请求流水号
     */
//    @NotBlank(message = "请输入售后单号")
    private String refundOrderId;
    /**
     * 订单号
     */
    @NotBlank(message = "请输入正确的订单号")
    private String orderId;
    /**
     * 返回 0：收货验货成功，1：收货验货失败
     */
    @NotBlank(message = "审核结果不能为空")
    private String receiptResult;
    /**
     * 验货失败原因	当 receiptResults返回1时必填
     */
    private String receiptReason;
}
