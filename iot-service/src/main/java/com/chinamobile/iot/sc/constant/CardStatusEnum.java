package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22
 * @description 卡片状态枚举类
 */
public enum CardStatusEnum {

    NOT_SELL("1","未销售"),
    SELLING("2","销售中"),
    SELL_SUCCESS("3","已销售"),
    CAN_NOT_SELL("9","不可销售"),
    ;

    /**
     * 卡片状态类型
     */
    private String type;

    /**
     * 卡片状态描述
     */
    private String desc;

    CardStatusEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(String type) {
        for (CardStatusEnum value : CardStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
