package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22
 * @description 销售状态枚举类
 */
public enum SellStatusEnum {

    NOT_SELL("1","未销售"),
    SELLING("2","销售中"),
    SELL_SUCCESS("3","已销售"),
    SELL_FAIL("4","销售失败"),
    CAN_NOT_SELL("9","不可销售")
    ;

    /**
     * 销售类型
     */
    private String type;

    /**
     * 销售描述
     */
    private String desc;

    SellStatusEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(String type) {
        for (SellStatusEnum value : SellStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
