package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfo;
import com.chinamobile.iot.sc.pojo.param.CardRelationImportInfoParam;
import com.chinamobile.iot.sc.pojo.vo.CardRelationImportInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12
 * @description 卡+X终端导入批次相关信息service接口类
 */
public interface CardRelationImportInfoService {

    /**
     * 批量新增卡+X终端导入相关信息
     * @param cardRelationImportInfoList
     */
    void batchAddCardRelationImportInfo(List<CardRelationImportInfo> cardRelationImportInfoList);


    /**
     * 分页查询卡+X终端导入相关信息
     * @param cardRelationImportInfoParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<CardRelationImportInfoVO> pageCardRelationImportInfo(CardRelationImportInfoParam cardRelationImportInfoParam,
                                                     LoginIfo4Redis loginIfo4Redis);

}
