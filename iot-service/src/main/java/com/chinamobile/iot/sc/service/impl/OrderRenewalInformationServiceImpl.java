package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.OrderRenewalInformationMapper;
import com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformation;
import com.chinamobile.iot.sc.service.OrderRenewalInformationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/27
 * @description
 */
@Service
public class OrderRenewalInformationServiceImpl implements OrderRenewalInformationService {

    @Resource
    private OrderRenewalInformationMapper orderRenewalInformationMapper;

    @Override
    public void addOrderRenewalInformation(OrderRenewalInformation orderRenewalInformation) {
        orderRenewalInformationMapper.insert(orderRenewalInformation);
    }
}
