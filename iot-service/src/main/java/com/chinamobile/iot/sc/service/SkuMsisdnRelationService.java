package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelation;
import com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelationExample;
import com.chinamobile.iot.sc.pojo.param.GetOrderResultParam;
import com.chinamobile.iot.sc.pojo.vo.GetOrderResultVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/2
 * @description 订单原子信息和卡号关系service接口类
 */
public interface SkuMsisdnRelationService {

    /**
     * 批量新增订单原子信息和卡号关系
     * @param skuMsisdnRelationList
     */
    void batchAddSkuMsisdnRelation(List<SkuMsisdnRelation> skuMsisdnRelationList);

    /**
     * 根据需要更新原子信息卡关系
     * @param skuMsisdnRelation
     * @param skuMsisdnRelationExample
     */
    void updateSkuMsisdnRelationByNeed(SkuMsisdnRelation skuMsisdnRelation,
                                       SkuMsisdnRelationExample skuMsisdnRelationExample);

    /**
     * 根据需要获取订单与卡关系
     * @param skuMsisdnRelationExample
     * @return
     */
    List<SkuMsisdnRelation> listSkuMsisdnRelationByNeed(SkuMsisdnRelationExample skuMsisdnRelationExample);

    /**
     * 根据id更新订单和卡关系
     * @param skuMsisdnRelation
     */
    void updateSkuMsisdnRelationById(SkuMsisdnRelation skuMsisdnRelation);

    /**
     * 根据需要删除数据
     * @param skuMsisdnRelationExample
     */
    void deleteSkuMsisdnRelationByNeed(SkuMsisdnRelationExample skuMsisdnRelationExample);

    /**
     * 获取订购结果
     * @param getOrderResultParam
     * @return
     */
    List<GetOrderResultVO> listGetOrderResult(GetOrderResultParam getOrderResultParam);
}
