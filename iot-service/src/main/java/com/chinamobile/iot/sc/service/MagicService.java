package com.chinamobile.iot.sc.service;

import org.springframework.web.multipart.MultipartFile;

public interface MagicService {

    void testOrder2cInfoSM4Sql();

    void cutoverOrderProvinceorg(MultipartFile file);

    void duplicateOrderRoc(String prikey);

    void duplicateOrderRocModiStatus(String prikey, Integer innerStatus, Integer orgStatus, String updateTime);

    void duplicateOrderRocHistory(String atomId, String refundOrderId, Integer operType, Integer innerStatus, String createTime, String updateTime);

}
