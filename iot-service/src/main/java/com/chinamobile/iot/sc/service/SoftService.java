package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.param.SoftServiceOpenParam;
import com.chinamobile.iot.sc.request.sync.SyncCommonRequest;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

public interface SoftService {
    BaseAnswer<Void> feedback(SyncCommonRequest syncCommonRequest);
    BaseAnswer<Void> feedbackNew(SyncCommonRequest syncCommonRequest);
    BaseAnswer<Void> softServiceOpen(SoftServiceOpenParam softServiceOpenParam);
    BaseAnswer<Void> softServiceOpenTest(String platform, String operateType,String phone, String orderId,String code);
    BaseAnswer<Void> softServiceSyncIot(String orderId);


    BaseAnswer<Void> softServiceOpenFail(SoftServiceOpenParam softServiceOpenParam);
    BaseAnswer<Void> softServiceImport(MultipartFile file);
}
