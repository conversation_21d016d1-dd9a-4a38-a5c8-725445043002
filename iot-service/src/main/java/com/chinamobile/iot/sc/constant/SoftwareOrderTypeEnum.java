package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/25
 * @description 软件服务订单类型枚举类
 */
public enum SoftwareOrderTypeEnum {

    OPEN_ORDER("0","开通订单"),
    RENEWAL_ORDER("1","续费订单");

    /**
     * 软件服务订单类型
     */
    private String type;

    /**
     * 软件服务订单描述
     */
    private String desc;

    SoftwareOrderTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(String type) {
        for (SoftwareOrderTypeEnum value : SoftwareOrderTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
