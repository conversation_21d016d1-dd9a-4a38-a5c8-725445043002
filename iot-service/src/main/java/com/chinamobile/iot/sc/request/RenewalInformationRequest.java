package com.chinamobile.iot.sc.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/27
 * @description 续费操作信息实体类
 */
@Data
public class RenewalInformationRequest {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 操作
     * 0：关闭服务
     */
    private String operation;

    /**
     * 原因
     * 0：扣费失败
     * 1：客户取消续订
     * 当商城自动扣款失败发起解约时传0：扣费失败；其他解约场景下传1：客户取消续订
     */
    private String reason;

}
