package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.NewProductOnlineOfflineRequestStatusConstant;
import com.chinamobile.iot.sc.constant.RedisLockConstant;
import com.chinamobile.iot.sc.dao.NewProductRequestManageMapper;
import com.chinamobile.iot.sc.dao.ext.NewProductRequestManageMapperExt;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.ProductManageOperateEnum;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.DataUserVO;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo;
import com.chinamobile.iot.sc.pojo.NewProductRequestManage;
import com.chinamobile.iot.sc.pojo.NewProductRequestManageExample;
import com.chinamobile.iot.sc.pojo.dto.DateDTO;
import com.chinamobile.iot.sc.pojo.mapper.NewProductRequestManageListDO;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.NewProductManageDetailsVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineRequestVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductRequestManageListVO;
import com.chinamobile.iot.sc.request.product.ProvinceCityVO;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.NewProductRequestHandlerInfoService;
import com.chinamobile.iot.sc.service.NewProductRequestManageService;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineService;
import com.chinamobile.iot.sc.service.excel.BatchProductRequestExcel;
import com.chinamobile.iot.sc.service.excel.BatchProductRequestExcelListener;
import com.chinamobile.iot.sc.service.excel.BatchProductRequestFailed;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.IotLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chinamobile.iot.sc.common.BaseConstant.PRODUCT_OPERATOR_IMPORT_ROLE;
import static com.chinamobile.iot.sc.constant.NewProductOnlineOfflineRequestStatusConstant.NEW_ADD;
import static com.chinamobile.iot.sc.constant.NewProductOnlineStatusConstant.*;
import static com.chinamobile.iot.sc.constant.NewProductRequestFlowTypeConstant.ADD_NEW_PRODUCT;
import static com.chinamobile.iot.sc.constant.NewProductRequestHandlerConstant.*;
import static com.chinamobile.iot.sc.constant.NewProductRequestStatusConstant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13
 * @description 新产品引入申请管理service实现类
 */
@Service
@Slf4j
public class NewProductRequestManageServiceImpl implements NewProductRequestManageService {

    @Resource
    private NewProductRequestManageMapper newProductRequestManageMapper;

    @Resource
    private NewProductRequestManageMapperExt newProductRequestManageMapperExt;

    @Resource
    private NewProductRequestHandlerInfoService newProductRequestHandlerInfoService;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private SmsFeignClient smsFeignClient;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private NewProductRequestOnlineOfflineService newProductRequestOnlineOfflineService;

    @Resource
    private ProvinceCityConfig provinceCityConfig;

    @Resource
    private LogService logService;

    @Value("${sms.ProductInitiateTemplateId:106258}")
    private String productInitiateTemplateId;

    @Value("${sms.ProductNotPassTemplateId:106259}")
    private String productNotPassTemplateId;

    @Value("${sms.ProductPassTemplateId:106260}")
    private String productPassTemplateId;

    @Value("${supply.des.key}")
    private String encryptKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));

    @Override
    public List<NewProductOnlineRequestVO> listFirstNewProductOnlineRequest(LoginIfo4Redis loginIfo4Redis) {
        List<NewProductOnlineRequestVO> result = null;
        if(loginIfo4Redis.getIsAdmin()){
            result = newProductRequestManageMapperExt.listReadyOnlineAdmin(loginIfo4Redis.getUserId());
        }else{
            result = newProductRequestManageMapperExt.listReadyOnline(loginIfo4Redis.getUserId());
        }
        if (CollectionUtils.isNotEmpty(result)) {
            //手机号解码
            result.forEach(item -> item.setCooperatorPhone(IOTEncodeUtils.decryptSM4(item.getCooperatorPhone(), iotSm4Key, iotSm4Iv)));
        }
        return result;
    }

    @Override
    public NewProductRequestManage getNewProductRequestManageById(String id) {
        return newProductRequestManageMapper.selectByPrimaryKey(id);
    }

    @Override
    public PageData<NewProductOnlineOfflineVO> pageNewProductOnlineOffline(NewProductOnlineOfflineParam onlineOfflineParam,
                                                                           LoginIfo4Redis loginIfo4Redis) {
        PageData<NewProductOnlineOfflineVO> pageData = new PageData<>();
        Integer pageNum = onlineOfflineParam.getPageNum();
        Integer pageSize = onlineOfflineParam.getPageSize();

        onlineOfflineParam.setCurrentUserId(loginIfo4Redis.getUserId());

        Page<NewProductOnlineOfflineVO> page = new Page<>(pageNum, pageSize);
        List<NewProductOnlineOfflineVO> onlineOfflineVOList
                = newProductRequestManageMapperExt.listNewProductOnlineOffline(page, onlineOfflineParam);

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(onlineOfflineVOList);

        return pageData;
    }

    @Override
    public void updateOnlineRelatedInfo(String id,
                                        String onlineStatus,
                                        Integer onlineOfflineRequestStatus,
                                        String onlineOfflineCurrentHandlerUserId) {
        NewProductRequestManage manage = new NewProductRequestManage();
        manage.setOnlineStatus(onlineStatus);
        manage.setOnlineOfflineRequestStatus(onlineOfflineRequestStatus);
        manage.setOnlineOfflineCurrentHandlerUserId(onlineOfflineCurrentHandlerUserId);
        manage.setUpdateTime(new Date());
        manage.setId(id);
        newProductRequestManageMapper.updateByPrimaryKeySelective(manage);
    }


    @Override
    public void updateSpuAndSkuOfferingCode(NewProductUpdateSpuAndSkuCodeParam spuAndSkuCodeParam) {
        NewProductRequestManage manage = new NewProductRequestManage();
        manage.setSpuOfferingCode(spuAndSkuCodeParam.getSpuOfferingCode());
        manage.setSkuOfferingCode(spuAndSkuCodeParam.getSkuOfferingCode());
        manage.setUpdateTime(new Date());

        NewProductRequestManageExample example = new NewProductRequestManageExample();
        example.createCriteria()
                .andIdEqualTo(spuAndSkuCodeParam.getId())
                // 终审通过到运管审核状态才允许更新sku和spu编码
                .andOnlineOfflineRequestStatusEqualTo(NewProductOnlineOfflineRequestStatusConstant.ONLINE_OPERATOR_JUDGMENT);

        newProductRequestManageMapper.updateByExampleSelective(manage, example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insetNewProductRequestManage(NewProductRequestManageAddParam addParam, String userId, LoginIfo4Redis loginIfo4Redis) {
        /**通过角色是否持有产品引入权限控制*/
        //        String roleType = loginIfo4Redis.getRoleType();
//        if (!PARTNER_LORD_ROLE.equals(roleType)) {
//            throw new BusinessException(StatusConstant.AUTH_ERROR);
//        }
        //获取当前时间时间字符串年月日
        Date date = new Date();
        String dateToStr = DateUtils.dateToStr(date, DateUtils.DATE_FORMAT_NO_SYMBOL);
        String redisKey = RedisLockConstant.PRODUCT_APPLY_SERIAL.concat(dateToStr);
        String requestNo = newProductRequestOnlineOfflineService.getNewOddNumber(redisKey);
        log.info("产品申请编号：requestNo:{}", requestNo);
        String productManagerPhone = addParam.getProductManagerPhone();
        String productManagerEmail = addParam.getProductManagerEmail();
        if (!RegexUtil.regexPhone(productManagerPhone)) {

            throw new BusinessException(StatusConstant.PHONE_ERROR);
        }
        if (!RegexUtil.regexEmail(productManagerEmail)) {

            throw new BusinessException(StatusConstant.EMAIL_FORMAT_ERROR);
        }
        //获取产品引入初审员
//        DataUserVO dataUserVO = getProductTrial(requestNo);
        BaseAnswer<Data4User> baseAnswer = userFeignClient.userInfo(addParam.getOperatorImportUserId());
        if (baseAnswer == null || !baseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || baseAnswer.getData() == null) {
            log.error("调用获取初审员用户信息失败。用户ID:{}", addParam.getOperatorImportUserId());

            throw new BusinessException(BaseErrorConstant.GET_COOPERATOR_INFO_FAILED, "调用获取初审员用户信息失败。用户ID:" + addParam.getOperatorImportUserId());
        }
        DataUserVO dataUserVO = new DataUserVO();
        BeanUtils.copyProperties(baseAnswer.getData(),dataUserVO);
        String id = BaseServiceUtils.getId();
        //封装产品基础信息
        NewProductRequestManage newProductRequestManage = packagingNewProductRequest(id, userId, requestNo, addParam, date, dataUserVO);
        newProductRequestManageMapper.insertSelective(newProductRequestManage);
        //新增流程处理表信息（一个主账号申请流程）)
        insertNewProductHandlerInfo(id, loginIfo4Redis.getUserId(), loginIfo4Redis, dataUserVO, date);
        //发送短信至初审员
        sendNoteToFirstTrial(dataUserVO.getPhone(), requestNo, addParam.getSpuOfferingName(), addParam.getSkuOfferingName());

        //记录日志
/*        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INTRODUCE_MANAGE.code,
                IotLogUtil.insetNewProductContentFromRequest(addParam));*/
    }

    @Override
    public void updateNewProductRequestManage(NewProductRequestManageUpdateParam updateParam, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        //通过接口入口校验权限
//        if (!PARTNER_LORD_ROLE.equals(roleType)) {
//            throw new BusinessException(StatusConstant.AUTH_ERROR);
//        }
        NewProductRequestManage newProductRequestManage = newProductRequestManageMapper.selectByPrimaryKey(updateParam.getId());
        Integer requestStatus = newProductRequestManage.getRequestStatus();
        //未通过审核才能编辑
        if (!requestStatus.equals(NOT_PASS)) {
            throw new BusinessException(StatusConstant.PRODUCT_UPDATE_ONLY_NO_PASS);
        }
        if (!RegexUtil.regexPhone(updateParam.getProductManagerPhone())) {
            throw new BusinessException(StatusConstant.PHONE_ERROR);
        }
        if (!RegexUtil.regexEmail(updateParam.getProductManagerEmail())) {
            throw new BusinessException(StatusConstant.EMAIL_FORMAT_ERROR);
        }

        Date date = new Date();
//        DataUserVO dataUserVO = getProductTrial(newProductRequestManage.getRequestNo());
        BaseAnswer<Data4User> baseAnswer = userFeignClient.userInfo(updateParam.getOperatorImportUserId());
        if (baseAnswer == null || !baseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || baseAnswer.getData() == null) {
            log.error("调用获取初审员用户信息失败。用户ID:{}", updateParam.getOperatorImportUserId());
            throw new BusinessException(BaseErrorConstant.GET_COOPERATOR_INFO_FAILED, "调用获取初审员用户信息失败。用户ID:" + updateParam.getOperatorImportUserId());
        }
        DataUserVO dataUserVO = new DataUserVO();
        BeanUtils.copyProperties(baseAnswer.getData(),dataUserVO);
        BeanUtils.copyProperties(updateParam, newProductRequestManage);
        newProductRequestManage.setProductSaleAreaCode(updateParam.getProductSaleAreaCode());
        newProductRequestManage.setRequestStatus(FIRST_TRIAL);
        newProductRequestManage.setRequestCurrentHandlerUserId(updateParam.getOperatorImportUserId());
        newProductRequestManage.setUpdateTime(date);
        newProductRequestManageMapper.updateByPrimaryKeySelective(newProductRequestManage);
        //新增流程处理表信息（一个主账号申请流程）)
        insertNewProductHandlerInfo(newProductRequestManage.getId(), loginIfo4Redis.getUserId(), loginIfo4Redis, dataUserVO, date);

        //发送短信至初审员
        sendNoteToFirstTrial(dataUserVO.getPhone(), newProductRequestManage.getRequestNo(), updateParam.getSpuOfferingName(), updateParam.getSkuOfferingName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importProductRequest(MultipartFile file, String userId, LoginIfo4Redis loginIfo4Redis) {

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String roleType = loginIfo4Redis.getRoleType();
        //通过接口入口校验权限
//        if (!PARTNER_LORD_ROLE.equals(roleType)) {
//            throw new BusinessException(StatusConstant.AUTH_ERROR);
//        }
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        boolean fileTypeBool = oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"));
        if (fileTypeBool) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }
        try {
            BatchProductRequestExcelListener listener = new BatchProductRequestExcelListener();
            listener.setProvinceCityConfig(provinceCityConfig);
            listener.setUserFeignClient(userFeignClient);
            List<Object> list = EasyExcel.read(file.getInputStream(), BatchProductRequestExcel.class, listener)
                    .sheet(0).headRowNumber(1).doReadSync();

            if (list.size() == 0) {
                throw new BusinessException(BaseErrorConstant.EMPTY_EXCEL);
            }
            List<NewProductRequestManageAddParam> data = listener.getSucceedListData();
            List<BatchProductRequestFailed> failedListData = listener.getFailedListData();
            //有校验失败的返回错误信息
            if (CollectionUtils.isNotEmpty(failedListData)) {
                log.info("产品申请失败：{}", failedListData);
                String excelName = "批量产品申请失败信息";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                response.setHeader("Content-disposition", "attachment;filename=" + excelName + ".xlsx");
                response.addHeader("stateCode", BaseErrorConstant.BATCH_PRODUCT_APPLY_FAILED.getStateCode());
                response.addHeader("message", URLEncoder.encode(BaseErrorConstant.BATCH_PRODUCT_APPLY_FAILED.getMessage(), "UTF-8"));
                int[] mergeColumnIndex = {0};
                ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), BatchProductRequestFailed.class).build();
                WriteSheet sheetWriter = EasyExcel.writerSheet(0, "失败列表").build();
                excelWriter.write(failedListData, sheetWriter);
                excelWriter.finish();
            } else {
                for (NewProductRequestManageAddParam addParam : data) {
                    insetNewProductRequestManage(addParam, userId, loginIfo4Redis);
                }
                response.addHeader("stateCode", BaseErrorConstant.SUCCESS.getStateCode());
                response.addHeader("message", URLEncoder.encode("批量产品申请成功", "UTF-8"));
            }

        } catch (IOException ioe) {
            log.error("读取文件异常，文件名: {},异常描述：{}", oldName, ioe);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }

    }


    @Override
    public NewProductManageDetailsVO getProductManageDetails(String id) {
        return newProductRequestManageMapperExt.getNewProductManageDetailsById(id);
    }

    // 数据权限-产品引入管理
    @Override
    public PageData<NewProductRequestManageListVO> getPageManageApplyList(NewProductRequestManageWebParam manageWebParam, LoginIfo4Redis loginIfo4Redis) {

        // 商品名称搜索支持反斜杠适配
        if(manageWebParam.getSkuOfferingName() != null){
            manageWebParam.setSkuOfferingName(manageWebParam.getSkuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        if(manageWebParam.getSpuOfferingName() != null){
            manageWebParam.setSpuOfferingName(manageWebParam.getSpuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        PageData<NewProductRequestManageListVO> pageData = new PageData<>();
        Integer pageNum = manageWebParam.getPageNum();
        Integer pageSize = manageWebParam.getPageSize();
        Page<NewProductRequestManageListVO> page = new Page<>(pageNum, pageSize);
        String startTimeStr = manageWebParam.getStartTimeStr();
        String endTimeStr = manageWebParam.getEndTimeStr();
        Integer requestStatus = manageWebParam.getRequestStatus();
        NewProductRequestManagePageParam pageParam = new NewProductRequestManagePageParam();
        BeanUtils.copyProperties(manageWebParam, pageParam);
        if (StringUtils.isNotEmpty(startTimeStr) && StringUtils.isNotEmpty(endTimeStr)) {
            DateDTO dateDTO = checkTime(startTimeStr, endTimeStr);
            pageParam.setStartTime(dateDTO.getStartTime());
            pageParam.setEndTime(dateDTO.getEndTime());
        }
        //注入参数
        getRequestStatusList(pageParam, loginIfo4Redis, requestStatus);
        List<NewProductRequestManageListDO> manageList = newProductRequestManageMapperExt.getPageRequestManageList(page, pageParam);
        List<NewProductRequestManageListVO> listPage = manageList.stream().map(newProductRequestManageListDO -> {
            NewProductRequestManageListVO listVO = new NewProductRequestManageListVO();
            BeanUtils.copyProperties(newProductRequestManageListDO, listVO);
            return listVO;
        }).collect(Collectors.toList());

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(listPage);
        return pageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertProductAuditDispose(NewProductManageAuditParam auditParam, LoginIfo4Redis loginIfo4Redis,String ip) {
        if (loginIfo4Redis.getIsAdmin()) {

            throw new BusinessException(BaseErrorConstant.NOT_USER_AUDIT);
        }
//        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();
        String productId = auditParam.getProductId();
        String handlerRemark = auditParam.getHandlerRemark();
        String handlerStatus = auditParam.getHandlerStatus();
        String nextHandlerUserId = auditParam.getNextHandlerUserId();
        String nextHandlerUserName = auditParam.getNextHandlerUserName();
        String handlerPhone = auditParam.getHandlerPhone();
        NewProductRequestManage requestManage = newProductRequestManageMapper.selectByPrimaryKey(productId);
        Integer requestStatus = requestManage.getRequestStatus();
        if (requestStatus.equals(PASS) || requestStatus.equals(NOT_PASS)) {

            throw new BusinessException(BaseErrorConstant.PRODUCT_NOT_AUDIT);
        }
        String requestCurrentHandlerUserId = requestManage.getRequestCurrentHandlerUserId();
        //上一个流程处理信息
        NewProductRequestHandlerInfo reviewDetails = newProductRequestHandlerInfoService.getReviewDetails(productId, ADD_NEW_PRODUCT, userId);
        Date date = new Date();
        NewProductRequestHandlerInfo trialHandlerInfo = new NewProductRequestHandlerInfo();
        //超管拥有所有审批权限
        /**审批权限已经在接口认证拦截*/
        if ((loginIfo4Redis.getIsAdmin() || userId.equals(requestCurrentHandlerUserId))) {
            if (handlerStatus.equals(HANDLER_STATUS_PASS)) {
                if (FIRST_TRIAL.equals(requestStatus)) {
                    BigDecimal salePrice = auditParam.getSalePrice();
                    BigDecimal supplyPrice = requestManage.getSupplyPrice();
                    if (supplyPrice.compareTo(salePrice) > 0) {
                        throw new BusinessException(BaseErrorConstant.SUPPLY_PRICE_NOT_THAN_SALE_PRICE);
                    }
                    if (salePrice == null) {

                        throw new BusinessException(BaseErrorConstant.SALE_PRICE_NOT_NULL);
                    }
                    requestManage.setSalePrice(salePrice);
                    requestManage.setRequestStatus(RECHECK);
                    requestManage.setRequestCurrentHandlerUserId(nextHandlerUserId);
                    requestManage.setUpdateTime(date);

                    trialHandlerInfo.setRequestLink("产品引入-初审");
                } else if (RECHECK.equals(requestStatus)) {
                    requestManage.setRequestStatus(FINAL_JUDGMENT);
                    requestManage.setRequestCurrentHandlerUserId(nextHandlerUserId);
                    requestManage.setUpdateTime(date);

                    trialHandlerInfo.setRequestLink("产品引入-复审");
                } else if (FINAL_JUDGMENT.equals(requestStatus)) {
                    requestManage.setRequestStatus(PASS);
                    requestManage.setOnlineStatus(NOT_ONLINE);
                    requestManage.setOnlineOfflineRequestStatus(NEW_ADD);
                    requestManage.setRequestCurrentHandlerUserId(StringUtils.EMPTY);
                    requestManage.setUpdateTime(date);

                    trialHandlerInfo.setRequestLink("产品引入-终审");
                }
            }
        } else {

            throw new BusinessException(BaseErrorConstant.NOT_USER_AUDIT);
        }

//        //角色类型初审员
//        if (PRODUCT_OPERATOR_IMPORT_ROLE.equals(roleType)) {
//            if (FIRST_TRIAL.equals(requestStatus) && userId.equals(requestCurrentHandlerUserId)) {
//                if (handlerStatus.equals(HANDLER_STATUS_PASS)) {
//                    BigDecimal salePrice = auditParam.getSalePrice();
//                    BigDecimal supplyPrice = requestManage.getSupplyPrice();
//                    if (supplyPrice.compareTo(salePrice) > 0) {
//                        throw new BusinessException(BaseErrorConstant.SUPPLY_PRICE_NOT_THAN_SALE_PRICE);
//                    }
//                    if (salePrice == null) {
//                        throw new BusinessException(BaseErrorConstant.SALE_PRICE_NOT_NULL);
//                    }
//                    requestManage.setSalePrice(salePrice);
//                    requestManage.setRequestStatus(RECHECK);
//                    requestManage.setRequestCurrentHandlerUserId(nextHandlerUserId);
//                    requestManage.setUpdateTime(date);
//
//                    trialHandlerInfo.setRequestLink("产品引入-初审");
//                }
//            } else {
//                throw new BusinessException(BaseErrorConstant.NOT_USER_AUDIT);
//            }
//            //角色为复审
//        } else if (PRODUCT_OPERATOR_RECHECK_ROLE.equals(roleType)) {
//            if (RECHECK.equals(requestStatus) && userId.equals(requestCurrentHandlerUserId)) {
//                if (handlerStatus.equals(HANDLER_STATUS_PASS)) {
//                    requestManage.setRequestStatus(FINAL_JUDGMENT);
//                    requestManage.setRequestCurrentHandlerUserId(nextHandlerUserId);
//                    requestManage.setUpdateTime(date);
//
//                    trialHandlerInfo.setRequestLink("产品引入-复审");
//                }
//            } else {
//                throw new BusinessException(BaseErrorConstant.NOT_USER_AUDIT);
//            }
//            //角色为终审(终审员可操作终审)
//        } else if (PRODUCT_OPERATOR_ULTIMATELY_ROLE.equals(roleType)) {
//            if (FINAL_JUDGMENT.equals(requestStatus) && userId.equals(requestCurrentHandlerUserId)) {
//                if (handlerStatus.equals(HANDLER_STATUS_PASS)) {
//                    requestManage.setRequestStatus(PASS);
//                    requestManage.setOnlineStatus(NOT_ONLINE);
//                    requestManage.setOnlineOfflineRequestStatus(NEW_ADD);
//                    requestManage.setRequestCurrentHandlerUserId(StringUtils.EMPTY);
//                    requestManage.setUpdateTime(date);
//
//                    trialHandlerInfo.setRequestLink("产品引入-终审");
//                }
//            } else {
//                throw new BusinessException(BaseErrorConstant.NOT_USER_AUDIT);
//            }
//        } else {
//            throw new BusinessException(BaseErrorConstant.PRODUCT_NOT_AUDIT);
//        }
        //所有角色审核不通过修改参数一样
        if (handlerStatus.equals(HANDLER_STATUS_NO_PASS)) {
            //不通过
            if (StringUtils.isEmpty(handlerRemark)) {

                throw new BusinessException(BaseErrorConstant.HANDLING_OPINION_NOT_NULL);
            }
            requestManage.setRequestStatus(NOT_PASS);
            requestManage.setRequestCurrentHandlerUserId(requestManage.getCreator());
            requestManage.setUpdateTime(date);
            if (FIRST_TRIAL.equals(requestStatus)) {
                trialHandlerInfo.setRequestLink("产品引入-初审");
            } else if (RECHECK.equals(requestStatus)) {
                trialHandlerInfo.setRequestLink("产品引入-复审");
            } else if (FINAL_JUDGMENT.equals(requestStatus)) {
                trialHandlerInfo.setRequestLink("产品引入-终审");
            }
        }
        //修改产品信息
        newProductRequestManageMapper.updateByPrimaryKeySelective(requestManage);

        trialHandlerInfo.setId(BaseServiceUtils.getId());
        trialHandlerInfo.setFlowSourceId(requestManage.getId());
        trialHandlerInfo.setFlowType(FLOW_TYPE_IMPORT);
        trialHandlerInfo.setCurrentHandlerUserId(userId);
        trialHandlerInfo.setCurrentHandlerUserName(loginIfo4Redis.getUserName());
        trialHandlerInfo.setNextHandlerUserId(nextHandlerUserId);
        trialHandlerInfo.setNextHandlerUserName(nextHandlerUserName);
        trialHandlerInfo.setHandlerRemark(handlerRemark);
        trialHandlerInfo.setHandlerStatus(handlerStatus);
        trialHandlerInfo.setCreateTime(reviewDetails.getUpdateTime());
        trialHandlerInfo.setUpdateTime(date);
        newProductRequestHandlerInfoService.saveHandlerInfo(trialHandlerInfo);
        //发送短信
        sendManageNote(handlerStatus, handlerPhone, requestManage,
                 requestStatus, userId, requestCurrentHandlerUserId);

        //记录日志
/*        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code,
                ProductManageOperateEnum.INTRODUCE_MANAGE.code,
                IotLogUtil.auditNewProductContentFromRequest(requestManage));*/
    }

    @Override
    public Integer getProductByRequestUserIdOrOnlineUserId(String userId) {
        List<Integer> requestStatus = new ArrayList<>();
        List<String> onlineStatus = new ArrayList<>();
        requestStatus.add(PASS);
        requestStatus.add(NOT_PASS);
        onlineStatus.add(ONLINING);
        onlineStatus.add(OFFLINING);
        NewProductRequestManageExample requestExample = new NewProductRequestManageExample().createCriteria().andRequestCurrentHandlerUserIdEqualTo(userId).andRequestStatusNotIn(requestStatus).example();
        NewProductRequestManageExample onlineExample = new NewProductRequestManageExample().createCriteria().andOnlineOfflineCurrentHandlerUserIdEqualTo(userId).andOnlineStatusIn(onlineStatus).example();
        List<NewProductRequestManage> requestStatusManages = newProductRequestManageMapper.selectByExample(requestExample);
        List<NewProductRequestManage> onlineStatusManages = newProductRequestManageMapper.selectByExample(onlineExample);
        List<NewProductRequestManage> collect = Stream.concat(requestStatusManages.stream(), onlineStatusManages.stream()).collect(Collectors.toList());
        return collect.size();
    }

    @Override
    public List<ProvinceCityVO> getProvinceCityList() {
        return provinceCityConfig.getProvinceCityList();
    }


    /**
     * 封装新增产品信息产品
     *
     * @param userId
     * @param requestNoNew
     * @param addParam
     * @param date
     * @param dataUserVO
     * @return
     */
    public NewProductRequestManage packagingNewProductRequest(String id, String userId, String requestNoNew,
                                                              NewProductRequestManageAddParam addParam, Date date,
                                                              DataUserVO dataUserVO) {

        NewProductRequestManage newProductRequestManage = new NewProductRequestManage();
        newProductRequestManage.setId(id);
        newProductRequestManage.setRequestNo(requestNoNew);
        newProductRequestManage.setSpuOfferingName(addParam.getSpuOfferingName());
        newProductRequestManage.setSkuOfferingName(addParam.getSkuOfferingName());
        newProductRequestManage.setRequestStatus(FIRST_TRIAL);
        newProductRequestManage.setCooperatorId(addParam.getCooperatorId());
        newProductRequestManage.setBrand(addParam.getBrand());
        newProductRequestManage.setModel(addParam.getModel());
        newProductRequestManage.setColor(addParam.getColor());
        newProductRequestManage.setMaterialCode(addParam.getMaterialCode());
        newProductRequestManage.setNetworkProperty(addParam.getNetworkProperty());
        newProductRequestManage.setApplicationDomain(addParam.getApplicationDomain());
        newProductRequestManage.setProductIntroduction(addParam.getProductIntroduction());
        newProductRequestManage.setProductSaleContent(addParam.getProductSaleContent());
        newProductRequestManage.setProductSaleAreaCode(addParam.getProductSaleAreaCode());
        newProductRequestManage.setProductSaleArea(addParam.getProductSaleArea());
        newProductRequestManage.setSupplyPrice(addParam.getSupplyPrice());
        newProductRequestManage.setMarketPrice(addParam.getMarketPrice());
        // newProductRequestManage.setSalePrice(addParam.getSalePrice());
        newProductRequestManage.setProductManagerName(addParam.getProductManagerName());
        newProductRequestManage.setProductManagerPhone(addParam.getProductManagerPhone());
        newProductRequestManage.setProductManagerEmail(addParam.getProductManagerEmail());
        newProductRequestManage.setRequestCurrentHandlerUserId(dataUserVO.getUserId());
        newProductRequestManage.setCreator(userId);
        newProductRequestManage.setCreateTime(date);
        newProductRequestManage.setUpdateTime(date);
        return newProductRequestManage;
    }


    /**
     * 校验时间
     *
     * @param startTimeStr
     * @param endTimeStr
     * @return
     */
    private DateDTO checkTime(String startTimeStr, String endTimeStr) {
        Date startTime = null;
        Date endTime = null;
        try {
            if (StringUtils.isNotEmpty(startTimeStr)) {
                startTime = DateTimeUtil.getFormatDate(startTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT);
            }
            if (StringUtils.isNotEmpty(endTimeStr)) {
                endTime = DateTimeUtil.getFormatDate(endTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT);
            }
        } catch (ParseException e) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "日期格式错误");
        }
        if (startTime != null && endTime != null && startTime.after(endTime)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "开始时间不能大于结束时间");
        }
        DateDTO dateDTO = new DateDTO();
        dateDTO.setStartTime(startTime);
        dateDTO.setEndTime(endTime);
        return dateDTO;
    }

    /**
     * 封装查询状态参数
     *
     * @param loginIfo4Redis
     * @param requestStatus
     * @return
     */
    public void getRequestStatusList(NewProductRequestManagePageParam pageParam, LoginIfo4Redis loginIfo4Redis, Integer requestStatus) {
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_NEW_PRODUCT_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_NEW_PRODUCT_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_NEW_PRODUCT_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        List<Integer> requestStatusList = new ArrayList<>();
        String userId = loginIfo4Redis.getUserId();
        String roleType = loginIfo4Redis.getRoleType();
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        if (requestStatus != null) {
            //查询状态为待审核时
            if (FIRST_TRIAL.equals(requestStatus)) {
                requestStatusList.add(FIRST_TRIAL);
                requestStatusList.add(RECHECK);
                requestStatusList.add(FINAL_JUDGMENT);
                //查询状态为通过
            } else if (PASS.equals(requestStatus)) {
                requestStatusList.add(PASS);
                //不通过
            } else if (NOT_PASS.equals(requestStatus)) {
                requestStatusList.add(NOT_PASS);
                //已完结
            } else if (CRACKING.equals(requestStatus)) {
                requestStatusList.add(PASS);
                requestStatusList.add(NOT_PASS);
            }

        }
        // 超管应该可以看全部,其他人只能看跟自己相关的
        if (!isAdmin) {
            pageParam.setUserId(userId);
        }

        if (CollectionUtils.isNotEmpty(requestStatusList)) {
            pageParam.setRequestStatusList(requestStatusList);
        }
    }


    /**
     * 发送短信
     *
     * @param handlerStatus
     * @param handlerPhone
     * @param requestManage
     * @param requestStatus
     * @param userId
     * @param requestCurrentHandlerUserId
     */
    public void sendManageNote(String handlerStatus, String handlerPhone, NewProductRequestManage requestManage,
                               Integer requestStatus, String userId, String requestCurrentHandlerUserId) {
        log.info("产品申请审核发送短信,phone:{},requestNo{}", handlerPhone, requestManage.getRequestNo());
        List<String> phones = new ArrayList<>();
        phones.add(handlerPhone);
        Msg4Request msgRequest = new Msg4Request();
        if (handlerStatus.equals(HANDLER_STATUS_PASS) && StringUtils.isNotEmpty(handlerPhone)) {
            //发送短信下一个处理人
            msgRequest.setTemplateId(productInitiateTemplateId);
        }
        //不通过发送短信给主合作伙伴
        if (handlerStatus.equals(HANDLER_STATUS_NO_PASS) && StringUtils.isNotEmpty(handlerPhone)) {
            msgRequest.setTemplateId(productNotPassTemplateId);
        }
        /**根据是否拥有终审权限判断*/
//        //终审通过结束发送短信给主合作伙伴
//        if (PRODUCT_OPERATOR_ULTIMATELY_ROLE.equals(roleType)) {
//            if (FINAL_JUDGMENT.equals(requestStatus) && userId.equals(requestCurrentHandlerUserId) && handlerStatus.equals(HANDLER_STATUS_PASS)) {
//                msgRequest.setTemplateId(productPassTemplateId);
//            }
//        }
        if (FINAL_JUDGMENT.equals(requestStatus) && userId.equals(requestCurrentHandlerUserId) && handlerStatus.equals(HANDLER_STATUS_PASS)) {
            msgRequest.setTemplateId(productPassTemplateId);
        }
        msgRequest.setMobiles(phones);
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("requestNo", requestManage.getRequestNo());
        msgMap.put("spuOfferingName", requestManage.getSpuOfferingName());
        msgMap.put("skuOfferingName", requestManage.getSkuOfferingName());
        msgRequest.setMessage(msgMap);
        smsFeignClient.asySendMessage(msgRequest);
    }

    /**
     * 获取产品初审员信息
     *
     * @param requestNo
     * @return
     */
    public DataUserVO getProductTrial(String requestNo) {
        //根据拥有初审权限进行赛选人员
        BaseAnswer<List<DataUserVO>> users = userFeignClient.getListUserByRoleType(PRODUCT_OPERATOR_IMPORT_ROLE);
//        BaseAnswer<List<DataUserVO>> users = userFeignClient.getListUserByAuthCode(PRODUCT_APPLY_MANAGE_VERIFY_FIRST);
        if (users == null || !users.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || users.getData() == null) {
            log.error("查询产品引入初审员信息错误，申请编号requestNo={}", requestNo);
            throw new BusinessException(BaseErrorConstant.PRODUCT_IMPORT_INFO_ERROR, "查询产品引入初审员信息错误，申请编号 requestNo: " + requestNo);
        }
        //产品引入初审员只有一个
        List<DataUserVO> data = users.getData();
        if (data.size() > FIRST_TRIAL) {
            throw new BusinessException(StatusConstant.PRODUCT_IMPORT_SOLE);
        }
        DataUserVO dataUserVO = data.get(0);
        return dataUserVO;
    }

    /**
     * 新增产品申请处理流程信息
     *
     * @param id
     * @param currentHandlerUserId
     * @param loginIfo4Redis
     * @param dataUserVO
     * @param date
     */
    public void insertNewProductHandlerInfo(String id, String currentHandlerUserId, LoginIfo4Redis loginIfo4Redis, DataUserVO dataUserVO, Date date) {
        //新增流程处理表信息（一个主账号申请流程）)
        NewProductRequestHandlerInfo startHandlerInfo = new NewProductRequestHandlerInfo();
        startHandlerInfo.setId(BaseServiceUtils.getId());
        startHandlerInfo.setFlowSourceId(id);
        startHandlerInfo.setFlowType(FLOW_TYPE_IMPORT);
        startHandlerInfo.setRequestLink("产品引入-发起");
        startHandlerInfo.setCurrentHandlerUserId(currentHandlerUserId);
        startHandlerInfo.setCurrentHandlerUserName(loginIfo4Redis.getUserName());
        startHandlerInfo.setNextHandlerUserId(dataUserVO.getUserId());
        startHandlerInfo.setNextHandlerUserName(dataUserVO.getName());
        startHandlerInfo.setHandlerRemark("发起");
        startHandlerInfo.setHandlerStatus(HANDLER_STATUS_SPONSOR);
        startHandlerInfo.setCreateTime(date);
        startHandlerInfo.setUpdateTime(date);
        newProductRequestHandlerInfoService.saveHandlerInfo(startHandlerInfo);
    }

    /**
     * 发送短信给初审员
     *
     * @param phone
     * @param requestNo
     * @param spuOfferingName
     * @param skuOfferingName
     */
    public void sendNoteToFirstTrial(String phone, String requestNo, String spuOfferingName, String skuOfferingName) {
        List<String> phones = new ArrayList<>();
        phones.add(phone);
        Msg4Request msg4Request = new Msg4Request();
        msg4Request.setMobiles(phones);
        msg4Request.setTemplateId(productInitiateTemplateId);
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("requestNo", requestNo);
        msgMap.put("spuOfferingName", spuOfferingName);
        msgMap.put("skuOfferingName", skuOfferingName);
        msg4Request.setMessage(msgMap);
        smsFeignClient.asySendMessage(msg4Request);
    }
}
