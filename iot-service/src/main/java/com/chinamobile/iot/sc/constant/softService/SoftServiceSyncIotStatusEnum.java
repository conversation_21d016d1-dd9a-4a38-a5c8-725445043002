package com.chinamobile.iot.sc.constant.softService;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/10
 * @description 软件服务同步商城状态枚举类
 */
public enum SoftServiceSyncIotStatusEnum {

    SYNC_NOT(0,"未同步"),
    SYNC_OPEN_FAIL(1,"开通成功同步iot商城失败"),
    SYNC_USE_FAIL(2,"使用中同步iot商城失败");

    /**
     * 同步商城状状态类型
     */
    private Integer type;

    /**
     * 同步商城状状态描述
     */
    private String desc;

    SoftServiceSyncIotStatusEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(Integer type) {
        for (SoftServiceSyncIotStatusEnum value : SoftServiceSyncIotStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
