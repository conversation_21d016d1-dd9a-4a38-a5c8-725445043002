package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.constant.NoticeTypeConstant;
import com.chinamobile.iot.sc.constant.PlatformClassEnum;
import com.chinamobile.iot.sc.constant.softService.SoftServiceAtomStatusEnum;
import com.chinamobile.iot.sc.constant.softService.SoftServiceOpenStatusEnum;
import com.chinamobile.iot.sc.constant.softService.SoftServiceRetailStatusEnum;
import com.chinamobile.iot.sc.constant.softService.SoftServiceSyncIotStatusEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.SoftServiceMapperExt;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.excel.SoftServiceImportExcelListener;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.*;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.SoftServiceImportDTO;
import com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig;
import com.chinamobile.iot.sc.pojo.entity.ChargeItemConfigExample;
import com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo;
import com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfoExample;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoDetailHandle;
import com.chinamobile.iot.sc.pojo.param.SoftResultToIotParam;
import com.chinamobile.iot.sc.pojo.param.SoftServiceFeedbackNewParam;
import com.chinamobile.iot.sc.pojo.param.SoftServiceFeedbackParam;
import com.chinamobile.iot.sc.pojo.param.SoftServiceOpenParam;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import com.chinamobile.iot.sc.request.order2c.SoftServiceParam;
import com.chinamobile.iot.sc.request.sync.SyncCommonRequest;
import com.chinamobile.iot.sc.response.SoftServiceResponse;
import com.chinamobile.iot.sc.service.OrderCooperatorRelationService;
import com.chinamobile.iot.sc.service.SoftService;
import com.chinamobile.iot.sc.service.UserRefundKxService;
import com.chinamobile.iot.sc.util.*;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SOFT_SERVICE_SUCESS;

@Slf4j
@Service
public class SoftServiceImpl implements SoftService {
    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    @Resource
    private Order2cInfoMapper order2cInfoMapper;
    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;
    @Resource
    private ChargeItemConfigMapper chargeItemConfigMapper;
    @Resource
    private ServiceOpenInfoMapper serviceOpenInfoMapper;
    @Resource
    private QlySoftOpenClient qlySoftOpenClient;
    @Resource
    private OnenetSoftOpenClient onenetSoftOpenClient;
    @Resource
    private OyeCyberSoftOpenClient oyeCyberSoftOpenClient;
    @Resource
    private XingCheWeishiSoftOpenClient xingCheWeishiSoftOpenClient;
    @Resource
    SoftServiceMapperExt softServiceMapperExt;
    @Resource
    private HemuSoftOpenClient hemuSoftOpenClient;

    @Resource
    private GxMiNiSoftOpenClient gxMiNiSoftOpenClient;

    @Resource
    private XCWSSoftOpenClient xcwsSoftOpenClient;

    @Resource
    private JsKangYangSoftOpenClient jsKangYangSoftOpenClient;
    @Resource
    private YtSoftOpenClient ytSoftOpenClient;
    @Value("${iot.secretKey}")
    private String iotSecretKey;
    @Value("${softService.secretKey}")
    private String secretKey;
    @Value("${softService.osSecretKey}")
    private String osSecretKey;
    @Value("${softService.sm4Key}")
    private String sm4Key;
    @Value("${iot.serviceNumberOrderResult}")
    private String softServiceResultUrl;
    @Resource
    private UserFeignClient userFeignClient;
    @Value("${sms.smsSoftServiceTemplateId: 107683}")
    private String smsSoftServiceTemplateId;
    @Autowired
    private SmsFeignClient smsFeignClient;
    @Resource
    private UserRefundKxService userRefundKxService;

    @Resource
    private OrderCooperatorRelationService orderCooperatorRelationService;

    @Resource
    private OrderHandleMapper orderHandleMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;
    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");
    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);


    public BaseAnswer<Void> feedback(SyncCommonRequest syncCommonRequest) {
        BaseAnswer<Void> answer = new BaseAnswer<>();
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        // sign验签
        SignUtils.checkSign(input, sign, osSecretKey);
        SoftServiceFeedbackParam param = JSON.parseObject(input, SoftServiceFeedbackParam.class);
        //更新开通状态
        List<SoftServiceFeedbackParam.productInfo> productInfoList = param.getServiceInfoList();
        log.info("软件服务反馈信息：{}", JSON.toJSONString(param));
        //获取原子订单信息

        Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(param.getOrderId());

        if (order2cAtomInfo == null) {
            log.info("未找到对应的原子订单信息");
            throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ORDER_NOT_FOUND, "未找到对应的原子订单信息");
        }

        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(order2cAtomInfo.getOrderId());
        if (order2cInfo == null) {
            throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ORDER_NOT_FOUND, "未找到对应的订单信息");
        }
        ServiceOpenInfoExample serviceOpenInfoExampleAll = new ServiceOpenInfoExample().createCriteria().andAtomOrderIdEqualTo(param.getOrderId()).example();
        List<ServiceOpenInfo> serviceOpenInfoAllList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExampleAll);
        if (serviceOpenInfoAllList == null || serviceOpenInfoAllList.size() == 0) {

            throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ID_NOT_FOUND, "未找到对应的软件服务");
        }
        //获取不同原子
        List<ServiceOpenInfo> serviceOpenInfoList1 = new ArrayList<>();
        ;
        List<String> atomList = new ArrayList<>();
        for (ServiceOpenInfo serviceOpenInfo : serviceOpenInfoAllList) {
            if (!atomList.contains(serviceOpenInfo.getAtomOfferingCode())) {
                atomList.add(serviceOpenInfo.getAtomOfferingCode());
                serviceOpenInfoList1.add(serviceOpenInfo);
            }
        }
        //获取反馈中操作失败的的软件服务
        List<String> serviceOpenFeedInfoList = new ArrayList<>();

//        0-开通回复
//        1-退订回复  operateType
//        2-“使用中指令”回复
        if (param.getOpreateType() == 0) {
            //获取数据库未开通，或开通失败的软件服务
            List<Integer> openStatusList = new ArrayList<>();
            openStatusList.add(0);
            ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample().createCriteria().andAtomOrderIdEqualTo(param.getOrderId()).andSoftServiceOpenStatusNotIn(openStatusList).example();
            List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
            if (serviceOpenInfoList == null || serviceOpenInfoList.size() == 0) {
                throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ID_NOT_FOUND, "未找到对应的软件服务");
            }
            Integer sucessNum = 0;
            Boolean isFail = false;
            for (SoftServiceFeedbackParam.productInfo productInfo : productInfoList) {
                ServiceOpenInfo serviceOpenInfo = new ServiceOpenInfo();

                serviceOpenInfo.withId(productInfo.getOrderSerialNumber())
                        .withSoftServiceOpenStatus(productInfo.getResult())
                        .withSoftServiceOpenTime(new Date())
                        .withOpenFailReason(productInfo.getFailReason() == null ? null : productInfo.getFailReason());
                serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo);
                if (productInfo.getResult() == 0) {
                    sucessNum += 1;
                } else {
                    isFail = true;
                }
            }
            //如果有开通失败，且同步回调接口没有失败，则短信通知
            if (isFail) {
                //休眠几秒，兼容同步返回失败且异步返回失败
//                try {
//                    Thread.sleep(8000);
//                    Order2cAtomInfo order2cAtomInfoS = order2cAtomInfoMapper.selectByPrimaryKey(param.getOrderId());
//                    log.info("原子订单信息:{}", JSON.toJSONString(order2cAtomInfoS));
//                    if (order2cAtomInfoS == null) {
//                        throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ORDER_NOT_FOUND, "未找到对应的原子订单信息");
//                    }
//                    if (order2cAtomInfoS.getSoftServiceStatus() != 1) {
//                        //更新原子总体状态
//                        Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
//                                .withId(param.getOrderId())
//                                .withSoftServiceStatus(1);
//                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
//                        //发送短信提示用户
//                        sendMsg(serviceOpenInfoList.get(0), param.getOpreateType());
//                    }
//                } catch (InterruptedException e) {
//                    log.error("回传失败,原子订单号:{},原因:{}",param.getOrderId(), e.getMessage());
//                }
                if (order2cAtomInfo.getSoftServiceStatus() != 1) {
                    //更新原子总体状态
                    log.info("原子订单信息:{}", JSON.toJSONString(order2cAtomInfo));
                    Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                            .withId(param.getOrderId())
                            .withSoftServiceStatus(1);
                    order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                    //更新订单的更新时间，避免覆盖其他字段
                    Order2cInfo updateOrder2cInfo = new Order2cInfo();
                    updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                    updateOrder2cInfo.setUpdateTime(new Date());
                    order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                    //发送短信提示用户
                    sendMsg(serviceOpenInfoList.get(0), param.getOpreateType());
                }

            } else {
                //判断原子订单是否已经全部开通
                if (serviceOpenInfoList.size() == sucessNum) {
                    //该原子全部开通成功
                    //更新原子状态为开通成功
                    Order2cAtomInfo order2cAtomInfo2 = new Order2cAtomInfo()
                            .withId(param.getOrderId())
                            .withSoftServiceStatus(0);
                    order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo2);
                    //更新订单的更新时间，避免覆盖其他字段
                    Order2cInfo updateOrder2cInfo = new Order2cInfo();
                    updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                    updateOrder2cInfo.setUpdateTime(new Date());
                    order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                    //判断是否存在其他原子，并且其它原子是否已经开通成功
                    Boolean otherAtomSucess = true;
                    List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(new Order2cAtomInfoExample().createCriteria()
                            .andOrderIdEqualTo(serviceOpenInfoList.get(0).getOrderId()).example());
                    //目前只有A13需要向商城反馈
                    if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(order2cInfo.getSpuOfferingClass())) {
                        if (order2cAtomInfoList.size() > 1) {
                            //判断另外一个原子是否开通成功
                            for (Order2cAtomInfo order2cAtomInfoItem : order2cAtomInfoList) {
                                if (!order2cAtomInfoItem.getId().equals(serviceOpenInfoList.get(0).getAtomOrderId())) {
                                    if (order2cAtomInfoItem.getSoftServiceStatus() != null && order2cAtomInfoItem.getSoftServiceStatus() != 0 && order2cAtomInfoItem.getSoftServiceStatus() != 6) {
                                        otherAtomSucess = false;
                                    }
                                }
                            }
                            if (otherAtomSucess) {
                                //同步商城开通成功
                                //获取请求体参数
                                //构建请求体
                                SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                softResultToIotParam.setServiceResult("1");
                                syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());

                            }
                        } else {
                            //同步商城开通成功
                            //获取请求体参数
                            //构建请求体
                            SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                            softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                            softResultToIotParam.setServiceResult("1");
                            syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());

                        }
                    }

                }
            }
        } else if (param.getOpreateType() == 1) {
            Boolean isFail = false;
            Integer sucessNum = 0;
            List<Integer> openStatusList = new ArrayList<>();
            openStatusList.add(0);

            ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample().createCriteria().andAtomOrderIdEqualTo(param.getOrderId()).andSoftServiceRetailStatusEqualTo(2).example();
            List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
            if (serviceOpenInfoList == null || serviceOpenInfoList.size() == 0) {

                throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ID_NOT_FOUND, "未找到对应的软件服务");
            }

            for (SoftServiceFeedbackParam.productInfo productInfo : productInfoList) {
                ServiceOpenInfo serviceOpenInfo = new ServiceOpenInfo();
                serviceOpenInfo
                        .withId(productInfo.getOrderSerialNumber())
                        .withSoftServiceRetailStatus(productInfo.getResult())
                        .withSoftServiceRetailTime(new Date())
                        .withRetailFailReason(productInfo.getFailReason() == null ? null : productInfo.getFailReason());
                if (productInfo.getResult() == 0) {
                    sucessNum += 1;
                } else {
                    isFail = true;
                }
                //退订成功，更新开通成功的软件服务
                if (productInfo.getResult() == 0) {
                    serviceOpenInfo.setSoftServiceOpenStatus(1);
                    serviceOpenInfo.setOpenFailReason("用户退订");
                }
                serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo);
            }
            if (isFail) {
                //有退订失败的,发送短信提示用户
                Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                        .withId(param.getOrderId())
                        .withSoftServiceStatus(4); //退订失败
                order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                //更新订单的更新时间，避免覆盖其他字段
                Order2cInfo updateOrder2cInfo = new Order2cInfo();
                updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                updateOrder2cInfo.setUpdateTime(new Date());
                order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                sendMsg(serviceOpenInfoList.get(0), param.getOpreateType());
            } else {
                //判断原子订单是否已经全部退订成功
                if (serviceOpenInfoList.size() == sucessNum) {
                    Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                            .withId(param.getOrderId())
                            .withSoftServiceStatus(3); //退订成功
                    order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                    //更新订单的更新时间，避免覆盖其他字段
                    Order2cInfo updateOrder2cInfo = new Order2cInfo();
                    updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                    updateOrder2cInfo.setUpdateTime(new Date());
                    order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                }
            }
        } else if (param.getOpreateType() == 2) {
            try {

                Boolean inUse = false;
                Boolean isSyncOpenFail = false;
                Thread.sleep(2000);
                if (param.getReturnType() == 0) {
                    //批量回传，全部处于使用中
                    for (SoftServiceFeedbackParam.productInfo productInfo : productInfoList) {
                        //获取开通手机号
                        ServiceOpenInfo serviceOpenInfo = new ServiceOpenInfo();
                        serviceOpenInfo
                                .withId(productInfo.getOrderSerialNumber())
                                .withSoftServiceUseTime(new Date());
                        serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo);
                    }
                    Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                            .withId(param.getOrderId())
                            .withSoftServiceStatus(6); //使用中
                    order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                    //更新订单的更新时间，避免覆盖其他字段
                    Order2cInfo updateOrder2cInfo = new Order2cInfo();
                    updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                    updateOrder2cInfo.setUpdateTime(new Date());
                    order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                    inUse = true;
                    ServiceOpenInfo serviceOpenInfo = serviceOpenInfoMapper.selectByPrimaryKey(productInfoList.get(0).getOrderSerialNumber());
                    if (serviceOpenInfo.getSyncIotFailStatus() == 1) {
                        isSyncOpenFail = true;
                    }

                } else {

                    ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample().createCriteria().andAtomOrderIdEqualTo(param.getOrderId()).andSoftServiceUseTimeIsNull().andSoftServiceOpenStatusEqualTo(0).example();
                    List<ServiceOpenInfo> serviceOpenInfoList2 = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
                    //单条回传
                    for (SoftServiceFeedbackParam.productInfo productInfo : productInfoList) {
                        //获取开通手机号
                        ServiceOpenInfo serviceOpenInfo = new ServiceOpenInfo();
                        serviceOpenInfo
                                .withId(productInfo.getOrderSerialNumber())
                                .withSoftServiceUseTime(new Date());
                        serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo);
                    }
                    //最后一条
                    if (serviceOpenInfoList2.size() == productInfoList.size()) {

                        Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                                .withId(param.getOrderId())
                                .withSoftServiceStatus(6); //使用中
                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                        //更新订单的更新时间，避免覆盖其他字段
                        Order2cInfo updateOrder2cInfo = new Order2cInfo();
                        updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                        updateOrder2cInfo.setUpdateTime(new Date());
                        order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                        inUse = true;
                        ServiceOpenInfo serviceOpenInfo = serviceOpenInfoMapper.selectByPrimaryKey(productInfoList.get(0).getOrderSerialNumber());
                        if (serviceOpenInfo.getSyncIotFailStatus() == 1) {
                            isSyncOpenFail = true;
                        }
                    }
                }
                if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(order2cInfo.getSpuOfferingClass())) {
                    if (inUse) {
                        //判断其他原子订单是否全部使用中
                        Boolean otherAtomUse = true;

                        List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(new Order2cAtomInfoExample().createCriteria()
                                .andOrderIdEqualTo(order2cInfo.getOrderId()).example());
                        if (order2cAtomInfoList.size() > 1) {
                            //判断另外原子是否处于使用中
                            for (Order2cAtomInfo order2cAtomInfoItem : order2cAtomInfoList) {
                                if (!order2cAtomInfoItem.getId().equals(param.getOrderId())) {
                                    if (order2cAtomInfoItem.getSoftServiceStatus() != null && order2cAtomInfoItem.getSoftServiceStatus() != 6) {
                                        otherAtomUse = false;
                                    }
                                    List<ServiceOpenInfo> serviceOpenInfoList2 = serviceOpenInfoMapper.selectByExample(new ServiceOpenInfoExample().createCriteria().andAtomOrderIdEqualTo(order2cAtomInfoItem.getId()).example());
                                    if (!serviceOpenInfoList2.isEmpty()) {
                                        if (serviceOpenInfoList2.get(0).getSyncIotFailStatus() == 1) {
                                            isSyncOpenFail = true;
                                        }
                                    }
                                }
                            }
                            if (otherAtomUse) {

                                if (isSyncOpenFail) {
                                    //构建请求体
                                    //同步开通中
                                    SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                    softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                    softResultToIotParam.setServiceResult("1");
                                    Boolean result = syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                    if (result) {
                                        //同步使用中
                                        SoftResultToIotParam softResultToIotParam1 = new SoftResultToIotParam();
                                        softResultToIotParam1.setOrderId(order2cInfo.getOrderId());
                                        softResultToIotParam1.setServiceResult("3");
                                        syncSoftResultToIot(softResultToIotParam1, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                    }
                                } else {
                                    //同步开通中
                                    SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                    softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                    softResultToIotParam.setServiceResult("3");
                                    syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                }


                            }
                        } else {
                            //判断是否有同步开通成功失败的订单，有先同步开通成功，在同步使用中
                            if (isSyncOpenFail) {
                                //构建请求体
                                SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                softResultToIotParam.setServiceResult("1");
                                Boolean result = syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                if (result) {
                                    //同步使用中
                                    SoftResultToIotParam softResultToIotParam1 = new SoftResultToIotParam();
                                    softResultToIotParam1.setOrderId(order2cInfo.getOrderId());
                                    softResultToIotParam1.setServiceResult("3");
                                    syncSoftResultToIot(softResultToIotParam1, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                }
                            } else {
                                //构建请求体
                                SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                softResultToIotParam.setServiceResult("3");
                                syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                            }


                        }

                    }
                }


            } catch (Exception e) {
                log.error("回传失败", e);
            }


        }

        answer.setStatus(SOFT_SERVICE_SUCESS);
        return answer;
    }

    @Override
    public BaseAnswer<Void> feedbackNew(SyncCommonRequest syncCommonRequest) {
        BaseAnswer<Void> answer = new BaseAnswer<>();
        String input = syncCommonRequest.getInput();
/*       String sign = syncCommonRequest.getSign();
        // sign验签
        SignUtils.checkSign(input, sign, osSecretKey);*/
        SoftServiceFeedbackNewParam param = JSON.parseObject(input, SoftServiceFeedbackNewParam.class);
        //更新开通状态

        log.info("软件服务反馈信息softwareService：{}", JSON.toJSONString(param));
        //获取原子订单信息

        Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(param.getOrderId());

        if (order2cAtomInfo == null) {
            log.info("未找到对应的原子订单信息");
            throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ORDER_NOT_FOUND, "未找到对应的原子订单信息");
        }

        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(order2cAtomInfo.getOrderId());
        if (order2cInfo == null) {
            throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ORDER_NOT_FOUND, "未找到对应的订单信息");
        }
        Integer specialAfterMarketHandle = order2cInfo.getSpecialAfterMarketHandle();
        String specialAfterStatus = order2cInfo.getSpecialAfterStatus();
        String effectiveRules = order2cInfo.getEffectiveRules();
        String spuOfferingClass = order2cInfo.getSpuOfferingClass();
        String specialAfterRefundsNumber = order2cInfo.getSpecialAfterRefundsNumber();
        ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample().createCriteria().andAtomOrderIdEqualTo(param.getOrderId()).example();
        List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
        if (serviceOpenInfoList == null || serviceOpenInfoList.size() == 0) {

            throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ID_NOT_FOUND, "未找到对应的软件服务");
        }

//        0-开通回复
//        1-退订回复  operateType
//        2-“使用中指令”回复
        if (param.getOpreateType() == 0) {
            List<Integer> openStatusList = new ArrayList<>();
            openStatusList.add(SoftServiceOpenStatusEnum.OPEN_FAIL.getType());
            openStatusList.add(SoftServiceOpenStatusEnum.OPENING.getType());
            //获取数据库未开通，或开通失败的软件服务
            if( !openStatusList.contains( serviceOpenInfoList.get(0).getSoftServiceOpenStatus())){
                throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ID_NOT_FOUND, "当前软件服务不处于开通失败或开通中状态");
            }

            Boolean isFail = false;
            Boolean isAllOpen=false;
            if (param.getResult() != 0) {
                isFail = true;
            } else {
                //更新原子状态为开通成功 判断该原子是否全部开通成功
                //对于特殊退款的服务 部分退款完成的重新开通剩余的 在定时开通回调判断（现在主要是千里眼的）

                if (specialAfterMarketHandle==1 && "6".equals(specialAfterStatus)
                        && SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuOfferingClass) && "1".equals(effectiveRules)){
                    log.info("开通数据sucessNum：{},specialAfterRefundsNumber:{},OpenSucessNum:{},OpenNum:{}",param.getSucessNum(),specialAfterRefundsNumber,serviceOpenInfoList.get(0).getOpenSucessNum(),serviceOpenInfoList.get(0).getOpenNum());
                    if (param.getSucessNum()+Integer.parseInt(specialAfterRefundsNumber)+serviceOpenInfoList.get(0).getOpenSucessNum()  == serviceOpenInfoList.get(0).getOpenNum()){
                        Order2cAtomInfo order2cAtomInfo2 = new Order2cAtomInfo()
                                .withId(param.getOrderId())
                                .withSoftServiceStatus(SoftServiceOpenStatusEnum.OPEN_SUCESS.getType());
                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo2);
                        isAllOpen=true;
                    }

                }else {
                    log.info("开通数据sucessNum：{},OpenSucessNum:{},OpenNum:{}",param.getSucessNum(),serviceOpenInfoList.get(0).getOpenSucessNum(),serviceOpenInfoList.get(0).getOpenNum());
                    if (param.getSucessNum() + serviceOpenInfoList.get(0).getOpenSucessNum() == serviceOpenInfoList.get(0).getOpenNum()) {
                        Order2cAtomInfo order2cAtomInfo2 = new Order2cAtomInfo()
                                .withId(param.getOrderId())
                                .withSoftServiceStatus(SoftServiceOpenStatusEnum.OPEN_SUCESS.getType());
                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo2);
                        isAllOpen=true;
                    }
                }
            }
            ServiceOpenInfo serviceOpenInfo = new ServiceOpenInfo();

            serviceOpenInfo.withId(serviceOpenInfoList.get(0).getId())
                    .withSoftServiceOpenStatus(isAllOpen? SoftServiceOpenStatusEnum.OPEN_SUCESS.getType(): SoftServiceOpenStatusEnum.OPEN_FAIL.getType())
                    .withSoftServiceOpenTime(new Date())
                    .withOpenFailReason(param.getFailReason() == null ? null : param.getFailReason())
                    .withOpenSucessNum(serviceOpenInfoList.get(0).getOpenSucessNum() + param.getSucessNum());
            if(param.getResult()==0){

                serviceOpenInfo.setSoftServiceOpenStatus(isAllOpen? SoftServiceOpenStatusEnum.OPEN_SUCESS.getType(): SoftServiceOpenStatusEnum.OPENING.getType());
            }else{
                serviceOpenInfo.setSoftServiceOpenStatus(SoftServiceOpenStatusEnum.OPEN_FAIL.getType());
            }
            serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo);
            //如果有开通失败，且同步回调接口没有失败，则短信通知
            if (isFail) {
                //休眠几秒，兼容同步返回失败且异步返回失败
                if (order2cAtomInfo.getSoftServiceStatus() != 1) {
                    //更新原子总体状态
                    log.info("原子订单信息:{}", JSON.toJSONString(order2cAtomInfo));
                    Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                            .withId(param.getOrderId())
                            .withSoftServiceStatus(SoftServiceAtomStatusEnum.OPEN_FAIL.getType());
                    order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                    //更新订单的更新时间，避免覆盖其他字段
                    Order2cInfo updateOrder2cInfo = new Order2cInfo();
                    updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                    updateOrder2cInfo.setUpdateTime(new Date());
                    order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                    //发送短信提示用户
                    sendMsg(serviceOpenInfoList.get(0), param.getOpreateType());
                }

            } else {
                  //目前不知道特殊退款的商城要怎么判断 同步  先和原来的分开写  特殊退款 定时生效 部分退款成功重新开通
                if (specialAfterMarketHandle==1 && "6".equals(specialAfterStatus)
                        && SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuOfferingClass) && "1".equals(effectiveRules)){
                    log.info("特殊退款部分退款开通 开通中状态serviceOpenInfoList：{}",serviceOpenInfoList.get(0));
                    log.info("开通数据sucessNum：{},specialAfterRefundsNumber:{},OpenSucessNum:{},OpenNum:{}",param.getSucessNum(),specialAfterRefundsNumber,serviceOpenInfoList.get(0).getOpenSucessNum(),serviceOpenInfoList.get(0).getOpenNum());
                    if (param.getSucessNum()+Integer.parseInt(specialAfterRefundsNumber)+serviceOpenInfoList.get(0).getOpenSucessNum() == serviceOpenInfoList.get(0).getOpenNum()){
                        //更新订单的更新时间，避免覆盖其他字段
                        Order2cInfo updateOrder2cInfo = new Order2cInfo();
                        updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                        updateOrder2cInfo.setUpdateTime(new Date());
                        order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                        //目前只有A13需要向商城反馈
                        if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(order2cInfo.getSpuOfferingClass())) {
                            //同步商城开通成功
                            //获取请求体参数
                            //构建请求体
                            SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                            softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                            softResultToIotParam.setServiceResult("1");
                            syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                        }
                    }
                }else {
                    //判断订单是否已经全部开通
                    log.info("开通数据sucessNum：{},OpenSucessNum:{},OpenNum:{}",param.getSucessNum(),serviceOpenInfoList.get(0).getOpenSucessNum(),serviceOpenInfoList.get(0).getOpenNum());
                    if (param.getSucessNum() + serviceOpenInfoList.get(0).getOpenSucessNum() == serviceOpenInfoList.get(0).getOpenNum()) {
                        //更新订单的更新时间，避免覆盖其他字段
                        Order2cInfo updateOrder2cInfo = new Order2cInfo();
                        updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                        updateOrder2cInfo.setUpdateTime(new Date());
                        order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                        //目前只有A13需要向商城反馈
                        if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(order2cInfo.getSpuOfferingClass())) {
                            //同步商城开通成功
                            //获取请求体参数
                            //构建请求体
                            SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                            softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                            softResultToIotParam.setServiceResult("1");
                            syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                        }
                    }
                }


            }
        } else if (param.getOpreateType() == 1) {
            Boolean isFail = false;

            if(!Objects.equals(serviceOpenInfoList.get(0).getSoftServiceRetailStatus(), SoftServiceRetailStatusEnum.RETAILING.getType())){
                throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ID_NOT_FOUND, "当前软件服务不处于退订中状态");
            }

            ServiceOpenInfo serviceOpenInfo = new ServiceOpenInfo();
            serviceOpenInfo
                    .withId(serviceOpenInfoList.get(0).getId())
                    .withSoftServiceRetailStatus(param.getResult())
                    .withSoftServiceRetailTime(new Date())
                    .withRetailSucessNum(serviceOpenInfoList.get(0).getRetailSucessNum() + param.getSucessNum())
                    .withRetailFailReason(param.getFailReason() == null ? null : param.getFailReason());
            if (param.getResult() == 0) {
                serviceOpenInfo.setSoftServiceOpenStatus(1);
                serviceOpenInfo.setOpenFailReason("用户退订");
            } else {
                isFail = true;
            }
            serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo);

            if (isFail) {
                //有退订失败的,发送短信提示用户
                Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                        .withId(param.getOrderId())
                        .withSoftServiceStatus(SoftServiceAtomStatusEnum.RETAIL_FAIL.getType()); //退订失败
                order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                //更新订单的更新时间，避免覆盖其他字段
                Order2cInfo updateOrder2cInfo = new Order2cInfo();
                updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                updateOrder2cInfo.setUpdateTime(new Date());
                order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                sendMsg(serviceOpenInfoList.get(0), param.getOpreateType());
            } else {
                //判断原子订单是否已经全部退订成功
                if (order2cInfo.getSpecialAfterRefundsNumber() != null) {

                    if (serviceOpenInfoList.get(0).getRetailSucessNum() + param.getSucessNum() == Long.parseLong(order2cInfo.getSpecialAfterRefundsNumber())) {
                        Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                                .withId(param.getOrderId())
                                .withSoftServiceStatus(SoftServiceAtomStatusEnum.RETAIL_SUCESS.getType()); //退订成功
                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                        //更新订单的更新时间，避免覆盖其他字段
                        Order2cInfo updateOrder2cInfo = new Order2cInfo();
                        updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                        updateOrder2cInfo.setUpdateTime(new Date());
                        order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                    }
                } else {
                    if (serviceOpenInfoList.get(0).getRetailSucessNum() + param.getSucessNum() == serviceOpenInfoList.get(0).getOpenNum()) {
                        Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                                .withId(param.getOrderId())
                                .withSoftServiceStatus(SoftServiceAtomStatusEnum.RETAIL_SUCESS.getType()); //退订成功
                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                        //更新订单的更新时间，避免覆盖其他字段
                        Order2cInfo updateOrder2cInfo = new Order2cInfo();
                        updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                        updateOrder2cInfo.setUpdateTime(new Date());
                        order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                    }
                }


            }
        } else if (param.getOpreateType() == 2) {
            try {
                Boolean inUse = false;
                Boolean isSyncOpenFail = false;
                Thread.sleep(2000);
                //获取数据库未开通，或开通失败的软件服务
                if (!Objects.equals(serviceOpenInfoList.get(0).getSoftServiceOpenStatus(), SoftServiceOpenStatusEnum.OPEN_SUCESS.getType())) {

                    throw new BusinessException(BaseErrorConstant.SOFT_SERVICE_ID_NOT_FOUND, "当前软件服务不处于开通成功状态");
                }

                    ServiceOpenInfo serviceOpenInfo = new ServiceOpenInfo();
                    serviceOpenInfo
                            .withId(serviceOpenInfoList.get(0).getId())
                            .withSoftServiceUseTime(new Date());
                    serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo);
                     //特殊退款完成只有部分开通成功 判断校验变一变
                    //默认就是全部回传
                //目前不知道特殊退款的商城要怎么判断 同步  先和原来的分开写  特殊退款 定时生效 部分退款成功重新开通
                if (specialAfterMarketHandle==1 && "6".equals(specialAfterStatus)
                        && SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuOfferingClass) && "1".equals(effectiveRules)){
                    log.info("特殊退款部分退款开通使用中serviceOpenInfoList：{}",serviceOpenInfoList.get(0));
                    log.info("开通数据sucessNum：{},specialAfterRefundsNumber:{},OpenSucessNum:{},OpenNum:{}",param.getSucessNum(),specialAfterRefundsNumber,serviceOpenInfoList.get(0).getOpenSucessNum(),serviceOpenInfoList.get(0).getOpenNum());
                    if (param.getSucessNum()+Integer.parseInt(specialAfterRefundsNumber)  == serviceOpenInfoList.get(0).getOpenNum()){
                        Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                                .withId(param.getOrderId())
                                .withSoftServiceStatus(SoftServiceAtomStatusEnum.USEING.getType()); //使用中
                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                        //更新订单的更新时间，避免覆盖其他字段
                        Order2cInfo updateOrder2cInfo = new Order2cInfo();
                        updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                        updateOrder2cInfo.setUpdateTime(new Date());
                        order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                        inUse = true;

                        if (Objects.equals(serviceOpenInfoList.get(0).getSyncIotFailStatus(), SoftServiceSyncIotStatusEnum.SYNC_OPEN_FAIL.getType())) {
                            isSyncOpenFail = true;
                        }
                    }
                }else {
                    log.info("开通数据sucessNum：{},OpenSucessNum:{},OpenNum:{}",param.getSucessNum(),serviceOpenInfoList.get(0).getOpenSucessNum(),serviceOpenInfoList.get(0).getOpenNum());
                    if(Objects.equals(param.getSucessNum(), serviceOpenInfoList.get(0).getOpenNum())){
                        Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                                .withId(param.getOrderId())
                                .withSoftServiceStatus(SoftServiceAtomStatusEnum.USEING.getType()); //使用中
                        order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                        //更新订单的更新时间，避免覆盖其他字段
                        Order2cInfo updateOrder2cInfo = new Order2cInfo();
                        updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                        updateOrder2cInfo.setUpdateTime(new Date());
                        order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                        inUse = true;

                        if (Objects.equals(serviceOpenInfoList.get(0).getSyncIotFailStatus(), SoftServiceSyncIotStatusEnum.SYNC_OPEN_FAIL.getType())) {
                            isSyncOpenFail = true;
                        }
                    }
                }

                if (SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(order2cInfo.getSpuOfferingClass())) {
                    if (inUse) {
                        //判断其他原子订单是否全部使用中
                        Boolean otherAtomUse = true;

                        List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(new Order2cAtomInfoExample().createCriteria()
                                .andOrderIdEqualTo(order2cInfo.getOrderId()).example());
                        if (order2cAtomInfoList.size() > 1) {
                            //判断另外原子是否处于使用中
                            for (Order2cAtomInfo order2cAtomInfoItem : order2cAtomInfoList) {
                                if (!order2cAtomInfoItem.getId().equals(param.getOrderId())) {
                                    if (order2cAtomInfoItem.getSoftServiceStatus() != null && !Objects.equals(order2cAtomInfoItem.getSoftServiceStatus(), SoftServiceAtomStatusEnum.USEING.getType())) {
                                        otherAtomUse = false;
                                    }
                                    List<ServiceOpenInfo> serviceOpenInfoList2 = serviceOpenInfoMapper.selectByExample(new ServiceOpenInfoExample().createCriteria().andAtomOrderIdEqualTo(order2cAtomInfoItem.getId()).example());
                                    if (!serviceOpenInfoList2.isEmpty()) {
                                        if (Objects.equals(serviceOpenInfoList2.get(0).getSyncIotFailStatus(), SoftServiceSyncIotStatusEnum.SYNC_OPEN_FAIL.getType())) {
                                            isSyncOpenFail = true;
                                        }
                                    }
                                }
                            }
                            if (otherAtomUse) {

                                if (isSyncOpenFail) {
                                    //构建请求体
                                    //同步开通中
                                    SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                    softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                    softResultToIotParam.setServiceResult("1");
                                    Boolean result = syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                    if (result) {
                                        //同步使用中
                                        SoftResultToIotParam softResultToIotParam1 = new SoftResultToIotParam();
                                        softResultToIotParam1.setOrderId(order2cInfo.getOrderId());
                                        softResultToIotParam1.setServiceResult("3");
                                        syncSoftResultToIot(softResultToIotParam1, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                    }
                                } else {
                                    //同步开通中
                                    SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                    softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                    softResultToIotParam.setServiceResult("3");
                                    syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                }


                            }
                        } else {
                            //判断是否有同步开通成功失败的订单，有先同步开通成功，在同步使用中
                            if (isSyncOpenFail) {
                                //构建请求体
                                SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                softResultToIotParam.setServiceResult("1");
                                Boolean result = syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                if (result) {
                                    //同步使用中
                                    SoftResultToIotParam softResultToIotParam1 = new SoftResultToIotParam();
                                    softResultToIotParam1.setOrderId(order2cInfo.getOrderId());
                                    softResultToIotParam1.setServiceResult("3");
                                    syncSoftResultToIot(softResultToIotParam1, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                                }
                            } else {
                                //构建请求体
                                SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
                                softResultToIotParam.setOrderId(order2cInfo.getOrderId());
                                softResultToIotParam.setServiceResult("3");
                                syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                            }


                        }

                    }
                }


            } catch (Exception e) {
                log.error("回传失败", e);
            }


        }

        answer.setStatus(SOFT_SERVICE_SUCESS);
        return answer;
    }

    public void sendMsg(ServiceOpenInfo serviceOpenInfoItem, Integer operateType) {
        //获取原子商品信息
        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample().createCriteria().andSpuCodeEqualTo(serviceOpenInfoItem.getSpuOfferingCode())
                .andSkuCodeEqualTo(serviceOpenInfoItem.getSkuOfferingCode())
                .andOfferingCodeEqualTo(serviceOpenInfoItem.getAtomOfferingCode())
                .example();
        List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
        if (atomOfferingInfoList == null || atomOfferingInfoList.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到原子商品信息");
        }
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
        //判断开通平台
        ChargeItemConfigExample chargeItemConfigExample = new ChargeItemConfigExample();
        ChargeItemConfigExample.Criteria criteria = chargeItemConfigExample.createCriteria();
        if (atomOfferingInfo.getChargeId() != null) {
            criteria.andChargeIdEqualTo(atomOfferingInfo.getChargeId());
        }
        if (atomOfferingInfo.getProductType() != null) {
            criteria.andProductTypeIdEqualTo(atomOfferingInfo.getProductType());
        }
        List<ChargeItemConfig> chargeItemConfigList = chargeItemConfigMapper.selectByExample(chargeItemConfigExample);
        if (chargeItemConfigList.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应账目项配置");

        }
        ChargeItemConfig chargeItemConfig = chargeItemConfigList.get(0);

        if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.Q.getPlatformCode())) {
            //千里眼业务平台
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_QLY_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "千里眼软件服务", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("千里眼软件服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("未配置千里眼软件服务通失败的数据通知指定人员");
            }

        }else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.GXMINI.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_GXMINI_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "广西打印小程序业务平台软件服务", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("广西打印小程序业务平台服开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("广西打印小程序业务平台软件服务开通失败的数据通知指定人员");
            }

        }else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.JSKY.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_JSKY_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "江苏康养软件服务", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("JSKY江苏康养软件服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("JSKY江苏康养软件服务开通失败的数据通知指定人员");
            }

        } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.X.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_XCWS_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "行车卫士短信预警软件服务", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("行车卫士短信预警软件服开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("未配置行车卫士短信预警软件服务开通失败的数据通知指定人员");
            }

        }else if(chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.XCWS.getPlatformCode())){
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                    .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_XCWSYW_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "行车卫士业务平台服务",
                        operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("行车卫士业务平台服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("行车卫士业务平台服务开通失败的数据通知指定人员");
            }
        } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.O.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_ONENET_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "OneNET公有云服务", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("OneNET公有云服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("未配置OneNET公有云服务开通失败的数据通知指定人员");
            }
        } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.H.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_HM_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "和目业务平台", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("和目业务平台服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("和目业务平台服务开通失败的数据通知指定人员");
            }
        } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.OC.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_ONECYBER_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "OneCyber5G 专网运营平台", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("OneCyber5G 专网运营平台服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("未配置OneCyber5G 专网运营平台服务开通失败的数据通知指定人员");
            }
        }/* else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.H.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_HM_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "和目业务平台", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("和目业务平台服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("和目业务平台服务开通失败的数据通知指定人员");
            }
        }*/else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.YT.getPlatformCode())) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_YT_SOFTSERVICE_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                BaseAnswer<Void> messageKxAnswer = sendTurnOnFailSMS(userRefundKxVOList, "云瞳业务平台", operateType, serviceOpenInfoItem.getOrderId(), atomOfferingInfo.getCooperatorId());
                log.info("云瞳业务平台服务开通失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
            } else {
                log.warn("云瞳业务平台服务开通失败的数据通知指定人员");
            }
        }
        else {
            log.info("软件服务开通失败短信通知未找到业务开通平台");
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到业务开通平台");

        }
    }

    /**
     * 开通失败的短信调用
     *
     * @param userRefundKxVOList
     * @param param1
     * @param param2
     * @return
     */
    private BaseAnswer<Void> sendTurnOnFailSMS(List<UserRefundKxVO> userRefundKxVOList,
                                               String param1, Integer param2, String param3, String cooperatorId) {
        List<String> kxPhoneList = userRefundKxVOList.stream().map(UserRefundKxVO::getPhone)
                .collect(Collectors.toList());

        Msg4Request requestKx = new Msg4Request();
        List<String> mobileKxList = new ArrayList<>();
        if (cooperatorId != null) {
            Data4User userInfo = userFeignClient.userInfo(cooperatorId).getData();
            Boolean isSend = userInfo.getIsSend();
            if (isSend != null && isSend && !kxPhoneList.contains(userInfo.getPhone())) {
                mobileKxList.add(userInfo.getPhone());
            }

            /*Data4User lordUser = null;
            if (!userInfo.getIsPrimary()) {
                BaseAnswer<Data4User> answer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
                lordUser = answer.getData();
            }
            if (lordUser != null && lordUser.getIsSend() && !kxPhoneList.contains(userInfo.getPhone())) {
                mobileKxList.add(lordUser.getPhone());
            }*/

            // 获取订单和从合作伙伴关系
            List<Data4User> data4UserList = orderCooperatorRelationService.listCooperatorUserInfo("",param3);
            List<String> cooperatorPhoneList = data4UserList.stream()
                    .map(Data4User::getPhone)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cooperatorPhoneList)){
                cooperatorPhoneList.stream().forEach(cooperatorPhone->{
                    if (!kxPhoneList.contains(cooperatorPhone)){
                        kxPhoneList.add(cooperatorPhone);
                    }
                });
            }
        }
        mobileKxList.addAll(kxPhoneList);

        mobileKxList = mobileKxList.stream().distinct().collect(Collectors.toList());
        Map<String, String> messageKx = new HashMap<>();
        messageKx.put("softService", param1);
        messageKx.put("operate", param2 == 0 ? "开通" : "退订");
        messageKx.put("orderId", param3);
        requestKx.setMobiles(mobileKxList);
        requestKx.setMessage(messageKx);
        requestKx.setTemplateId(smsSoftServiceTemplateId);
        BaseAnswer<Void> messageKxAnswer = smsFeignClient.asySendMessage(requestKx);
        return messageKxAnswer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> softServiceOpenFail(SoftServiceOpenParam softServiceOpenParam) {
        //获取请求体参数
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(softServiceOpenParam.getOrderId());
        if (order2cInfo == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单号不存在");
        }
        //获取开通失败原因
        ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample().createCriteria().andOrderIdEqualTo(softServiceOpenParam.getOrderId()).example();
        List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
        if (serviceOpenInfoList.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应的软件服务");

        }
        //随便取一条作为开通失败原因
        String failReaon = null;
        for (ServiceOpenInfo serviceOpenInfo : serviceOpenInfoList) {
            if (serviceOpenInfo.getSoftServiceOpenStatus() == 1 && failReaon == null) {
                failReaon = serviceOpenInfo.getOpenFailReason();
            }
            //将所有软件服务状态变为开通失败
//            if (serviceOpenInfo.getSoftServiceOpenStatus() == 0) {
//                ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
//                BeanUtils.copyProperties(serviceOpenInfo, serviceOpenInfo1);
//                serviceOpenInfo1.setOpenFailReason("系统请求失败");
//                serviceOpenInfo1.setSoftServiceOpenStatus(1);
//                serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo1);
//            }
        }
        //构建请求体
        SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
        softResultToIotParam.setOrderId(softServiceOpenParam.getOrderId());
        softResultToIotParam.setServiceResult("2");
        softResultToIotParam.setReason(failReaon == null ? "软件服务开通异常" : failReaon);
        syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());

        return new BaseAnswer<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> softServiceImport(MultipartFile file) {

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        boolean fileTypeBool = oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"));
        if (fileTypeBool) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }

        SoftServiceImportExcelListener excelListener = new SoftServiceImportExcelListener(serviceOpenInfoMapper);

        try {
            List<Object> list = EasyExcel.read(file.getInputStream(), SoftServiceImportDTO.class, excelListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            //成功数
            List<SoftServiceImportDTO> softServiceImportSucceedList = excelListener.getSoftServiceImportSucceedList();
            //失败数量
            List<SoftServiceImportFailDTO> softServiceImportFailList = excelListener.getSoftServiceImportFailList();
            if (CollectionUtils.isNotEmpty(softServiceImportFailList)) {
                String excelName = "批量导入订单开通信息失败";
//                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/soft_service_error.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();
                String stateCode = BaseErrorConstant.SOFT_SERVICE_IMPORT_FAIL.getStateCode();
                String message = BaseErrorConstant.SOFT_SERVICE_IMPORT_FAIL.getMessage();
                //构建填充excel参数
                Map<String, Object> map = new HashMap<String, Object>();
                EasyExcelUtils.exportExcel(response, "list", softServiceImportFailList, map, excelName, templateFileName,
                        0, "失败描述", stateCode, message);
            } else {
                List<String> collect = softServiceImportSucceedList.stream().map(SoftServiceImportDTO::getOrderNum).collect(Collectors.toList());
                for (String orderNum : collect) {
                    List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(new ServiceOpenInfoExample().createCriteria().andOrderIdEqualTo(orderNum).example());
                    if (serviceOpenInfoList != null && serviceOpenInfoList.size() > 0) {
                        softServiceMapperExt.updateSoftServiceOpenStatusByOrderId(orderNum);
                    }
//                    更新原子订单信息
                    List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(new Order2cAtomInfoExample().createCriteria().andOrderIdEqualTo(orderNum).example());
                    if (order2cAtomInfoList != null && order2cAtomInfoList.size() > 0) {
                        for (Order2cAtomInfo order2cAtomInfo : order2cAtomInfoList) {
                            order2cAtomInfo.setSoftServiceStatus(6);
                            order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo);

                        }
                    }
                    Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderNum);
                    //更新订单的更新时间，避免覆盖其他字段
                    Order2cInfo updateOrder2cInfo = new Order2cInfo();
                    updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
                    updateOrder2cInfo.setUpdateTime(new Date());
                    order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
                }

            }
        } catch (IOException e) {
            log.error("读取文件异常，文件名: {},异常描述：{}", oldName, e);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }
        return BaseAnswer.success(null);
    }

    /**
     * 开通、退订软件服务
     */
    @Override
    public BaseAnswer<Void> softServiceOpen(SoftServiceOpenParam softServiceOpenParam) {
        String orderId = softServiceOpenParam.getOrderId();
//        String atomOrderId = softServiceOpenParam.getAtomOrderId();

        //获取订单信息
        Order2cInfoExample example = new Order2cInfoExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<Order2cInfo> list = order2cInfoMapper.selectByExample(example);
        if (list.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应订单信息");
        }
        Order2cInfo order2cInfo = list.get(0);
        if (softServiceOpenParam.getOperateType() == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "操作类型不能为空");
        }

        //os前端开通/退订,单个原子
        /*if (softServiceOpenParam.getAtomOrderId() != null) {
            atomSoftServiceOpen(softServiceOpenParam, order2cInfo);
        } else {*/
            //订单级开通退订,自动退订
            List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(new Order2cAtomInfoExample().createCriteria()
                    .andOrderIdEqualTo(softServiceOpenParam.getOrderId()).example());
            for (Order2cAtomInfo order2cAtomInfo : order2cAtomInfoList) {
                SoftServiceOpenParam softServiceOpenParam1 = new SoftServiceOpenParam();
                softServiceOpenParam1.setAtomOrderId(order2cAtomInfo.getId());
                softServiceOpenParam1.setSpuOfferingCode(order2cAtomInfo.getSpuOfferingCode());
                softServiceOpenParam1.setSkuOfferingCode(order2cAtomInfo.getSkuOfferingCode());
                softServiceOpenParam1.setAtomOfferingCode(order2cAtomInfo.getAtomOfferingCode());
                softServiceOpenParam1.setOrderId(softServiceOpenParam.getOrderId());
                softServiceOpenParam1.setOperateType(softServiceOpenParam.getOperateType());
                if (softServiceOpenParam.getSpecialAfterRefundsNumber() != null) {
                    softServiceOpenParam1.setSpecialAfterRefundsNumber(softServiceOpenParam.getSpecialAfterRefundsNumber());
                }
                atomSoftServiceOpen(softServiceOpenParam1, order2cInfo);
            }
//    }
        return BaseAnswer.success(null);

    }

    public void atomSoftServiceOpen(SoftServiceOpenParam softServiceOpenParam, Order2cInfo order2cInfo) {
        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample().createCriteria().andSpuCodeEqualTo(softServiceOpenParam.getSpuOfferingCode())
                .andSkuCodeEqualTo(softServiceOpenParam.getSkuOfferingCode())
                .andOfferingCodeEqualTo(softServiceOpenParam.getAtomOfferingCode())
                .example();
        List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
        if (atomOfferingInfoList.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应原子信息");

        }
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
        //判断开通平台
        ChargeItemConfigExample chargeItemConfigExample = new ChargeItemConfigExample();
        ChargeItemConfigExample.Criteria criteria = chargeItemConfigExample.createCriteria();
        if (atomOfferingInfo.getChargeId() != null) {
            criteria.andChargeIdEqualTo(atomOfferingInfo.getChargeId());
        }
        if (atomOfferingInfo.getProductType() != null) {
            criteria.andProductTypeIdEqualTo(atomOfferingInfo.getProductType());
        }
        List<ChargeItemConfig> chargeItemConfigList = chargeItemConfigMapper.selectByExample(chargeItemConfigExample);
        if (chargeItemConfigList.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应账目项配置");

        }
        ChargeItemConfig chargeItemConfig = chargeItemConfigList.get(0);
        if (chargeItemConfig.getPlatformCode() == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该原子没有绑定业务平台");
        }
        ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample();
        ServiceOpenInfoExample.Criteria createCriteria = serviceOpenInfoExample.createCriteria();
        createCriteria.andAtomOrderIdEqualTo(softServiceOpenParam.getAtomOrderId());
        List<Integer> openStatusList = new ArrayList<>();
        if (softServiceOpenParam.getOperateType().equals("01")) {
            openStatusList.add(0);
            openStatusList.add(2);
            createCriteria.andSoftServiceOpenStatusNotIn(openStatusList);
        } else {
            //只有开通成功的才能退订
            createCriteria.andSoftServiceOpenStatusEqualTo(0);
        }
        List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
        if ((serviceOpenInfoList == null || serviceOpenInfoList.size() == 0) && softServiceOpenParam.getOperateType().equals("01")) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到订单对应的软件服务");

        }
        if (softServiceOpenParam.getSpecialAfterRefundsNumber() != null) {
            serviceOpenInfoList = serviceOpenInfoList.subList(0, Integer.parseInt(softServiceOpenParam.getSpecialAfterRefundsNumber()));
        }
        if (serviceOpenInfoList != null && serviceOpenInfoList.size() > 0) {
            //更新状态为开通中或退订中
            for (ServiceOpenInfo serviceOpenInfo : serviceOpenInfoList) {
                ServiceOpenInfo serviceOpenInfo1 = new ServiceOpenInfo();
                BeanUtils.copyProperties(serviceOpenInfo, serviceOpenInfo1);
                if (softServiceOpenParam.getOperateType().equals("01")) {
                    serviceOpenInfo1.setSoftServiceOpenStatus(2);
                } else if (softServiceOpenParam.getOperateType().equals("03")) {

                    serviceOpenInfo1.setSoftServiceRetailStatus(2);
                }
                serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfo1);
            }
            Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample().createCriteria().andIdEqualTo(softServiceOpenParam.getAtomOrderId()).example();
            Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
            if (softServiceOpenParam.getOperateType().equals("01")) {
                order2cAtomInfo.setSoftServiceStatus(2);
            } else if (softServiceOpenParam.getOperateType().equals("03")) {
                order2cAtomInfo.setSoftServiceStatus(5);
            }
//                    .withSoftServiceStatus(6); //开通中或退订中
            order2cAtomInfoMapper.updateByExampleSelective(order2cAtomInfo, order2cAtomInfoExample);
            //更新订单的更新时间，避免覆盖其他字段
            Order2cInfo updateOrder2cInfo = new Order2cInfo();
            updateOrder2cInfo.setOrderId(order2cInfo.getOrderId());
            updateOrder2cInfo.setUpdateTime(new Date());
            order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo);
            ServiceOpenInfo serviceOpenInfo = serviceOpenInfoList.get(0);
            try {
                //构造body参数
                SoftServiceParam softServiceParam = new SoftServiceParam();
                SoftServiceParam.OrderInfo orderInfo = new SoftServiceParam.OrderInfo();
                orderInfo.setOrderId(softServiceOpenParam.getAtomOrderId());
                //手机号加密处理
                final String encrypt = Sm4Util.encrypt(serviceOpenInfo.getSoftServicePhone(), sm4Key);
                orderInfo.setOrderPhone(encrypt);
                orderInfo.setProvinceCode(order2cInfo.getBeId());
                orderInfo.setCityCode(order2cInfo.getLocation());
                orderInfo.setOrgType(String.valueOf(Integer.parseInt(order2cInfo.getCustomerType())+1));
                orderInfo.setOrderingChannelSource(order2cInfo.getOrderingChannelSource());
                orderInfo.setSxtSoftOfferingCode(serviceOpenInfo.getExtSoftOfferingCode());
                orderInfo.setCountryCode(order2cInfo.getRegionId());
                orderInfo.setCustCode(IOTEncodeUtils.decryptSM4(order2cInfo.getCustCode(), iotSm4Key, iotSm4Iv));
                orderInfo.setOrderType(softServiceOpenParam.getOperateType());
                orderInfo.setEquipmentCode(serviceOpenInfo.getEquipmentCode() == null ? null
                        :  IOTEncodeUtils.decryptSM4(serviceOpenInfo.getEquipmentCode(), iotSm4Key, iotSm4Iv));
                orderInfo.setMallOrderId(order2cInfo.getOrderId());
                log.info("软件服务同步业务平台设备码号:{}", serviceOpenInfo.getEquipmentCode() == null ? null : serviceOpenInfo.getEquipmentCode());
                List<SoftServiceParam.productInfo> productList = serviceOpenInfoList.stream().map(a -> {
                    SoftServiceParam.productInfo vo = new SoftServiceParam.productInfo();
                    vo.setExtSoftOfferingCode(a.getExtSoftOfferingCode());
                    vo.setOrderSerialNumber(a.getId());
                    return vo;
                }).collect(Collectors.toList());
                //千里眼采用新开通方式
                List<String> pList=new ArrayList<>();
                pList.add(PlatformClassEnum.Q.getPlatformCode());
                pList.add(PlatformClassEnum.YT.getPlatformCode());
                pList.add(PlatformClassEnum.GXMINI.getPlatformCode());
                pList.add(PlatformClassEnum.XCWS.getPlatformCode());
                pList.add(PlatformClassEnum.JSKY.getPlatformCode());
                if(pList.contains(chargeItemConfig.getPlatformCode())){
                    orderInfo.setNum(serviceOpenInfo.getOpenNum()==null?null: serviceOpenInfo.getOpenNum());
                }else{
                    softServiceParam.setProductList(productList);
                }
                ObjectMapper objectMapper = new ObjectMapper();
                String jsonString = objectMapper.writeValueAsString(orderInfo);
                log.info("验签加密前字符串:" + jsonString);
                softServiceParam.setOrderInfo(jsonString);
                String sign = softServiceUtil.toCheckIotMallSign(jsonString, secretKey);
                Map<String, String> signParam = new HashMap<>();
                signParam.put("sign", sign);
                SoftServiceResponse softServiceResponse;
                log.info("软件服务同步业务平台签名:{}", sign);
                log.info("软件服务同步业务平台参数:{}", softServiceParam);
                if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.Q.getPlatformCode())) {
                    //千里眼业务平台开通
                    log.info("千里眼平台开通");
                    softServiceResponse = qlySoftOpenClient.softServiceOpen(signParam, softServiceParam);

                } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.X.getPlatformCode())) {
                    log.info("行车卫士平台开通");
                    softServiceResponse = xingCheWeishiSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.O.getPlatformCode())) {
                    log.info("onenet平台开通");
                    softServiceResponse = onenetSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.OC.getPlatformCode())) {
                    log.info("oneCyber平台开通");
                    softServiceResponse = oyeCyberSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.H.getPlatformCode())) {
                    log.info("HEMU平台开通");
                    softServiceResponse = hemuSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                }else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.YT.getPlatformCode())) {
                    log.info("云瞳平台开通");
                    softServiceResponse = ytSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                }else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.GXMINI.getPlatformCode())) {
                    log.info("GXMINI手动广西打印小程序业务平台开通");
                    softServiceResponse = gxMiNiSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                }else if(chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.XCWS.getPlatformCode())){
                    log.info("XCWSYW行车卫士业务平台");
                    softServiceResponse = xcwsSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                } else if (chargeItemConfig.getPlatformCode().equals(PlatformClassEnum.JSKY.getPlatformCode())) {
                    log.info("JSKY江苏康养软件服务业务平台开通");
                    softServiceResponse = jsKangYangSoftOpenClient.softServiceOpen(signParam, softServiceParam);
                } else {
                    log.info("软件服务同步业务平台失败", "未找到业务开通平台");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到业务开通平台");
                }
                if (!softServiceResponse.getResultCode().equals("0")) {
                    log.info("软件服务软件服务开通失败", softServiceResponse.getResultDesc());
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "软件服务开通失败" + softServiceResponse.getResultDesc());

                } else {
                    log.info("软件服务请求业务平台操作成功，请求业务结果返回为:{}", softServiceResponse);
                }

            } catch (Exception e) {
                //更新状态为开通或退订失败
                ServiceOpenInfoExample serviceOpenInfoExample1 = new ServiceOpenInfoExample();
                ServiceOpenInfoExample.Criteria createCriteria1 = serviceOpenInfoExample1.createCriteria();
                createCriteria1.andOrderIdEqualTo(softServiceOpenParam.getOrderId());
                List<ServiceOpenInfo> serviceOpenInfoList1 = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample1);
                if (serviceOpenInfoList1 != null && serviceOpenInfoList1.size() > 0) {

                    Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
                            .withId(softServiceOpenParam.getAtomOrderId());

                    if (softServiceOpenParam.getOperateType().equals("01")) {
                        order2cAtomInfo1.setSoftServiceStatus(1);
                    } else if (softServiceOpenParam.getOperateType().equals("03")) {
                        order2cAtomInfo1.setSoftServiceStatus(4);
                    }

                    order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
                    //更新订单的更新时间，避免覆盖其他字段
                    Order2cInfo updateOrder2cInfo1 = new Order2cInfo();
                    updateOrder2cInfo1.setOrderId(order2cInfo.getOrderId());
                    updateOrder2cInfo1.setUpdateTime(new Date());
                    order2cInfoMapper.updateByPrimaryKeySelective(updateOrder2cInfo1);
                }
                for (ServiceOpenInfo serviceOpenInfoItem : serviceOpenInfoList) {
                    serviceOpenInfoItem.setSoftServiceRetailTime(new Date());
                    if (softServiceOpenParam.getOperateType().equals("01")) {
                        serviceOpenInfoItem.setSoftServiceOpenStatus(1);
                        sendMsg(serviceOpenInfoList.get(0), 0);
                    } else if (softServiceOpenParam.getOperateType().equals("03")) {
                        serviceOpenInfoItem.setSoftServiceRetailStatus(1);
                        sendMsg(serviceOpenInfoList.get(0), 1);
                    }

                    serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfoItem);
                }

                log.error("软件服务同步业务平台失败", e);
                throw new BusinessException(BaseErrorConstant.API_RETURN_ERROR, "软件服务同步业务平台失败" + e.getMessage());

            }
        } else {
            //自动退款,没有开通的软件服务,更新软件服务状态和原子订单状态
//            ServiceOpenInfoExample serviceOpenInfoExample1 = new ServiceOpenInfoExample();
//            ServiceOpenInfoExample.Criteria createCriteria1 = serviceOpenInfoExample1.createCriteria();
//            createCriteria1.andOrderIdEqualTo(softServiceOpenParam.getOrderId());
//            List<ServiceOpenInfo> serviceOpenInfoList1 = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample1);
//            if (serviceOpenInfoList1 != null && serviceOpenInfoList1.size() > 0) {
//                Order2cAtomInfo order2cAtomInfo1 = new Order2cAtomInfo()
//                        .withId(softServiceOpenParam.getAtomOrderId())
//                        .withSoftServiceStatus(3);
//                order2cAtomInfoMapper.updateByPrimaryKeySelective(order2cAtomInfo1);
//            }
//            for (ServiceOpenInfo serviceOpenInfoItem : serviceOpenInfoList) {
//                serviceOpenInfoItem.setSoftServiceRetailTime(new Date());
//                serviceOpenInfoItem.setSoftServiceRetailStatus(0);
//                serviceOpenInfoMapper.updateByPrimaryKeySelective(serviceOpenInfoItem);
//            }

        }

    }

    public BaseAnswer<Void> softServiceOpenTest(String platform, String operateType, String phone, String orderId, String code) {
        //构造body参数
        SoftServiceParam softServiceParam = new SoftServiceParam();
        SoftServiceParam.OrderInfo orderInfo = new SoftServiceParam.OrderInfo();
        orderInfo.setOrderId(orderId);

        try {
            //手机号加密处理
            final String encrypt = Sm4Util.encrypt(phone, sm4Key);
            orderInfo.setOrderPhone(encrypt);
            log.info("手机号解密:" + Sm4Util.decrypt(encrypt, sm4Key));
            orderInfo.setProvinceCode("371");
            orderInfo.setCityCode("3770");
            orderInfo.setCountryCode("10000649");
            orderInfo.setOrderType(operateType);
            softServiceParam.setOrderInfo(JSON.toJSONString(orderInfo));
            List<SoftServiceParam.productInfo> productList = new ArrayList<>();
            SoftServiceParam.productInfo vo = new SoftServiceParam.productInfo();
            vo.setExtSoftOfferingCode(code);
            vo.setOrderSerialNumber("11223");
            productList.add(vo);
            SoftServiceParam.productInfo vo1 = new SoftServiceParam.productInfo();
            vo1.setExtSoftOfferingCode(code);
            vo1.setOrderSerialNumber("112238");
            productList.add(vo);
            softServiceParam.setProductList(productList);
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(orderInfo);
            log.info("验签加密前字符串:" + jsonString);
            String sign = softServiceUtil.toCheckIotMallSign(jsonString, secretKey);
            //对手机号进行加密处理
            Map<String, String> signParam = new HashMap<>();
            signParam.put("sign", sign);
            SoftServiceResponse softServiceResponse;
            log.info("软件服务同步业务平台参数:{}", softServiceParam);
            if (platform.equals(PlatformClassEnum.Q.getPlatformCode())) {
                //千里眼业务平台开通
                softServiceResponse = qlySoftOpenClient.softServiceOpen(signParam, softServiceParam);

            } else if (platform.equals(PlatformClassEnum.X.getPlatformCode())) {
                softServiceResponse = xingCheWeishiSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else if (platform.equals(PlatformClassEnum.O.getPlatformCode())) {
                log.info("调用onenet接口");
                softServiceResponse = onenetSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else if (platform.equals(PlatformClassEnum.H.getPlatformCode())) {
                log.info("HEMU平台开通");
                softServiceResponse = hemuSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else if (platform.equals(PlatformClassEnum.OC.getPlatformCode())) {
                log.info("调用oneCyber接口");
                softServiceResponse = oyeCyberSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else if (platform.equals(PlatformClassEnum.H.getPlatformCode())) {
                log.info("HEMU平台开通");
                softServiceResponse = hemuSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            }else if (platform.equals(PlatformClassEnum.YT.getPlatformCode())) {
                log.info("云瞳平台开通");
                softServiceResponse = ytSoftOpenClient.softServiceOpen(signParam, softServiceParam);
            } else {
                log.info("软件服务同步业务平台失败", "未找到业务开通平台");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到业务开通平台");

            }
            if (!softServiceResponse.getResultCode().equals("0")) {
                log.info("软件服务同步业务平台失败", softServiceResponse.getResultDesc());
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "软件服务开通失败" + softServiceResponse.getResultDesc());

            }
            log.info("软件服务请求业务平台操作{}成功，请求业务结果返回为:{}", operateType, softServiceResponse);


            return BaseAnswer.success(null);
        } catch (Exception e) {
            log.error("软件服务同步业务平台失败", e);
            throw new BusinessException(BaseErrorConstant.API_RETURN_ERROR, "软件服务同步业务平台失败" + e.getMessage());

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> softServiceSyncIot(String orderId) {
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
        if (order2cInfo == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到该订单");
        }
        ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample();
        ServiceOpenInfoExample.Criteria createCriteria = serviceOpenInfoExample.createCriteria();
        createCriteria.andOrderIdEqualTo(orderId);
        List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
        if (serviceOpenInfoList.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到该订单对应的软件服务");
        }
        ServiceOpenInfo serviceOpenInfo = serviceOpenInfoList.get(0);
        log.info("同步结果状态为：" + serviceOpenInfo.getSyncIotFailStatus());
        if (serviceOpenInfo.getSyncIotFailStatus() != null && serviceOpenInfo.getSyncIotFailStatus() != 0) {
            SoftResultToIotParam softResultToIotParam = new SoftResultToIotParam();
            softResultToIotParam.setOrderId(orderId);
            if (serviceOpenInfo.getSyncIotFailStatus() == 1) {
                softResultToIotParam.setServiceResult("1");
            } else if (serviceOpenInfo.getSyncIotFailStatus() == 2) {
                softResultToIotParam.setServiceResult("3");
            } else {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "同步商城状态正常，无法重复同步");
            }
            if (softResultToIotParam.getServiceResult().equals("1") && serviceOpenInfo.getSoftServiceUseTime() != null) {
                Boolean result = syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());
                if (result) {
                    softResultToIotParam.setServiceResult("3");
                    executor.execute(() -> syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId()));

                }
            }
            syncSoftResultToIot(softResultToIotParam, order2cInfo.getBeId(), softServiceResultUrl, order2cInfo.getRegionId());

        }


        return BaseAnswer.success(null);
    }

    /**
     * 同步开通结果，以及使用中指令到商城
     */
    public Boolean syncSoftResultToIot(SoftResultToIotParam o, String beId, String url, String regionId) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");
        String iotRequest = IOTRequestUtils.getIotRequest(JSON.toJSONString(o), iotSecretKey, beId, regionId);
        log.info("软件服务请求IOT商城内容为:" + iotRequest);
        HttpEntity<String> requestEntity = new HttpEntity<>(iotRequest, headers);
        ResponseEntity<IOTAnswer> response;
        Boolean isSuccess = false;
        try {
            HttpComponentsClientHttpRequestFactory factory = RestTemplateConfig.generateHttpRequestFactory();
            factory.setConnectTimeout(20000);
            factory.setConnectionRequestTimeout(20000);
            factory.setReadTimeout(20000);
            RestTemplate restTemplateHttps = new RestTemplate(factory);
            response = restTemplateHttps.postForEntity(url, requestEntity, IOTAnswer.class);
        } catch (Exception e) {
            log.error("{}软件服务请求IOT异常捕获:{}", iotRequest, e);
            Integer iotStatus = 0;
            if (o.getServiceResult().equals("1")) {
                iotStatus = 1;
            } else if (o.getServiceResult().equals("3")) {
                iotStatus = 2;
            }
            softServiceMapperExt.updateSoftServiceIotStatus(o.getOrderId(), iotStatus, e.getMessage());
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED);
        }
        IOTAnswer iotAnswer = response.getBody();
        if (iotAnswer == null || !"0".equals(iotAnswer.getResultCode())) {
            //没返回信息则为提示同步失败，返回错误码则提示返回错误描述
            log.error("{}软件服务同步到IOT商城失败，请求地址为:{}，返回结果为:{}", iotRequest, url, iotAnswer);
            Integer iotStatus = 0;
            if (o.getServiceResult().equals("1")) {
                iotStatus = 1;
            } else if (o.getServiceResult().equals("3")) {
                iotStatus = 2;
            }
            log.info("重新同步iot商城订单id:{},状态为:{},原因:{}", o.getOrderId(), iotStatus, iotAnswer.getResultDesc());
            softServiceMapperExt.updateSoftServiceIotStatus(o.getOrderId(), iotStatus, iotAnswer.getResultDesc());
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED, iotAnswer == null ? "软件服务同步到IOT商城失败，请重试" : iotAnswer.getResultDesc());
        } else {
            //如果没有同步失败的订单，则更新为同步成功
            ServiceOpenInfoExample serviceOpenInfoExample = new ServiceOpenInfoExample();
            ServiceOpenInfoExample.Criteria createCriteria = serviceOpenInfoExample.createCriteria();
            createCriteria.andOrderIdEqualTo(o.getOrderId());
            List<ServiceOpenInfo> serviceOpenInfoList = serviceOpenInfoMapper.selectByExample(serviceOpenInfoExample);
            ServiceOpenInfo serviceOpenInfo = serviceOpenInfoList.get(0);
            if (serviceOpenInfo.getSyncIotFailStatus() != 0) {
                log.info("重新同步iot商城订单id:{},状态为:{},原因:{}", o.getOrderId(), 0, iotAnswer.getResultDesc());
                softServiceMapperExt.updateSoftServiceIotStatus(o.getOrderId(), 0, null);
            }
            isSuccess = true;

        }
        log.info("{}软件服务请求IOT商城结果返回为:{}", iotRequest, iotAnswer);
        return isSuccess;
    }


}
