package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.DkcardxInventoryAtomInfoMapper;
import com.chinamobile.iot.sc.dao.ext.DkcardxInventoryAtomInfoMapperExt;
import com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfo;
import com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfoExample;
import com.chinamobile.iot.sc.pojo.dto.AtomDetailInventoryDTO;
import com.chinamobile.iot.sc.service.DkcardxInventoryAtomInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/23
 * @description 卡+X终端库存原子详情service实现类
 */
@Service
public class DkcardxInventoryAtomInfoServiceImpl implements DkcardxInventoryAtomInfoService {

    @Resource
    private DkcardxInventoryAtomInfoMapper dkcardxInventoryAtomInfoMapper;

    @Resource
    private DkcardxInventoryAtomInfoMapperExt dkcardxInventoryAtomInfoMapperExt;

    @Override
    public void deleteInventoryAtomInfoByExample(DkcardxInventoryAtomInfoExample inventoryAtomInfoExample) {
        dkcardxInventoryAtomInfoMapper.deleteByExample(inventoryAtomInfoExample);
    }

    @Override
    public List<DkcardxInventoryAtomInfo> getInventoryAtomInfoByExample(DkcardxInventoryAtomInfoExample inventoryAtomInfoExample) {
        return dkcardxInventoryAtomInfoMapper.selectByExample(inventoryAtomInfoExample);
    }

    @Override
    public void batchAddInventoryAtomInfo(List<DkcardxInventoryAtomInfo> inventoryAtomInfoList) {
        dkcardxInventoryAtomInfoMapper.batchInsert(inventoryAtomInfoList);
    }

    @Override
    public List<AtomDetailInventoryDTO> listAtomDetailInventory() {
        return dkcardxInventoryAtomInfoMapperExt.listAtomDetailInventory();
    }
}
