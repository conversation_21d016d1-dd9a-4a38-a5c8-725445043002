package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SoftServiceParam implements Serializable {
//    /**商城订单编号*/
//    private String orderId;
//
//    /**订购手机号*/
//    private String orderPhone;
//
//    /**客户注册商城省份编号*/
//    private String provinceCode;
//    /**手客户注册商城地市编号*/
//    private String cityCode;
//
//    /**客户注册商城区县编号*/
//    private String countryCode;
//
//    /**订购类型  01：订购，02：退订*/
//    private String orderType;
    /**
     * 订单信息
     */
    private String orderInfo;
    /**
     * 产品信息列表
     */
    private List<SoftServiceParam.productInfo> productList;


    @Data
    public static class OrderInfo implements Serializable {
        /**
         * 商城原子订单编号
         */
        private String orderId;
        /**
         * 商城订单编号
         */
        private String mallOrderId;
        /**
         * 1:个人客户
         * 2:集团客户
         */
        private String orgType;

        /**
         * 订购手机号
         */
        private String orderPhone;

        /**
         * 设备码号
         */
        private String equipmentCode;
        /**
         * 客户编码
         */
        private String custCode;

        /**
         * 客户注册商城省份编号
         */
        private String provinceCode;
        /**
         * 手客户注册商城地市编号
         */
        private String cityCode;

        /**
         * 客户注册商城区县编号
         */
        private String countryCode;

        /**
         * 订购类型  01：订购，02：退订
         */
        private String orderType;
        /**
         * 订购数量
         */
        private Long num;
        /**
         * 软件服务商品编码
         */
        private String sxtSoftOfferingCode;
        /**
         * 订购渠道来源
         */
        private String orderingChannelSource;
    }

    @Data
    public static class productInfo implements Serializable {
        /**
         * 商城os系统订购软件服务商品流水号
         */
        private String orderSerialNumber;

        /**
         * 软件平台商品编码
         */
        private String extSoftOfferingCode;
    }

}
