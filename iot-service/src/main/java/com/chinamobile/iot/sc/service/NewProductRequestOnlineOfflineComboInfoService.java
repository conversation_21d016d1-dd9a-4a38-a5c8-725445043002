package com.chinamobile.iot.sc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfo;
import com.chinamobile.iot.sc.pojo.param.NewProductOnlineOfflineParam;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架套餐信息service接口类
 */
public interface NewProductRequestOnlineOfflineComboInfoService {

    /**
     * 新增上架请求套餐信息
     *
     * @param comboInfo
     */
    void saveComboInfo(NewProductRequestOnlineOfflineComboInfo comboInfo);

    /**
     * 更新上线套餐信息
     * @param comboInfo
     */
    void updateComboInfoByRequestIdAndComboInfoId(NewProductRequestOnlineOfflineComboInfo comboInfo);

    /**
     * 更新申请上下架请求是否通过
     * @param requestPass
     * @param id
     */
    void updateRequestPassById(int requestPass,String id);

    /**
     * 更新下架原因和下架用户
     * @param id
     * @param offlineReason
     * @param requestOfflineUserId
     */
    void updateOfflineReasonAndOfflineUser(String id,
                                           String offlineReason,
                                           String requestOfflineUserId);

    /**
     * 根据主键获取套餐信息
     * @param id
     * @return
     */
    NewProductRequestOnlineOfflineComboInfo getComboInfoById(String id);

    /**
     * 分页查询上下架状态的列表(超管)
     * @param page
     * @param onlineOfflineParam
     * @return
     */
    List<NewProductOnlineOfflineVO> listNewProductOnlineOfflineToSuperAdmin(@Param("page") Page page, @Param(value = "onlineOfflineParam") NewProductOnlineOfflineParam onlineOfflineParam);

    /**
     * 分页查询上下架状态的列表(审核人员)
     * @param page
     * @param onlineOfflineParam
     * @return
     */
    List<NewProductOnlineOfflineVO> listNewProductOnlineOfflineToJudgeUser(@Param("page") Page page, @Param(value = "onlineOfflineParam") NewProductOnlineOfflineParam onlineOfflineParam);
}
