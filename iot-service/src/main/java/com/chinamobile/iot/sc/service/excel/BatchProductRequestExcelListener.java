package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.DataUserVO;
import com.chinamobile.iot.sc.mode.QueryPartnerListParam;
import com.chinamobile.iot.sc.pojo.param.NewProductRequestManageAddParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.PRODUCT_APPLY_MANAGE_VERIFY_FIRST;
/**
 * <AUTHOR> xiemaohua
 * @date : 2022/9/16 11:23
 * @description: 批量导入产品引入表格监听类
 **/
@Slf4j
public class BatchProductRequestExcelListener extends AnalysisEventListener<BatchProductRequestExcel>  {



    private List<NewProductRequestManageAddParam> succeedList =new ArrayList<>();

    private List<BatchProductRequestFailed> failedList = new ArrayList<>();

    private ProvinceCityConfig provinceCityConfig;

    public void setProvinceCityConfig(ProvinceCityConfig provinceCityConfig) {
        this.provinceCityConfig = provinceCityConfig;
    }

    private UserFeignClient userFeignClient;

    public void setUserFeignClient(UserFeignClient userFeignClient) {
        this.userFeignClient = userFeignClient;
    }
    @Override
    public void invoke(BatchProductRequestExcel batchProductRequestExcel, AnalysisContext analysisContext) {

        int currentRow = analysisContext.readRowHolder().getRowIndex() + 1;
        String failedReason="";
        log.info("解析到一条产品引入数据:{},当前行:{}", batchProductRequestExcel, currentRow);
        HashMap<String, String> provinceNameCodeMap = provinceCityConfig.getProvinceNameCodeMap();
        HashMap<String, String> cityNameCodeMap = provinceCityConfig.getCityNameCodeMap();
        NewProductRequestManageAddParam addParam = new NewProductRequestManageAddParam();
        BatchProductRequestFailed batchProductRequestFailed = new BatchProductRequestFailed();
        BeanUtils.copyProperties(batchProductRequestExcel,addParam);
        String productManagerPhone = batchProductRequestExcel.getProductManagerPhone();
        String productManagerEmail = batchProductRequestExcel.getProductManagerEmail();
        String supplyPriceStr = batchProductRequestExcel.getSupplyPriceStr();
        String marketPriceStr = batchProductRequestExcel.getMarketPriceStr();
        if (!RegexUtil.regexPhone(productManagerPhone)) {
            failedReason =StatusConstant.PHONE_ERROR.getMessage().concat("错误行"+currentRow);
            //throw new BusinessException(StatusConstant.PHONE_ERROR.getStateCode(),StatusConstant.PHONE_ERROR.getMessage().concat("错误行:"+currentRow));
        }
        if (!RegexUtil.regexEmail(productManagerEmail)) {
            if (StringUtils.isNotEmpty(failedReason)){
                failedReason =failedReason.concat(",").concat(StatusConstant.EMAIL_FORMAT_ERROR.getMessage()).concat("错误行"+currentRow);
            }else {
                failedReason =StatusConstant.EMAIL_FORMAT_ERROR.getMessage().concat("错误行"+currentRow);
            }
           // throw new BusinessException(StatusConstant.EMAIL_FORMAT_ERROR.getStateCode(),StatusConstant.EMAIL_FORMAT_ERROR.getMessage().concat("错误行："+currentRow));
        }
        //校验商品规格供货价（元）小于市场价格（元
        BigDecimal supplyPrice = new BigDecimal(supplyPriceStr).setScale(2,BigDecimal.ROUND_HALF_UP);

        BigDecimal marketPrice = new BigDecimal(marketPriceStr).setScale(2,BigDecimal.ROUND_HALF_UP);
        if (supplyPrice.compareTo(marketPrice)>0){
            if (StringUtils.isNotEmpty(failedReason)){
                failedReason =failedReason.concat(",").concat(StatusConstant.SUPPLY_PRICE_NOT_THAN_MARKET_PRICE.getMessage()).concat("错误行"+currentRow);
            }else {
                failedReason =StatusConstant.SUPPLY_PRICE_NOT_THAN_MARKET_PRICE.getMessage().concat("错误行"+currentRow);
            }
        }
        String productSaleArea = addParam.getProductSaleArea();
        try {
            if ("全国".equals(productSaleArea)){
                addParam.setProductSaleAreaCode(provinceNameCodeMap.get(productSaleArea));
            }else if (productSaleArea.contains("全国") && !"全国".equals(productSaleArea)){
                throw new BusinessException(StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getStateCode(),StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getMessage().concat("错误行："+currentRow));
            } else {
                //String regex = ",|，";
                String regex =",";
                String[] areas = productSaleArea.split(regex);
                List<String> areaCode = new ArrayList<>();
                for (String area : areas) {
                    if (area.contains("-")){
                        String provinceName = area.substring(0, area.indexOf("-"));
                        String cityName = area.substring(provinceName.length()+1);
                        //判断省份是否带省字
                        String province = provinceName.substring(provinceName.length() - 1);
                        if ("省".equals(province) || "市".equals(province)){
                             provinceName = provinceName.substring(0, provinceName.length() - 1);
                        }
                        String provinceCode = provinceNameCodeMap.get(provinceName);
                        String cityCode = cityNameCodeMap.get(cityName);
                        if (StringUtils.isEmpty(provinceCode) || StringUtils.isEmpty(cityCode)){
                            throw new BusinessException(StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getStateCode(),StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getMessage().concat("错误行："+currentRow));
                        }
                        String concat = provinceCode.concat("-").concat(cityCode);
                        areaCode.add(concat);
                    }else {
                    //不包含说明只有省份
                        String provinceCode = provinceNameCodeMap.get(area);
                        if (StringUtils.isEmpty(provinceCode)){
                            throw new BusinessException(StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getStateCode(),StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getMessage().concat("错误行："+currentRow));
                        }
                        areaCode.add(provinceCode);
                    }
                }
                String productAreaCode = String.join(",", areaCode);
                addParam.setProductSaleAreaCode(productAreaCode);

            }
            // 产品引入初审员相关
            BaseAnswer<List<DataUserVO>> users = userFeignClient.getListUserByAuthCodeNoAdmin(PRODUCT_APPLY_MANAGE_VERIFY_FIRST);
            if (users == null || !users.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || users.getData() == null) {
                throw new BusinessException(BaseErrorConstant.PRODUCT_IMPORT_INFO_ERROR, "查询产品引入初审员信息错误".concat("错误行："+currentRow));
            }
            List<DataUserVO> data = users.getData().stream().filter(item->item.getName().equals(batchProductRequestExcel.getOperatorImportUser())).collect(Collectors.toList());
            if(data.size() == 0){
                throw new BusinessException(StatusConstant.PRODUCT_IMPORT_INFO_ERROR.getStateCode(),"未查询到此产品引入初审员信息".concat("错误行："+currentRow));
            }else if (data.size() > 1){
                throw new BusinessException(StatusConstant.PRODUCT_IMPORT_SOLE.getStateCode(),StatusConstant.PRODUCT_IMPORT_SOLE.getMessage().concat("错误行："+currentRow));
            }
            addParam.setOperatorImportUserId(data.get(0).getUserId());

            // 查询合作伙伴主账号
            QueryPartnerListParam queryPartnerListParam = new QueryPartnerListParam();
            queryPartnerListParam.setPartnerName(batchProductRequestExcel.getCooperatorName());
            queryPartnerListParam.setIsPrimary(true);
            queryPartnerListParam.setIsCancel(false);
            BaseAnswer<List<Data4User>> userPartnerList = userFeignClient.getPartnerList(queryPartnerListParam);
            if(ObjectUtils.isEmpty(userPartnerList) || ObjectUtils.isEmpty(userPartnerList.getData())){
                throw new BusinessException(BaseErrorConstant.COOPERATOR_NOT_EXIST,"合作伙伴用户不存在".concat("错误行："+currentRow));
            }
            addParam.setCooperatorId(userPartnerList.getData().get(0).getUserId());
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(failedReason)){
                failedReason =failedReason.concat(",").concat(e.getMessage());
            }else {
                failedReason =e.getMessage();
            }
            //throw new BusinessException(StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getStateCode(),StatusConstant.PRODUCT_IMPORT_AREA_ERROR.getMessage().concat("错误行："+currentRow));
        }

        addParam.setSupplyPrice(new BigDecimal(supplyPriceStr));
        addParam.setMarketPrice(new BigDecimal(marketPriceStr));
      if (StringUtils.isNotEmpty(failedReason)){
          batchProductRequestFailed.setSpuOfferingName(addParam.getSpuOfferingName());
          batchProductRequestFailed.setSkuOfferingName(addParam.getSkuOfferingName());
          batchProductRequestFailed.setFailedReason(failedReason);
          failedList.add(batchProductRequestFailed);
      }else {
          succeedList.add(addParam);
      }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("产品引入所有数据解析完成！");
    }

    public List<NewProductRequestManageAddParam> getSucceedListData(){
        return succeedList;
    }

    public List<BatchProductRequestFailed> getFailedListData(){
        return failedList;
    }


}
