package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.EditFlowParam;
import com.chinamobile.iot.sc.pojo.param.ProductFlowListParam;
import com.chinamobile.iot.sc.pojo.vo.FlowRoleListVO;
import com.chinamobile.iot.sc.pojo.vo.FlowTypeListVO;
import com.chinamobile.iot.sc.pojo.vo.LimitListVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowDetailVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowListVO;

import java.util.List;

/**
 * created by liuxiang on 2024/3/8 10:56
 */
public interface ProductFlowService {

    BaseAnswer<PageData<ProductFlowListVO>> getFlowList(ProductFlowListParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<ProductFlowDetailVO> getFlowDetail(String flowId, LoginIfo4Redis loginIfo4Redis,Boolean addLog);

    BaseAnswer init();

    BaseAnswer<List<FlowTypeListVO>> getFlowTypeList();

    BaseAnswer editFlow(EditFlowParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<List<LimitListVO>> getLimitList();

    BaseAnswer<List<FlowRoleListVO>> getFlowRoleList();

}
