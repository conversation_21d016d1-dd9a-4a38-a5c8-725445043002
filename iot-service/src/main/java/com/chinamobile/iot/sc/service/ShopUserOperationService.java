package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/6/27 10:30
 * @description: 商城用户操作接口类
 **/
public interface ShopUserOperationService {

    /**
     * 导入解析文件
     * @param file
     */
    void sftpOperationCustomerList(MultipartFile file);



    /**
     * 商城客户经理用户数据同步字段新增更新历史数据
     * @param file
     */
    void shopManagerAddFieldImportList(MultipartFile file);

    /**
     * 商城普通用户经理用户数据同步字段新增更新历史数据
     * @param file
     */
    void shopCustomerAddFieldImportList(MultipartFile file);


    /**
     * 解析获取商城客户经理用户数据 根据传入的参数名进行匹配解析
     * @param fileNameParam
     */
    void sftpOperationAccountManagerDataBack(String fileNameParam);


    /**
     * 解析获取商城普通，分销用户数据 根据传入的参数名进行匹配解析
     * @param fileNameParam
     */
    void sftpOperationCustomerDataBack(String fileNameParam);

    IOTAnswer<Void> syncAccountManagerDataRealTime(IOTRequest baseRequest);

    IOTAnswer<Void> syncCustomerDataRealTime(IOTRequest iotRequest);

}
