package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2022/1/4 15:29
 * @Description: 退货物流信息IOT同步到OS系统封装类
 */
@Data
public class ReturnOrdersLogisInfo {
    private List<OrderInfoDTO> orderInfo;

    @Data
    public static class OrderInfoDTO {
        private String refundOrderId;
        private String orderId;
        private List<LogisInfo> logisInfo;
    }

    @Data
    public static class LogisInfo {
        private String logisCode;
        private String supplierName;
    }
}
