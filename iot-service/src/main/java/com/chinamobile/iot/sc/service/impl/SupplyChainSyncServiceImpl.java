package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.K3ProMaterialMapperExt;
import com.chinamobile.iot.sc.entity.Base64Upload;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.exception.ServicePowerException;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.mapper.SupplyChainIotCostAllocDetailsDO;
import com.chinamobile.iot.sc.pojo.mapper.SupplyChainIotLineInfoDO;
import com.chinamobile.iot.sc.pojo.vo.ContractMaterialDetailItemVO;
import com.chinamobile.iot.sc.pojo.vo.ContractProvinceInfoVO;
import com.chinamobile.iot.sc.request.supplychain.*;
import com.chinamobile.iot.sc.request.sync.SyncCommonRequest;
import com.chinamobile.iot.sc.response.iot.SoftwareOrderInfoSyncResponse;
import com.chinamobile.iot.sc.response.iot.importPoDraftResponse;
import com.chinamobile.iot.sc.service.IStorageService;
import com.chinamobile.iot.sc.service.SupplyChainSyncService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.service.excel.SyncOrderToMarketSystemCutOver;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class SupplyChainSyncServiceImpl implements SupplyChainSyncService {

    @Autowired
    private SyncClaimMaterialMapper syncClaimMaterialMapper;

    @Autowired
    private SyncPaymentStatusMapper syncPaymentStatusMapper;

    @Autowired
    private K3syncStatisProcityMapper k3syncStatisProcityMapper;

    @Autowired
    private K3syncStatisProvinceMapper k3syncStatisProvinceMapper;

    @Autowired
    private K3syncStatisCityMapper k3syncStatisCityMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private IStorageService storageService;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private K3ProMaterialMapperExt k3ProMaterialMapperExt;

    @Resource
    private UserK3Mapper userK3Mapper;

    @Resource

    private Order2cAtomInfoMapper order2cAtomInfoMapper;


    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedisTemplate redisTemplate;


    private static final String supplyChainScm = "SCM";
    private static final String province = "371";

    private static final String vendorCode = "MDM_106201379";

    private static final String vendorName = "中移物联网有限公司";

    public final static String REDIS_MARKET_B2C_KEY = "SC:MARKET:B2C";

    @Value("${scm.importPoDraftUrl}")
    private String importPoDraftUrl;


    @Value("${scm.marketSystem.orderSyncUrl}")
    private String orderSyncUrl;

    @Value("${scm.marketSystem.orderSyncUrlB2B:http://10.12.3.189:8082/saleCenter-app/externalAuthApi/b2bOrderSync}")
    private String orderSyncUrlB2B;

    @Value("${scm.marketSystem.key}")
    private String marketSystemKey;

    @Value("${scm.marketSystem.secret}")
    private String marketSystemSecret;

    @Value("${scm.marketSystem.secretB2B:FKhRHW5GR9YYktlYwAsCEcKeRsUHCZ}")
    private String marketSystemSecretB2B;

    // 没拿到生成规则，丰总说直接用当时软件服务的key
    @Value("${softService.secretKey}")
    private String secretKey;
    @Value("${softService.sm4Key}")
    private String osSecretKey;
    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");

    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);

    public static List<String> AAndC = new ArrayList<>();

    static {
        AAndC.add(AtomOfferingClassEnum.A.getAtomOfferingClass());
        AAndC.add(AtomOfferingClassEnum.C.getAtomOfferingClass());
    }
    /**
     * 同步订单报账材料
     * @param syncCommonRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> claimMaterials(SyncCommonRequest syncCommonRequest) {
        log.info("同步订单报账材料, data:{}", JSON.toJSONString(syncCommonRequest));
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
//      sign验签
        try {
            SignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        if (!supplyChainScm.equals(syncCommonRequest.getSubscriber())) {
        throw new BusinessException(StatusConstant.INTERNAL_ERROR, "subscriber错误");
        }
        SupplyChainSyncClaimMaterialsRequest request = JSON.parseObject(input, SupplyChainSyncClaimMaterialsRequest.class);
        Base64Upload base64File = new Base64Upload();
        base64File.setBase64(request.getFileContent());
        base64File.setFileName(request.getFileName());
        try {
            log.info("开始上传文件");
            BaseAnswer<UpResult> upResultBaseAnswer = storageService.uploadBase64(base64File);
            log.info("上传文件结果, data:{}", upResultBaseAnswer);
            if(!(upResultBaseAnswer.getStateCode().equals("00000"))){
                throw new BusinessException(upResultBaseAnswer.getStateCode(), upResultBaseAnswer.getMessage());
            }
            String fileOuterUrl = upResultBaseAnswer.getData().getOuterUrl();

            String segment1 = request.getSegment1();
            String iotMallNumber = request.getIotMallNumber();
            String poSegment1 = request.getPoSegment1();

            SyncClaimMaterialExample syncClaimMaterialExample= new SyncClaimMaterialExample().createCriteria()
                    .andSegment1EqualTo(segment1)
                    .andIotMallNumberEqualTo(iotMallNumber)
                    .andPoSegment1EqualTo(poSegment1)
                    .andSubscriberEqualTo(supplyChainScm)
                    .andProvinceEqualTo(province)
                    .example();

            List<SyncClaimMaterial> syncClaimMaterialList = syncClaimMaterialMapper.selectByExample(syncClaimMaterialExample);
            Date now = new Date();
            if (CollectionUtils.isNotEmpty(syncClaimMaterialList)) {
                SyncClaimMaterial syncClaimMaterial = new SyncClaimMaterial();
                syncClaimMaterial.setFileOuterUrl(fileOuterUrl);
                syncClaimMaterial.setUpdateTime(now);
                syncClaimMaterialMapper.updateByExampleSelective(syncClaimMaterial, syncClaimMaterialExample);
            } else {
                SyncClaimMaterial syncClaimMaterial = new SyncClaimMaterial();
                BeanUtils.copyProperties(request,syncClaimMaterial);
                syncClaimMaterial.setId(BaseServiceUtils.getId());
                syncClaimMaterial.setFileOuterUrl(fileOuterUrl);
                syncClaimMaterial.setSubscriber(supplyChainScm);
                syncClaimMaterial.setProvince(province);
                syncClaimMaterial.setUpdateTime(now);
                syncClaimMaterial.setCreateTime(now);
                syncClaimMaterialMapper.insertSelective(syncClaimMaterial);
            }

            // 同步到自己的库
            K3syncStatisCityExample k3syncStatisCityExample= new K3syncStatisCityExample().createCriteria()
                    .andProDataCodeEqualTo(iotMallNumber)
                    .example();
            K3syncStatisCity k3syncStatisCity = new K3syncStatisCity();

            // 省采报账材料状态
            k3syncStatisCity.setProMaterialStatus(1);
            k3syncStatisCityMapper.updateByExampleSelective(k3syncStatisCity, k3syncStatisCityExample);

            K3syncStatisProvinceExample k3syncStatisProvinceExample= new K3syncStatisProvinceExample().createCriteria()
                    .andProDataCodeEqualTo(iotMallNumber)
                    .example();
            K3syncStatisProvince k3syncStatisProvince = new K3syncStatisProvince();

            // 省采报账材料状态
            k3syncStatisProvince.setProMaterialStatus(1);
            k3syncStatisProvinceMapper.updateByExampleSelective(k3syncStatisProvince, k3syncStatisProvinceExample);

            K3syncStatisProcityExample k3syncStatisProcityExample= new K3syncStatisProcityExample().createCriteria()
                    .andProDataCodeEqualTo(iotMallNumber)
                    .example();
            K3syncStatisProcity k3syncStatisProcity = new K3syncStatisProcity();

            // 省采报账材料状态
            k3syncStatisProcity.setProMaterialStatus(1);
            k3syncStatisProcityMapper.updateByExampleSelective(k3syncStatisProcity, k3syncStatisProcityExample);
        }catch (Exception e){
            log.error("同步订单报账材料失败！，错误信息：{}", e.getMessage());
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        return baseAnswer;

    }

    /**
     * 同步报账支付进度
     * @param syncCommonRequest
     * @return
     */
    @Override
    public BaseAnswer<Void> paymentStatus(SyncCommonRequest syncCommonRequest) {
        log.info("同步报账支付进度, data:{}", JSON.toJSONString(syncCommonRequest));
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
    //  sign验签
        try {
            SignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        if (!supplyChainScm.equals(syncCommonRequest.getSubscriber())) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "subscriber错误");
        }
        SupplyChainSyncPaymentStatusRequest request = JSON.parseObject(input, SupplyChainSyncPaymentStatusRequest.class);
        try {

            String segment1 = request.getSegment1();
            String iotMallNumber = request.getIotMallNumber();
            String poSegment1 = request.getPoSegment1();

            SyncPaymentStatusExample syncPaymentStatusExample= new SyncPaymentStatusExample().createCriteria()
                    .andSegment1EqualTo(segment1)
                    .andIotMallNumberEqualTo(iotMallNumber)
                    .andPoSegment1EqualTo(poSegment1)
                    .andSubscriberEqualTo(supplyChainScm)
                    .andProvinceEqualTo(province)
                    .example();

            List<SyncPaymentStatus> syncPaymentStatusList = syncPaymentStatusMapper.selectByExample(syncPaymentStatusExample);
            Date now = new Date();
            if (CollectionUtils.isNotEmpty(syncPaymentStatusList)) {
                SyncPaymentStatus syncPaymentStatus = new SyncPaymentStatus();
                BeanUtils.copyProperties(request,syncPaymentStatus);
                syncPaymentStatus.setUpdateTime(now);
                syncPaymentStatusMapper.updateByExampleSelective(syncPaymentStatus, syncPaymentStatusExample);
            } else {
                SyncPaymentStatus syncPaymentStatus = new SyncPaymentStatus();
                BeanUtils.copyProperties(request,syncPaymentStatus);
                syncPaymentStatus.setId(BaseServiceUtils.getId());
                syncPaymentStatus.setSubscriber(supplyChainScm);
                syncPaymentStatus.setProvince(province);
                syncPaymentStatus.setUpdateTime(now);
                syncPaymentStatus.setCreateTime(now);
                syncPaymentStatusMapper.insertSelective(syncPaymentStatus);
            }
//            String scmStatusCode = request.getStatusCode();
            // 同步到自己的库
            K3syncStatisCityExample k3syncStatisCityExample= new K3syncStatisCityExample().createCriteria()
                    .andProDataCodeEqualTo(iotMallNumber)
                    .example();
            K3syncStatisCity k3syncStatisCity = new K3syncStatisCity();

            // 省采报账状态
            k3syncStatisCity.setProSubmitAccountStatus("1");
            k3syncStatisCityMapper.updateByExampleSelective(k3syncStatisCity, k3syncStatisCityExample);

            K3syncStatisProvinceExample k3syncStatisProvinceExample= new K3syncStatisProvinceExample().createCriteria()
                    .andProDataCodeEqualTo(iotMallNumber)
                    .example();
            K3syncStatisProvince k3syncStatisProvince = new K3syncStatisProvince();

            // 省采报账状态
            k3syncStatisProvince.setProSubmitAccountStatus("1");
            k3syncStatisProvinceMapper.updateByExampleSelective(k3syncStatisProvince, k3syncStatisProvinceExample);

            K3syncStatisProcityExample k3syncStatisProcityExample= new K3syncStatisProcityExample().createCriteria()
                    .andProDataCodeEqualTo(iotMallNumber)
                    .example();
            K3syncStatisProcity k3syncStatisProcity = new K3syncStatisProcity();

            // 省采报账状态
            k3syncStatisProcity.setProSubmitAccountStatus("1");
            k3syncStatisProcityMapper.updateByExampleSelective(k3syncStatisProcity, k3syncStatisProcityExample);
        }catch (Exception e){
            log.error("同步报账支付进度！，错误信息：{}", e.getMessage());
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        return baseAnswer;
    }

    /**
     * 同步省采
     * @param request
     * @return
     */
    @Override
    public BaseAnswer<Void> importPoDraft(SupplyChainImportPoDraftIOTRequest request) {
        String contractId = request.getContractId();
        String contractNum =  request.getContractNum();
        String type =  request.getType();
        String contractVat;
        Date time = new Date();
        // TODO 合同为省时是否只用不用查下面的地市编码
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        SupplyChainImportPoDraftRequest supplyChainImportPoDraftRequest = new SupplyChainImportPoDraftRequest();
        System.out.println("合同id:{}" + contractId);
        List<Contract> contracts = contractMapper.selectByExample(
                new ContractExample().createCriteria()
                        .andNumberEqualTo(contractNum)
                        .example()
        );
        if (contracts != null && contracts.size() > 0) {
            Contract contract = contracts.get(0);
            contractVat = contract.getfNumber();
        } else {
            contractVat = null;
        }
        // 找出来所有物料
//        List<Order2cInfo> order2cInfoList = order2cInfoMapper.selectByExample(new Order2cInfoExample().createCriteria().andSyncK3IdEqualTo(contractId).example());
        List<ContractMaterialDetailItemVO> materialList =  k3ProMaterialMapperExt.getMaterialDetailItem(contractId, contractNum);
        System.out.println("原始物料list:{}" + materialList);

        // 先保证同物料编码同省市只存在一条数据
        Map<String, ContractMaterialDetailItemVO> materiaDetaillMap = new LinkedHashMap<>();
        // 物料编码set
        Set<String> materialsCodeSet = new HashSet<>();
        // 先保证数据同物料编码同省市只存在一条再按照物料编码进行划分
        materialList.stream().forEach(item -> {
            String ouCode = item.getOuCode();
            String materialsCode = item.getMaterialsCode();
            String mapKey = materialsCode + "_" + ouCode;
            // 过滤信息不全的物料
            if(StringUtils.isNotEmpty(materialsCode)){
                materialsCodeSet.add(materialsCode);
                if (!materiaDetaillMap.containsKey(mapKey)) {
                    materiaDetaillMap.put(mapKey, item);
                } else {
                    ContractMaterialDetailItemVO itemDetail = materiaDetaillMap.get(mapKey);
                    BigDecimal materialsNumber = itemDetail.getMaterialsNumber().add(item.getMaterialsNumber());
                    itemDetail.setMaterialsNumber(materialsNumber);
                    materiaDetaillMap.put(mapKey, itemDetail);
                }
            }
            // 比较税率 取字符串里面的数字比较
            if(!contractVat.equals(getNumeric(item.getTaxRate()))){
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, "合同下与省采税率不同，请检查");
            }

        });
        // 不同物料编码物料list
        List<SupplyChainIotLineInfoDO> iotLineInfoList = new ArrayList<>();

        System.out.println("物料编码Set:{}"+materialsCodeSet);
        // 物料编码set为空说明excel未导入应抛出错误
        if(materialsCodeSet.isEmpty()){
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "合同下未找到对应物料,请检查excel是否导入");
        }
        // 不含税金额
        BigDecimal poAmount = new BigDecimal("0.00");

        // 含税金额
        BigDecimal poAmountTax = new BigDecimal("0.00");

        // 行税金
        BigDecimal taxSum = new BigDecimal("0.00");

        for(String value : materialsCodeSet){
            SupplyChainIotLineInfoDO lineInfoItem = new SupplyChainIotLineInfoDO();
            // 留一个未处理过的物料当作原始数据方便赋值
            ContractMaterialDetailItemVO detailItemRet = new ContractMaterialDetailItemVO();
            // 同物料编码不同地市物料list
            List<SupplyChainIotCostAllocDetailsDO> iotCostDetailList = new ArrayList<>();
            // 数量合计
            BigDecimal materialsNumber = new BigDecimal("0.00");
            // 同一个物料编码的放进一个list
            for(ContractMaterialDetailItemVO mapValue : materiaDetaillMap.values()){
                if(value.equals(mapValue.getMaterialsCode())){
                    // 留一个未处理过的物料当作原始数据方便赋值
                    BeanUtils.copyProperties(mapValue,detailItemRet);

                    SupplyChainIotCostAllocDetailsDO iotCostDetailItem = new SupplyChainIotCostAllocDetailsDO();

                    BeanUtils.copyProperties(mapValue, iotCostDetailItem);
                    // 含税金额 costAmountTax 不含税金额 costAmount 税额 costTax
                    BigDecimal detailItemMaterialsNumber = mapValue.getMaterialsNumber();
                    // 含税金额
                    BigDecimal costAmountTax = getRound2Dec(detailItemMaterialsNumber.multiply(mapValue.getDiscountedUnitPrice()));
                    // 不含税金额
                    BigDecimal costAmount = getRound2Dec(detailItemMaterialsNumber.multiply(mapValue.getUnitPrice()));
                    // 税额
                    BigDecimal costTax = costAmountTax.subtract(costAmount);
                    // 数量合计
                    materialsNumber = materialsNumber.add(detailItemMaterialsNumber);
                    iotCostDetailItem.setCostAmountTax(costAmountTax);
                    iotCostDetailItem.setCostTax(costTax);
                    iotCostDetailItem.setCostAmount(costAmount);
                    // 据那边的要求，最下级的行号与上一级保持一致，暂使用物料编码
                    iotCostDetailItem.setLineNum(value);
                    iotCostDetailList.add(iotCostDetailItem);
                }
            }
            System.out.println("物料编号:{}"+value);
            System.out.println("同物料编码不同省市物料List:{}"+ JSONObject.toJSONString(iotCostDetailList));
            BeanUtils.copyProperties(detailItemRet,lineInfoItem);
            // 含税金额 lineAmountTax 不含税金额 lineAmount 税额 lineTax
            // 含税金额
            BigDecimal lineAmountTax = getRound2Dec(materialsNumber.multiply(lineInfoItem.getDiscountedUnitPrice()));
            // 不含税金额
            BigDecimal lineAmount = getRound2Dec(materialsNumber.multiply(lineInfoItem.getUnitPrice()));
            // 税额
            BigDecimal lineTax = lineAmountTax.subtract(lineAmount);

            poAmount = poAmount.add(lineAmount);
            poAmountTax = poAmountTax.add(lineAmountTax);
            taxSum = lineTax.add(taxSum);

            lineInfoItem.setLineAmount(lineAmount);
            lineInfoItem.setLineAmountTax(lineAmountTax);
            lineInfoItem.setLineTax(lineTax);
            // 不知道行号是干嘛的，暂定省市为纬度的的时候用省市编码，物料编码为纬度的用物料编码
            lineInfoItem.setLineNum(value);
            lineInfoItem.setCreateDate(time);
            lineInfoItem.setLastUpdateDate(time);
            lineInfoItem.setScmIotCostAllocDetails(iotCostDetailList);
            lineInfoItem.setMaterialsNumber(materialsNumber);
            iotLineInfoList.add(lineInfoItem);
        }
        System.out.println("生成的list: "+ JSONObject.toJSONString(iotLineInfoList));

        // 获取合同信息
        ContractProvinceInfoVO contractInfo =  k3ProMaterialMapperExt.getContractDetail(contractNum);

        if(contractInfo == null){
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "未找到对应省采合同,请检查excel是否导入");
        }else{
            BeanUtils.copyProperties(contractInfo, supplyChainImportPoDraftRequest);
            supplyChainImportPoDraftRequest.setPoAmount(poAmount);
            supplyChainImportPoDraftRequest.setPoAmountTax(poAmountTax);
            supplyChainImportPoDraftRequest.setTaxSum(taxSum);
            supplyChainImportPoDraftRequest.setVendorCode(vendorCode);
            supplyChainImportPoDraftRequest.setVendorName(vendorName);
            supplyChainImportPoDraftRequest.setScmIotPoLineInfo(iotLineInfoList);
            supplyChainImportPoDraftRequest.setRecordNumber(contractId);
        }

        // 请求省采接口
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(supplyChainImportPoDraftRequest), headers);

            log.info("同步省采接口request:{}",JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<importPoDraftResponse> response = restTemplateHttps.postForEntity(importPoDraftUrl, requestEntity, importPoDraftResponse.class);
            log.info("同步省采接口response:{}",JSON.toJSONString(response));
            importPoDraftResponse importPoDraftResponse = response.getBody();
            // 0成功，1失败
            if (importPoDraftResponse == null || 1 == importPoDraftResponse.getResp_code()) {
                // 0-地市 1-省 3-省地市
                if(type.equals("0")){
                    K3syncStatisCityExample k3syncStatisCityExample= new K3syncStatisCityExample().createCriteria()
                            .andProDataCodeEqualTo(contractId)
                            .example();
                    K3syncStatisCity k3syncStatisCity = new K3syncStatisCity();
                    // 省采同步状态 0：未同步； 1：成功；2：失败
                    k3syncStatisCity.setProSyncStatus("2");
                    k3syncStatisCityMapper.updateByExampleSelective(k3syncStatisCity, k3syncStatisCityExample);

                }else if (type.equals("1")){
                    K3syncStatisProvinceExample k3syncStatisProvinceExample= new K3syncStatisProvinceExample().createCriteria()
                            .andProDataCodeEqualTo(contractId)
                            .example();
                    K3syncStatisProvince k3syncStatisProvince = new K3syncStatisProvince();
                    // 省采同步状态 0：未同步； 1：成功；2：失败
                    k3syncStatisProvince.setProSyncStatus("2");
                    k3syncStatisProvinceMapper.updateByExampleSelective(k3syncStatisProvince, k3syncStatisProvinceExample);
                }else if (type.equals("3")){
                    K3syncStatisProcityExample k3syncStatisProcityExample= new K3syncStatisProcityExample().createCriteria()
                            .andProDataCodeEqualTo(contractId)
                            .example();
                    K3syncStatisProcity k3syncStatisProcity = new K3syncStatisProcity();
                    // 省采同步状态 0：未同步； 1：成功；2：失败
                    k3syncStatisProcity.setProSyncStatus("2");
                    k3syncStatisProcityMapper.updateByExampleSelective(k3syncStatisProcity, k3syncStatisProcityExample);
                }
                //没返回信息则为提示同步失败，返回错误码则提示返回错误描述
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, importPoDraftResponse == null ? "同步省采失败，请重试" : importPoDraftResponse.getResp_msg());
            }
            // 获取供应链编号
            String segment1 = importPoDraftResponse.getRecordNumber();
            // TODO 等待返回处理
            // 0-地市 1-省 3-省地市
            if(type.equals("0")){
                K3syncStatisCityExample k3syncStatisCityExample= new K3syncStatisCityExample().createCriteria()
                        .andProDataCodeEqualTo(contractId)
                        .example();
                K3syncStatisCity k3syncStatisCity = new K3syncStatisCity();

                // 省采返回编号
                k3syncStatisCity.setProRetNum(segment1);
                // 省采同步状态 0：未同步； 1：成功；2：失败
                k3syncStatisCity.setProSyncStatus("1");
                k3syncStatisCityMapper.updateByExampleSelective(k3syncStatisCity, k3syncStatisCityExample);

            }else if (type.equals("1")){
                K3syncStatisProvinceExample k3syncStatisProvinceExample= new K3syncStatisProvinceExample().createCriteria()
                        .andProDataCodeEqualTo(contractId)
                        .example();
                K3syncStatisProvince k3syncStatisProvince = new K3syncStatisProvince();

                // 省采返回编号
                k3syncStatisProvince.setProRetNum(segment1);
                // 省采同步状态 0：未同步； 1：成功；2：失败
                k3syncStatisProvince.setProSyncStatus("1");
                k3syncStatisProvinceMapper.updateByExampleSelective(k3syncStatisProvince, k3syncStatisProvinceExample);
            }else if (type.equals("3")){
                K3syncStatisProcityExample k3syncStatisProcityExample= new K3syncStatisProcityExample().createCriteria()
                        .andProDataCodeEqualTo(contractId)
                        .example();
                K3syncStatisProcity k3syncStatisProcity = new K3syncStatisProcity();

                // 省采返回编号
                k3syncStatisProcity.setProRetNum(segment1);
                // 省采同步状态 0：未同步； 1：成功；2：失败
                k3syncStatisProcity.setProSyncStatus("1");
                k3syncStatisProcityMapper.updateByExampleSelective(k3syncStatisProcity, k3syncStatisProcityExample);
            }else{
                log.debug("传入的合同type不符合条件",type);
            }
        }catch (Exception e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }

        return baseAnswer;
    }

    /**
     * 返回报账材料下载地址
     * @param contractId
     * @return
     */
    @Override
    public BaseAnswer<String> getMaterial(String contractId, String segment1) {
        log.info("返回报账材料下载地址合同id: ", contractId);
        log.info("返回报账材料下载地址供应链单号: ", segment1);
        try {

            SyncClaimMaterialExample syncClaimMaterialExample= new SyncClaimMaterialExample().createCriteria()
                    .andSegment1EqualTo(segment1)
                    .andIotMallNumberEqualTo(contractId)
                    .andSubscriberEqualTo(supplyChainScm)
                    .andProvinceEqualTo(province)
                    .example();

            List<SyncClaimMaterial> syncClaimMaterialList = syncClaimMaterialMapper.selectByExample(syncClaimMaterialExample);
            if (CollectionUtils.isNotEmpty(syncClaimMaterialList)) {
                SyncClaimMaterial syncClaimMaterial = syncClaimMaterialList.get(0);
                String fileUrl = syncClaimMaterial.getFileOuterUrl();
                return BaseAnswer.success(fileUrl);
            } else {
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, "文件未找到");
            }
        }catch (Exception e){
            log.error("同步订单报账材料失败！，错误信息：{}", e.getMessage());
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
    }

    /**
     * 同步订单数据给市场销售支撑系统B2C
     * @param OrderId
     * @return
     */
    @Override
    public BaseAnswer<Void> syncOrderToMarketSystem(List<String> OrderId) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        return redisUtil.smartLock(REDIS_MARKET_B2C_KEY, () -> {
            SoftwareOrderInfoSyncRequest softwareOrderInfoSyncRequest = new SoftwareOrderInfoSyncRequest();
            SoftwareOrderInfoSyncRequest.ROOT root = new SoftwareOrderInfoSyncRequest.ROOT();
            softwareOrderInfoSyncRequest.setROOT(root);
            SoftwareOrderInfoSyncRequest.HEADER header = new SoftwareOrderInfoSyncRequest.HEADER();
            root.setHEADER(header);
            SoftwareOrderInfoSyncRequest.BODY body = new SoftwareOrderInfoSyncRequest.BODY();
            root.setBODY(body);
            SoftwareOrderInfoSyncRequest.AuthInfo authInfo = new SoftwareOrderInfoSyncRequest.AuthInfo();
            SoftwareOrderInfoSyncRequest.BusiInfo busiInfo = new SoftwareOrderInfoSyncRequest.BusiInfo();
            String timestamp = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            String signBefore = marketSystemKey + "|" + marketSystemSecret +"|"+timestamp;
            String sign = null;
            try {
                sign = encrypt(signBefore, marketSystemSecret);
            } catch (Exception e) {
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
            }
            authInfo.setUSER_ID(marketSystemKey);
            authInfo.setPOST_DATE(DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL));
            authInfo.setSIGN(sign);
            body.setAUTH_INFO(authInfo);
            body.setBUSI_INFO(busiInfo);
            List<SoftwareOrderInfoSyncRequest.OrderHeaderInfo> orderHeaderInfoList = new ArrayList<>();
            busiInfo.setRECEIVABLE_INFO_LIST(orderHeaderInfoList);
            Integer billNoCount = (Integer) redisTemplate.opsForValue().get(Constant.REDIS_KEY_BILL_NO);
            String billNoNumber = null;
            for(String id : OrderId){
                SoftwareOrderInfoSyncRequest.OrderHeaderInfo orderHeaderInfo = new SoftwareOrderInfoSyncRequest.OrderHeaderInfo();
                setAllFieldsToEmptyString(orderHeaderInfo);
                List<SoftwareOrderInfoSyncRequest.SaleLineInfo> saleLineInfoList = k3ProMaterialMapperExt.getMaterialByOrder(id);
                List<SoftwareOrderInfoSyncRequest.SaleLineInfo> saleLineInfoListResult = new ArrayList<>();
                log.info("saleLineInfoList = {}",saleLineInfoList);
                if(StringUtils.isEmpty(saleLineInfoList.get(0).getContractNumber())){
                    log.info("请求市场销售支撑系统B2C接口失败:订单对应虚拟合同不存在");
                    baseAnswer.setMessage("订单对应虚拟合同不存在");
                    baseAnswer.setStateCode("10004");
                    return baseAnswer;
                }
                // 增加重复同步校验机制
                if(StringUtils.isNotEmpty(saleLineInfoList.get(0).getBillNoNumber())){
                    log.info("请求市场销售支撑系统B2C接口失败:订单已同步过市场销售系统");
                    baseAnswer.setMessage("订单已同步过市场销售系统");
                    baseAnswer.setStateCode("10004");
                    return baseAnswer;
                }
                String taxRate = saleLineInfoList.get(0).getLabelTax().replace("%", "");
                String outTaxRate = String.valueOf(new BigDecimal(Double.parseDouble(saleLineInfoList.get(0).getLabelTax().replace("%", ""))).divide(BigDecimal.valueOf(100)));
                for(SoftwareOrderInfoSyncRequest.SaleLineInfo saleLineInfo : saleLineInfoList){
                    SoftwareOrderInfoSyncRequest.SaleLineInfo saleLineInfo1 = new SoftwareOrderInfoSyncRequest.SaleLineInfo();
                    BeanUtils.copyProperties(saleLineInfo,saleLineInfo1);
                    setAllFieldsToEmptyString(saleLineInfo1);
                    saleLineInfo1.setTAX_PRICE(getRound2Dec(saleLineInfo1.getTAX_PRICE()));
                    saleLineInfo1.setVALOREM_TOTAL(getRound2Dec(saleLineInfo1.getVALOREM_TOTAL()));
                    String outPutTax = String.valueOf(new BigDecimal(Double.parseDouble(saleLineInfo1.getLabelTax().replace("%", ""))).divide(BigDecimal.valueOf(100)));
                    // 算不含税单价
                    saleLineInfo1.setPRICE(
                            saleLineInfo1.getTAX_PRICE()
                                    .divide(new BigDecimal(outPutTax)
                                            .add(BigDecimal.valueOf(1)),2, RoundingMode.HALF_UP)
                    );
                    // 算不含税金额
                    saleLineInfo1.setNO_TAX_PRICE(
                            saleLineInfo1.getVALOREM_TOTAL()
                                    .divide(new BigDecimal(outPutTax)
                                            .add(BigDecimal.valueOf(1)),2, RoundingMode.HALF_UP)
                    );
                    // 算税额
                    saleLineInfo1.setTAX(saleLineInfo1.getVALOREM_TOTAL()
                            .subtract((saleLineInfo1.getNO_TAX_PRICE()))
                            .setScale(2, RoundingMode.HALF_UP)
                    );
                    saleLineInfo1.setTotalPrice(null);
                    saleLineInfo1.setContractNumber(null);
                    saleLineInfo1.setOrderStatusTime(null);
                    saleLineInfo1.setCreateTime(null);
                    saleLineInfo1.setLabelTax(null);
                    saleLineInfo1.setBillNoNumber(null);

                    saleLineInfo1.setTAX_RATE(new BigDecimal(taxRate));
                    saleLineInfo1.setDIS_COUNT_AMOUNT_FOR(new BigDecimal(0));
                    saleLineInfo1.setENTRY_DISCOUNT_RATE(new BigDecimal(0));
                    //先用0
                    saleLineInfo1.setCOST_AMOUNT(new BigDecimal(0));
                    saleLineInfoListResult.add(saleLineInfo1);
                }

                orderHeaderInfo.setMATERIAL_LIST(saleLineInfoListResult);

                List<UserK3> userList = userK3Mapper.selectByExample(new UserK3Example().createCriteria()
                        .andProvincecodeEqualTo("000")
                        .example());
                if (userList == null || userList.size() == 0) {
                    log.info("同步订单数据给市场销售支撑B2C系统失败，获取k3销售员信息失败，sellerUserList is null");
                    baseAnswer.setMessage("获取k3销售员信息失败");
                    baseAnswer.setStateCode("10004");
                    return baseAnswer;
                }
//            Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(id);
                if (null == billNoCount) {
                    orderHeaderInfo.setBILL_NO("ARE00000000");
                    billNoNumber = "ARE00000000";
                    billNoCount = 1;
                }else{
                    orderHeaderInfo.setBILL_NO("ARE"+String.format("%08d",billNoCount));
                    billNoNumber = "ARE"+String.format("%08d",billNoCount);
                    billNoCount++;
                }
                orderHeaderInfo.setBUSINESS_DATE(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT));

                orderHeaderInfo.setCOMPANY("0*********");
                orderHeaderInfo.setCOMPANY_NAME("物联网公司");
                orderHeaderInfo.setCURRENCY("PRE001");
                orderHeaderInfo.setBILL_TYPE_ID("1");

                orderHeaderInfo.setAPPROVE_DATE(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT));
                orderHeaderInfo.setAPPROVE_ID(userList.get(0).getUsercode());

                orderHeaderInfo.setCLIENT("*********");
                orderHeaderInfo.setCLIENT_NAME("IoT应用商城");
                orderHeaderInfo.setSALE_ORG_ID(userList.get(0).getSellerdept());
                orderHeaderInfo.setSALE_ORG_NAME(userList.get(0).getDepartment());
                orderHeaderInfo.setCOST_CENTER_CODE(userList.get(0).getCostcenter());
                orderHeaderInfo.setCOST_CENTER_NAME(userList.get(0).getCostcentername());
                orderHeaderInfo.setSALES_MAN(userList.get(0).getUsercode());
                orderHeaderInfo.setSALES_MAN_NAME(userList.get(0).getName());
                orderHeaderInfo.setPAY_ORG_ID("0*********");
                orderHeaderInfo.setPAY_ORG_NAME("物联网公司");

                orderHeaderInfo.setRED_BLUE("1");
                orderHeaderInfo.setDOCUMENT_STATUS("A");
                orderHeaderInfo.setYES_OR_NO_SERVICE("true");
                orderHeaderInfo.setPROVINCE("重庆");
                orderHeaderInfo.setCITY("重庆");
                orderHeaderInfo.setBUSINESS_CATEGORY("U");
                orderHeaderInfo.setBUSINESS_MODE("standard");
                orderHeaderInfo.setRESPONSIBLE_PERSON(userList.get(0).getUsercode());

                orderHeaderInfo.setSALE_DEPT_ID(userList.get(0).getSellerdept());
                orderHeaderInfo.setSAL_ORDER_DATE(saleLineInfoList.get(0).getCreateTime());

                orderHeaderInfo.setMALL_ORDER_TYPE("1");

                orderHeaderInfo.setCONTRACT(saleLineInfoList.get(0).getContractNumber());


                //销售总金额(含税)
                orderHeaderInfo.setVALOREM_TOTAL(saleLineInfoList.get(0).getTotalPrice()
                        .setScale(2, RoundingMode.HALF_UP));
                //销售总金额(不含税)
                //税率应该是一样的 随便取一个就行
                orderHeaderInfo.setNO_TAX_AMOUNT_FOR(
                        saleLineInfoList.get(0).getTotalPrice()
                                .divide(new BigDecimal(outTaxRate)
                                        .add(BigDecimal.valueOf(1)),2, RoundingMode.HALF_UP)
                                .toString()
                );
                //销售总税额
                orderHeaderInfo.setTAX_AMOUNT_FOR(orderHeaderInfo.getVALOREM_TOTAL()
                        .subtract(new BigDecimal(orderHeaderInfo.getNO_TAX_AMOUNT_FOR()))
                        .setScale(2, RoundingMode.HALF_UP)
                        .toString());
                orderHeaderInfoList.add(orderHeaderInfo);

            }

            System.out.println("生成的body: "+ JSONObject.toJSONString(softwareOrderInfoSyncRequest));


            // 请求市场销售支撑系统
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(softwareOrderInfoSyncRequest), headers);

                log.info("请求市场销售支撑系统B2C接口request:{}",JSON.toJSONString(requestEntity));
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                ResponseEntity<String> response = restTemplateHttps.postForEntity(orderSyncUrl, requestEntity, String.class);
                log.info("请求市场销售支撑系统B2C接口response:{}",JSON.toJSONString(response));
                SoftwareOrderInfoSyncResponse response1 = JSON.parseObject(response.getBody(), SoftwareOrderInfoSyncResponse.class);
                if(!response1.getROOT().getBODY().getRETURN_CODE().equals("0")){
                    log.info("请求市场销售支撑系统B2C接口失败:{}", response1.getROOT().getBODY().getDETAIL_MSG());
                    baseAnswer.setMessage(response1.getROOT().getBODY().getDETAIL_MSG());
                    baseAnswer.setStateCode("10004");
                }else{
                    redisTemplate.opsForValue().set(Constant.REDIS_KEY_BILL_NO, billNoCount);
                    // 订单表存一下
                    for(String id : OrderId){
                        Order2cInfo order2cInfo = new Order2cInfo();
                        order2cInfo.setOrderId(id);
                        order2cInfo.setBillNoNumber(billNoNumber);
                        order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);

                        // 增加同步时间
                        Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                        order2cAtomInfo.setBillNoTime(new Date());
                        order2cAtomInfoMapper.updateByExampleSelective(
                                order2cAtomInfo,
                                new Order2cAtomInfoExample().createCriteria()
                                        .andOrderIdEqualTo(id)
                                        .andAtomOfferingClassEqualTo("A")
                                        .example()
                        );
                    }
                }
            }catch (Exception e) {
            }

            return baseAnswer;
        });
    }


    /**
     * 同步订单数据给市场销售支撑系统B2B
     * @param orderId
     * @return
     */
    @Override
    public BaseAnswer<Void> syncOrderToMarketSystemB2B(String orderId) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        SoftwareOrderInfoB2BSyncRequest softwareOrderInfoSyncRequest = new SoftwareOrderInfoB2BSyncRequest();
        SoftwareOrderInfoB2BSyncRequest.ROOT root = new SoftwareOrderInfoB2BSyncRequest.ROOT();
        softwareOrderInfoSyncRequest.setROOT(root);
        SoftwareOrderInfoB2BSyncRequest.HEADER header = new SoftwareOrderInfoB2BSyncRequest.HEADER();
        root.setHEADER(header);
        SoftwareOrderInfoB2BSyncRequest.BODY body = new SoftwareOrderInfoB2BSyncRequest.BODY();
        root.setBODY(body);
        SoftwareOrderInfoB2BSyncRequest.AuthInfo authInfo = new SoftwareOrderInfoB2BSyncRequest.AuthInfo();
        SoftwareOrderInfoB2BSyncRequest.BusiInfo busiInfo = new SoftwareOrderInfoB2BSyncRequest.BusiInfo();
        String timestamp = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
        String signBefore = marketSystemKey + "|" + marketSystemSecretB2B +"|"+timestamp;
        String sign = null;
        String orderStatus = null;
        try {
            sign = encrypt(signBefore, marketSystemSecretB2B);
        } catch (Exception e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        authInfo.setUSER_ID(marketSystemKey);
        authInfo.setPOST_DATE(DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL));
        authInfo.setSIGN(sign);
        body.setAUTH_INFO(authInfo);
        body.setBUSI_INFO(busiInfo);
        List<SoftwareOrderInfoB2BSyncRequest.OrderHeaderInfo> orderHeaderInfoList = new ArrayList<>();
        busiInfo.setHEADER_INFO_LIST(orderHeaderInfoList);


        SoftwareOrderInfoB2BSyncRequest.OrderHeaderInfo orderHeaderInfo = new SoftwareOrderInfoB2BSyncRequest.OrderHeaderInfo();
        setAllFieldsToEmptyString(orderHeaderInfo);
        List<SoftwareOrderInfoB2BSyncRequest.SaleLineInfo> saleLineInfoList = k3ProMaterialMapperExt.getMaterialByOrderB2B(orderId);
        if(CollectionUtils.isEmpty(saleLineInfoList)){
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "订单对应物料信息未找到");
        }
        if(StringUtils.isEmpty(saleLineInfoList.get(0).getSALE_CONTRACT_NO())){
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "订单下商品对应物料所对应的合同未找到");
        }
        List<SoftwareOrderInfoB2BSyncRequest.SaleLineInfo> saleLineInfoListResult = new ArrayList<>();
        Integer lineNum = 1;
        log.info("saleLineInfoList = {}",saleLineInfoList);
//        String taxRate = saleLineInfoList.get(0).getOUTPUT_TAX_RATE();
//        String totalPrice = saleLineInfoList.get(0).getTotalPrice();

        // 物料编码set
        Set<String> materialsCodeSet = new HashSet<>();

        Map<String, SoftwareOrderInfoB2BSyncRequest.SaleLineInfo> materiaDetaillMap = new LinkedHashMap<>();
        // 算同物料数量和金额
        saleLineInfoList.stream().forEach(item -> {
            String materialsCode = item.getMATERIAL_CODE();
            String mapKey = materialsCode;
            // 过滤信息不全的物料
            if(StringUtils.isNotEmpty(materialsCode)){
                materialsCodeSet.add(materialsCode);
                if (!materiaDetaillMap.containsKey(mapKey)) {
                    materiaDetaillMap.put(mapKey, item);
                } else {
                    SoftwareOrderInfoB2BSyncRequest.SaleLineInfo itemDetail = materiaDetaillMap.get(mapKey);
                    BigDecimal materialsNumber = new BigDecimal(itemDetail.getSALES_VOLUMES()).add(new BigDecimal(item.getSALES_VOLUMES()));
                    BigDecimal saleAmountHasTax = new BigDecimal(itemDetail.getSALE_AMOUNT_HAS_TAX()).add(new BigDecimal(item.getSALE_AMOUNT_HAS_TAX()));
                    itemDetail.setSALES_VOLUMES(materialsNumber.toString());
                    itemDetail.setSALE_AMOUNT_HAS_TAX(saleAmountHasTax.toString());
                    materiaDetaillMap.put(mapKey, itemDetail);
                }
            }
        });
        // 不含税金额
        BigDecimal poAmount = new BigDecimal("0.00");

        // 含税金额
        BigDecimal poAmountTax = new BigDecimal("0.00");

        // 行税金
        BigDecimal taxSum = new BigDecimal("0.00");

        String orderType = saleLineInfoList.get(0).getOrderType();
        // 订单状态
        // 普通订单没交易成功是0 代客下单的都是1 退款了就5
        orderStatus = saleLineInfoList.get(0).getOrderStatus();
        String saleOrderStatus = null;
        if(orderStatus.equals("8")){
            saleOrderStatus = "5";
        } else{
            if(!orderType.equals("01")){
                saleOrderStatus = "1";
            }else{
                if(orderStatus.equals("7")){
                    saleOrderStatus = "1";
                }else{
                    saleOrderStatus = "0";
                }
            }
        }
//        String outTaxRate = String.valueOf(new BigDecimal(Double.parseDouble(saleLineInfoList.get(0).getOUTPUT_TAX_RATE().replace("%", ""))).divide(BigDecimal.valueOf(100)));
        for (SoftwareOrderInfoB2BSyncRequest.SaleLineInfo saleLineInfo : materiaDetaillMap.values()) {
            SoftwareOrderInfoB2BSyncRequest.SaleLineInfo saleLineInfo1 = new SoftwareOrderInfoB2BSyncRequest.SaleLineInfo();
            BeanUtils.copyProperties(saleLineInfo,saleLineInfo1);
            setAllFieldsToEmptyString(saleLineInfo1);
            SoftwareOrderInfoB2BSyncRequest.SaleLineInfo saleLineInfoMapperDetail = materiaDetaillMap.get(saleLineInfo.getMATERIAL_CODE());
            saleLineInfo1.setSALES_VOLUMES(saleLineInfoMapperDetail.getSALES_VOLUMES());
            saleLineInfo1.setSALE_AMOUNT_HAS_TAX(String.valueOf(new BigDecimal(saleLineInfoMapperDetail.getSALE_AMOUNT_HAS_TAX()).setScale(2, RoundingMode.HALF_UP)));
            saleLineInfo1.setUNIT_PRICE_HAS_TAX(String.valueOf(new BigDecimal(saleLineInfoMapperDetail.getUNIT_PRICE_HAS_TAX()).setScale(2, RoundingMode.HALF_UP)));
            saleLineInfo1.setLINE_NUM(lineNum.toString());
            String outPutTax = String.valueOf(new BigDecimal(Double.parseDouble(saleLineInfo1.getOUTPUT_TAX_RATE().replace("%", ""))).divide(BigDecimal.valueOf(100)));
            // 算不含税单价
            saleLineInfo1.setUNIT_PRICE_NO_TAX(
                    new BigDecimal(saleLineInfo1.getUNIT_PRICE_HAS_TAX())
                            .divide(new BigDecimal(outPutTax)
                                    .add(BigDecimal.valueOf(1)),2, RoundingMode.HALF_UP)
                            .toString()
            );
            // 算不含税金额
            saleLineInfo1.setSALE_AMOUNT_NO_TAX(
                    new BigDecimal(saleLineInfo1.getSALE_AMOUNT_HAS_TAX())
                            .divide(new BigDecimal(outPutTax)
                                    .add(BigDecimal.valueOf(1)),2, RoundingMode.HALF_UP)
                            .toString()
            );
            // 算税额
            saleLineInfo1.setTAX_AMOUNT(new BigDecimal(saleLineInfo1.getSALE_AMOUNT_HAS_TAX())
                    .subtract(new BigDecimal(saleLineInfo1.getSALE_AMOUNT_NO_TAX()))
                    .toString()
            );
            poAmount = poAmount.add(new BigDecimal(saleLineInfo1.getSALE_AMOUNT_NO_TAX()));
            poAmountTax = poAmountTax.add(new BigDecimal(saleLineInfo1.getSALE_AMOUNT_HAS_TAX()));
            taxSum = taxSum.add(new BigDecimal(saleLineInfo1.getTAX_AMOUNT()));
            String taxRate = saleLineInfoList.get(0).getOUTPUT_TAX_RATE().replace("%", "");
            saleLineInfo1.setOUTPUT_TAX_RATE(taxRate);
            saleLineInfo1.setTotalPrice(null);
            // 设置销售订单状态
            saleLineInfo1.setSALE_ORDER_STATUS(saleOrderStatus);
            saleLineInfoListResult.add(saleLineInfo1);
            lineNum++;
        }

        orderHeaderInfo.setSALE_LINE_INFO(saleLineInfoListResult);


//            Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(id);
        BeanUtils.copyProperties(saleLineInfoList.get(0),orderHeaderInfo);
        orderHeaderInfo.setCORPORATE_NAME("中移物联网公司");
        orderHeaderInfo.setCORPORATE_CODE("*********");
        orderHeaderInfo.setCURRENCY("CNY");
        orderHeaderInfo.setSIGN_CLIENT_TYPE("0");
        orderHeaderInfo.setSETTLEMENT_TYPE("一次性结算");
        orderHeaderInfo.setSALE_ORDER_STATUS(saleOrderStatus);
        orderHeaderInfo.setPOSTING_DATE(DateTimeUtil.formatDate(new Date(),DateTimeUtil.STANDARD_DAY));
        // 省市
//        String orgName = saleLineInfoList.get(0).getOrgName();



        // 测试用
//        orderHeaderInfo.setCITY_NAME("郑州市");
//        orderHeaderInfo.setPROVINCE_NAME("河南省");
//        orderHeaderInfo.setCLIENT_NAME("中国移动通信集团河南有限公司郑州分公司");
//        orderHeaderInfo.setCLIENT_CODE("100702982");


//        if (StringUtils.isNotEmpty(orgName)) {
//            String[] split = orgName.split("-");
//            orderHeaderInfo.setPROVINCE_NAME(split[0]);
//            if (split.length >= 2) {
//                orderHeaderInfo.setCITY_NAME(split[1]);
//                orderHeaderInfo.setCLIENT_NAME("中国移动"+split[1]+"（地市）分公司");
//                if (split.length >= 3) {
//                }
//            }
//        }
        //销售总金额(含税)
        orderHeaderInfo.setSALE_TOTAL_AMOUNT_HAS_TAX(poAmountTax.toString());
        //销售总金额(不含税)
        //税率应该是一样的 随便取一个就行
        orderHeaderInfo.setSALE_TOTAL_AMOUNT_NO_TAX(
                poAmount.toString()
        );
        //销售总税额
        orderHeaderInfo.setSALE_TAX_AMOUNT(taxSum.toString());
        orderHeaderInfoList.add(orderHeaderInfo);



        System.out.println("生成的body: "+ JSONObject.toJSONString(softwareOrderInfoSyncRequest));


        // 请求市场销售支撑系统
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(softwareOrderInfoSyncRequest), headers);

            log.info("请求市场销售支撑系统B2B接口request:{}",JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<String> response = restTemplateHttps.postForEntity(orderSyncUrlB2B, requestEntity, String.class);
            log.info("请求市场销售支撑系统B2B接口response:{}",JSON.toJSONString(response));
            SoftwareOrderInfoSyncResponse response1 = JSON.parseObject(response.getBody(), SoftwareOrderInfoSyncResponse.class);
            if(!response1.getROOT().getBODY().getRETURN_CODE().equals("0")){
                log.info("请求市场销售支撑系统B2B接口失败:{}", response1.getROOT().getBODY().getDETAIL_MSG());
                baseAnswer.setMessage(response1.getROOT().getBODY().getDETAIL_MSG());
                baseAnswer.setStateCode("10004");
            }else{
                Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                // 请求成功后更新订单结算状态
                //结算状态，0-销售草稿（锁定），1-销售草稿， 2-销售审批中， 3-销售已审批， 4-计收完成， 5-订单取消
                //普通订单没交易成功是0 代客下单的都是1 退款了就5
//                if(orderStatus.equals("0") || orderStatus.equals("10")){
//                    order2cAtomInfo.setSettleStatus(0);
//                }else if (orderStatus.equals("7")){
//                    order2cAtomInfo.setSettleStatus(1);
//                }else if (orderStatus.equals("8")){
//                    order2cAtomInfo.setSettleStatus(5);
//                }else{
//                    throw new BusinessException(BaseErrorConstant.OUTER_ORDER_NOT_FOUND, "订单状态错误");
//                }
                order2cAtomInfo.setSettleStatus(Integer.valueOf(saleOrderStatus));
                order2cAtomInfoMapper.updateByExampleSelective(order2cAtomInfo, new Order2cAtomInfoExample().createCriteria()
                        .andOrderIdEqualTo(orderId)
                                .andAtomOfferingClassNotIn(AAndC)
                        .example());
            }
        }catch (Exception e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }


        return baseAnswer;
    }

    @Override
    public BaseAnswer<Void> orderStatusFeedback(SyncCommonRequest syncCommonRequest) {
        BaseAnswer<Void> answer = new BaseAnswer<>();
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        try {
            SignUtils.checkSign(input, sign, osSecretKey);
        } catch (ServicePowerException e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        SoftwareOrderInfoFeedbackParam param = JSON.parseObject(input, SoftwareOrderInfoFeedbackParam.class);
        log.info("市场系统反馈信息：{}", JSON.toJSONString(param));

         Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample().createCriteria()
                .andOrderIdEqualTo(param.getOrderId())
                .example();
        //获取原子订单信息
        List<Order2cAtomInfo> order2cAtomList = order2cAtomInfoMapper.selectByExample(order2cAtomInfoExample);

        if (CollectionUtils.isEmpty(order2cAtomList)) {
            throw new BusinessException(BaseErrorConstant.OUTER_ORDER_NOT_FOUND, "未找到对应的原子订单信息");
        }
        Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
        //1-销售审批中 2-销售已审批 3-计收完成
        //对应过来 结算状态，0-销售草稿（锁定），1-销售草稿， 2-销售审批中， 3-销售已审批， 4-计收完成， 5-订单取消
        if(param.getStatus() == 1){
            order2cAtomInfo.setSettleStatus(2);
        }else if(param.getStatus() == 2){
            order2cAtomInfo.setSettleStatus(3);
        }else if(param.getStatus() == 3){
            order2cAtomInfo.setSettleStatus(4);
        }else{
            throw new BusinessException(BaseErrorConstant.OUTER_ORDER_NOT_FOUND, "订单状态错误");
        }

        order2cAtomInfoMapper.updateByExampleSelective(order2cAtomInfo, new Order2cAtomInfoExample().createCriteria()
                .andOrderIdEqualTo(param.getOrderId())
                .example());


        answer.setStatus(BaseErrorConstant.OUTER_SUCCESS);
        return answer;
    }
    private BigDecimal getRound2Dec (BigDecimal num) {
         return num.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 过滤非数字
     * @param str
     * @return
     */
    private String getNumeric(String str) {
        String regEx="[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    public String encrypt(String password, String strKey) throws
            Exception{
        byte[] keyBytes = Arrays.copyOf(strKey.getBytes("ASCII"), 16);
        SecretKey key = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] cleartext = password.getBytes("UTF-8");
        byte[] ciphertextBytes = cipher.doFinal(cleartext);
        return new String(Hex.encodeHex(ciphertextBytes));
    }

    public static void setAllFieldsToEmptyString(Object obj) {
        try {
            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getType() == String.class) {
                    Object value = field.get(obj);
                    if (value == null) {
                        field.set(obj, "");
                    }
                } else if (field.getType().isArray() && field.getType().getComponentType() == String.class) {
                    Object value = field.get(obj);
                    if (value == null) {
                        field.set(obj, new String[0]);
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    @Override
    public BaseAnswer syncOrderToMarketSystemCutOver(MultipartFile excel) {
        List<String> errMsg = new ArrayList<>();
        try {
            List<SyncOrderToMarketSystemCutOver> orderList = EasyExcel.read(excel.getInputStream(), SyncOrderToMarketSystemCutOver.class, null).sheet(0).headRowNumber(1).doReadSync();
            for(SyncOrderToMarketSystemCutOver item : orderList){
                List orderIdList = new ArrayList();
                orderIdList.add(item.getOrderId());
                //看那边有没有并发限制
                executor.execute(() -> syncOrderToMarketSystem(orderIdList));
            }
        } catch (IOException e) {
            // 该服务仅供内部开发人员自己调用，异常不用理会
            throw new RuntimeException(e);
        }
        if (errMsg.isEmpty()) {
            return BaseAnswer.success("");
        } else {
            return BaseAnswer.success(errMsg);
        }
    }

    @Override
    public void syncOrderToMarketSystemLastMonth() {
        // 获取上月的起始日期和结束日期
        Calendar calendar = Calendar.getInstance();

        // 设置为上月的第一天 0 点
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startDate = calendar.getTime(); // 上月初 0 点

        // 设置为现在 为了防止后续变为交易成功后时间跨越的情况
        Date endDate = new Date();

        List <String> orderIdList = k3ProMaterialMapperExt.getOrderIdLastMonth(startDate,endDate);
        log.info("orderIdList is :{}",orderIdList);
        for(String id : orderIdList){
            List<String> orderIds = new ArrayList<>();
            orderIds.add(id);
            executor.execute(() -> syncOrderToMarketSystem(orderIds));
        }
    }

    @Override
    public BaseAnswer syncOrderToMarketSystemCutOverClear(MultipartFile excel) {
        List<String> errMsg = new ArrayList<>();
        try {
            List<SyncOrderToMarketSystemCutOver> orderList = EasyExcel.read(excel.getInputStream(), SyncOrderToMarketSystemCutOver.class, null).sheet(0).headRowNumber(1).doReadSync();
            for(SyncOrderToMarketSystemCutOver item : orderList){
                //看那边有没有并发限制
                executor.execute(() -> {
                    Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(item.getOrderId());
                    order2cInfo.setBillNoNumber(null);
                    order2cInfoMapper.updateByPrimaryKey(order2cInfo);
                });
            }
        } catch (IOException e) {
            // 该服务仅供内部开发人员自己调用，异常不用理会
            throw new RuntimeException(e);
        }
        if (errMsg.isEmpty()) {
            return BaseAnswer.success("");
        } else {
            return BaseAnswer.success(errMsg);
        }
    }

    @Override
    public BaseAnswer<Void> syncOrderToMarketSystemB2BMulti(List<String> orderIds) {
        for(String orderId : orderIds){
            //看那边有没有并发限制
            executor.execute(() -> syncOrderToMarketSystemB2B(orderId));
        }
        return BaseAnswer.success(null);
    }
}
