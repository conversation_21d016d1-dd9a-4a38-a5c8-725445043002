package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;

import java.util.List;

public interface BRMService {

    /**
     * 上传文件到brm
     */
    void sftpUploadBRM(IOPUploadParam param);

    /**
     * 导出BRM数据
     *
     * @param param 导出参数
     * @return 文件路径
     */
    String exportBRMData();


    /**
     * 生成BRM详细数据文本文件
     *
     * @param fileName 文件名前缀
     * @param isWrite 是否写入文件
     * @return 生成的文件名列表
     */
    List<String> generateDetailTxtFiles(String fileName, Boolean isWrite);

    /**
     * 上传BRM详细数据文件到SFTP服务器
     *
     * @param fileNames 要上传的文件名列表
     */
    void uploadDetailFilesToSftp(List<String> fileNames);
}
