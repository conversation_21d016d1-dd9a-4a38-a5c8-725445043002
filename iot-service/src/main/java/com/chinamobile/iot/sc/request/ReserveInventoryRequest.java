package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/15 17:49
 * @Description: 库存预占
 */
@Data
public class ReserveInventoryRequest {
    private List<InventoryInfo> inventoryInfo;

    /**
     * 提单人归属区域
     */
    private String regionId;


    @Data
    public static class InventoryInfo{
        /**
         * 销售商品信息
         */
        private List<SpuOfferingInfo> spuOfferingInfo;

        /**
         * 预占流水号
         */
        private String bookId;
    }

    @Data
    public  static class SpuOfferingInfo {
        /**
         * 销售商品编码
         */
        private String spuOfferingCode;
        /**
         * 预占流水号
         */
        private String offeringClass;
        /**
         * DICT范式产品线条
         */
        private String dictProductLines;

        /**
         * 规格商品信息
         */
        private List<SkuOfferingInfo> skuOfferingInfo;
    }

    @Data
    public  static class SkuOfferingInfo {
        /**
         * 规格商品编码
         */
        private String offeringCode;
        /**
         * 预占数量
         */
        private Long quantity;


        /**
         * 规格商品信息
         */
        private List<AtomOfferingInfo> atomOfferingInfo;
    }

    @Data
    public  static class AtomOfferingInfo {
        /**
         * 原子商品编码
         */
        private String offeringCode;
        /**
         * 预占数量
         */
        private Long quantity;
        /**
         * 预占额度
         */
        private String limit;
    }
}
