package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.KafkaTopic;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ServiceConfig;
import com.chinamobile.iot.sc.constant.productflow.ProductFlowAttachmentTypeEnum;
import com.chinamobile.iot.sc.dao.DocumentMapper;
import com.chinamobile.iot.sc.dao.DocumentVisibleRangeMapper;
import com.chinamobile.iot.sc.dao.ext.DocumentMapperExt;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.entity.user.RoleInfo;
import com.chinamobile.iot.sc.enums.log.HomePageEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.SystemInstallEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.AddMessageParam;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Document;
import com.chinamobile.iot.sc.pojo.DocumentExample;
import com.chinamobile.iot.sc.pojo.DocumentVisibleRange;
import com.chinamobile.iot.sc.pojo.DocumentVisibleRangeExample;
import com.chinamobile.iot.sc.pojo.mapper.AnnouncementDO;
import com.chinamobile.iot.sc.pojo.param.DocumentListParam;
import com.chinamobile.iot.sc.pojo.vo.AnnouncementVO;
import com.chinamobile.iot.sc.pojo.vo.DocumentListVO;
import com.chinamobile.iot.sc.service.DocumentService;
import com.chinamobile.iot.sc.service.IStorageService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.MessageCenterService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;
import static com.chinamobile.iot.sc.util.DateUtils.DEFAULT_DATETIME_FORMAT;

@Slf4j
@Service
public class DocumentServiceImpl implements DocumentService {


    @Resource
    private DocumentMapper documentMapper;

    @Autowired
    private LogService logService;

    @Resource
    private IStorageService storageService;

    @Resource
    private DocumentMapperExt documentMapperExt;

    @Resource
    private DocumentVisibleRangeMapper documentVisibleRangeMapper;

    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate;

    @Resource
    private UserFeignClient userFeignClient;

   /* @Resource
    private IotFeignClient iotFeignClient;*/

    @Resource
    private MessageCenterService messageCenterService;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Autowired
    private ServiceConfig serviceConfig;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer saveDocument(MultipartFile file, String name, String visibleRange, Boolean isRepetitionName, LoginIfo4Redis loginIfo4Redis) {
        //应信安要求，限制非法文件上传
        String filename = file.getOriginalFilename();
        log.info("uploadFile filename = {}", filename);
        if(StringUtils.isEmpty(filename)){
            throw new BusinessException("500", "文件名不能为空");
        }
        int lastDotIndex = filename.lastIndexOf(".");
        if(lastDotIndex == -1 || lastDotIndex == filename.length() - 1){
            throw new BusinessException("500", "文件名格式错误");
        }
        String suffix = filename.substring(lastDotIndex+1).trim();
        if(suffix.equals("php") || suffix.equals("jsp") || suffix.equals("jar")
                || suffix.equals("asp") || suffix.equals("c") || suffix.equals("java") || suffix.equals("js")){
            throw new BusinessException("500", "不支持非法文件上传");
        }
        if (!isRepetitionName) {
            //进行名称校验
            List<Document> documents = documentMapper.selectByExample(new DocumentExample().createCriteria().andNameEqualTo(name).example());
            if (CollectionUtils.isNotEmpty(documents)) {
                throw new BusinessException(BaseErrorConstant.ADD_SYSTEM_DOCUMENT_NAME_EXIST.getStateCode(), BaseErrorConstant.ADD_SYSTEM_DOCUMENT_NAME_EXIST.getMessage());
            }else {
                addDocument(file,name,visibleRange,loginIfo4Redis);
            }
        } else {
            addDocument(file,name,visibleRange,loginIfo4Redis);
        }
        return new BaseAnswer<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer updateDocument(String id, MultipartFile file, String name, String visibleRange, Boolean isRepetitionName, LoginIfo4Redis loginIfo4Redis) {
        //应信安要求，限制非法文件上传
        if (file != null) {
        String filename = file.getOriginalFilename();
        log.info("uploadFile filename = {}", filename);
        if(StringUtils.isEmpty(filename)){
            throw new BusinessException("500", "文件名不能为空");
        }
        int lastDotIndex = filename.lastIndexOf(".");
        if(lastDotIndex == -1 || lastDotIndex == filename.length() - 1){
            throw new BusinessException("500", "文件名格式错误");
        }
        String suffix = filename.substring(lastDotIndex+1).trim();
        if(suffix.equals("php") || suffix.equals("jsp") || suffix.equals("jar")
                || suffix.equals("asp") || suffix.equals("c") || suffix.equals("java") || suffix.equals("js")){
            throw new BusinessException("500", "不支持非法文件上传");
        }
        }
        Document documentOld = documentMapper.selectByPrimaryKey(id);
        String nameOld = documentOld.getName();
        Integer fileSize = documentOld.getFileSize();
        if (!isRepetitionName && StringUtils.isNotEmpty(name)) {
            //进行名称校验
            if (!name.equals(nameOld)) {
                List<Document> documents = documentMapper.selectByExample(new DocumentExample().createCriteria().andNameEqualTo(name).example());
                if (CollectionUtils.isNotEmpty(documents)) {
                    throw new BusinessException(BaseErrorConstant.ADD_SYSTEM_DOCUMENT_NAME_EXIST.getStateCode(), BaseErrorConstant.ADD_SYSTEM_DOCUMENT_NAME_EXIST.getMessage());
                }else {
                    updateDocument(documentOld,file,name,visibleRange,fileSize,loginIfo4Redis);
                }
            }else {
                updateDocument(documentOld,file,name,visibleRange,fileSize,loginIfo4Redis);
            }
        } else {
            updateDocument(documentOld,file,name,visibleRange,fileSize,loginIfo4Redis);
        }
        return new BaseAnswer<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer deleteDocument(String id) {
        Document document = documentMapper.selectByPrimaryKey(id);
        String name = document.getName();
        DocumentVisibleRangeExample documentVisibleRangeExample = new DocumentVisibleRangeExample();
        DocumentVisibleRangeExample.Criteria criteria = documentVisibleRangeExample.createCriteria();
        criteria.andDocumentIdEqualTo(id).example();
        List<DocumentVisibleRange> documentVisibleRanges = documentVisibleRangeMapper.selectByExample(documentVisibleRangeExample);
        String visibleRange = documentVisibleRanges.stream().map(DocumentVisibleRange::getVisibleRange).collect(Collectors.joining(","));
        //String visibleRange = document.getVisibleRange();
        Integer fileSize = document.getFileSize();
        documentMapper.deleteByPrimaryKey(id);

        documentVisibleRangeMapper.deleteByExample(documentVisibleRangeExample);
        //文件系统删除
        try {
            storageService.delete(document.getFileKey());
        } catch (Exception e) {
            log.error("删除文件出错", e);
        }
        String[] split = visibleRange.split(",");
        List<RoleInfo> roleInfos = new ArrayList<>();
        for (String roleId : split) {
            BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
            if(roleInfoBase == null || roleInfoBase.getData() == null){
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
            }
            roleInfos.add(roleInfoBase.getData());
        }
        String collectNew = roleInfos.stream().map(RoleInfo::getName).collect(Collectors.joining(","));
        //记录日志
        StringBuilder logContent = new StringBuilder();
        logContent.append("【删除】").append("\n");
        logContent.append("文件名称").append(name).append("\n");
        logContent.append("可见范围").append(collectNew).append("\n");
        logContent.append("文件大小").append(fileSize).append("\n");
        logContent.append("更新时间 ").append(DateTimeUtil.formatDate(document.getUpdateTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT));
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                SystemInstallEnum.DOCUMENT_MANAGE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);

        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<Document> selectDocumentDetail(String id) {
        Document document = documentMapper.selectByPrimaryKey(id);
        /*String visibleRange = document.getVisibleRange();
        String[] split = visibleRange.split(",");
        List<RoleInfo> roleInfos = new ArrayList<>();
        for (String roleId : split) {
            BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
            if(roleInfoBase == null || roleInfoBase.getData() == null){
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
            }
            roleInfos.add(roleInfoBase.getData());
        }
        String collectNew = roleInfos.stream().map(RoleInfo::getName).collect(Collectors.joining(","));
        document.setVisibleRange(collectNew);*/
        DocumentVisibleRangeExample documentVisibleRangeExample = new DocumentVisibleRangeExample();
        DocumentVisibleRangeExample.Criteria criteria = documentVisibleRangeExample.createCriteria();
        criteria.andDocumentIdEqualTo(id).example();
        List<DocumentVisibleRange> documentVisibleRanges = documentVisibleRangeMapper.selectByExample(documentVisibleRangeExample);
        String visibleRange = documentVisibleRanges.stream().map(DocumentVisibleRange::getVisibleRange).collect(Collectors.joining(","));
        document.setVisibleRange(visibleRange);


        BaseAnswer<Data4User> creatUser = userFeignClient.userInfoById(document.getCreateUserId());
        if (creatUser == null || !SUCCESS.getStateCode().equals(creatUser.getStateCode())) {
            throw new BusinessException("10004", "查询用户信息错误");
        }
        Data4User dataCreatUser = creatUser.getData();
        String userCreatName = dataCreatUser.getName();
        document.setCreateUserId(userCreatName);

        BaseAnswer<Data4User> updateUser = userFeignClient.userInfoById(document.getUpdateUserId());
        if (updateUser == null || !SUCCESS.getStateCode().equals(updateUser.getStateCode())) {
            throw new BusinessException("10004", "查询用户信息错误");
        }
        Data4User dataUpdateUser = updateUser.getData();
        String userUpdateName = dataUpdateUser.getName();
        document.setUpdateUserId(userUpdateName);
        BaseAnswer<Document> baseAnswer = new BaseAnswer<>();
        baseAnswer.setData(document);
        return baseAnswer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer downloadDocumentFile(String id,String operate,LoginIfo4Redis loginIfo4Redis) {

        Document document = documentMapper.selectByPrimaryKey(id);
        //消息中心提醒用户
        AddMessageParam messageParam = new AddMessageParam();
        messageParam.setModule(ModuleEnum.SYSTEM_INSTALL.name);
        messageParam.setContent(" 首页文档，请点击下载");
        messageParam.setType("文档下载");
        messageParam.setUserId(loginIfo4Redis.getUserId());
        messageParam.setFileKey(document.getFileKey());
        messageParam.setUrl(document.getFileUrl());
        messageParam.setSource(1);
        messageCenterService.addMessage(messageParam);

        String phone = loginIfo4Redis.getPhone();
            //发送短信提示用户
            if (StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
                Msg4Request msg4Request = new Msg4Request();
                msg4Request.setTemplateId(serviceConfig.getNewMessageSms());
                List<String> mobiles = new ArrayList<>();
                mobiles.add(phone);
                msg4Request.setMobiles(mobiles);
                smsFeignClient.asySendMessage(msg4Request);
            }
        //记录日志
        String visibleRange = documentVisibleRangeMapper.selectByExample(new DocumentVisibleRangeExample().createCriteria()
                .andDocumentIdEqualTo(id).example()).stream().map(DocumentVisibleRange::getVisibleRange).collect(Collectors.joining(","));
        String[] split = visibleRange.split(",");
        List<RoleInfo> roleInfos = new ArrayList<>();
        for (String roleId : split) {
            BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
            if(roleInfoBase == null || roleInfoBase.getData() == null){
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
            }
            roleInfos.add(roleInfoBase.getData());
        }
        String collectNew = roleInfos.stream().map(RoleInfo::getName).collect(Collectors.joining(","));
        StringBuilder logContent = new StringBuilder();
        logContent.append("【下载】").append("\n");
        logContent.append("文件名称").append(document.getName()).append("\n");
        logContent.append("可见范围").append(collectNew).append("\n");
        logContent.append("文件大小").append(document.getFileSize()).append("\n");
        logContent.append("更新时间").append(DateTimeUtil.formatDate(document.getUpdateTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT));
        if ("0".equals(operate)){
            //首页下载
            logService.recordOperateLog(ModuleEnum.HOME_PAGE.code,
                    HomePageEnum.DOCUMENT_OPERATE.code,
                    logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        }else if ("1".equals(operate)){
            logService.recordOperateLog(ModuleEnum.HOME_PAGE.code,
                    HomePageEnum.MORE_DOCUMENT_OPERATE.code,
                    logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        }

        //通过kafka发送websocket提示前端更新消息未读数
        ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
        kafkaTemplate.send(record);
        return new BaseAnswer();
    }

    @Override
    public BaseAnswer<PageData<DocumentListVO>> getDocumentList(DocumentListParam param ) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        PageData<DocumentListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);

        PageHelper.startPage(pageNum,pageSize);
        List<DocumentListVO> documentList = documentMapperExt.getDocumentList(param);
        if(CollectionUtils.isEmpty(documentList)){
            return BaseAnswer.success(documentList);
        }
        PageInfo<DocumentListVO> pageInfo = new PageInfo<>(documentList);
        pageData.setCount(pageInfo.getTotal());
        for (DocumentListVO documentListVO : documentList) {
            String updateUserName = documentListVO.getUpdateUserName();
            String visibleRange = documentListVO.getVisibleRange();
            String[] split = visibleRange.split(",");
            List<RoleInfo> roleInfos = new ArrayList<>();
            for (String roleId : split) {
                BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
                if(roleInfoBase == null || roleInfoBase.getData() == null){
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
                }
                roleInfos.add(roleInfoBase.getData());
            }
            String collectNew = roleInfos.stream().map(RoleInfo::getName).collect(Collectors.joining(","));
            documentListVO.setVisibleRange(collectNew);
            BaseAnswer<Data4User> baseAnswer = userFeignClient.userInfoById(updateUserName);
            if (baseAnswer == null || !SUCCESS.getStateCode().equals(baseAnswer.getStateCode())) {
                throw new BusinessException("10004", "查询用户信息错误");
            }
            Data4User data4User = baseAnswer.getData();
            String userName = data4User.getName();
            documentListVO.setUpdateUserName(userName);
        }
        pageData.setData(documentList);
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer<PageData<DocumentListVO>> getDocumentHomeList(DocumentListParam param,LoginIfo4Redis loginIfo4Redis) {
        String roleIdHome = loginIfo4Redis.getRoleId();
        param.setRoleId(roleIdHome);
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        PageData<DocumentListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);

        PageHelper.startPage(pageNum,pageSize);
        List<DocumentListVO> documentList = documentMapperExt.getDocumentList(param);
        if(CollectionUtils.isEmpty(documentList)){
            return BaseAnswer.success(documentList);
        }
        PageInfo<DocumentListVO> pageInfo = new PageInfo<>(documentList);
        pageData.setCount(pageInfo.getTotal());
        for (DocumentListVO documentListVO : documentList) {
            String updateUserName = documentListVO.getUpdateUserName();
            String visibleRange = documentListVO.getVisibleRange();
            String[] split = visibleRange.split(",");
            List<RoleInfo> roleInfos = new ArrayList<>();
            for (String roleId : split) {
                BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
                if(roleInfoBase == null || roleInfoBase.getData() == null){
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
                }
                roleInfos.add(roleInfoBase.getData());
            }
            String collectNew = roleInfos.stream().map(RoleInfo::getName).collect(Collectors.joining(","));
            documentListVO.setVisibleRange(collectNew);
            BaseAnswer<Data4User> baseAnswer = userFeignClient.userInfoById(updateUserName);
            if (baseAnswer == null || !SUCCESS.getStateCode().equals(baseAnswer.getStateCode())) {
                throw new BusinessException("10004", "查询用户信息错误");
            }
            Data4User data4User = baseAnswer.getData();
            String userName = data4User.getName();
            documentListVO.setUpdateUserName(userName);
        }
        pageData.setData(documentList);
        return BaseAnswer.success(pageData);
    }

    /**
     * 新增文档参数
     * @param file
     * @param name
     * @param visibleRange
     * @param loginIfo4Redis
     */
    public void addDocument(MultipartFile file, String name, String visibleRange, LoginIfo4Redis loginIfo4Redis){
        if (file == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件不能为空");
        }
        long size = file.getSize();
        if (size > 100 * 1024 * 1024) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件最大限制100M");
        }
        String userId = loginIfo4Redis.getUserId();
        try {
            InputStream fileInputStream = file.getInputStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 将FileOutputStream的内容转移到ByteArrayOutputStream
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fileInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }
            // 关闭流
            fileInputStream.close();
            String traceId = String.valueOf(System.currentTimeMillis());
            //上传文件到对象存
            String filename = file.getOriginalFilename();
            String filenamePath = traceId + "_" + userId+"_"+filename;
            String filePathName = String.format("%s/%s", "OsSystemDocument", filenamePath);
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(filePathName);
            byteArrayUpload.setBytes(byteArrayOutputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            log.info("resultBaseAnswer:{}上传系统文档返回数据", JSONObject.toJSONString(resultBaseAnswer));
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                log.info("addSystemDocumentName 上传系统文档错误信息,请联系管理员 retCode = {}", resultBaseAnswer.getStateCode());
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传系统文档错误信息,请联系管理员");
            }
            log.info("traceId:{}上传系统文档完成", traceId);
            Date date = new Date();
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            Document document = new Document();
            String id = BaseServiceUtils.getId();
            document.setId(id);
            document.setName(name);
            document.setFileName(filename);
            document.setFileStorageName(filenamePath);
            document.setFileSize(Integer.valueOf(String.valueOf(size)));
            document.setFileKey(fileKey);
            document.setFileUrl(outerUrl);
            //document.setVisibleRange(visibleRange);
            document.setCreateUserId(userId);
            document.setUpdateUserId(userId);
            document.setCreateTime(date);
            document.setUpdateTime(date);
            documentMapper.insert(document);
            //记录日志
            String[] split = visibleRange.split(",");
            List<RoleInfo> roleInfos = new ArrayList<>();
            List<DocumentVisibleRange> documentVisibleRanges = new ArrayList<>();
            for (String roleId : split) {
                BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
                if(roleInfoBase == null || roleInfoBase.getData() == null){
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
                }
                roleInfos.add(roleInfoBase.getData());
                DocumentVisibleRange documentVisibleRange = new DocumentVisibleRange();
                documentVisibleRange.setId(BaseServiceUtils.getId());
                documentVisibleRange.setDocumentId(id);
                documentVisibleRange.setVisibleRange(roleId);
                documentVisibleRange.setCreateTime(date);
                documentVisibleRange.setUpdateTime(date);
                documentVisibleRanges.add(documentVisibleRange);
            }
            documentVisibleRangeMapper.batchInsert(documentVisibleRanges);

            String collect = roleInfos.stream().map(RoleInfo::getName).collect(Collectors.joining(","));
            StringBuilder logContent = new StringBuilder();
            logContent.append("【新建】").append("\n");
            logContent.append("文件名称").append(name).append("\n");
            logContent.append("可见范围 ").append(collect).append("\n");
            logContent.append("文件大小").append(file.getSize()).append("\n");
            logContent.append("创建时间 ").append(DateTimeUtil.formatDate(date,DateTimeUtil.DEFAULT_DATE_DEFAULT));
            logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                    SystemInstallEnum.DOCUMENT_MANAGE.code,
                    logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("10008", "上传文档到存储起信息异常:" + e);
        }
    }

    /**
     * 修改文档参数
     * @param documentOld
     * @param file
     * @param name
     * @param visibleRange
     * @param fileSize
     * @param loginIfo4Redis
     */
    public void updateDocument(Document documentOld, MultipartFile file, String name, String visibleRange,Integer fileSize, LoginIfo4Redis loginIfo4Redis){

        Document document = new Document();
        Date date = new Date();
        String userId = loginIfo4Redis.getUserId();
        String fileKey = documentOld.getFileKey();
        String outerUrl = documentOld.getFileUrl();
        String filePath = documentOld.getFileStorageName();
        if (file != null) {
            //删除原有文件
            //文件系统删除
            try {
                storageService.delete(fileKey);
            } catch (Exception e) {
                log.error("删除文件出错", e);
            }

            long size = file.getSize();
            if (size > 100 * 1024 * 1024) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件最大限制100M");
            }

            try {
                InputStream fileInputStream = file.getInputStream();
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                // 将FileOutputStream的内容转移到ByteArrayOutputStream
                byte[] buffer = new byte[1024];
                int length;
                while ((length = fileInputStream.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, length);
                }
                // 关闭流
                fileInputStream.close();
                String traceId = String.valueOf(System.currentTimeMillis());
                //上传文件到对象存
                filePath =traceId + "_" + userId+"_"+file.getOriginalFilename();
                String filePathName = String.format("%s/%s", "OsSystemDocument", filePath);
                ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
                byteArrayUpload.setFileName(filePathName);
                byteArrayUpload.setBytes(byteArrayOutputStream.toByteArray());
                BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);

                if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                    log.info("addSystemDocumentName 上传系统文档错误信息,请联系管理员 retCode = {}", resultBaseAnswer.getStateCode());
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传系统文档错误信息,请联系管理员");
                }
                log.info("{}上传系统文档完成", traceId);

                UpResult upResult = resultBaseAnswer.getData();
                outerUrl = upResult.getOuterUrl();
                fileKey = upResult.getKey();
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException("10008", "上传文档到存储起信息异常:" + e);
            }
        }
        document.setId(documentOld.getId());
        if (StringUtils.isNotEmpty(name)){
            document.setName(name);
        }
        if (file != null){
            document.setFileName(file.getOriginalFilename());
            document.setFileStorageName(filePath);
            document.setFileSize(Integer.valueOf(String.valueOf(file.getSize())));
            document.setFileKey(fileKey);
            document.setFileUrl(outerUrl);
        }
        document.setUpdateUserId(userId);
        document.setUpdateTime(date);
        documentMapper.updateByPrimaryKeySelective(document);
        String oldVisibleRange = documentVisibleRangeMapper.selectByExample(new DocumentVisibleRangeExample().createCriteria()
                .andDocumentIdEqualTo(documentOld.getId()).example()).stream().map(DocumentVisibleRange::getVisibleRange).collect(Collectors.joining(","));
        if (StringUtils.isNotEmpty(visibleRange)){
            //删除原来保存的
            documentVisibleRangeMapper.deleteByExample(new DocumentVisibleRangeExample().createCriteria()
                    .andDocumentIdEqualTo(documentOld.getId()).example());
            //zai 重新新增
            String[] split = visibleRange.split(",");
            List<RoleInfo> roleInfos = new ArrayList<>();
            List<DocumentVisibleRange> documentVisibleRanges = new ArrayList<>();
            for (String roleId : split) {
                BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
                if(roleInfoBase == null || roleInfoBase.getData() == null){
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
                }
                roleInfos.add(roleInfoBase.getData());
                DocumentVisibleRange documentVisibleRange = new DocumentVisibleRange();
                documentVisibleRange.setId(BaseServiceUtils.getId());
                documentVisibleRange.setDocumentId(documentOld.getId());
                documentVisibleRange.setVisibleRange(roleId);
                documentVisibleRange.setCreateTime(date);
                documentVisibleRange.setUpdateTime(date);
                documentVisibleRanges.add(documentVisibleRange);
            }
            documentVisibleRangeMapper.batchInsert(documentVisibleRanges);
            //document.setVisibleRange(visibleRange);
        }

        //记录日志
        StringBuilder logContent = new StringBuilder();
        logContent.append("【编辑】").append("\n");
        if (!documentOld.getName().equals(name)){
            logContent.append("文件名称由").append(documentOld.getName()).append("修改为").append(name).append("\n");
        }


        if (!visibleRange.equals(oldVisibleRange)){
            String[] split = visibleRange.split(",");
            List<RoleInfo> roleInfos = new ArrayList<>();
            for (String roleId : split) {
                BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
                if(roleInfoBase == null || roleInfoBase.getData() == null){
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
                }
                roleInfos.add(roleInfoBase.getData());
            }
            String collectNew = roleInfos.stream().map(RoleInfo::getName).collect(Collectors.joining(","));

            String[] splitOld = oldVisibleRange.split(",");
            List<RoleInfo> roleInfosOld = new ArrayList<>();
            for (String roleId : splitOld) {
                BaseAnswer<RoleInfo> roleInfoBase = userFeignClient.getRoleInfoByRoleIdMessage(roleId);
                if(roleInfoBase == null || roleInfoBase.getData() == null){
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"系统角色id:"+roleId+"不存在");
                }
                roleInfosOld.add(roleInfoBase.getData());
            }
            String collectOld = roleInfosOld.stream().map(RoleInfo::getName).collect(Collectors.joining(","));
            logContent.append("可见范围由").append(collectOld).append("修改为").append(collectNew).append("\n");
        }
        if (file != null){
            logContent.append("文件大小由").append(fileSize).append("修改为").append(file.getSize()).append("\n");
        }
        logContent.append("更新时间 ").append(DateTimeUtil.formatDate(date,DateTimeUtil.DEFAULT_DATE_DEFAULT));
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                SystemInstallEnum.DOCUMENT_MANAGE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
    }
}
