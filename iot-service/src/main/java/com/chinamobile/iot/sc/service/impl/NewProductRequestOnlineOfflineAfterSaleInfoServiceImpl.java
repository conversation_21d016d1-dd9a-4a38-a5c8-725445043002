package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineAfterSaleInfoMapper;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfo;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfoExample;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineAfterSaleInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架售后信息service实现类
 */
@Service
public class NewProductRequestOnlineOfflineAfterSaleInfoServiceImpl implements NewProductRequestOnlineOfflineAfterSaleInfoService {

    @Resource
    private NewProductRequestOnlineOfflineAfterSaleInfoMapper newProductRequestOnlineOfflineAfterSaleInfoMapper;

    @Override
    public void saveAfterSaleInfo(NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo) {
        newProductRequestOnlineOfflineAfterSaleInfoMapper.insert(afterSaleInfo);
    }

    @Override
    public void updateAfterSaleInfoByRequestIdAndComboInfoId(NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo) {
        NewProductRequestOnlineOfflineAfterSaleInfoExample example = new NewProductRequestOnlineOfflineAfterSaleInfoExample();
        example.createCriteria()
                .andComboInfoIdEqualTo(afterSaleInfo.getComboInfoId())
                .andNewProductRequestIdEqualTo(afterSaleInfo.getNewProductRequestId());
        newProductRequestOnlineOfflineAfterSaleInfoMapper.updateByExampleSelective(afterSaleInfo,example);
    }

    @Override
    public NewProductRequestOnlineOfflineAfterSaleInfo getAfterSaleInfoByRequestIdAndComboInfoId(String newProductRequestId, String comboInfoId) {
        NewProductRequestOnlineOfflineAfterSaleInfoExample example = new NewProductRequestOnlineOfflineAfterSaleInfoExample();
        example.createCriteria()
                .andNewProductRequestIdEqualTo(newProductRequestId)
                .andComboInfoIdEqualTo(comboInfoId);
        List<NewProductRequestOnlineOfflineAfterSaleInfo> afterSaleInfoList = newProductRequestOnlineOfflineAfterSaleInfoMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(afterSaleInfoList)){
            return afterSaleInfoList.get(0);
        }else {
            return null;
        }
    }
}
