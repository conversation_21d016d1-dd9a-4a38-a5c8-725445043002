package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.QlyOrderSn;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/18
 * @description 千里眼订单接口类
 */
public interface QlyOrderService {


    /**
     * 千里眼设备开通
     */
    BaseAnswer<List<String>> turnOnDevices(String orderId,String phone,List<String> chargeCodeList,List<String> devicesSns
            ,String areaCode, String cityCode, String countyCode);

    BaseAnswer<Void> cancelDevices(String orderId, String phone, List<String> chargeCodeList,String areaCode, String cityCode, String countyCode);

    BaseAnswer<List<String>> turnOnDevices(String orderId,List<String> devicesSns);

    BaseAnswer<Void> cancelDevices(String orderId);
    void qlyOrderImport(LoginIfo4Redis loginIfo4Redis, MultipartFile file);

    void exportFailedSn(String orderId,HttpServletResponse response) throws IOException;

    BaseAnswer<List<QlyOrderSn>> getImeiList(String orderId);
}
