package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.ChannelDataWashSucceedDTO;
import com.chinamobile.iot.sc.pojo.entity.UserMiniProgram;
import com.chinamobile.iot.sc.request.order2c.OrderInfoDTO;
import com.chinamobile.iot.sc.request.order2c.SpuOfferingInfoDTO;
import com.chinamobile.iot.sc.request.product.NavigationInfoDTO;
import com.chinamobile.iot.sc.request.product.SkuOfferingInfoDTO;
import com.chinamobile.iot.sc.request.sku.SkuOfferingInfoMDTO;
import com.chinamobile.iot.sc.request.sku.SkuReleaseTargetDTO;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19
 * @description gio埋点
 */
public interface GioBurialPointService {

     void sendAtomMsg(AtomOfferingInfo remoteAtomInfo,AtomStdService atomStdService);
     void sendSkuMsg(String spuOfferingCode, List<NavigationInfo> navigationInfoList);
     void sendSkuMsg(SpuOfferingInfo spuOfferingInfo, List<SpuSaleLabelHistory> spuSaleLabelHistoryList);
     void sendSkuMsg(SkuOfferingInfo skuOfferingInfo, SkuOfferingInfoMDTO remoteSkuInfo);

     void sendSkuMsg(SkuOfferingInfo skuOfferingInfo, SkuOfferingInfoDTO remoteSkuInfo, SpuOfferingInfo spuOfferingInfo,
               String spuVersion,String spuOfferingClass,List<SpuSaleLabelHistory> spuSaleLabelHistoryList,List<NavigationInfoDTO> navigationInfoList);

     void productShelfAnalysis(SkuOfferingInfo skuOfferingInfo, AtomOfferingInfo atomOfferingInfo,
               List<SkuReleaseTargetDTO> releaseTargetList, String userId);

     void sendH5ProductOrderCreateMsg(OrderInfoDTO orderInfo, SpuOfferingInfoDTO spuOfferingInfoDTO,
               com.chinamobile.iot.sc.request.order2c.SkuOfferingInfoDTO SkuOfferingInfoDTO, String atomVersion,
               AtomOfferingInfo originAtomInfo,Order2cAtomInfo order2cAtomInfo);

     void sendOrderDimensionalityMsg(OrderInfoDTO orderInfo, Order2cInfo order2cInfo, Integer innerStatus);

     void sendOrderDimensionalityMsg(List<ChannelDataWashSucceedDTO> channelDataList);

     void sendOrderDimensionalityMsgFromGrid(String orderId, String gridProvince, String gridCity, String gridDistrict, String gridName);

     void getUserInfo() throws IOException;

     void getSkuMsg() throws IOException;

     void getAtomMsg() throws IOException;

     void getOrderMsg(String startTime,  String endTime) throws IOException, ParseException;

     void sendH5OrderCreatedHDMsg(OrderInfoDTO orderInfo, Order2cInfo order2cInfo, List<Order2cAtomInfo> atomInfos);

     void sendH5OrderPndingInvoice(OrderInfoDTO orderInfo,String time);

     void sendUserMsg(UserMiniProgram userMiniProgram);

     void getOrderCreated( String startTime,  String endTime) throws IOException, ParseException;

     void getOrderPnding(String startTime, String endTime) throws IOException, ParseException;

     void getPrudctOrderPnding(String startTime, String endTime) throws IOException, ParseException;

     void getProductOrderCreate(String startTime,String endTime) throws IOException, ParseException;

     void getH5ProductLaunch(String userId) throws IOException, ParseException;
     void getH5Userregistration(String userId) throws IOException, ParseException;

     void sendH5Userregistration(UserMiniProgram userMiniProgram);



}
