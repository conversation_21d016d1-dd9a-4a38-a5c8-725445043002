package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.OrderRocTypeEnum;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.constant.RefundTypeEnum;
import com.chinamobile.iot.sc.dao.ExHandleInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cAtomHistoryMapper;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cRocInfoMapper;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.exhandle.ExHandleInfo;
import com.chinamobile.iot.sc.request.exhandle.Request4ExHandleAdd;
import com.chinamobile.iot.sc.request.exhandle.Request4ExHandleUpdate;
import com.chinamobile.iot.sc.service.IExHandleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: ExHandleServiceImpl
 * @description: 异常处理订单Service实现类
 * @author: zyj
 * @create: 2022/1/28 15:09
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class ExHandleServiceImpl implements IExHandleService {

    private static final String ORDER_2C_TYPE = "0";
    private static final String ORDER_ROC_TYPE = "1";
    @Resource
    private ExHandleInfoMapper exHandleInfoMapper;
    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;
    @Resource
    private Order2cRocInfoMapper order2cRocInfoMapper;
    @Resource
    private Order2cAtomHistoryMapper order2cAtomHistoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> addExHandleInfo(Request4ExHandleAdd request, String userId) {
        Date now = new Date();
        //订单历史记录
        List<Order2cAtomHistory> order2cAtomHistoryList = new ArrayList<>();
        Order2cAtomInfo order2cAtomInfo = atomOrderInfoMapper.selectByPrimaryKey(request.getAtomOrderId());
        //校验订单是否存在
        if(ObjectUtils.isEmpty(order2cAtomInfo)){
            throw new BusinessException(StatusConstant.NO_ORDER);
        }
        String orderId = request.getOrderId();
        //需要对所有非X和C原子订单进行操作
        List<String> sAndC = new ArrayList<>();
        sAndC.add(AtomOfferingClassEnum.S.getAtomOfferingClass());
        sAndC.add(AtomOfferingClassEnum.C.getAtomOfferingClass());
        Order2cAtomInfoExample notSAndCAtomOrderExample = new Order2cAtomInfoExample().createCriteria()
                .andOrderIdEqualTo(orderId)
                .andAtomOfferingClassNotIn(sAndC)
                .example();
        List<Order2cAtomInfo> notSAndCAtomOrderList = atomOrderInfoMapper.selectByExample(notSAndCAtomOrderExample);

        //根据订单类型进行取消
        if(ORDER_2C_TYPE.equals(request.getOrderType())){
            for (Order2cAtomInfo atomOrderInfo : notSAndCAtomOrderList) {
                //更新操作记录
                Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
                order2cAtomHistory.withOrderId(orderId)
                        .withAtomOrderId(atomOrderInfo.getId())
                        .withOperateType(1)
                        .withOperatorId(userId)
                        .withInnerStatus(OrderStatusInnerEnum.ORDER_FAIL.getStatus());
                order2cAtomHistoryList.add(order2cAtomHistory);

                ExHandleInfo exHandleInfo = new ExHandleInfo();
                BeanUtils.copyProperties(request, exHandleInfo);
                exHandleInfo.setId(BaseServiceUtils.getId());
                exHandleInfo.setAtomOrderId(atomOrderInfo.getId());
                exHandleInfo.setCreateTime(now);
                exHandleInfo.setUpdateTime(now);
                exHandleInfoMapper.insert(exHandleInfo);

                atomOrderInfo.setExHandleId(exHandleInfo.getId());
                atomOrderInfo.setOrderStatus(OrderStatusInnerEnum.ORDER_FAIL.getStatus());
                atomOrderInfo.setUpdateTime(now);
                atomOrderInfoMapper.updateByPrimaryKeySelective(atomOrderInfo);
            }
        }else if(ORDER_ROC_TYPE.equals(request.getOrderType())){
            //查询退货订单
            Order2cRocInfoExample order2cRocInfoExample = new Order2cRocInfoExample();
            Order2cRocInfoExample.Criteria criteria = order2cRocInfoExample.createCriteria();
            if(StringUtils.isNotBlank(request.getRefundOrderId())){
                criteria.andRefundOrderIdEqualTo(request.getRefundOrderId());
            }
            List<Order2cRocInfo> order2cRocInfoList = order2cRocInfoMapper.selectByExample(criteria
                    .andOrderIdEqualTo(orderId)
                    .example());
            if(ObjectUtils.isEmpty(order2cRocInfoList)){
                throw new BusinessException(StatusConstant.NO_ORDER);
            }
            for (Order2cRocInfo order2cRocInfo : order2cRocInfoList) {
                //判断退货订单类型：仅退款 or 退款退货
                Integer innerStatus;
                if (order2cRocInfo.getRefundsType().equals(RefundTypeEnum.REFUND.getRefundType())) {
                    innerStatus = OrderRocTypeEnum.REFUND_CANCEL.getStatus();
                } else if (order2cRocInfo.getRefundsType().equals(RefundTypeEnum.RETURN.getRefundType())) {
                    innerStatus = OrderRocTypeEnum.RETURN_CANCEL.getStatus();
                } else {
                    throw new BusinessException("30999", "退货订单状态不支持转换！");
                }
                //修改退货订单记录
                order2cRocInfo.withInnerStatus(innerStatus);
                order2cRocInfo.setUpdateTime(now);
                order2cRocInfoMapper.updateByPrimaryKeySelective(order2cRocInfo);
                //更新操作记录
                Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
                order2cAtomHistory.withOrderId(orderId)
                        .withAtomOrderId(order2cRocInfo.getAtomOrderId())
                        .withOperateType(2)
                        .withOperatorId(userId)
                        .withInnerStatus(innerStatus);
                order2cAtomHistoryList.add(order2cAtomHistory);

                ExHandleInfo exHandleInfo = new ExHandleInfo();
                BeanUtils.copyProperties(request, exHandleInfo);
                exHandleInfo.setId(BaseServiceUtils.getId());
                exHandleInfo.setRefundOrderId(order2cRocInfo.getRefundOrderId());
                exHandleInfo.setAtomOrderId(order2cRocInfo.getAtomOrderId());
                exHandleInfo.setCreateTime(now);
                exHandleInfo.setUpdateTime(now);
                exHandleInfoMapper.insert(exHandleInfo);

                Order2cAtomInfo order2cAtomInfoToUpdate = new Order2cAtomInfo();
                order2cAtomInfoToUpdate.setId(order2cAtomInfo.getId());
                order2cAtomInfoToUpdate.setExHandleId(exHandleInfo.getId());
                order2cAtomInfoToUpdate.setOrderStatus(OrderStatusInnerEnum.ORDER_FAIL.getStatus());
                order2cAtomInfoToUpdate.setUpdateTime(now);
                atomOrderInfoMapper.updateByPrimaryKeySelective(order2cAtomInfoToUpdate);
            }
        }
        //新增订单操作记录
        order2cAtomHistoryMapper.batchInsert(order2cAtomHistoryList);
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<Void> updateExHandleInfo(Request4ExHandleUpdate request, String userId) {
        return null;
    }

    @Override
    public BaseAnswer<ExHandleInfo> queryExHandleInfo(String orderId, String orderType, String refundOrderId, String userId) {
        BaseAnswer<ExHandleInfo> answer = new BaseAnswer<>();
        List<ExHandleInfo> exHandleInfos = exHandleInfoMapper.selectList(new QueryWrapper<ExHandleInfo>().lambda()
                .eq(ExHandleInfo::getOrderId, orderId)
                .eq(ExHandleInfo::getOrderType, orderType)
                .eq(ObjectUtils.isNotEmpty(refundOrderId), ExHandleInfo::getRefundOrderId, refundOrderId));
        if(ObjectUtils.isNotEmpty(exHandleInfos)){
            answer.setData(exHandleInfos.get(0));
        }
        return answer;
    }
}
