package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.dao.Order2cAgentInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cInfoMapper;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OperationKanbanOperateEnum;
import com.chinamobile.iot.sc.excel.ChannelDataWashExcel;
import com.chinamobile.iot.sc.excel.ChannelDataWashExcelListener;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Order2cAgentInfo;
import com.chinamobile.iot.sc.pojo.Order2cAgentInfoExample;
import com.chinamobile.iot.sc.pojo.dto.ChannelDataWashFailDTO;
import com.chinamobile.iot.sc.pojo.dto.ChannelDataWashSucceedDTO;
import com.chinamobile.iot.sc.pojo.param.OrderChannelDataWashParam;
import com.chinamobile.iot.sc.pojo.param.UpdateOrderChannelParam;
import com.chinamobile.iot.sc.service.ChannelDataWashService;
import com.chinamobile.iot.sc.service.GioBurialPointService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.ExcelHeaderCheckUtil;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xiemaohua
 * @date : 2025/4/29 14:46
 * @description: 渠道商清洗接口实现类
 **/
@Service
@Slf4j
public class ChannelDataWashServiceImpl implements ChannelDataWashService {


    @Resource
    private Order2cAgentInfoMapper order2cAgentInfoMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private LogService logService;
    @Resource
    private GioBurialPointService gioBurialPointService;

    @Override
    public void importChannelDataWash(LoginIfo4Redis loginIfo4Redis,MultipartFile file) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        boolean fileTypeBool = oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"));
        if (fileTypeBool) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }
        try {
            List<String> batchDeliverHeaderList = Arrays.asList("订单编号,渠道商全称,渠道商编码,渠道商类别,渠道商标签".split(","));
            //校验表头
            if(!ExcelHeaderCheckUtil.checkExcelHeaders(file,batchDeliverHeaderList,0)){
                response.setHeader("message", URLEncoder.encode("文件模板错误", "UTF-8").replaceAll("\\+", "%20"));
                response.setHeader("statecode", "88888");
                return;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        String userName = loginIfo4Redis.getUserName();
        ChannelDataWashExcelListener excelListener = new ChannelDataWashExcelListener(order2cInfoMapper);
        try {

            List<Object> list = EasyExcel.read(file.getInputStream(), ChannelDataWashExcel.class, excelListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            List<ChannelDataWashSucceedDTO> channelDataWashSucceedList = excelListener.getChannelDataWashSucceedList();
            List<ChannelDataWashFailDTO> channelDataWashFailList = excelListener.getChannelDataWashFailList();
            if (CollectionUtils.isNotEmpty(channelDataWashFailList)){
                String excelName = "批量导入订单渠道商信息失败";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/channel_wash_error.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();
                String stateCode = BaseErrorConstant.IMPORT_CHANNEL_DATA_WASH_FAIL.getStateCode();
                String message = BaseErrorConstant.IMPORT_CHANNEL_DATA_WASH_FAIL.getMessage();
                //构建填充excel参数
                Map<String, Object> map = new HashMap<String, Object>();
                EasyExcelUtils.exportExcel(response, "list", channelDataWashFailList, map, excelName, templateFileName,
                        0, "失败描述", stateCode, message);
            }else {
                Date date = new Date();
                for (ChannelDataWashSucceedDTO channelDataWashSucceedDTO : channelDataWashSucceedList) {
                    //筛选过滤看那些是有渠道商信息订单 那些没有  有修改 没有新增
                    String orderId = channelDataWashSucceedDTO.getOrderId();
                    List<Order2cAgentInfo> order2cAgentInfos = order2cAgentInfoMapper.selectByExample(new Order2cAgentInfoExample().createCriteria().andOrderIdEqualTo(orderId).example());
                    if (CollectionUtils.isNotEmpty(order2cAgentInfos)){
                        Order2cAgentInfo order2cAgentInfo = new Order2cAgentInfo();
                        order2cAgentInfo.setOrderId(channelDataWashSucceedDTO.getOrderId());
                        order2cAgentInfo.setAgentCategoryWash(channelDataWashSucceedDTO.getAgentCategoryWash());
                        order2cAgentInfo.setAgentLabelWash(channelDataWashSucceedDTO.getAgentLabelWash());
                        order2cAgentInfo.setAgentNameWash(channelDataWashSucceedDTO.getAgentNameWash());
                        order2cAgentInfo.setAgentNumberWash(channelDataWashSucceedDTO.getAgentNumberWash());
                        order2cAgentInfo.setIsWash("1");
                        order2cAgentInfo.setOperatorWash(userName);
                        order2cAgentInfo.setUpdateTime(date);
                        order2cAgentInfoMapper.updateByExampleSelective(order2cAgentInfo,
                                new Order2cAgentInfoExample().createCriteria().andOrderIdEqualTo(channelDataWashSucceedDTO.getOrderId()).example());
                    }else {
                        Order2cAgentInfo order2cAgentInfo = new Order2cAgentInfo();
                        order2cAgentInfo.setId(BaseServiceUtils.getId());
                        order2cAgentInfo.setOrderId(channelDataWashSucceedDTO.getOrderId());
                        order2cAgentInfo.setAgentCategoryWash(channelDataWashSucceedDTO.getAgentCategoryWash());
                        order2cAgentInfo.setAgentLabelWash(channelDataWashSucceedDTO.getAgentLabelWash());
                        order2cAgentInfo.setAgentNameWash(channelDataWashSucceedDTO.getAgentNameWash());
                        order2cAgentInfo.setAgentNumberWash(channelDataWashSucceedDTO.getAgentNumberWash());
                        order2cAgentInfo.setIsWash("1");
                        order2cAgentInfo.setOperatorWash(userName);
                        order2cAgentInfo.setUpdateTime(date);
                        order2cAgentInfoMapper.insertSelective(order2cAgentInfo);
                    }

                }
                StringBuilder builder = new StringBuilder();
                builder.append("【批量导入清洗】").append("\n");
                builder.append("清洗").append(channelDataWashSucceedList.size()).append("个订单的渠道商");
                //记录日志
                logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code, OperationKanbanOperateEnum.ORDER_DATA_WASH.code,
                        builder.toString(), LogResultEnum.LOG_SUCESS.code, null);
                gioBurialPointService.sendOrderDimensionalityMsg(channelDataWashSucceedList);
            }

        } catch (IOException e) {
            log.error("读取文件异常，文件名: {},异常描述：{}", oldName, e);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }


    }

    @Override
    public void exportOrderChannelDataWash(OrderChannelDataWashParam param) {

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        String orderId = param.getOrderId();
        String agentCategoryWash = param.getAgentCategoryWash();
        String agentNameWash = param.getAgentNameWash();
        String agentLabelWash = param.getAgentLabelWash();
        String agentNumberWash = param.getAgentNumberWash();
        Order2cAgentInfoExample order2cAgentInfoExample = new Order2cAgentInfoExample();
        Order2cAgentInfoExample.Criteria criteria = order2cAgentInfoExample.createCriteria();
        criteria.andIsWashEqualTo("1");
        if (StringUtils.isNotEmpty(orderId)){
            criteria.andOrderIdEqualTo(orderId);
        }
        if (StringUtils.isNotEmpty(agentCategoryWash)){
            criteria.andAgentCategoryWashLike("%"+agentCategoryWash+"%");
        }
        if (StringUtils.isNotEmpty(agentNameWash)){
            criteria.andAgentNameWashLike("%"+agentNameWash+"%");
        }
        if (StringUtils.isNotEmpty(agentLabelWash)){
            criteria.andAgentLabelWashLike("%"+agentLabelWash+"%");
        }
        if (StringUtils.isNotEmpty(agentNumberWash)){
            criteria.andAgentNumberLike("%"+agentNumberWash+"%");
        }
        List<Order2cAgentInfo> order2cAgentInfos = order2cAgentInfoMapper.selectByExample(order2cAgentInfoExample);
        List<ChannelDataWashExcel> collect = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(order2cAgentInfos)){
            collect  = order2cAgentInfos.stream().map(order2cAgentInfo -> {
                ChannelDataWashExcel channelDataWashExcel = new ChannelDataWashExcel();
                BeanUtils.copyProperties(order2cAgentInfo, channelDataWashExcel);
                return channelDataWashExcel;
            }).collect(Collectors.toList());
        }
        try {
            String excelName = "批量导出订单渠道商信息";
            //excelName = URLEncoder.encode(excelName, "UTF-8");
            ClassPathResource classPathResource;
            if (CollectionUtils.isNotEmpty(collect)){
                classPathResource = new ClassPathResource("template/channel_wash_export.xlsx");
            }else {
                classPathResource = new ClassPathResource("template/channel_wash_export_null.xlsx");
            }

            InputStream templateFileName = classPathResource.getInputStream();
            //构建填充excel参数
            Map<String, Object> map = new HashMap<String, Object>();
            EasyExcelUtils.exportExcel(response, "list", collect, map, excelName, templateFileName,
                    0, "渠道商清洗信息", BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());

            StringBuilder builder = new StringBuilder();
            builder.append("【数据导出】").append("\n");
            builder.append("导出").append(collect.size()).append("个订单的渠道商");
            //记录日志
            logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code, OperationKanbanOperateEnum.ORDER_DATA_WASH.code,
                    builder.toString(), LogResultEnum.LOG_SUCESS.code, null);
        } catch (Exception e) {
            //便于前端拿到异常，将异常信息放入header
            try {
                if (e instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode", businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                } else {
                    log.error("导出订单渠道商信息发生异常,", e);
                    response.addHeader("stateCode", BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }

        }

    }

    @Override
    public BaseAnswer<PageData<Order2cAgentInfo>> getOrderChannelDataWashList(OrderChannelDataWashParam param) {
        BaseAnswer<PageData<Order2cAgentInfo>> baseAnswer = new BaseAnswer<>();
        Integer pageSize = param.getPageSize();
        Integer pageNum = param.getPageNum();
        PageData<Order2cAgentInfo> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        String orderId = param.getOrderId();
        String agentCategoryWash = param.getAgentCategoryWash();
        String agentNameWash = param.getAgentNameWash();
        String agentLabelWash = param.getAgentLabelWash();
        String agentNumberWash = param.getAgentNumberWash();
        Order2cAgentInfoExample order2cAgentInfoExample = new Order2cAgentInfoExample();
        order2cAgentInfoExample.setOrderByClause("update_time DESC");
        Order2cAgentInfoExample.Criteria criteria = order2cAgentInfoExample.createCriteria();
        criteria.andIsWashEqualTo("1");
        if (StringUtils.isNotEmpty(orderId)){
            criteria.andOrderIdEqualTo(orderId);
        }
        if (StringUtils.isNotEmpty(agentCategoryWash)){
            criteria.andAgentCategoryWashLike("%"+agentCategoryWash+"%");
        }
        if (StringUtils.isNotEmpty(agentNameWash)){
            criteria.andAgentNameWashLike("%"+agentNameWash+"%");
        }
        if (StringUtils.isNotEmpty(agentLabelWash)){
            criteria.andAgentLabelWashLike("%"+agentLabelWash+"%");
        }
        if (StringUtils.isNotEmpty(agentNumberWash)){
            criteria.andAgentNumberWashLike("%"+agentNumberWash+"%");
        }
        List<Order2cAgentInfo> list = order2cAgentInfoMapper.selectByExample(order2cAgentInfoExample);
        PageInfo<Order2cAgentInfo> pageInfo = new PageInfo<>(list);
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(list);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    @Override
    public void orderAccurateAgentWash(LoginIfo4Redis loginIfo4Redis,UpdateOrderChannelParam param) {
        String id = param.getId();
        StringBuilder builder = new StringBuilder();
        Order2cAgentInfo order2cAgentInfo = order2cAgentInfoMapper.selectByPrimaryKey(id);
        builder.append("【重新清洗】/ 【清洗】").append("\n");
        builder.append("渠道商全称由").append(order2cAgentInfo.getAgentNameWash()).append(param.getAgentNameWash()).append("(清洗渠道商全称为)").append(param.getAgentNameWash()).append(",\n");
        builder.append("渠道商编码由").append(order2cAgentInfo.getAgentNumberWash()).append(param.getAgentNumberWash()).append("(清洗渠道商编码为)").append(param.getAgentNumberWash()).append(",\n");
        builder.append("渠道类型由").append(order2cAgentInfo.getAgentCategoryWash()).append(param.getAgentCategoryWash()).append("(清洗渠道类型为)").append(param.getAgentCategoryWash()).append(",\n");
        builder.append("渠道商标签由").append(order2cAgentInfo.getAgentLabelWash()).append(param.getAgentLabelWash()).append("(清洗渠道商标签为)").append(param.getAgentLabelWash()).append(",\n");
        order2cAgentInfo.setAgentNameWash(param.getAgentNameWash());
        order2cAgentInfo.setAgentNumberWash(param.getAgentNumberWash());
        order2cAgentInfo.setAgentCategoryWash(param.getAgentCategoryWash());
        order2cAgentInfo.setAgentLabelWash(param.getAgentLabelWash());
        order2cAgentInfo.setOperatorWash(loginIfo4Redis.getUserName());
        order2cAgentInfo.setIsWash("1");
        order2cAgentInfo.setUpdateTime(new Date());
        order2cAgentInfoMapper.updateByPrimaryKeySelective(order2cAgentInfo);
        //记录日志
        logService.recordOperateLog(ModuleEnum.OPERATION_KANBAN.code, OperationKanbanOperateEnum.ORDER_DATA_WASH.code,
                builder.toString(), LogResultEnum.LOG_SUCESS.code, null);
    }
}
