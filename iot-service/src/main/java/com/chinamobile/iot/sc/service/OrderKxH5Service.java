package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.InvoiceInfoH5Param;
import com.chinamobile.iot.sc.pojo.param.OrderKxH5Param;
import com.chinamobile.iot.sc.pojo.param.OrderRocKxH5Param;
import com.chinamobile.iot.sc.pojo.vo.InvoiceInfoH5VO;
import com.chinamobile.iot.sc.pojo.vo.NotHandleKxOrderH5VO;
import com.chinamobile.iot.sc.pojo.vo.OrderKxH5VO;
import com.chinamobile.iot.sc.pojo.vo.OrderRocKxH5VO;
import org.springframework.web.bind.annotation.RequestAttribute;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28
 * @description 订单卡+X的H5 service接口类
 */
public interface OrderKxH5Service {

    /**
     * 获取未处理的卡+x、联合销售、合同履约订单等数量
     * @param loginIfo4Redis
     * @return
     */
    NotHandleKxOrderH5VO getNotHandleKxOrderCount(LoginIfo4Redis loginIfo4Redis);

    /**
     * 分页获取发票相关的H5数据
     * @param invoiceInfoH5Param
     * @param loginIfo4Redis
     * @return
     */
    PageData<InvoiceInfoH5VO> pageInvoiceH5(InvoiceInfoH5Param invoiceInfoH5Param,
                                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis);

    /**
     * 分页获取订单的卡+X的H5数据
     * @param orderKxH5Param
     * @param loginIfo4Redis
     * @return
     */
    PageData<OrderKxH5VO> pageOrderKxH5(OrderKxH5Param orderKxH5Param,
                                         @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis);


    /**
     * 分页获取订单退换的卡+X的H5数据
     * @param orderRocKxH5Param
     * @param loginIfo4Redis
     * @return
     */
    PageData<OrderRocKxH5VO> pageOrderRocKxH5(OrderRocKxH5Param orderRocKxH5Param,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis);
}
