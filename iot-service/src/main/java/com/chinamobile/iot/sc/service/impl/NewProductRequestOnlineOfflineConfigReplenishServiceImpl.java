package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineConfigReplenishMapper;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenish;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenishExample;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineConfigReplenishService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架配置补充service实现类
 */
@Service
public class NewProductRequestOnlineOfflineConfigReplenishServiceImpl implements NewProductRequestOnlineOfflineConfigReplenishService {

    @Resource
    private NewProductRequestOnlineOfflineConfigReplenishMapper newProductRequestOnlineOfflineConfigReplenishMapper;

    @Override
    public void saveConfigReplenish(NewProductRequestOnlineOfflineConfigReplenish configReplenish) {
        newProductRequestOnlineOfflineConfigReplenishMapper.insertSelective(configReplenish);
    }

    @Override
    public void updateConfigReplenishByRequestIdAndComboInfoId(NewProductRequestOnlineOfflineConfigReplenish configReplenish) {
        NewProductRequestOnlineOfflineConfigReplenishExample example = new NewProductRequestOnlineOfflineConfigReplenishExample();
        example.createCriteria()
                .andComboInfoIdEqualTo(configReplenish.getComboInfoId())
                .andNewProductRequestIdEqualTo(configReplenish.getNewProductRequestId());
        newProductRequestOnlineOfflineConfigReplenishMapper.updateByExampleSelective(configReplenish,example);
    }

    @Override
    public void updateConfigReplenishById(NewProductRequestOnlineOfflineConfigReplenish configReplenish) {
        newProductRequestOnlineOfflineConfigReplenishMapper.updateByPrimaryKeySelective(configReplenish);
    }

    @Override
    public NewProductRequestOnlineOfflineConfigReplenish getConfigReplenishByRequestIdAndComboInfoId(String newProductRequestId, String comboInfoId) {
        NewProductRequestOnlineOfflineConfigReplenishExample example = new NewProductRequestOnlineOfflineConfigReplenishExample();
        example.createCriteria()
                .andNewProductRequestIdEqualTo(newProductRequestId)
                .andComboInfoIdEqualTo(comboInfoId);
        List<NewProductRequestOnlineOfflineConfigReplenish> configReplenishList = newProductRequestOnlineOfflineConfigReplenishMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(configReplenishList)){
            return configReplenishList.get(0);
        }else {
            return null;
        }
    }
}
