package com.chinamobile.iot.sc.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.RowTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.constant.SellStatusEnum;
import com.chinamobile.iot.sc.constant.TerminalTypeEnum;
import com.chinamobile.iot.sc.dao.ext.CardRelationMapperExt;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.exception.ServicePowerException;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.dto.ImportCardXType0DeliverDTO;
import com.chinamobile.iot.sc.pojo.dto.InventoryAreaDTO;
import com.chinamobile.iot.sc.pojo.entity.CardChooseDelivery;
import com.chinamobile.iot.sc.pojo.entity.CardChooseDeliveryExample;
import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import com.chinamobile.iot.sc.pojo.entity.CardRelationExample;
import com.chinamobile.iot.sc.pojo.mapper.OrderProductCardRelationListDO;
import com.chinamobile.iot.sc.pojo.param.DeliverCardParam;
import com.chinamobile.iot.sc.service.CardChooseDeliveryService;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.service.CardRelationService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.Constant.REDIS_COMMIT_INVENTORY_AREA;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/31
 * @description 卡+X交付插拔卡excel处理类
 */
@Slf4j
@Component
public class ExcelCardXType0DeliverImportListener extends AnalysisEventListener<ImportCardXType0DeliverDTO> {

    /**
     * 核心合并单元格的缓存
     * 可以把map当成一个二位excel表格，里面记录每个存储的被合并单元格的内容。
     * key 为 [行号]
     */
    private Map<Integer, String> mergedCellsIndexMap = new HashMap<>();

    /**
     * 校验未通过的数据,因为只要有一个数据未通过，所有的数据都需要返回，所以这里保存所有的数据
     */
    private List<ImportCardXType0DeliverDTO> failList = new ArrayList<>();

    /**
     * 校验通过的数据,用于对比导入的数据和通过的数据是否相等，用于最终判断是否有校验未通过的数据
     */
    private List<ImportCardXType0DeliverDTO> successList = new ArrayList<>();

    List<Order2cAtomInfo> order2cAtomInfoList = new ArrayList<>();

    List<AtomOfferingInfo> atomOfferingInfoList = new ArrayList<>();

    private Order2cInfo order2cInfo = new Order2cInfo();

    public List<AtomOfferingInfo> getAtomOfferingInfoList() {
        return atomOfferingInfoList;
    }

    public void setAtomOfferingInfoList(List<AtomOfferingInfo> atomOfferingInfoList) {
        this.atomOfferingInfoList = atomOfferingInfoList;
    }

    public Order2cInfo getOrder2cInfo() {
        return order2cInfo;
    }

    public void setOrder2cInfo(Order2cInfo order2cInfo) {
        this.order2cInfo = order2cInfo;
    }

    public List<Order2cAtomInfo> getOrder2cAtomInfoList() {
        return order2cAtomInfoList;
    }

    public void setOrder2cAtomInfoList(List<Order2cAtomInfo> order2cAtomInfoList) {
        this.order2cAtomInfoList = order2cAtomInfoList;
    }

    public List<ImportCardXType0DeliverDTO> getFailList() {
        return failList;
    }

    public List<ImportCardXType0DeliverDTO> getSuccessList() {
        return successList;
    }

    private CardRelationService cardRelationService;

    private StringRedisTemplate stringRedisTemplate;

    private CardChooseDeliveryService cardChooseDeliveryService;

    private CardInfoService cardInfoService;

    public ExcelCardXType0DeliverImportListener(CardRelationService cardRelationService,
                                                StringRedisTemplate stringRedisTemplate,
                                                CardChooseDeliveryService cardChooseDeliveryService,
                                                CardInfoService cardInfoService){
        this.cardRelationService = cardRelationService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.cardChooseDeliveryService = cardChooseDeliveryService;
        this.cardInfoService = cardInfoService;
    }



    @Override
    public void invoke(ImportCardXType0DeliverDTO importCardXType0DeliverDTO, AnalysisContext analysisContext) {
        int currentRow = analysisContext.readRowHolder().getRowIndex() + 1;
        boolean hasError = false;
        ImportCardXType0DeliverDTO failDTO = new ImportCardXType0DeliverDTO();
        BeanUtils.copyProperties(importCardXType0DeliverDTO,failDTO);

        String deviceVersion = importCardXType0DeliverDTO.getDeviceVersion();
        String imei = importCardXType0DeliverDTO.getImei();
        String msisdn = importCardXType0DeliverDTO.getMsisdn();
        String tempIccid = importCardXType0DeliverDTO.getTempIccid();
        log.info("ExcelCardXType0 invok currentRow = {} ,imei = {}, msisdn = {}",currentRow,imei, msisdn);

        if (StringUtils.isEmpty(deviceVersion)){
            deviceVersion = mergedCellsIndexMap.get(currentRow-1);
            importCardXType0DeliverDTO.setDeviceVersion(deviceVersion);
            failDTO.setDeviceVersion(deviceVersion);
        }
        mergedCellsIndexMap.put(currentRow,deviceVersion);

        if (StringUtil.isEmpty(imei)){
            hasError = true;
            failDTO.setErrorMsg("imei不能为空;");
            failList.add(failDTO);
        }else{
            CardRelationExample cardRelationExample = new CardRelationExample();
            cardRelationExample.createCriteria()
                    .andDeleteTimeIsNull()
                    .andImeiEqualTo(imei)
                    .andDeviceVersionEqualTo(deviceVersion);
            List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(cardRelationExample);
            if (CollectionUtils.isEmpty(cardRelationList)){
                hasError = true;
                failDTO.setErrorMsg("imei不存在;");
                failList.add(failDTO);
            }else {
                CardRelation cardRelation = cardRelationList.get(0);
                importCardXType0DeliverDTO.setTempIccid(cardRelation.getTempIccid());
                String errorMsg = "";
                if (!cardRelation.getSellStatus().equals(SellStatusEnum.NOT_SELL.getType())){
                    hasError = true;
                    errorMsg = errorMsg.concat("imei已销售;");
                }

                String terminalType = cardRelation.getTerminalType();
                if (!terminalType.equals(TerminalTypeEnum.CHA_BO_CARD.getType())
                    && !terminalType.equals(TerminalTypeEnum.NO_CARD.getType())){
                    hasError = true;
                    errorMsg = errorMsg.concat("imei对应的类型非插拔卡或不含卡;");
                }


                // 用于判断当前imei是否有配置在当前订单对应的原子商品上
                boolean isBelongAtom = false;
                boolean isImeiImport = false;
                boolean isMsisdnImport = false;
                for (int j = 0;j < order2cAtomInfoList.size();j++){
                    if (!isBelongAtom || !isImeiImport || !isMsisdnImport){
                        Order2cAtomInfo order2cAtomInfo = order2cAtomInfoList.get(j);
                        String orderId = order2cAtomInfo.getOrderId();
                        String atomOrderId = order2cAtomInfo.getId();
                        String atomOfferingClass = order2cAtomInfo.getAtomOfferingClass();
                        //插拔卡的时候校验 码号信息是否正确
                        if (terminalType.equals(TerminalTypeEnum.CHA_BO_CARD.getType()) &&
                                AtomOfferingClassEnum.X.getAtomOfferingClass().equals(atomOfferingClass)){
                            //插拔卡需要校验码号
                            BaseAnswer<List<String>> cardAnswer = cardInfoService.orderProductTemplateCardList(orderId,null);
                            if(cardAnswer == null || !cardAnswer.getStatus().getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())){
                                hasError = true;
                                isImeiImport = true;
                                errorMsg = errorMsg.concat("查询和当前订单商品的开卡模板名称一致的码号列表失败！");
                            }else {
                                List<String> correctMsisdnList = cardAnswer.getData();
                                if (!correctMsisdnList.contains(msisdn)){
                                    hasError = true;
                                    isImeiImport = true;
                                    errorMsg = errorMsg.concat("码号").concat(msisdn).concat("订单商品的开卡模板名称不一致！");
                                }
                            }
                        }

                        for (int i = 0;i < atomOfferingInfoList.size();i++){
                            AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(i);
                            String id = atomOfferingInfo.getId();
                            //获取预占的区域和数量信息，根据区域去拉取对应区域可选的x终端
                            String redisData = stringRedisTemplate.opsForValue().get(REDIS_COMMIT_INVENTORY_AREA + orderId + "_" + id);
                            if (StringUtils.isEmpty(redisData)) {
                                hasError = true;
                                errorMsg = errorMsg.concat("码号").concat(msisdn).concat("订单为"+orderId+"原子id为"+id+"的预占仓库信息不存在");
//                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单为"+orderId+"原子id为"+id+"的预占仓库信息不存在");
                            }
                            List<InventoryAreaDTO> inventoryAreaDTOList = JSON.parseArray(redisData, InventoryAreaDTO.class);
                            String beId = null;
                            String location = null;
                            //预占区域最多2条，一条省级，一条是提单人归属的市级
                        /*
                        三种情况：1.只预占省级仓库： beId有值，location无值
                                 2.只预占市级仓库： beId无值，location有值
                                 3.同时预占省级和市级仓库： beId有值，location有值
                         */
                            for (InventoryAreaDTO inventoryAreaDTO : inventoryAreaDTOList) {
                                String areaCode = inventoryAreaDTO.getAreaCode();
                                if (areaCode.length() == 3) {
                                    beId = areaCode;
                                } else if (areaCode.length() == 4) {
                                    location = areaCode;
                                }

                                String atomOfferingCode = order2cAtomInfo.getAtomOfferingCode();
                                String spuOfferingCode = order2cAtomInfo.getSpuOfferingCode();
                                String skuOfferingCode = order2cAtomInfo.getSkuOfferingCode();
                                // 根据原子商品关联的X终端仓库去查询
                                List<OrderProductCardRelationListDO> doList
                                        = cardRelationService.cardRelationDeliverList(spuOfferingCode, skuOfferingCode,
                                        atomOfferingCode, beId, location, imei,deviceVersion);
                            /*if (CollectionUtils.isEmpty(doList)){
                                hasError = true;
                                errorMsg = errorMsg.concat("imei不属于当前原子商品;");
                            }*/
                                // imei有配置在当前订单对应的原子商品上
                                if (CollectionUtils.isNotEmpty(doList)){
                                    isBelongAtom = true;
                                }

                                CardChooseDeliveryExample cardChooseDeliveryExample = new CardChooseDeliveryExample();
                                cardChooseDeliveryExample.createCriteria()
                                        .andAtomOrderIdEqualTo(order2cAtomInfo.getId())
                                        .andImeiEqualTo(imei);
                                List<CardChooseDelivery> cardChooseDeliveryList = cardChooseDeliveryService.listCardChooseDeliveryByNeed(cardChooseDeliveryExample);
                                if (CollectionUtils.isNotEmpty(cardChooseDeliveryList)){
                                    hasError = true;
                                    isImeiImport = true;
                                    errorMsg = errorMsg.concat("imei已经导入;");
                                }

                                if (StringUtils.isNotEmpty(msisdn)){
                                    cardChooseDeliveryExample = new CardChooseDeliveryExample();
                                    cardChooseDeliveryExample.createCriteria()
                                            .andAtomOrderIdEqualTo(order2cAtomInfo.getId())
                                            .andMsisdnEqualTo(msisdn);
                                    cardChooseDeliveryList = cardChooseDeliveryService.listCardChooseDeliveryByNeed(cardChooseDeliveryExample);
                                    if (CollectionUtils.isNotEmpty(cardChooseDeliveryList)){
                                        hasError = true;
                                        isMsisdnImport = true;
                                        errorMsg = errorMsg.concat("msisdn已经导入;");
                                    }
                                }
                            }
                        }
                    }
                }

                if (!isBelongAtom){
                    hasError = true;
                    errorMsg = errorMsg.concat("imei不属于当前原子商品;");
                }

                if (StringUtil.isNotEmpty(errorMsg)){
                    hasError = true;
                    failDTO.setErrorMsg(errorMsg);
                    failList.add(failDTO);
                }
            }
        }


        if (!hasError){
            successList.add(importCardXType0DeliverDTO);
        }
    }


    @Override
    public boolean hasNext(AnalysisContext context) {
        if(RowTypeEnum.EMPTY.equals(context.readRowHolder().getRowType())){
            doAfterAllAnalysed(context);
            return false;
        }
        return super.hasNext(context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完成！");
    }

}
