package com.chinamobile.iot.sc.constant.limit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 * @description dict额度类型枚举类
 */
public enum DictLimitTypeEnum {

    HDJ_LIMIT("00","和对讲增值服务","121033"),
    YSX_LIMIT("01","云视讯增值服务","121063");

    /**
     * 产品线条类型
     */
    private String type;

    /**
     * 产品线条描述
     */
    private String desc;
    private String chargeId;

    DictLimitTypeEnum(String type, String desc,String chargeId){
        this.type = type;
        this.desc = desc;
        this.chargeId = chargeId;
    }

    public static String getDesc(String type) {
        for (DictLimitTypeEnum value : DictLimitTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }
    public static String getChargeId(String desc) {
        for (DictLimitTypeEnum value : DictLimitTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return value.chargeId;
            }
        }
        return "";
    }

    public static Boolean containDesc(String desc) {
        for (DictLimitTypeEnum value : DictLimitTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
    public String getChargeId() {
        return chargeId;
    }
}

