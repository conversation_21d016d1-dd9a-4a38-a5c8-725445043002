package com.chinamobile.iot.sc.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/12/14 10:40
 * @Description:
 */
@Data
public class UnionSellOrderDownloadRequest {
    /**
     * 密码
     */
    @NotBlank(message = "请输入密码")
    private String password;
    /**
     * 日期数组
     */
    @NotEmpty(message = "请输入日期数组")
    private List<String> date;

    /**
     * 导出短信验证码
     */
    private Integer exportMask;

    /**
     * 导出验证码的电话
     */
    private String exportPhone;

}
