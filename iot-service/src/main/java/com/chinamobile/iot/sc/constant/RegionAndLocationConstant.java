package com.chinamobile.iot.sc.constant;

import java.util.Map;

/**
 * created by liuxiang on 2022/5/25 16:25
 */
public class RegionAndLocationConstant {

    //存储区县信息到redis的key, filed = code, value=区县名称
    public static final String REDIS_REGION_KEY = "region";
    //存储地市信息到redis的key, filed = code, value=地市名称
    public static final String REDIS_LOCATION_KEY = "location";

    public static Map<Object,Object> LOCATION_MAP;
    public static Map<Object,Object> REGION_MAP;
}
