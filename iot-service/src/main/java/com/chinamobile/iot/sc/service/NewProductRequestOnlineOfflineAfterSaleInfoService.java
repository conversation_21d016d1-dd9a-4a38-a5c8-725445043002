package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架售后信息service接口类
 */
public interface NewProductRequestOnlineOfflineAfterSaleInfoService {

    /**
     * 新增售后信息
     * @param afterSaleInfo
     */
    void saveAfterSaleInfo(NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo);

    /**
     * 更新售后信息
     * @param afterSaleInfo
     */
    void updateAfterSaleInfoByRequestIdAndComboInfoId(NewProductRequestOnlineOfflineAfterSaleInfo afterSaleInfo);

    /**
     * 根据商品申请id和套餐信息id获取售后信息
     * @param newProductRequestId
     * @param comboInfoId
     * @return
     */
    NewProductRequestOnlineOfflineAfterSaleInfo getAfterSaleInfoByRequestIdAndComboInfoId(String newProductRequestId,
                                                                                          String comboInfoId);
}
