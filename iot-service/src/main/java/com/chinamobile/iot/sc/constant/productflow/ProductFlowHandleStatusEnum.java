package com.chinamobile.iot.sc.constant.productflow;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/26 15:21
 */
public enum  ProductFlowHandleStatusEnum {

    UNHANDLE(0,"未处理"),
    PASS(1,"通过"),
    REJECT(2,"驳回"),
    REDIRECT(3,"转办"),
    KNOWN(4,"知悉"),
    CANCEL(5,"废止"),
    ;

    public Integer code;
    public String name;

    ProductFlowHandleStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
