package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.service.BRMService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.Date;
import java.util.List;


/**
 * BRM数据管理控制器
 */
@RestController
@RequestMapping("/osweb/brm")
@Slf4j
public class BRMController {

    @Autowired
    private BRMService brmService;

    /**
     * 导出BRM数据
     *
     * @return 文件下载
     */
    @PostMapping("/export")
    public ResponseEntity<Resource> exportBRMData() {
        try {
            String filePath = brmService.exportBRMData();
            File file = new File(filePath);

            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"")
                    .body(resource);
        } catch (Exception e) {
            log.error("BRM数据导出失败", e);
            return ResponseEntity.badRequest().build();
        }
    }


    /**
     * 生成广东终端ECSS文本文件
     *
     * @param fileName 文件名前缀（可选，默认使用日期格式）
     * @return 生成结果
     */
    @PostMapping("/generateDetailFiles")
    public BaseAnswer<List<String>> generateDetailFiles(
            @RequestParam(value = "fileName", required = false) String fileName) {
        try {
            // 如果没有传入文件名，使用默认格式
            if (fileName == null || fileName.trim().isEmpty()) {
                String dateStr = DateTimeUtil.formatDate(new Date(), "yyyyMMdd");
                fileName = "BIOT-BRM-ECSS_" + dateStr;
            }
            // 生成详细数据文件
            List<String> generatedFiles = brmService.generateDetailTxtFiles(fileName, true);
            log.info("广东终端ECSS文件生成完成，共生成{}个文件：{}", generatedFiles.size(), generatedFiles);
            // 上传文件到SFTP服务器
            brmService.uploadDetailFilesToSftp(generatedFiles);
            return BaseAnswer.success(generatedFiles);
        } catch (Exception e) {
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                    "广东终端ECSS上传失败");
        }
    }

}