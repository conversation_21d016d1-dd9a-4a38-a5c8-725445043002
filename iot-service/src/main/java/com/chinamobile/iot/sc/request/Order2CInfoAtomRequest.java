package com.chinamobile.iot.sc.request;

import com.chinamobile.iot.sc.request.order2c.OrderInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: wqq
 * @Date: 2025/4/17 16:59
 * @Description: 小程序订单同步请求体
 */
@Data
public class Order2CInfoAtomRequest {
    /**
     * 订单基本信息
     */
    OrderInfoDTO orderInfo;
    /**
     * 原子订单基本信息
     */
    List<Order2cKfkAtomInfo> order2cAtomInfos;
    @Data
    public static class Order2cKfkAtomInfo {
        private String orderId;
        private String id;
        private Long skuPrice;
        private Long atomQuantity;
        private String skuOfferingCode;
    }

}
