package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27
 * @description 已存在产品数据流程service接口类
 */
public interface ProductExistDataFlowService {

    /**
     * 导出发布了的存量商品信息
     * @param response
     * @throws IOException
     */
    void exportUsedProductExistData(HttpServletResponse response) throws IOException;

    /**
     * 导入发布了的存量商品信息
     * @param file
     * @param iotFile
     * @param request
     * @param response
     * @throws Exception
     * @return
     */
    BaseAnswer importUsedProductExistData(MultipartFile file,
                                          MultipartFile iotFile, HttpServletRequest request,
                                          HttpServletResponse response) throws Exception;

}
