package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * sku割接
 */
@Data
public class SkuCutOver {

    @ExcelProperty(value="规格编码")
    private String skuCode;

    @ExcelProperty(value="规格商品名称")
    private String skuName;

    @ExcelProperty(value="规格简称")
    private String skuShortName;
    
    @ExcelProperty(value="合作伙伴（接单）")
    private String receiver;

    @ExcelProperty(value="合作伙伴（交付）")
    private String deliver;

    @ExcelProperty(value="合作伙伴（售后）")
    private String afterSalesman;

    @ExcelProperty(value="商品轮播图文件名")
    private String bannerImage;

    @ExcelProperty(value="商品视频信息文件名")
    private String videoFile;
}
