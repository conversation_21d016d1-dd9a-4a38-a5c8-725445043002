package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.constant.productflow.ProductShelfStatusEnum;
import com.chinamobile.iot.sc.dao.ProductFlowInstanceSpuMapper;
import com.chinamobile.iot.sc.dao.ProductFlowInstanceSpuMapper;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpuExample;
import com.chinamobile.iot.sc.pojo.param.ProductFlowSkuParam;
import com.chinamobile.iot.sc.service.ProductFlowInstanceSpuService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/8
 * @description 产品流程spu service实现类
 */
@Service
public class ProductFlowInstanceSpuServiceImpl implements ProductFlowInstanceSpuService {

    @Resource
    private ProductFlowInstanceSpuMapper productFlowInstanceSpuMapper;

    @Override
    public BaseAnswer<List<ProductFlowInstanceSpu>> listProductFlowInstanceSpu(String spuInfo) {
        BaseAnswer<List<ProductFlowInstanceSpu>> baseAnswer = new BaseAnswer<>();
        ProductFlowInstanceSpuExample productFlowInstanceSpuExample = new ProductFlowInstanceSpuExample();
//        ProductFlowInstanceSpuExample.Criteria criteria = productFlowInstanceSpuExample.createCriteria();
        if(StringUtils.isNotBlank(spuInfo)){
            ProductFlowInstanceSpuExample.Criteria criteria1 = productFlowInstanceSpuExample.or();
            criteria1.andSpuCodeLike("%" + spuInfo + "%");
            ProductFlowInstanceSpuExample.Criteria criteria2 = productFlowInstanceSpuExample.or();
            criteria2.andSpuNameLike("%" + spuInfo + "%");


        }
        List<ProductFlowInstanceSpu> productFlowInstanceSpuList = productFlowInstanceSpuMapper.selectByExample(productFlowInstanceSpuExample);
        if (CollectionUtils.isNotEmpty(productFlowInstanceSpuList)){
            baseAnswer.setData(productFlowInstanceSpuList);
        }
        return baseAnswer;
    }
}
