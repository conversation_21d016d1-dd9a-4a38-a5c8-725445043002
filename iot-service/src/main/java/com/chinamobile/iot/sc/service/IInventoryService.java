package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.dto.KXInventoryDetailImeiDTO;
import com.chinamobile.iot.sc.pojo.dto.KXInventoryDetailLocationDTO;
import com.chinamobile.iot.sc.pojo.dto.LimitSyncContent;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.request.InventoryConfigRequest;
import com.chinamobile.iot.sc.mode.InventoryInfoRequest;
import com.chinamobile.iot.sc.request.InventoryTypeRequest;
import com.chinamobile.iot.sc.request.inventory.LimitListRequest;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.mode.InventoryInfoResponse;
import com.chinamobile.iot.sc.response.iot.ReserveInventoryResponse;
import com.chinamobile.iot.sc.response.web.*;

import java.io.IOException;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/4 17:20
 * @Description:
 */
public interface IInventoryService {


    BaseAnswer<Void> configInventory(InventoryConfigRequest request, String userId, LoginIfo4Redis loginIfo4Redis, String ip);

    IOTAnswer qryInventory(IOTRequest baseRequest);

    //为了小程序复用，所以把库存查询抽取出来
    IOTAnswer<InventoryInfoResponse> getInventoryInfoResponse(IOTRequest baseRequest, InventoryInfoRequest inventoryInfoRequest, IOTAnswer<InventoryInfoResponse> iotAnswer, Boolean isMini);

    IOTAnswer<Void> LimitSynchronizationInfo(IOTRequest baseRequest);

    IOTAnswer<LimitSyncContent> UsedLimitInquiry(IOTRequest baseRequest);


    IOTAnswer<Void> releaseInventory(IOTRequest baseRequest);

    IOTAnswer<ReserveInventoryResponse> reserveInventory(IOTRequest baseRequest);

    BaseAnswer<PageData<InventoryInfoDTO>> getInventoryList(String spuOfferingName, String skuOfferingName,
                                                            String spuOfferingCode, String skuOfferingCode,
                                                            String atomOfferingName, String spuOfferingClass,
                                                            String partnerName, String cooperatorName,
                                                            Integer inventoryStatus,
                                                            String spuOfferingStatus, String skuOfferingStatus, String h5Key,
                                                            List<String> h5SpuOfferingClasses,
                                                            LoginIfo4Redis loginIfo4Redis,
                                                            Integer page, Integer num);

    /**
     * 配置卡+X商品的库存
     *
     * @param configCardXInventoryParam
     * @param loginIfo4Redis
     */
    void configCardXInventory(ConfigCardXInventoryParam configCardXInventoryParam,
                              LoginIfo4Redis loginIfo4Redis);

    /**
     * 库存导出
     *
     * @param inventoryExportParam
     * @param loginIfo4Redis
     */
    void exportInventory(InventoryExportParam inventoryExportParam,
                         LoginIfo4Redis loginIfo4Redis);

    /**
     * 对于卡+X商品配置省侧库存模式
     *
     * @param atomId
     * @param inventoryManagementModeKx
     * @return
     */
    String inventoryManagementModeConfigKx(String atomId, String inventoryManagementModeKx);

    /**
     * 查询卡+x库存信息集合
     *
     * @param inventoryId
     * @return
     */
    List<DkCardxInventoryInfoDTO> getKxInventoryList(String inventoryId);

    /**
     * 查询卡+x库存配置集合
     *
     * @param inventoryId
     * @param saleStatus
     * @param cityCode
     * @return
     */
    List<InventoryConfigKxDetailsDTO> getInventoryConfigKxList(String inventoryId, String saleStatus, String cityCode);

    /**
     * 剔除卡+x库存配置信息
     *
     * @param id
     * @param loginIfo4Redis
     */
    void deleteKxInventoryConfig(String id, LoginIfo4Redis loginIfo4Redis);

    /**
     * 卡+x库存不足发短信
     *
     * @param atomOfferingInfo
     * @param dkInventoryInfo
     */
    void sendInventoryKxDeficiencyNote(AtomOfferingInfo atomOfferingInfo, DkcardxInventoryInfo dkInventoryInfo);

    /**
     * 设置K+x预警值
     *
     * @param params
     * @param loginIfo4Redis
     */
    void setInventoryWarnValue(InstallInventoryWarnParam params, LoginIfo4Redis loginIfo4Redis);

    /**
     * 配置库存模式
     *
     * @param param
     */
    BaseAnswer<Void> setInventoryType(InventoryTypeRequest param);

    BaseAnswer<PageData<InventoryInfoDTO>> getInventoryNewList(String spuOfferingName, String skuOfferingName,
                                                               String spuOfferingCode, String skuOfferingCode,
                                                               String atomOfferingName, List<String> spuOfferingClass,
                                                               String partnerName, String cooperatorName,
                                                               Integer inventoryStatus,
                                                               List<String> spuOfferingStatus, List<String> skuOfferingStatus, String h5Key,
                                                               List<String> h5SpuOfferingClasses,
                                                               String inventoryType,
                                                               LoginIfo4Redis loginIfo4Redis,
                                                               Integer page, Integer num);

    BaseAnswer<PageData<LimitInfoDTO>> getLimitList(LimitListRequest param,
                                                    LoginIfo4Redis loginIfo4Redis
    );

    BaseAnswer<List<SimpleItemDTO>> getLimitStatus();

    void getLimitExport(LimitListRequest param,
                        LoginIfo4Redis loginIfo4Redis
    ) throws IOException;


    BaseAnswer<PageData<InventoryInfoDTO>> getInventoryTypeList(String spuOfferingName,
                                                                String spuOfferingCode,
                                                                String spuOfferingClass,
                                                                List<String> h5SpuOfferingClasses,
                                                                String inventoryType,
                                                                LoginIfo4Redis loginIfo4Redis,
                                                                Integer page, Integer num);

    /**
     * 查询卡+x库存配置集合
     *
     * @param param
     * @return
     */
    List<DkcardxInventoryMainInfo> getInventoryKxDeviceList(ConfigCardXInventoryDeviceParam param);

    /**
     * 卡+X库存配置详情imei相关
     *
     * @param kxInventoryDetailImeiParam
     * @return
     */
    PageData<KXInventoryDetailImeiDTO> listKXInventoryDetailImei(KXInventoryDetailImeiParam kxInventoryDetailImeiParam);

    /**
     * 卡+X库存详情地市列表
     *
     * @param atomId
     * @return
     */
    List<KXInventoryDetailLocationDTO> listKXInventoryDetailLocation(String atomId);

    /**
     * 查询卡+x原子详情列表
     *
     * @param atomId
     * @return
     */
    List<DkCardxInventoryDetailInfoDTO> getInventoryKxDetailList(String atomId);

    void disposePassageKXOrderInventoryMessage();

    /**
     * 处理历史终端原子预占数信息
     *
     * @param id
     * @param atomInventory
     */
    void updateHistoryInventoryAtomInfo(String id, Long atomInventory);

    void updateInventoryAtomInfoFix();
}
