package com.chinamobile.iot.sc.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/3/30 09:29
 * 导出订单查询条件请求体
 */
@Data
public class OrderExportRequest {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 下单开始时间  yyyy-MM-dd HH:mm:ss
     */
//    @NotNull(message = "开始时间必选")
    private String startTime;

    /**
     * 下单结束时间  yyyy-MM-dd HH:mm:ss
     */
//    @NotNull(message = "结束时间必选")
    private String endTime;

    /**
     * 完成开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String finishStartTime;

    /**
     * 完成结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String finishEndTime;

    /**
     * （商品类型）SPU一级销售目录
     */
    private String spuOfferingClass;
    /**
     * 商品规格名称
     */
    private String skuOfferingName;
    /**
     * 商品规格编码
     */
    private String skuOfferingCode;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 售后状态
     */
    private Integer rocStatus;
    /**
     * 收货地区
     */
    private String deliveryArea;

    /**
     * 收货人手机号
     */
    private String receiverPhone;

    /**
     * excel加密数据
     */
    @Size(min = 4,max = 4,message = "excel加密长度为4")
    @NotEmpty(message = "excel加密数据不能为空")
    private String exportPwd;

    /**
     * 导出短信验证码
     */
    private Integer exportMask;

    /**
     * 导出验证码的电话
     */
    private String exportPhone;
    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 是否开启查询特殊退款 0：未开启 1：已开启
     */
    private Integer specialAfterMarketHandle;

    /**
     * 特殊退款订单状态
     */
    private String specialAfterStatus;
    /**
     * 订单类型  01-- 自主下单 00-- 代客下单
     */
    private String orderType;

}
