package com.chinamobile.iot.sc.request.supplychain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 同步报账支付进度
 **/
@Data
public class SupplyChainSyncPaymentStatusRequest {

    /**
     * IOT商城结算单号
     */
    private String iotMallNumber;

    /**
     * 供应链申购单号
     */
    private String segment1;

    /**
     * 供应链订单号
     */
    private String poSegment1;

    /**
     * 报账系统单号
     */
    private String reimNo;

    /**
     * 报账金额
     */
    private BigDecimal paymentAmount;

//    /**
//     * 报账系统单状态码
//     */
//    private String statusCode;

    /**
     * 报账系统单状态
     */
    private String statusName;
}
