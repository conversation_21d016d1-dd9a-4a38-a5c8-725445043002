package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.config.CommonConstant;
import com.chinamobile.iot.sc.config.OneLinkConfig;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.param.OneLinkRealNameResultParam;
import com.chinamobile.iot.sc.pojo.param.OneLinkRealNameUrParam;
import com.chinamobile.iot.sc.pojo.param.UploadSimInfoParam;
import com.chinamobile.iot.sc.pojo.param.RealNameUrlParam;
import com.chinamobile.iot.sc.pojo.vo.OneLinkRealNameResultVO;
import com.chinamobile.iot.sc.pojo.vo.OneLinkRealNameUrlVO;
import com.chinamobile.iot.sc.pojo.vo.RealNameResultVO;
import com.chinamobile.iot.sc.pojo.vo.UploadSimInfoVO;
import com.chinamobile.iot.sc.pojo.vo.RealNameUrlVO;
import com.chinamobile.iot.sc.service.RealNameService;
import com.chinamobile.iot.sc.task.OneLinkTokenRefreshTask;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.iot.sc.util.IOTRequestUtils;
import com.chinamobile.iot.sc.util.OneLinkUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * created by liuxiang on 2023/9/25 14:29
 */
@Slf4j
@Service
public class RealNameServiceImpl implements RealNameService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OneLinkTokenRefreshTask oneLinkTokenRefreshTask;

    @Autowired
    private OneLinkUtil oneLinkUtil;

    @Autowired
    private OneLinkConfig oneLinkConfig;

    @Value("${iot.secretKey}")
    private String secretKey;

    @Value("${iot.realNameResultUrl}")
    private String realNameResultUrl;

    @Override
    public IOTAnswer getVideoUrl(IOTRequest request) {
        log.info("getVideoUrl入参:"+ JSON.toJSONString(request));
        IOTAnswer<Object> iotAnswer = new IOTAnswer<>();
        String token = stringRedisTemplate.opsForValue().get(CommonConstant.ONELINK_TOKEN_KEY);
        if(StringUtils.isEmpty(token)){
            stringRedisTemplate.delete(OneLinkTokenRefreshTask.lockKey);
            oneLinkTokenRefreshTask.work();
            token = stringRedisTemplate.opsForValue().get(CommonConstant.ONELINK_TOKEN_KEY);
        }
        log.info("token值:{}",token);
        RealNameUrlParam param = JSON.parseObject(request.getContent(), RealNameUrlParam.class);
        String serialNo = param.getSerialNo();
        String userPhone = param.getUserPhone();
        String urlType = param.getUrlType();
        //第一步:卡号信息同步
        UploadSimInfoParam uploadSimInfoParam = new UploadSimInfoParam();
        uploadSimInfoParam.setToken(token);
        uploadSimInfoParam.setTransId(oneLinkUtil.getTransId());
        uploadSimInfoParam.setIccids(param.getIccid());
        Map simInfoParamMap = JSON.parseObject(JSON.toJSONString(uploadSimInfoParam), Map.class);
        RealNameUrlVO vo = new RealNameUrlVO();
        log.info("请求onlink getUploadSimInfoUrl参数:{}",JSON.toJSONString(simInfoParamMap));
        try {
            String respStr = HttpUtil.get(oneLinkConfig.getUploadSimInfoUrl(), null, simInfoParamMap, 10000, 10000);
            log.info("getVideoUrl收到OneLink的UploadSimInfoUrl响应:{}",respStr);
            UploadSimInfoVO uploadSimInfoVO = JSON.parseObject(respStr, UploadSimInfoVO.class);
            if(!"0".equals(uploadSimInfoVO.getStatus())){
                iotAnswer.setResultCode("-1");
                vo.setReturnCode("1");
                vo.setReturnMessage(uploadSimInfoVO.getMessage());
                iotAnswer.setContent(vo);
                return iotAnswer;
            }
            //因为商城请求实名认证是单个请求，所以只要有失败的就表示请求失败
            List<UploadSimInfoVO.FailItem> failinfos = uploadSimInfoVO.getResult().get(0).getFailinfos();
            if(CollectionUtils.isNotEmpty(failinfos)){
                UploadSimInfoVO.FailItem failItem = failinfos.get(0);
                iotAnswer.setResultCode("-1");
                vo.setReturnCode("1");
                vo.setReturnMessage(failItem.getMessage());
                iotAnswer.setContent(vo);
                return iotAnswer;
            }
        } catch (Exception e) {
            log.error("登记卡号信息发生异常",e);
            iotAnswer.setResultCode("-1");
            vo.setReturnCode("1");
            vo.setReturnMessage("登记卡号信息发生异常");
            iotAnswer.setContent(vo);
            return iotAnswer;
        }
        token = stringRedisTemplate.opsForValue().get(CommonConstant.ONELINK_TOKEN_KEY);
        //第二步:获取实名认证url
        OneLinkRealNameUrParam realNameUrParam = new OneLinkRealNameUrParam();
        realNameUrParam.setTransId(oneLinkUtil.getTransId());
        realNameUrParam.setToken(token);
        realNameUrParam.setInnerChannelId("01");
        realNameUrParam.setIccid(param.getIccid());
        realNameUrParam.setUrlType(urlType);
        Map realNameUrlParamMap = JSON.parseObject(JSON.toJSONString(realNameUrParam), Map.class);
        String url = null;
        String oneLinkBusiSeq = null;
        log.info("请求onelink getRealNameUrl参数:{}",JSON.toJSONString(realNameUrlParamMap));
        try {
            String realNameUrlRespStr = HttpUtil.get(oneLinkConfig.getRealNameUrl(), null, realNameUrlParamMap, 10000, 10000);
            log.info("getVideoUrl收到OneLink的getRealNameUrl响应:{}",realNameUrlRespStr);
            OneLinkRealNameUrlVO oneLinkRealNameUrlVO = JSON.parseObject(realNameUrlRespStr, OneLinkRealNameUrlVO.class);
            if(!"0".equals(oneLinkRealNameUrlVO.getStatus())){
                iotAnswer.setResultCode("-1");
                vo.setReturnCode("1");
                vo.setReturnMessage(oneLinkRealNameUrlVO.getMessage());
                iotAnswer.setContent(vo);
                return iotAnswer;
            }
            url = oneLinkRealNameUrlVO.getResult().get(0).getUrl();
            oneLinkBusiSeq = oneLinkRealNameUrlVO.getResult().get(0).getBusiSeq();
        } catch (Exception e) {
            log.error("获取实名认证url发生异常",e);
            iotAnswer.setResultCode("-1");
            vo.setReturnCode("1");
            vo.setReturnMessage("获取实名认证url发生异常");
            iotAnswer.setContent(vo);
            return iotAnswer;
        }

        //映射商城请求流水号和oneLink流水号,缓存用户手机号用于推送结果返回商城
        stringRedisTemplate.opsForHash().put(CommonConstant.REDIS_ONELINK_MALL_SEQ_KEY,oneLinkBusiSeq,serialNo);
        stringRedisTemplate.opsForHash().put(CommonConstant.REDIS_MALL_SEQ_PHONE,serialNo,userPhone);
        vo.setReturnCode("0");
        vo.setVideoUrl(url);
        vo.setReturnMessage("成功");
        iotAnswer.setContent(vo);
        return iotAnswer;
    }

    @Override
    public OneLinkRealNameResultVO realNameResult(OneLinkRealNameResultParam param) {
        log.info("realNameResult入参:"+ JSON.toJSONString(param));
        OneLinkRealNameResultVO oneLinkRealNameResultVO = new OneLinkRealNameResultVO();

        if(CollectionUtils.isEmpty(param.getResult())){
            oneLinkRealNameResultVO.setStatus("-1");
            oneLinkRealNameResultVO.setMessage("推送结果处理出错,缺少result，无法获取流水号");
            return oneLinkRealNameResultVO;
        }
        try {
            OneLinkRealNameResultParam.Item item = param.getResult().get(0);
            String serialNo = (String)stringRedisTemplate.opsForHash().get(CommonConstant.REDIS_ONELINK_MALL_SEQ_KEY, item.getBusiSeq());
            String phone = (String)stringRedisTemplate.opsForHash().get(CommonConstant.REDIS_MALL_SEQ_PHONE, serialNo);
            RealNameResultVO realNameResultVO = new RealNameResultVO();
            realNameResultVO.setSerialNo(serialNo);
            realNameResultVO.setUserPhone(phone);
            boolean success = "00000".equals(item.getRegStatus()) && ("0".equals(param.getStatus()));
            realNameResultVO.setResultMessage(success ? "成功" : param.getMessage() +" "+item.getRegMsg());
            realNameResultVO.setRealNameResult(success ? "1" : "2");

            //推送至iot商城
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");
            //beId -- 002 代表物联网公司
            String iotRequest = IOTRequestUtils.getIotRequest(JSON.toJSONString(realNameResultVO), secretKey, "002", null);
            log.info("realNameResult请求IOT商城内容为:" + iotRequest);
            HttpEntity<String> requestEntity = new HttpEntity<>(iotRequest, headers);
            ResponseEntity<IOTAnswer> response;
            try {
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                response = restTemplateHttps.postForEntity(realNameResultUrl, requestEntity, IOTAnswer.class);
            } catch (Exception e) {
                log.error("realNameResult请求商城认证结果推送url出错:", e);
                oneLinkRealNameResultVO.setStatus("-1");
                oneLinkRealNameResultVO.setMessage("请求商城认证结果推送url出错");
                return oneLinkRealNameResultVO;
            }
            IOTAnswer iotAnswer = response.getBody();
            log.info("realNameResult收到IOT商城响应为:" + JSON.toJSONString(iotAnswer));
            if (iotAnswer == null || !"0".equals(iotAnswer.getResultCode())) {
                //没返回信息则为提示同步失败，返回错误码则提示返回错误描述
                log.error("realNameResult请求商城认证结果推送url返回失败,{}", iotAnswer);
                oneLinkRealNameResultVO.setStatus("-1");
                oneLinkRealNameResultVO.setMessage("请求商城认证结果推送url出错");
                return oneLinkRealNameResultVO;
            }
           return oneLinkRealNameResultVO;
        } catch (Exception e) {
            log.error("realNameResult推送结果出错",e);
            oneLinkRealNameResultVO.setStatus("-1");
            oneLinkRealNameResultVO.setMessage("推送结果处理出错");
            return oneLinkRealNameResultVO;
        }
    }
}
