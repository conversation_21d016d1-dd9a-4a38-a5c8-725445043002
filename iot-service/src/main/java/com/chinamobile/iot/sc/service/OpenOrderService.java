package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.OpenAbilityOrganizationRO;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.CyzqListParam;
import com.chinamobile.iot.sc.pojo.vo.CyzqListVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/21
 * @description 订单数据对外接口service接口类
 */
public interface OpenOrderService {

    /**
     * 分页获取云南彩云智企商客产品订单数据查询
     * @param cyzqListParam
     * @param organizationRO
     * @return
     */
    PageData<CyzqListVO> pageCyzqList(CyzqListParam cyzqListParam,
                                      OpenAbilityOrganizationRO organizationRO);
}
