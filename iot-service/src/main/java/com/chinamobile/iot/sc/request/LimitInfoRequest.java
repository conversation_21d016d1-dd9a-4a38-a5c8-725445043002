package com.chinamobile.iot.sc.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30
 * @description 授权额度/额度释放同步
 */
@Data
public class LimitInfoRequest {

    /**
     * 交易流水，交易的唯一标识，发起方保证唯一
     */
    @NotEmpty(message = "交易流水不能为空")
    private String transID;

    /**
     * 省代码，参见附录4.1省代码
     */
    @NotEmpty(message = "省代码不能为空")
    private String companyID;
    /**
     * 产品名称，和对讲增值服务，云视讯增值服务
     */
    @NotEmpty(message = "产品名称不能为空")
    private String productName;
    /**
     * 服务包编码，参考4.15服务列表
     */
    @NotEmpty(message = "服务包编码不能为空")
    private String serviceCode;
    /**
     * 服务包名称，参考4.15服务列表
     */
    @NotEmpty(message = "服务包名称不能为空")
    private String serviceName;
    /**
     * 状态，生效或失效,0:生效,1失效
     */
    @NotEmpty(message = "状态不能为空")
    private String status;
    /**
     * 生效时间，格式：YYYYMMDDHH24MISS
     */
    @NotEmpty(message = "生效时间不能为空")
    private String efftime;
    /**
     * 截止时间，格式：YYYYMMDDHH24MISS
     */
    @NotEmpty(message = "截止时间不能为空")
    private String exptime;
    /**
     * 商城授权额度（含税），保留小数点后2位，单位：元
     */
    @NotEmpty(message = "商城授权额度不能为空")
    private String iot_limit;
    /**
     * 操作类型，1：额度增加，2：额度释放
     */
    @NotEmpty(message = "操作类型不能为空")
    private String operType;


}
