package com.chinamobile.iot.sc.request;

import com.chinamobile.iot.sc.request.product.ManagerInfoDTO;
import com.chinamobile.iot.sc.request.sku.SkuOfferingInfoMDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19
 * @description 省内融合包信息同步
 */
@Data
public class BenefitOfferingsInfoRequest {
    /**
     * 统一产商品编码
     */
    private String goodsId;

    /**
     * IoT省内融合包名称
     */
    private String iotMallOfferingName;

    /**
     * IoT省内融合包状态
     */
    private String iotMallOfferingType;

    /**
     * IoT省内融合包发布区域
     */
    private String iotReleaseAreaId;

    /**
     * 关联商品信息
     */
    private List<BenefitOfferinginfoDTO> offeringinfo;
}
