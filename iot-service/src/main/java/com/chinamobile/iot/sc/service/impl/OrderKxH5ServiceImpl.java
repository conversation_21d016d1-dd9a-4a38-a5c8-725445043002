package com.chinamobile.iot.sc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.constant.OrderRocTypeEnum;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.ext.OrderKxH5MapperExt;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.InvoiceInfoH5Param;
import com.chinamobile.iot.sc.pojo.param.NotHandleKxOrderH5Param;
import com.chinamobile.iot.sc.pojo.param.OrderKxH5Param;
import com.chinamobile.iot.sc.pojo.param.OrderRocKxH5Param;
import com.chinamobile.iot.sc.pojo.vo.InvoiceInfoH5VO;
import com.chinamobile.iot.sc.pojo.vo.NotHandleKxOrderH5VO;
import com.chinamobile.iot.sc.pojo.vo.OrderKxH5VO;
import com.chinamobile.iot.sc.pojo.vo.OrderRocKxH5VO;
import com.chinamobile.iot.sc.service.OrderKxH5Service;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28
 * @description 订单卡+X的H5 service实现类
 */
@Service
public class OrderKxH5ServiceImpl implements OrderKxH5Service {

    @Resource
    private OrderKxH5MapperExt orderKxH5MapperExt;

    @Resource
    private UserFeignClient userFeignClient;

    @Value("${iot.encodeKey}")
    private String encodeKey;
    @Resource
    private RedisTemplate redisTemplate;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Override
    public NotHandleKxOrderH5VO getNotHandleKxOrderCount(LoginIfo4Redis loginIfo4Redis) {
        NotHandleKxOrderH5Param notHandleKxOrderH5Param = new NotHandleKxOrderH5Param();
        setCooperatorIdAndBeIdAndLocation(loginIfo4Redis,notHandleKxOrderH5Param);

        Integer orderCount = orderKxH5MapperExt.getNotHandleKxOrderCount(notHandleKxOrderH5Param);
        Integer orderRocCount = orderKxH5MapperExt.getNotHandleKxOrderRocCount(notHandleKxOrderH5Param);
        Integer invoiceCount = orderKxH5MapperExt.getNotHandleInvoiceCount(notHandleKxOrderH5Param);

        NotHandleKxOrderH5VO notHandleKxOrderH5VO = new NotHandleKxOrderH5VO();
        notHandleKxOrderH5VO.setOrderCount(orderCount);
        notHandleKxOrderH5VO.setOrderRocCount(orderRocCount);
        notHandleKxOrderH5VO.setInvoiceCount(invoiceCount);
        return notHandleKxOrderH5VO;
    }

    @Override
    public PageData<InvoiceInfoH5VO> pageInvoiceH5(InvoiceInfoH5Param invoiceInfoH5Param,
                                                   LoginIfo4Redis loginIfo4Redis) {
        List<String> cooperatorIdList = setCooperatorId(loginIfo4Redis);

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_SYSTEM)
                        &&  !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }
        if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_PERSONAL)){

            //主合作伙伴
        }else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_INVOICE_COMPANY)){
            BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(loginIfo4Redis.getUserId());

            if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                cooperatorIdList = downUserIds.getData();
            }
            cooperatorIdList.add(loginIfo4Redis.getUserId());

        }
        invoiceInfoH5Param.setCooperatorIdList(cooperatorIdList);
        PageData<InvoiceInfoH5VO> pageData = new PageData<>();
        Integer pageNum = invoiceInfoH5Param.getPageNum();
        Integer pageSize = invoiceInfoH5Param.getPageSize();

        Page<InvoiceInfoH5VO> page = new Page<>(pageNum, pageSize);
        List<InvoiceInfoH5VO> invoiceInfoH5VOList = orderKxH5MapperExt.listInvoiceInfoH5(page, invoiceInfoH5Param);
        if (CollectionUtils.isNotEmpty(invoiceInfoH5VOList)){
            BigDecimal thousand = new BigDecimal(1000);
            invoiceInfoH5VOList.stream().forEach(invoiceInfoH5VO -> {
                Long price = invoiceInfoH5VO.getPrice();
                BigDecimal priceDec = new BigDecimal(price).divide(thousand, 2, RoundingMode.HALF_UP);
                invoiceInfoH5VO.setPriceDec(priceDec);

                Date createTime = invoiceInfoH5VO.getCreateTime();
                invoiceInfoH5VO.setCreateTimeStr(DateUtils.dateToStr(createTime,DateUtils.DEFAULT_DATETIME_FORMAT));
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(invoiceInfoH5VOList);

        return pageData;
    }

    @Override
    public PageData<OrderKxH5VO> pageOrderKxH5(OrderKxH5Param orderKxH5Param,
                                               LoginIfo4Redis loginIfo4Redis) {
        NotHandleKxOrderH5Param notHandleKxOrderH5Param = new NotHandleKxOrderH5Param();
        //使用公共方法获取合伙人列表，beId,location
        setCooperatorIdAndBeIdAndLocation(loginIfo4Redis,notHandleKxOrderH5Param);
        BeanUtils.copyProperties(notHandleKxOrderH5Param,orderKxH5Param);

        PageData<OrderKxH5VO> pageData = new PageData<>();
        Integer pageNum = orderKxH5Param.getPageNum();
        Integer pageSize = orderKxH5Param.getPageSize();

        Page<OrderKxH5VO> page = new Page<>(pageNum, pageSize);
        List<OrderKxH5VO> orderKxH5VOList = orderKxH5MapperExt.listOrderKxH5(page, orderKxH5Param);
        if (CollectionUtils.isNotEmpty(orderKxH5VOList)){
            orderKxH5VOList.stream().forEach(orderKxH5VO -> {
                String createTime = orderKxH5VO.getCreateTime();
                createTime = DateUtils.toStringDate(createTime,DateUtils.DATETIME_FORMAT_NO_SYMBOL,DateUtils.DEFAULT_DATETIME_FORMAT);
                orderKxH5VO.setCreateTime(createTime);

                String orderStatusName = OrderStatusInnerEnum.getDescribe(orderKxH5VO.getOrderStatus());
                orderKxH5VO.setOrderStatusName(orderStatusName);

                String totalPrice = IOTEncodeUtils.decryptSM4(orderKxH5VO.getTotalPrice(), iotSm4Key, iotSm4Iv);
                BigDecimal totalPriceDec = new BigDecimal(totalPrice).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
                orderKxH5VO.setTotalPriceDec(totalPriceDec);
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(orderKxH5VOList);

        return pageData;
    }

    @Override
    public PageData<OrderRocKxH5VO> pageOrderRocKxH5(OrderRocKxH5Param orderRocKxH5Param,
                                                     LoginIfo4Redis loginIfo4Redis) {
        List<String> cooperatorIdList = setCooperatorId(loginIfo4Redis);
        orderRocKxH5Param.setCooperatorIdList(cooperatorIdList);

        PageData<OrderRocKxH5VO> pageData = new PageData<>();
        Integer pageNum = orderRocKxH5Param.getPageNum();
        Integer pageSize = orderRocKxH5Param.getPageSize();

        Page<OrderRocKxH5VO> page = new Page<>(pageNum, pageSize);

        List<OrderRocKxH5VO> orderRocKxH5VOList = orderKxH5MapperExt.listOrderRocKxH5(page, orderRocKxH5Param);
        if (CollectionUtils.isNotEmpty(orderRocKxH5VOList)){
            orderRocKxH5VOList.stream().forEach(orderRocKxH5VO -> {
                String rocStatusName = OrderRocTypeEnum.getDescribe(orderRocKxH5VO.getRocStatus());
                orderRocKxH5VO.setRocStatusName(rocStatusName);

                BigDecimal returnPriceDec = new BigDecimal(orderRocKxH5VO.getReturnPrice()).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
                orderRocKxH5VO.setReturnPriceDec(returnPriceDec);
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(orderRocKxH5VOList);

        return pageData;
    }

    /**
     * 设置合作伙伴id
     *
     * @param loginIfo4Redis
     */
    private List<String> setCooperatorId(LoginIfo4Redis loginIfo4Redis) {
        List<String> cooperatorIdList = new ArrayList<>();
        String userId = loginIfo4Redis.getUserId();
        cooperatorIdList.add(userId);
        Boolean isPrimary = loginIfo4Redis.getIsPrimary();
        if (isPrimary != null && isPrimary) {
            BaseAnswer<List<String>> downUserIdList = userFeignClient.getDownUserIds(userId);
            List<String> data = downUserIdList.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                cooperatorIdList.addAll(data);
            }
        }
        return cooperatorIdList;
    }

    /**
     * 设置合作伙伴id,区域信息
     */
    private void setCooperatorIdAndBeIdAndLocation(LoginIfo4Redis loginIfo4Redis, NotHandleKxOrderH5Param notHandleKxOrderH5Param) {
        List<String> cooperatorIdList = new ArrayList<>();
        String userId = loginIfo4Redis.getUserId();
        cooperatorIdList.add(userId);
        Boolean isPrimary = loginIfo4Redis.getIsPrimary();
        String roleType = loginIfo4Redis.getRoleType();
        if (isPrimary != null && isPrimary) {
            //主合作伙伴，查看旗下所有从账号数据
            BaseAnswer<List<String>> downUserIdList = userFeignClient.getDownUserIds(userId);
            List<String> data = downUserIdList.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                cooperatorIdList.addAll(data);
            }
        }else if(roleType.equals(BaseConstant.PARTNER_PROVINCE)){
            //合作伙伴省管，根据地市域展示数据
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !BaseErrorConstant.SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                throw new BusinessException("10004", "合作伙伴省管账号错误");
            }
            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
            String userLocation = data4User.getLocationIdPartner();
            if (isProvinceUser) {
                BaseAnswer<Data4User> userPartner = userFeignClient.getUserPartnerPrimaryByPartnerName(data4User.getPartnerName());
                if (userPartner == null || !BaseErrorConstant.SUCCESS.getStateCode().equals(userPartner.getStateCode())) {
                    throw new BusinessException("10004", "合作伙伴省管获取主合作伙伴账号错误");
                }
                Data4User userPartnerData = userPartner.getData();
                if (Optional.ofNullable(userPartnerData).isPresent()) {
                    BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userPartnerData.getUserId());
                    if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                        cooperatorIdList.addAll(downUserIds.getData());
                    }
                    cooperatorIdList.add(userPartnerData.getUserId());
                }
                if ("all".equals(userLocation)) {
                    notHandleKxOrderH5Param.setBeId(data4User.getBeIdPartner());
                } else {
                    notHandleKxOrderH5Param.setLocation(userLocation);
                }
            } else {
                throw new BusinessException(StatusConstant.AUTH_ERROR);
            }
        }else if(roleType.equals(BaseConstant.PARTNER_ROLE)){
            //合作伙伴从账号,查看自己的,已经添加userId,这里无需添加
        }else {
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }
        notHandleKxOrderH5Param.setCooperatorIdList(cooperatorIdList);
    }
}
