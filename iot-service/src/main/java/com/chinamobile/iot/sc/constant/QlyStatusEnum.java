package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/25
 * @description 千里眼开通状态枚举类
 */
public enum QlyStatusEnum {

    QLY_NOT_CHECK(0, "未校验"),
    QLY_CHECK_SUCCESS(1, "校验成功"),
    QLY_TURN_ON_SUCCESS(2, "开通成功"),
    QLY_TURN_ON_FAILED(3, "开通失败"),
    QLY_TURN_ON_PART_SUCCESS(4, "部分开通成功"),
    QLY_CANCEL_SUCCESS(5, "退订成功"),
    QLY_CANCEL_FAILED(6, "退订失败");

    /**
     * 千里眼开通状态码
     */
    private final Integer status;
    /**
     * 千里眼开通状态信息
     */
    private final String message;

    QlyStatusEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 获取千里眼开通状态信息
     * @param status
     * @return
     */
    public static String getQlyStatusMessage(Integer status){
        QlyStatusEnum[] values = QlyStatusEnum.values();
        for (QlyStatusEnum value : values) {
            if (value.getStatus().equals(status)){
                return value.getMessage();
            }
        }
        return "";
    }
}
