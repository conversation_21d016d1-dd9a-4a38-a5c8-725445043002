package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.dto.NullCardHandleDTO;
import com.chinamobile.iot.sc.pojo.entity.CardMallSync;
import com.chinamobile.iot.sc.pojo.entity.CardMallSyncExample;
import com.chinamobile.iot.sc.pojo.param.UpdateCardMallOrderToNullParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9
 * @description 商城同步的卡信息service接口类
 */
public interface CardMallSyncService {

    /**
     * 批量新增商城同步的卡信息
     * @param cardMallSyncList
     */
    void batchInsertCardMallSync(List<CardMallSync> cardMallSyncList);

    /**
     * 更新商城同步的卡信息为空
     * @param updateCardMallOrderToNullParam
     */
    void updateCardMallOrderToNull(UpdateCardMallOrderToNullParam updateCardMallOrderToNullParam);

    /**
     * 根据需要更新数据
     * @param cardMallSync
     * @param cardMallSyncExample
     */
    void updateByNeed(CardMallSync cardMallSync, CardMallSyncExample cardMallSyncExample);

    /**
     * 根据需要获取数据
     * @param cardMallSyncExample
     * @return
     */
    List<CardMallSync> listCardMallSyncByNeed(CardMallSyncExample cardMallSyncExample);

    /**
     * 获取存量空写卡的码号处理
     * @return
     */
    void handleNullCardCutOver();

    /**
     * 测试自己创建数据进行数据测试
     * @param dataCount
     */
    void testSelfCreateData(Integer dataCount,
                            String cardType);
}
