package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IGoodsService
 * @description: 货物清单Service
 * @author: zyj
 * @create: 2022/1/30 9:12
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IGoodsService {

    BaseAnswer<ResponseEntity<byte[]>> downLoadGoodsList(String orderId, String userId);

}
