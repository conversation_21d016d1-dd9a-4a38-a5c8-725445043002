package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.config.ProductFlowSmsConfig;
import com.chinamobile.iot.sc.dao.ext.BRMMapperExt;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.dto.BRMDetailUploadDTO;
import com.chinamobile.iot.sc.pojo.dto.BRMUploadDTO;
import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;
import com.chinamobile.iot.sc.service.BRMService;
import com.chinamobile.iot.sc.service.BaseSmsService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.common.Constant.REDIS_KEY_BRM_UPLOAD_TXT_PARAM;

@Service
@Slf4j
public class BRMServiceImpl implements BRMService {
    @Value("${brm.ftp.name}")
    private String sftpUserName;
    @Value("${brm.ftp.password}")
    private String sftpPassword;
    @Value("${brm.ftp.host}")
    private String sftpHost;
    @Value("${brm.ftp.port}")
    private Integer sftpPort;
    @Value("${brm.ftp.workPath}")
    private String sftpWorkPath;
   
    @Value("${brm.sms.templateId:108393}")
    private String iopMessageId;

    @Resource
    private BRMMapperExt brmMapperExt;

    @Autowired
    private ProductFlowSmsConfig productFlowSmsConfig;

    @Autowired
    private BaseSmsService baseSmsService;
    // 获取iop校验报告

    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${revenue.invoiceApply.isTest}")
    private String isTest;
    @Override
    public void sftpUploadBRM(IOPUploadParam param) {
        List<String> phones = new ArrayList<>();
        phones.add("***********");
        phones.add("***********");
        phones.add("***********");
        // 修改后代码（使用Calendar计算前一天）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        String fileName = "IOTSTOCKINFO_" + DateTimeUtil.formatDate(calendar.getTime(), "yyyyMMdd") + ".txt";

        // 生成文件
        generateTxtFile(fileName, false);
        FileInputStream fileInputStream = null;

        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接brmsftp上传接口文件，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpWorkPath);
        try {
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");
                log.info("开始上传文件原名:{}", fileName);
                File file = new File(fileName);
                fileInputStream = new FileInputStream(file);
                sftpUtil.upload(sftpWorkPath, fileName, fileInputStream);
                //将文本名字写入redis中
                redisTemplate.opsForValue().set(REDIS_KEY_BRM_UPLOAD_TXT_PARAM, fileName, 2, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("SFTP上传文件失败！:{}", e.getMessage());
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datetime", DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_T));
            paramMap.put("result", "失败");
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            //将文本名字写入redis中  测试
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                    StatusConstant.OSS_UPLOAD_ERR.getMessage());
        } finally {
            sftpUtil.logout();
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            log.info("登出sftp服务！");
        }

    }

    public void sendFtpFile(String fileName) {
        FileInputStream fileInputStream = null;
        List<String> phones = new ArrayList<>();
        phones.add("***********");
//        phones.add("***********");
        phones.add("***********");
        phones.add("***********");
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接brmsftp上传接口文件，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpWorkPath);
        try {
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");
                log.info("开始上传文件原名:{}", fileName);
                File file = new File(fileName);
                fileInputStream = new FileInputStream(file);
                sftpUtil.upload(sftpWorkPath, fileName, fileInputStream);
                redisTemplate.opsForValue().set(REDIS_KEY_BRM_UPLOAD_TXT_PARAM, fileName, 2, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("SFTP上传文件失败！:{}", e.getMessage());
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("date", "brm上传文件失败");
            paramMap.put("result", "失败");
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                    StatusConstant.OSS_UPLOAD_ERR.getMessage());
        } finally {
            sftpUtil.logout();
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            log.info("登出sftp服务！");
        }
    }

    public void generateTxtFile(String fileName, Boolean isWrite) {
        List<BRMUploadDTO> brmUploadDTOList = brmMapperExt.getBRMUploadList();
        List<BRMUploadDTO> brmUploadDTOListNew = new ArrayList<>();
        // 按想同纬度计算各自的可用库存量
        for (BRMUploadDTO dto : brmUploadDTOList) {
            BRMUploadDTO dto1 = new BRMUploadDTO();
            BeanUtils.copyProperties(dto, dto1);
            brmUploadDTOListNew.add(dto1);
        }
        Map<String, Long> map = new HashMap<>();
        // 先按照全省：省级+全地市的可用求和
        // 地市：省级+某地市的可用求和
        for (BRMUploadDTO dto : brmUploadDTOListNew) {

            if (dto.getRegion().equals("200")) {
                // 全省：省级+全地市的可用求和（多原子取最小值）
                for (BRMUploadDTO dto1 : brmUploadDTOList) {
                    Boolean isSame = isSame(dto, dto1);
                    // Boolean isSameT = isSameCardTemple(dto, dto1);
                    dto.setXStockNum(isSame ? dto.getXStockNum() + dto1.getXStockNum() : dto.getXStockNum());
                    dto.setXPickNum(isSame ? dto.getXPickNum() + dto1.getXPickNum() : dto.getXPickNum());
                    // dto.setCardStockNum(isSameT ? dto.getCardStockNum() + dto1.getCardStockNum()
                    // : dto.getCardStockNum());
                    // dto.setCardPickNum(isSameT ? dto.getCardPickNum() + dto1.getCardPickNum() :
                    // dto.getCardPickNum());
                }

            } else {
                // 全省+本市秋求和
                for (BRMUploadDTO dto1 : brmUploadDTOList) {
                    Boolean isSame = isSameCity(dto, dto1);
                    // Boolean isSameT = isSameCardTemple(dto, dto1);
                    dto.setXStockNum(isSame ? dto.getXStockNum() + dto1.getXStockNum() : dto.getXStockNum());
                    dto.setXPickNum(isSame ? dto.getXPickNum() + dto1.getXPickNum() : dto.getXPickNum());
                    // dto.setCardStockNum(isSameT ? dto.getCardStockNum() + dto1.getCardStockNum()
                    // : dto.getCardStockNum());
                    // dto.setCardPickNum(isSameT ? dto.getCardPickNum() + dto1.getCardPickNum() :
                    // dto.getCardPickNum());
                }

            }
        }
        // 省级同一维度原子取最小值 设备可用库存量
        List<BRMUploadDTO> brmUploadDTOListNew1 = new ArrayList<>();

        // 使用临时集合存储需要添加的元素，避免在遍历过程中直接修改目标集合
        List<BRMUploadDTO> tempAddList = new ArrayList<>();

        for (BRMUploadDTO dto : brmUploadDTOListNew) {
            if (dto.getRegion().equals("200")) {
                if (brmUploadDTOListNew1.isEmpty()) {
                    brmUploadDTOListNew1.add(dto);
                } else {
                    boolean isAdded = false; // 标记是否已处理当前 dto

                    for (BRMUploadDTO dto1 : brmUploadDTOListNew1) {
                        if (isSame(dto, dto1)) {
                            if (dto.getXStockNum() < dto1.getXStockNum()) {
                                dto1.setXStockNum(dto.getXStockNum()); // 更新最小值
                            }
                            dto1.setXPickNum(dto1.getXPickNum() + dto.getXPickNum());
                            isAdded = true; // 已处理，无需再添加
                        }
                    }

                    // 如果未找到匹配项，则将当前 dto 添加到临时集合
                    if (!isAdded) {

                        tempAddList.add(dto);
                    }
                }
            } else {
                BRMUploadDTO dto1 = new BRMUploadDTO();
                BeanUtils.copyProperties(dto, dto1);
                tempAddList.add(dto1); // 将非 "200" 区域的 DTO 添加到临时集合
            }
        }

        // 将临时集合中的元素批量添加到目标集合
        brmUploadDTOListNew1.addAll(tempAddList);

        // 将列表写入txt中

        try {
            File file = new File(fileName);
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fw = new FileWriter(file);
            BufferedWriter bw = new BufferedWriter(fw);

            // 写入标题行
            // 写入标题行，使用字段名称
            String headerLine = String.join("|",
                    "region", // 地区标识
                    "bossId", // 产商品编码
                    "vasName", // 产商品名称
                    "xModelName", // 设备型号
                    "xStockNum", // 设备可用库存
                    "xPickNum", // 设备预占量
                    "cardTempleCode", // 开卡模板编码
                    "cardTempleName", // 开卡模板名称
                    "cardVenderCode", // 卡服务商编码
                    "cardVenderName", // 卡服务商名称
                    "cardStockNum", // 卡可用库存
                    "cardPickNum", // 卡预占量
                    "saleNum" // 销量
            ) + "\r\n"; // 添加Windows换行符
            bw.write(headerLine);

            for (BRMUploadDTO dto : brmUploadDTOListNew1) {

                String line = String.join("|",
                        String.valueOf(dto.getRegion()), // 地区标识
                        dto.getBossId(), // 产商品编码
                        dto.getVasName(), // 产商品名称
                        dto.getXModelName(), // 设备型号
                        String.valueOf(dto.getXStockNum()), // 设备可用库存
                        String.valueOf(dto.getXPickNum()), // 设备预占量
                        dto.getCardTempleCode(), // 开卡模板编码
                        dto.getCardTempleName(), // 开卡模板名称
                        dto.getCardVenderCode(), // 卡服务商编码
                        dto.getCardVenderName(), // 卡服务商名称
                        String.valueOf(dto.getCardStockNum()), // 卡可用库存
                        String.valueOf(dto.getCardPickNum()), // 卡预占量
                        String.valueOf(dto.getSaleNum()) // 销量
                ) + "\r\n"; // 添加Windows换行符
                bw.write(line.toString());
            }
            //            log.info("txt内容:{}", bw);
            // 下载到本地
            if (isWrite) {
                bw.flush();
                bw.close();
            }
            log.info("txt文件生成成功！");
        } catch (Exception e) {
            log.error("生成txt文件失败：{}", e.getMessage());
        }


    }

    private static Boolean isSame(BRMUploadDTO now, BRMUploadDTO old) {
        if (now.getRegion().equals(old.getRegion()) && now.getId().equals(old.getId())) {
            return false;
        }
        if (!now.getCardTempleCode().equals(old.getCardTempleCode())) {
            return false;
        }
        if (!now.getCardVenderCode().equals(old.getCardVenderCode())) {
            return false;
        }
        if (!now.getBossId().equals(old.getBossId())) {
            return false;
        }
        if (!now.getXModelName().equals(old.getXModelName())) {
            return false;
        }
        return true;
    }

    private static Boolean isSameCity(BRMUploadDTO now, BRMUploadDTO old) {
        if (now.getId().equals(old.getId()) && now.getRegion().equals(old.getRegion())) {
            return false;
        }
        if (!now.getRegion().equals(old.getRegion()) && !old.getRegion().equals("200")) {
            return false;
        }
        if (!now.getCardTempleCode().equals(old.getCardTempleCode())) {
            return false;
        }
        if (!now.getCardVenderCode().equals(old.getCardVenderCode())) {
            return false;
        }
        if (!now.getBossId().equals(old.getBossId())) {
            return false;
        }
        if (!now.getXModelName().equals(old.getXModelName())) {
            return false;
        }
        return true;
    }
    // ... 现有代码 ...

    @Override
    public String exportBRMData() {
        // 使用Calendar计算日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1); // 默认导出前一天数据

        String dateStr = DateTimeUtil.formatDate(calendar.getTime(), "yyyyMMdd");
        String fileName = "IOTSTOCKINFO_" + dateStr + ".txt";

        // 生成文件
        generateTxtFile(fileName, true);
        //上传ftp
        sendFtpFile(fileName);
        log.info("BRM数据导出成功，文件名：{}", fileName);
        return fileName;
    }

    private static Boolean isSameCardTemple(BRMUploadDTO now, BRMUploadDTO old) {
        // 同一原子，同一省份，代表同一个BRMUploadDTO
        if (now.getRegion().equals(old.getRegion()) && now.getId().equals(old.getId())) {
            return false;
        }
        if (!now.getCardTempleCode().equals(old.getCardTempleCode())) {
            return false;
        }
        if (!now.getCardVenderCode().equals(old.getCardVenderCode())) {
            return false;
        }
        //
        // if(now.getRegion().equals(old.getBossId())){
        // return false;
        // }
        return true;
    }

    // 从字符串末尾截取指定长度的子字符串
    private static String reverseSubstring(String str, int length) {
        int startIndex = str.length() - length;
        if (startIndex < 0) {
            startIndex = 0;
        }

        return str.substring(startIndex);
    }



    @Override
    public List<String> generateDetailTxtFiles(String fileName, Boolean isWrite) {
        List<String> generatedFiles = new ArrayList<>();
        List<BRMDetailUploadDTO> allData = brmMapperExt.getBRMDetailUploadList();

        if (allData == null || allData.isEmpty()) {
            log.warn("没有查询到广东终端ECSS数据");
            return generatedFiles;
        }

        log.info("查询到广东终端ECSS数据总数：{}", allData.size());

        // 按40万条数据分批生成文件
        final int MAX_RECORDS_PER_FILE = 400000;
        int fileIndex = 1;
        int currentRecordCount = 0;
        List<BRMDetailUploadDTO> currentBatch = new ArrayList<>();

        for (BRMDetailUploadDTO data : allData) {
            // 计算当前数据的总条数（可用设备明细条数 + 销量设备明细条数）
            int dataRecordCount = data.getTotalCount();

            // 如果加上当前数据会超过40万条，则先生成当前批次的文件
            if (currentRecordCount + dataRecordCount > MAX_RECORDS_PER_FILE && !currentBatch.isEmpty()) {
                String currentFileName = generateBatchFileName(fileName, fileIndex);
                generateSingleDetailTxtFile(currentFileName, currentBatch, isWrite);
                generatedFiles.add(currentFileName);

                // 重置批次
                currentBatch.clear();
                currentRecordCount = 0;
                fileIndex++;
            }

            // 添加当前数据到批次
            currentBatch.add(data);
            currentRecordCount += dataRecordCount;
        }

        // 处理最后一个批次
        if (!currentBatch.isEmpty()) {
            String currentFileName = generateBatchFileName(fileName, fileIndex);
            generateSingleDetailTxtFile(currentFileName, currentBatch, isWrite);
            generatedFiles.add(currentFileName);
        }

        log.info("广东终端ECSS数据文件生成完成，共生成{}个文件", generatedFiles.size());
        return generatedFiles;
    }

    @Override
    public void uploadDetailFilesToSftp(List<String> fileNames) {
        List<String> phones = new ArrayList<>();
      if(!isTest.equals("Y")){
          phones.add("***********");
          phones.add("***********");
          phones.add("***********");
      }

        if (fileNames == null || fileNames.isEmpty()) {
            log.warn("没有文件需要上传到SFTP服务器");
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datetime", DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_T));
            paramMap.put("result", "广东终端ECSS数据文件生成失败");
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            return;
        }



        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接BRM SFTP上传详细数据文件，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpWorkPath);

        try {
            if (sftpUtil.login()) {
                log.info("SFTP连接成功！");

                int successCount = 0;
                int failCount = 0;

                for (String fileName : fileNames) {
                    FileInputStream fileInputStream = null;
                    try {
                        log.info("开始上传详细数据文件：{}", fileName);
                        File file = new File(fileName);
                        if (!file.exists()) {
                            log.warn("文件不存在，跳过上传：{}", fileName);
                            failCount++;
                            continue;
                        }

                        fileInputStream = new FileInputStream(file);
                        sftpUtil.upload(sftpWorkPath, fileName, fileInputStream);
                        log.info("文件上传成功：{}", fileName);
                        successCount++;

                    } catch (Exception e) {
                        log.error("上传文件失败：{}，错误信息：{}", fileName, e.getMessage());
                        failCount++;
                    } finally {
                        if (fileInputStream != null) {
                            try {
                                fileInputStream.close();
                            } catch (IOException e) {
                                log.error("关闭文件流失败：{}", e.getMessage());
                            }
                        }
                    }
                }

                log.info("广东终端ECSS数据文件上传完成，成功：{}个，失败：{}个", successCount, failCount);

                // 清除上传成功的本地文件
                cleanUpLocalFiles(fileNames, successCount);

            } else {
                log.error("SFTP连接失败！");
                throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                        "SFTP连接失败");
            }

        } catch (Exception e) {
            log.error("SFTP上传广东终端ECSS数据文件失败！:{}", e.getMessage());
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datetime", DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_T));
            paramMap.put("result", "广东终端ECSS数据文件上传失败");
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                    StatusConstant.OSS_UPLOAD_ERR.getMessage());
        } finally {
            sftpUtil.logout();
            log.info("登出SFTP服务！");
        }
    }

    /**
     * 生成批次文件名
     */
    private String generateBatchFileName(String baseFileName, int fileIndex) {
        if (baseFileName.contains(".")) {
            String nameWithoutExt = baseFileName.substring(0, baseFileName.lastIndexOf("."));
            String extension = baseFileName.substring(baseFileName.lastIndexOf("."));
            return nameWithoutExt + "_" + String.format("%03d", fileIndex) + extension;
        } else {
            return baseFileName + "_" + String.format("%03d", fileIndex) + ".txt";
        }
    }

    /**
     * 生成单个详细数据文本文件
     */
    private void generateSingleDetailTxtFile(String fileName, List<BRMDetailUploadDTO> dataList, Boolean isWrite) {
        try {
            File file = new File(fileName);
            if (!file.exists()) {
                file.createNewFile();
            }

            FileWriter fw = new FileWriter(file);
            BufferedWriter bw = new BufferedWriter(fw);

            // 写入标题行
            String headerLine = String.join("|",
                    "deviceModel", // 设备型号
                    "cardTemplateCode", // 开卡模板编码
                    "brand", // 品牌
                    "dataSource", // 数据来源
                    "regionCode", // 地区标识
                    "reserveQuantity", // 库存预占数
                    "currentInventory", // 当前库存数
                    "totalInventory", // 总库存数
                    "availableDeviceDetails", // 可用设备明细
                    "salesQuantity", // 销量数
                    "salesDeviceDetails", // 销量设备明细
                    "relatedSkuProducts" // 关联规格商品
            ) + "\r\n";
            bw.write(headerLine);

            // 写入数据行
            for (BRMDetailUploadDTO data : dataList) {
                String line = String.join("|",
                        nullToEmpty(data.getDeviceModel()),
                        nullToEmpty(data.getCardTemplateCode()),
                        nullToEmpty(data.getBrand()),
                        nullToEmpty(data.getDataSource()),
                        nullToEmpty(data.getRegionCode()),
                        String.valueOf(data.getReserveQuantity() != null ? data.getReserveQuantity() : 0),
                        String.valueOf(data.getCurrentInventory() != null ? data.getCurrentInventory() : 0),
                        String.valueOf(data.getTotalInventory() != null ? data.getTotalInventory() : 0),
                        nullToEmpty(data.getAvailableDeviceDetails()),
                        String.valueOf(data.getSalesQuantity() != null ? data.getSalesQuantity() : 0),
                        nullToEmpty(data.getSalesDeviceDetails()),
                        nullToEmpty(data.getRelatedSkuProducts())
                ) + "\r\n";
                bw.write(line);
            }

            if (isWrite) {
                bw.flush();
                bw.close();
            }

            log.info("详细数据文件生成成功：{}，包含{}条记录", fileName, dataList.size());
        } catch (Exception e) {
            log.error("生成详细数据文件失败：{}，错误：{}", fileName, e.getMessage(), e);
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(),
                    "生成详细数据文件失败：" + e.getMessage());
        }
    }

    /**
     * 清除本地生成的文件
     *
     * @param fileNames 文件名列表
     * @param successCount 成功上传的文件数量
     */
    private void cleanUpLocalFiles(List<String> fileNames, int successCount) {
        if (fileNames == null || fileNames.isEmpty()) {
            return;
        }

        int deletedCount = 0;
        int deleteFailCount = 0;

        for (String fileName : fileNames) {
            try {
                File file = new File(fileName);
                if (file.exists()) {
                    if (file.delete()) {
                        log.info("成功删除本地文件：{}", fileName);
                        deletedCount++;
                    } else {
                        log.warn("删除本地文件失败：{}", fileName);
                        deleteFailCount++;
                    }
                } else {
                    log.warn("本地文件不存在，无需删除：{}", fileName);
                }
            } catch (Exception e) {
                log.error("删除本地文件异常：{}，错误信息：{}", fileName, e.getMessage());
                deleteFailCount++;
            }
        }

        log.info("本地文件清理完成，成功删除：{}个，失败：{}个", deletedCount, deleteFailCount);
    }

    /**
     * 空值转换为空字符串
     */
    private String nullToEmpty(String str) {
        return str != null ? str : "";
    }

}
