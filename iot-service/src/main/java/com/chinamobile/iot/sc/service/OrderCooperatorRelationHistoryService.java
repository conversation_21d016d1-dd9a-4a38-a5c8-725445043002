package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryByGroupDTO;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryDTO;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationHistory;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationHistoryExample;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoHistoryByGroupParam;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoHistoryParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 * @description 订单和从合作伙伴关系历史service接口类
 */
public interface OrderCooperatorRelationHistoryService {

    /**
     * 批量新增订单和从合作伙伴关系历史
     *
     * @param orderCooperatorRelationHistoryList
     */
    void batchAddOrderCooperatorRelationHistory(List<OrderCooperatorRelationHistory> orderCooperatorRelationHistoryList);


    /**
     * 根据需要获取订单和从合作伙伴关系历史数据
     *
     * @param orderCooperatorRelationHistoryExample
     * @return
     */
    List<OrderCooperatorRelationHistory> listOrderCooperatorRelationHistoryByNeed(OrderCooperatorRelationHistoryExample orderCooperatorRelationHistoryExample);

    /**
     * 获取订单和从合作伙伴关系历史用户信息
     *
     * @param atomOrderId
     * @param orderId
     * @return
     */
    List<Data4User> listCooperatorHistoryUserInfo(String atomOrderId, String orderId);

    /**
     * 获取组装后的原子订单和合作伙伴历史列表
     *
     * @param orderCooperatorInfoHistoryByGroupParam
     * @return
     */
    List<OrderCooperatorInfoHistoryByGroupDTO> listCooperatorInfoHistoryByGroup(OrderCooperatorInfoHistoryByGroupParam orderCooperatorInfoHistoryByGroupParam);

    /**
     * 获取原子订单和合作伙伴历史列表
     *
     * @param orderCooperatorInfoHistoryParam
     * @return
     */
    List<OrderCooperatorInfoHistoryDTO> listCooperatorInfoHistory(OrderCooperatorInfoHistoryParam orderCooperatorInfoHistoryParam);

}
