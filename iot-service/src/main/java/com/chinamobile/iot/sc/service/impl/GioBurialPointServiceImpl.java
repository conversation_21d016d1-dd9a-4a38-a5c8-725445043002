package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.chinamobile.iot.sc.RoleTypeMiniEnum;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.config.GrowingIOConfig;
import com.chinamobile.iot.sc.config.SupplierInfoConfig;
import com.chinamobile.iot.sc.config.ThreadExecutorConfig;
import com.chinamobile.iot.sc.constant.BusinessCodeEnum;
import com.chinamobile.iot.sc.constant.ManagerStatusEnum;
import com.chinamobile.iot.sc.constant.OfferingStatusEnum;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.constant.gio.OrderingChannelNameEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.GioBurialPointMapperExt;
import com.chinamobile.iot.sc.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.iot.sc.dao.handle.ProductHandlerMapper;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.OrderTypeEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.ChannelDataWashSucceedDTO;
import com.chinamobile.iot.sc.pojo.dto.StdServiceConfigDTO;
import com.chinamobile.iot.sc.pojo.dto.gio.*;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.mapper.OrderExportDO;
import com.chinamobile.iot.sc.pojo.param.BurialPoint.CouponParam;
import com.chinamobile.iot.sc.request.order2c.*;
import com.chinamobile.iot.sc.request.product.NavigationInfoDTO;
import com.chinamobile.iot.sc.request.product.SkuOfferingInfoDTO;
import com.chinamobile.iot.sc.request.sku.SkuOfferingInfoMDTO;
import com.chinamobile.iot.sc.request.sku.SkuReleaseTargetDTO;
import com.chinamobile.iot.sc.response.web.LogisticsInfoDTO;
import com.chinamobile.iot.sc.service.GioBurialPointService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import io.growing.sdk.java.GrowingAPI;
import io.growing.sdk.java.dto.GioCdpEventMessage;
import io.growing.sdk.java.dto.GioCdpItemMessage;
import io.growing.sdk.java.dto.GioCdpUserMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GioBurialPointServiceImpl implements GioBurialPointService {
    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;
    @Autowired
    private GrowingIOConfig growingIOConfig;
    @Resource
    private GioBurialPointMapperExt gioBurialPointMapperExt;
    @Resource
    private SupplierInfoConfig supplierInfo;
    @Resource
    private CardValueAddedInfoMapper cardValueAddedInfoMapper;
    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;
    @Resource
    private Order2cInfoMapper order2cInfoMapper;
    @Resource
    private Order2cAtomSnMapper order2cAtomSnMapper;
    @Resource
    private AtomOfferingInfoHistoryMapper atomOfferingInfoHistoryMapper;
    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;
    @Resource
    private SkuRoleRelationMapper skuRoleRelationMapper;
    @Resource
    private SkuOfferingInfoHistoryMapper skuOfferingInfoHistoryMapper;
    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;
    @Resource
    private ShopCustomerInfoMapper shopCustomerInfoMapper;
    @Resource
    private ShopManagerInfoMapper shopManagerInfoMapper;
    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;
    @Resource
    private Order2cDistributorInfoMapper order2cDistributorInfoMapper;
    @Resource
    private Order2cAgentInfoMapper order2cAgentInfoMapper;
    @Resource
    private Order2cAtomInfoMapperExt order2cAtomInfoMapperExt;

    @Resource
    private LogisticsInfoMapper logisticsInfoMapper;
    @Resource
    private CategoryInfoMapper categoryInfoMapper;
    @Resource
    private ProductHandlerMapper productHandlerMapper;
    @Resource
    private SkuReleaseTargetMapper skuReleaseTargetMapper;
    @Resource
    private AreaDataConfig areaDataConfig;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;
    @Value("${gio.gioAccountId}")
    private String gioAccountId;
    @Value("${gio.gioDataSourceId}")
    private String gioDataSourceId;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    @Resource
    private CouponInfoMapper couponInfoMapper;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;
    @Resource
    private NavigationInfoMapper navigationInfoMapper;
    @Resource
    private ProductNavigationDirectoryMapper productNavigationDirectoryMapper;
    @Resource
    private SpuSaleLabelMapper spuSaleLabelMapper;
    @Resource
    private RiseOrder2cGridMapper riseOrder2cGridMapper;
    private GrowingAPI project = new GrowingAPI.Builder().setProjectKey(gioAccountId)
            .setDataSourceId(gioDataSourceId).build();
    private static ValueFilter filter = (object, name, value) -> {
        if (value instanceof String) {
            // 直接返回原始字符串，避免转义
            return value;
        }
        return value;
    };



    @Override
    public void sendAtomMsg(AtomOfferingInfo localAtomInfo, AtomStdService atomStdService) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {

                String chargeCodeName = localAtomInfo.getChargeCode();
                if (StringUtils.isNotEmpty(localAtomInfo.getChargeCode())) {
                    String[] chargeCodeSplit = localAtomInfo.getChargeCode().split("_");
                    // 取最后一个
                    chargeCodeName = chargeCodeSplit[chargeCodeSplit.length - 1];
                }
                // 获取原子商品标准服务信息
                StdServiceConfigDTO stdServiceConfig=new StdServiceConfigDTO();
                if(atomStdService == null || atomStdService.getStdServiceId() == null){
                    if(localAtomInfo.getId()==null){
                        //获取对应code的所有atom信息
                        List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(new AtomOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(localAtomInfo.getOfferingCode()).example());
                        if(atomOfferingInfoList.size()>0){
                            for(AtomOfferingInfo atomOfferingInfo:atomOfferingInfoList){
                                stdServiceConfig = productHandlerMapper.selectStdServiceConfigGio(atomOfferingInfo.getId());
                                GioCdpItemMessage msg1 = new GioCdpItemMessage.Builder()
                                        .id(atomOfferingInfo.getId()) // 维度表模型ID(记录ID) (必填)
                                        .key("offering") // 维度表标识符 (必填)
                                        .addItemVariable("item_id", atomOfferingInfo.getId())
                                        .addItemVariable("offering_code", localAtomInfo.getOfferingCode())
                                        .addItemVariable("offering_name", localAtomInfo.getOfferingName())
                                        .addItemVariable("offering_class",
                                                AtomOfferingClassEnum.getDescribe(localAtomInfo.getOfferingClass()))
                                        .addItemVariable("charge_code_name", chargeCodeName)
                                        .addItemVariable("charge_code", localAtomInfo.getChargeCode())
                                        .addItemVariable("ext_soft_offering_code", localAtomInfo.getExtSoftOfferingCode())
                                        .addItemVariable("ext_hard_offering_code", localAtomInfo.getExtHardOfferingCode())
                                        .addItemVariable("offering_quantity", String.valueOf(localAtomInfo.getQuantity()))
                                        .addItemVariable("offering_unit", localAtomInfo.getUnit())
                                        .addItemVariable("offering_model", localAtomInfo.getModel())
                                        .addItemVariable("offering_color", localAtomInfo.getColor())
                                        .addItemVariable("offering_price",
                                                localAtomInfo.getAtomSalePrice() == null ? null :
                                                        String.valueOf(new BigDecimal(localAtomInfo.getAtomSalePrice() / 1000.00)
                                                                .setScale(2, RoundingMode.HALF_UP)
                                                                .doubleValue()))
                                        .addItemVariable("offering_settlement_price", localAtomInfo.getSettlePrice() == null ? null :
                                                String.valueOf(new BigDecimal(localAtomInfo.getSettlePrice() / 1000.00)
                                                        .setScale(2, RoundingMode.HALF_UP)
                                                        .doubleValue()))
                                        .addItemVariable("service_code",
                                                stdServiceConfig == null ? "-" : stdServiceConfig.getStdSvcId())
                                        .addItemVariable("service_name",
                                                stdServiceConfig == null ? "-" : stdServiceConfig.getStdSvcName()) // 商品配置在埋点
                                        .addItemVariable("product_name",
                                                stdServiceConfig == null ? "-" : stdServiceConfig.getRealProductName()) // 商品配置在埋点
                                        .addItemVariable("product_department",
                                                stdServiceConfig == null ? "-" : stdServiceConfig.getProductDept()) // 商品配置在埋点
                                        .addItemVariable("product_attributes",
                                                stdServiceConfig == null ? "-" : stdServiceConfig.getProductProperty()) // 商品配置在埋点
                                        .addItemVariable("is_delete_offering", localAtomInfo.getDeleteTime() == null ? "否" : "是") // 商品配置在埋点
                                        .build();
                                growingIOConfig.getProject().send(msg1);
                                log.info("原子商品维度表埋点信息,原子商品id:{},配置服务信息:{}",atomOfferingInfo.getId(),stdServiceConfig);
                            }

                        }
                        return;
                    }else{
                        stdServiceConfig = productHandlerMapper.selectStdServiceConfigGio(localAtomInfo.getId());
                    }
                }else{
                    stdServiceConfig = productHandlerMapper.selectStdServiceConfigGioByStdServiceId(atomStdService.getStdServiceId());
                }

                GioCdpItemMessage msg1 = new GioCdpItemMessage.Builder()
                        .id(localAtomInfo.getId()) // 维度表模型ID(记录ID) (必填)
                        .key("offering") // 维度表标识符 (必填)
                        .addItemVariable("item_id", localAtomInfo.getId())
                        .addItemVariable("offering_code", localAtomInfo.getOfferingCode())
                        .addItemVariable("offering_name", localAtomInfo.getOfferingName())
                        .addItemVariable("offering_class",
                                AtomOfferingClassEnum.getDescribe(localAtomInfo.getOfferingClass()))
                        .addItemVariable("charge_code_name", chargeCodeName)
                        .addItemVariable("charge_code", localAtomInfo.getChargeCode())
                        .addItemVariable("ext_soft_offering_code", localAtomInfo.getExtSoftOfferingCode())
                        .addItemVariable("ext_hard_offering_code", localAtomInfo.getExtHardOfferingCode())
                        .addItemVariable("offering_quantity", String.valueOf(localAtomInfo.getQuantity()))
                        .addItemVariable("offering_unit", localAtomInfo.getUnit())
                        .addItemVariable("offering_model", localAtomInfo.getModel())
                        .addItemVariable("offering_color", localAtomInfo.getColor())
                        .addItemVariable("offering_price",
                                localAtomInfo.getAtomSalePrice() == null ? null :
                                        String.valueOf(new BigDecimal(localAtomInfo.getAtomSalePrice() / 1000.00)
                                                .setScale(2, RoundingMode.HALF_UP)
                                                .doubleValue()))
                        .addItemVariable("offering_settlement_price", localAtomInfo.getSettlePrice() == null ? null :
                                String.valueOf(new BigDecimal(localAtomInfo.getSettlePrice() / 1000.00)
                                        .setScale(2, RoundingMode.HALF_UP)
                                        .doubleValue()))
                        .addItemVariable("service_code",
                                stdServiceConfig == null ? "-" : stdServiceConfig.getStdSvcId())
                        .addItemVariable("service_name",
                                stdServiceConfig == null ? "-" : stdServiceConfig.getStdSvcName()) // 商品配置在埋点
                        .addItemVariable("product_name",
                                stdServiceConfig == null ? "-" : stdServiceConfig.getRealProductName()) // 商品配置在埋点
                        .addItemVariable("product_department",
                                stdServiceConfig == null ? "-" : stdServiceConfig.getProductDept()) // 商品配置在埋点
                        .addItemVariable("product_attributes",
                                stdServiceConfig == null ? "-" : stdServiceConfig.getProductProperty()) // 商品配置在埋点
                        .addItemVariable("is_delete_offering", localAtomInfo.getDeleteTime() == null ? "否" : "是") // 商品配置在埋点
                        .build();
                growingIOConfig.getProject().send(msg1);
                log.info("原子商品维度表埋点信息,原子商品id:{},配置服务信息:{}",localAtomInfo.getId(),stdServiceConfig);
            } catch (Exception e) {
                log.error("原子商品维度表埋点失败,原子商品id:{},失败原因:{}",localAtomInfo.getId() ,e);
            }
        });

    }

    @Override
    public void sendSkuMsg(SkuOfferingInfo skuOfferingInfo, SkuOfferingInfoMDTO remoteSkuInfo) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            // sku维度埋点
            try {
                List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(
                        new SpuOfferingInfoExample().createCriteria()
                                .andOfferingCodeEqualTo(remoteSkuInfo.getSpuOfferingCode())
                                .example());
                SpuOfferingInfo spuOfferingInfo = spuOfferingInfos.get(0);
                List<CategoryInfo> categoryInfos = categoryInfoMapper.selectByExample(
                        new CategoryInfoExample().createCriteria()
                                .andSpuIdEqualTo(spuOfferingInfo.getId())
                                .example());
                CategoryInfo categoryInfo = categoryInfos.get(0);
                // 获取发布省市
                List<SkuReleaseTarget> releaseTargets = skuReleaseTargetMapper.selectByExample(
                        new SkuReleaseTargetExample().createCriteria()
                                .andSkuOfferingCodeEqualTo(skuOfferingInfo.getOfferingCode())
                                .example());

                StringBuilder cityStr = new StringBuilder();
                for (SkuReleaseTarget releaseTarget : releaseTargets) {
                    String proCity = "";
                    Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                    Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();

                    if (StringUtils.isNotBlank(releaseTarget.getProvinceCode())) {
                        if ("471".equals(releaseTarget.getProvinceCode())) {
                            proCity = "内蒙古";

                        } else {
                            proCity = (String) provinceCodeNameMap.get(releaseTarget.getProvinceCode());

                        }
                    }
                    if (StringUtils.isNotBlank(releaseTarget.getCityCode())) {
                        proCity = proCity + "|" + (String) locationCodeNameMap.get(releaseTarget.getCityCode());

                    }
                    cityStr.append(proCity).append("||");

                }
                // 去除最后一个||
                if (cityStr.length() > 0) {
                    cityStr.delete(cityStr.length() - 2, cityStr.length());
                }
                // 积分
                List<SkuRoleRelation> skuRoleRelations = skuRoleRelationMapper.selectByExample(
                        new SkuRoleRelationExample().createCriteria().andSkuIdEqualTo(skuOfferingInfo.getId())
                                .example());
                SkuRoleRelation skuRoleRelation = skuRoleRelations == null ? null : skuRoleRelations.get(0);

                // 查询新增字段：导航目录、销售标签、商品关键字
                String firstLevelNavCatalog = null;
                String secondLevelNavCatalog = null;
                String thirdLevelNavCatalog = null;
                String mainSaleTag = null;
                String subSaleTag = null;
                String productKeyword = spuOfferingInfo.getProductKeywords();

                // 查询导航目录
                List<NavigationInfo> navigationInfos = navigationInfoMapper.selectByExample(
                        new NavigationInfoExample().createCriteria()
                                .andSpuOfferingCodeEqualTo(spuOfferingInfo.getOfferingCode())
                                .example());
                if (!CollectionUtils.isEmpty(navigationInfos)) {
                    NavigationInfo navigationInfo = navigationInfos.get(0);
                    // 使用重试机制查询导航目录
                    firstLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel1NavigationCode(), "一级");
                    secondLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel2NavigationCode(), "二级");
                    thirdLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel3NavigationCode(), "三级");
                }

                // 查询销售标签
                List<SpuSaleLabel> mainSaleLabels = spuSaleLabelMapper.selectByExample(
                        new SpuSaleLabelExample().createCriteria()
                                .andSpuCodeEqualTo(spuOfferingInfo.getOfferingCode())
                                .andTypeEqualTo("0")
                                .example());
                if (!CollectionUtils.isEmpty(mainSaleLabels)) {
                    mainSaleTag = mainSaleLabels.stream().map(SpuSaleLabel::getLabel).filter(Objects::nonNull).collect(Collectors.joining(","));
                }

                List<SpuSaleLabel> subSaleLabels = spuSaleLabelMapper.selectByExample(
                        new SpuSaleLabelExample().createCriteria()
                                .andSpuCodeEqualTo(spuOfferingInfo.getOfferingCode())
                                .andTypeEqualTo("1")
                                .example());
                if (!CollectionUtils.isEmpty(subSaleLabels)) {
                    subSaleTag = subSaleLabels.stream().map(SpuSaleLabel::getLabel).filter(Objects::nonNull).collect(Collectors.joining(","));
                }
                log.info("spu编码:{},副标签内容:{},商品关键字:{}",spuOfferingInfo.getOfferingCode(),subSaleTag,productKeyword);
                GioCdpItemMessage msg = new GioCdpItemMessage.Builder()
                        .id(skuOfferingInfo.getOfferingCode()) // 维度表模型ID(记录ID) (必填)
                        .key("Sku") // 维度表标识符 (必填)
                        .addItemVariable("item_id", skuOfferingInfo.getOfferingCode())
                        .addItemVariable("sku_name", skuOfferingInfo.getOfferingName())
                        .addItemVariable("spu_version", remoteSkuInfo.getSpuOfferingVersion())
                        .addItemVariable("sku_version", remoteSkuInfo.getSkuOfferingVersion())
                        .addItemVariable("sku_price", skuOfferingInfo.getPrice() == null ? null : String.valueOf(new BigDecimal(skuOfferingInfo.getPrice() / 1000.00)
                                .setScale(2, RoundingMode.HALF_UP)
                                .doubleValue()))
                        .addItemVariable("sku_status",
                                skuOfferingInfo.getOfferingStatus() == null ? null :
                                        OfferingStatusEnum.fromCode(skuOfferingInfo.getOfferingStatus()).name)
                        .addItemVariable("sku_province", cityStr.toString())

                        .addItemVariable("spu_name", spuOfferingInfo.getOfferingName())
                        .addItemVariable("spu_code", spuOfferingInfo.getOfferingCode())
                        .addItemVariable("spu_type", categoryInfo == null || categoryInfo.getOfferingClass() == null ? null : SPUOfferingClassEnum.getDisplay(categoryInfo.getOfferingClass()))
                        .addItemVariable("spu_create", skuOfferingInfo.getCreateTime() == null
                                ? remoteSkuInfo.getSkuOfferingStatusTime() == null ? null : DateTimeUtil.formatDate(remoteSkuInfo.getSkuOfferingStatusTime(),
                                DateTimeUtil.STANDARD_DAY)
                                : DateTimeUtil.formatDate(skuOfferingInfo.getCreateTime(), DateTimeUtil.STANDARD_DAY))
                        .addItemVariable("spu_sales_type",
                                spuOfferingInfo.getOfferingStatus() == null ? null :
                                        OfferingStatusEnum.fromCode(spuOfferingInfo.getOfferingStatus()).name)
                        .addItemVariable("supplier_code", "-")
                        .addItemVariable("supplier_name", skuOfferingInfo.getSupplierName())
                        .addItemVariable("supplier_contact", "-")
                        .addItemVariable("contact_phone", "-")
                        .addItemVariable("commission_rate",
                                skuRoleRelation == null || skuRoleRelation.getPointPercent() == null ? null :
                                        String.valueOf(new BigDecimal(skuRoleRelation.getPointPercent() / 100)
                                                .setScale(4, RoundingMode.HALF_UP)
                                                .doubleValue())) // 后面再加
                        .addItemVariable("is_delete", skuOfferingInfo.getDeleteTime() == null ? "否" : "是")
                        // 新增字段
                        .addItemVariable("firstLevelNavCatalog", firstLevelNavCatalog)
                        .addItemVariable("secondLevelNavCatalog", secondLevelNavCatalog)
                        .addItemVariable("thirdLevelNavCatalog", thirdLevelNavCatalog)
                        .addItemVariable("productKeyword", productKeyword)
                        .addItemVariable("mainSaleTag", mainSaleTag)
                        .addItemVariable("subSaleTag", subSaleTag)
                        .build();
                growingIOConfig.getProject().send(msg);
            } catch (Exception e) {
                log.error("sku维度表埋点失败,sku商品编码:{},失败原因:{}",skuOfferingInfo.getOfferingCode(), e);
            }
        });

    }

    @Override
    public void sendSkuMsg(SkuOfferingInfo skuOfferingInfo, SkuOfferingInfoDTO remoteSkuInfo,
                           SpuOfferingInfo spuOfferingInfo, String spuVersion, String spuOfferingClass, List<SpuSaleLabelHistory> spuSaleLabelHistoryList, List<NavigationInfoDTO> navigationInfoDTOS) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            // sku维度埋点
            try {
                CategoryInfo categoryInfo = null;
                if (spuOfferingClass == null) {
                    List<CategoryInfo> categoryInfos = categoryInfoMapper.selectByExample(
                            new CategoryInfoExample().createCriteria()
                                    .andSpuIdEqualTo(spuOfferingInfo.getId())
                                    .example());

                    if (categoryInfos != null && categoryInfos.size() > 0) {
                        categoryInfo = categoryInfos.get(0);
                    }

                }


                // 获取发布省市
                List<SkuReleaseTarget> releaseTargets = skuReleaseTargetMapper.selectByExample(
                        new SkuReleaseTargetExample().createCriteria()
                                .andSkuOfferingCodeEqualTo(skuOfferingInfo.getOfferingCode())
                                .example());
                StringBuilder cityStr = new StringBuilder();
                for (SkuReleaseTarget releaseTarget : releaseTargets) {
                    String proCity = "";
                    Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                    Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                    Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
                    Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();

                    if (StringUtils.isNotBlank(releaseTarget.getProvinceCode())) {
                        if ("471".equals(releaseTarget.getProvinceCode())) {
                            proCity = "内蒙古";

                        } else {
                            proCity = (String) provinceCodeNameMap.get(releaseTarget.getProvinceCode());

                        }
                    }
                    if (StringUtils.isNotBlank(releaseTarget.getCityCode())) {
                        proCity = proCity + "|" + (String) locationCodeNameMap.get(releaseTarget.getCityCode());

                    }
                    cityStr.append(proCity).append("||");

                }
                // 去除最后一个||
                if (cityStr.length() > 0) {
                    cityStr.delete(cityStr.length() - 2, cityStr.length());
                }
                // 积分
                List<SkuRoleRelation> skuRoleRelations = skuRoleRelationMapper.selectByExample(
                        new SkuRoleRelationExample().createCriteria().andSkuIdEqualTo(skuOfferingInfo.getId())
                                .example());
                SkuRoleRelation skuRoleRelation = null;
                if (skuRoleRelations != null && skuRoleRelations.size() > 0) {
                    skuRoleRelation = skuRoleRelations.get(0);
                }

                // 查询新增字段：导航目录、销售标签、商品关键字
                String firstLevelNavCatalog = null;
                String secondLevelNavCatalog = null;
                String thirdLevelNavCatalog = null;
                String mainSaleTag = null;
                String subSaleTag = null;
                String productKeyword = spuOfferingInfo.getProductKeywords();

                // 处理导航目录信息 - 优先从参数 navigationInfoDTOS 中获取
                NavigationInfo navigationInfo = null;

                // 先从参数 navigationInfoDTOS 中获取导航信息
                if (!CollectionUtils.isEmpty(navigationInfoDTOS)) {
                    NavigationInfoDTO navigationInfoDTO = navigationInfoDTOS.get(0);
                    if (navigationInfoDTO != null && !CollectionUtils.isEmpty(navigationInfoDTO.getLevel1Navigation())) {
                        // 从 NavigationInfoDTO 中提取导航编码信息
                        NavigationInfoDTO.Level1NavigationDTO level1 = navigationInfoDTO.getLevel1Navigation().get(0);
                        if (level1 != null) {
                            navigationInfo = new NavigationInfo();
                            navigationInfo.setLevel1NavigationCode(level1.getLevel1NavigationCode());

                            // 提取二级导航编码
                            if (!CollectionUtils.isEmpty(level1.getLevel2Navigation())) {
                                NavigationInfoDTO.Level2NavigationDTO level2 = level1.getLevel2Navigation().get(0);
                                if (level2 != null) {
                                    navigationInfo.setLevel2NavigationCode(level2.getLevel2NavigationCode());

                                    // 提取三级导航编码
                                    if (!CollectionUtils.isEmpty(level2.getLevel3Navigation())) {
                                        NavigationInfoDTO.Level3NavigationDTO level3 = level2.getLevel3Navigation().get(0);
                                        if (level3 != null) {
                                            navigationInfo.setLevel3NavigationCode(level3.getLevel3NavigationCode());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    log.info("从参数 navigationInfoDTOS 中获取到导航信息，SPU编码: {}", spuOfferingInfo.getOfferingCode());
                }

                // 如果从参数中没有获取到导航信息，则查询数据库
                if (navigationInfo == null) {
                    List<NavigationInfo> navigationInfos = navigationInfoMapper.selectByExample(
                            new NavigationInfoExample().createCriteria()
                                    .andSpuOfferingCodeEqualTo(spuOfferingInfo.getOfferingCode())
                                    .example());
                    if (!CollectionUtils.isEmpty(navigationInfos)) {
                        navigationInfo = navigationInfos.get(0);
                        log.info("从数据库中查询到导航信息，SPU编码: {}", spuOfferingInfo.getOfferingCode());
                    }
                }

                // 处理查询到的导航信息
                if (navigationInfo != null) {
                    // 使用重试机制查询导航目录
                    firstLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel1NavigationCode(), "一级");
                    secondLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel2NavigationCode(), "二级");
                    thirdLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel3NavigationCode(), "三级");
                }

                // 从参数spuSaleLabelHistoryList中获取销售标签
                if (!CollectionUtils.isEmpty(spuSaleLabelHistoryList)) {
                    // 获取主标签（type="0"）
                    mainSaleTag = spuSaleLabelHistoryList.stream()
                            .filter(label -> "0".equals(label.getType()))
                            .map(SpuSaleLabelHistory::getLabel)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(","));

                    // 获取副标签（type="1"）
                    subSaleTag = spuSaleLabelHistoryList.stream()
                            .filter(label -> "1".equals(label.getType()))
                            .map(SpuSaleLabelHistory::getLabel)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(","));
                }
                log.info("spu编码:{},副标签内容:{},商品关键字:{}",spuOfferingInfo.getOfferingCode(),subSaleTag,productKeyword);
                GioCdpItemMessage msg = new GioCdpItemMessage.Builder()
                        .id(skuOfferingInfo.getOfferingCode()) // 维度表模型ID(记录ID) (必填)
                        .key("Sku") // 维度表标识符 (必填)
                        .addItemVariable("item_id", skuOfferingInfo.getOfferingCode())
                        .addItemVariable("sku_name", skuOfferingInfo.getOfferingName())
                        .addItemVariable("spu_version", spuVersion)
                        .addItemVariable("sku_version", remoteSkuInfo.getSkuOfferingVersion())
                        .addItemVariable("sku_price", skuOfferingInfo.getPrice() == null ? null : String.valueOf(new BigDecimal(skuOfferingInfo.getPrice() / 1000.00)
                                .setScale(2, RoundingMode.HALF_UP)
                                .doubleValue()))
                        .addItemVariable("sku_status",
                                skuOfferingInfo.getOfferingStatus() == null ? null :
                                        OfferingStatusEnum.fromCode(skuOfferingInfo.getOfferingStatus()).name)
                        .addItemVariable("sku_province", cityStr.toString())
                        .addItemVariable("spu_name", spuOfferingInfo.getOfferingName())
                        .addItemVariable("spu_code", spuOfferingInfo.getOfferingCode())
                        .addItemVariable("spu_type", categoryInfo == null || categoryInfo.getOfferingClass() == null ? SPUOfferingClassEnum.getDisplay(spuOfferingClass) : SPUOfferingClassEnum.getDisplay(categoryInfo.getOfferingClass()))
                        .addItemVariable("spu_create", skuOfferingInfo.getCreateTime() == null
                                ? remoteSkuInfo.getSkuOfferingStatusTime() == null ? null : DateTimeUtil.formatDate(remoteSkuInfo.getSkuOfferingStatusTime(),
                                DateTimeUtil.STANDARD_DAY)
                                : DateTimeUtil.formatDate(skuOfferingInfo.getCreateTime(), DateTimeUtil.STANDARD_DAY))
                        .addItemVariable("spu_sales_type",
                                spuOfferingInfo.getOfferingStatus() == null ? null :
                                        OfferingStatusEnum.fromCode(spuOfferingInfo.getOfferingStatus()).name)
                        .addItemVariable("supplier_code", "-")
                        .addItemVariable("supplier_name", skuOfferingInfo.getSupplierName())
                        .addItemVariable("supplier_contact", "-")
                        .addItemVariable("contact_phone", "-")
                        .addItemVariable("commission_rate",
                                skuRoleRelation == null || skuRoleRelation.getPointPercent() == null ? null :
                                        String.valueOf(new BigDecimal(skuRoleRelation.getPointPercent() / 100)
                                                .setScale(4, RoundingMode.HALF_UP)
                                                .doubleValue())) // 后面再加
                        .addItemVariable("is_delete", skuOfferingInfo.getDeleteTime() == null ? "否" : "是")
                        // 新增字段
                        .addItemVariable("firstLevelNavCatalog", firstLevelNavCatalog)
                        .addItemVariable("secondLevelNavCatalog", secondLevelNavCatalog)
                        .addItemVariable("thirdLevelNavCatalog", thirdLevelNavCatalog)
                        .addItemVariable("productKeyword", productKeyword)
                        .addItemVariable("mainSaleTag", mainSaleTag)
                        .addItemVariable("subSaleTag", subSaleTag)
                        .build();
                growingIOConfig.getProject().send(msg);
            } catch (Exception e) {
                log.error("sku维度表埋点失败,sku商品编码:{},失败原因:{}",skuOfferingInfo.getOfferingCode() ,e);
            }
        });

    }

    @Override
    public void productShelfAnalysis(SkuOfferingInfo skuOfferingInfo, AtomOfferingInfo atomOfferingInfo,
                                     List<SkuReleaseTargetDTO> releaseTargetList, String userId) {
        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                if (releaseTargetList == null) {
                    List<SkuReleaseTarget> skuReleaseTargets = skuReleaseTargetMapper
                            .selectByExample(new SkuReleaseTargetExample().createCriteria()
                                    .andSkuOfferingCodeEqualTo(skuOfferingInfo.getOfferingCode()).example());
                    if (CollectionUtils.isNotEmpty(skuReleaseTargets)) {
                        for (SkuReleaseTarget skuReleaseTarget : skuReleaseTargets) {
                            String province = "";
                            String city = "";

                            if (StringUtils.isNotBlank(skuReleaseTarget.getProvinceCode())) {
                                if ("471".equals(skuReleaseTarget.getProvinceCode())) {
                                    province = "内蒙古";

                                } else {
                                    province = (String) provinceCodeNameMap.get(skuReleaseTarget.getProvinceCode());

                                }
                            }
                            if (StringUtils.isNotBlank(skuReleaseTarget.getCityCode())) {
                                city = (String) locationCodeNameMap.get(skuReleaseTarget.getCityCode());

                            }

                            GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
                                    .eventTime(System.currentTimeMillis()) // 默认为系统当前时间 (选填)
                                    .eventKey("H5_productLaunch") // 埋点事件标识 (必填)
                                    .loginUserId(userId) // 登陆用户ID (选填)
                                    .addEventVariable("skuCode_var", skuOfferingInfo.getOfferingCode())
                                    .addEventVariable("offeringId_var", atomOfferingInfo.getId())
                                    .addEventVariable("offeringCode_var", atomOfferingInfo.getOfferingCode())
                                    .addEventVariable("sku_province", province)
                                    .addEventVariable("sku_city", city)
                                    .addEventVariable("cusProvinceCode_var", skuReleaseTarget.getProvinceCode())
                                    .addEventVariable("cusCityCode_var", skuReleaseTarget.getCityCode())
                                    .build();
                            growingIOConfig.getProject().send(eventMessage);
                        }
                    }
                } else {
                    for (SkuReleaseTargetDTO skuReleaseTargetDTO : releaseTargetList) {
                        String province = "";
                        String city = "";

                        if (StringUtils.isNotBlank(skuReleaseTargetDTO.getProvince())) {
                            if ("471".equals(skuReleaseTargetDTO.getProvince())) {
                                province = "内蒙古";

                            } else {
                                province = (String) provinceCodeNameMap.get(skuReleaseTargetDTO.getProvince());

                            }
                        }
                        if (skuReleaseTargetDTO.getCityList() != null) {
                            for (String cusCityCode_var : skuReleaseTargetDTO.getCityList()) {

                                if (StringUtils.isNotBlank(cusCityCode_var)) {
                                    city = (String) locationCodeNameMap.get(cusCityCode_var);

                                }
                                GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
                                        .eventTime(System.currentTimeMillis()) // 默认为系统当前时间 (选填)
                                        .eventKey("H5_productLaunch") // 埋点事件标识 (必填)
                                        .loginUserId(userId) // 登陆用户ID (选填)
                                        .addEventVariable("skuCode_var", skuOfferingInfo.getOfferingCode())
                                        .addEventVariable("offeringId_var", atomOfferingInfo.getId())
                                        .addEventVariable("offeringCode_var", atomOfferingInfo.getOfferingCode())
                                        .addEventVariable("sku_province", province)
                                        .addEventVariable("sku_city", city)
                                        .addEventVariable("cusProvinceCode_var", skuReleaseTargetDTO.getProvince())
                                        .addEventVariable("cusCityCode_var", cusCityCode_var)
                                        .build();
                                growingIOConfig.getProject().send(eventMessage);
                            }
                        } else {
                            GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
                                    .eventTime(System.currentTimeMillis()) // 默认为系统当前时间 (选填)
                                    .eventKey("H5_productLaunch") // 埋点事件标识 (必填)
                                    .loginUserId(userId) // 登陆用户ID (选填)
                                    .addEventVariable("skuCode_var", skuOfferingInfo.getOfferingCode())
                                    .addEventVariable("offeringId_var", atomOfferingInfo.getId())
                                    .addEventVariable("offering_code", atomOfferingInfo.getOfferingCode())
                                    .addEventVariable("sku_province", province)
                                    .addEventVariable("sku_city", city)
                                    .addEventVariable("cusProvinceCode_var", skuReleaseTargetDTO.getProvince())
                                    .addEventVariable("cusCityCode_var", "-")
                                    .build();
                            growingIOConfig.getProject().send(eventMessage);
                        }

                    }
                }

            } catch (Exception e) {
                log.error("产品上架分析埋点失败,sku商品编码:{},失败原因:{}",skuOfferingInfo.getOfferingCode() ,e);
            }
        });

    }

    @Override
    public void sendH5ProductOrderCreateMsg(OrderInfoDTO orderInfo, SpuOfferingInfoDTO spuOfferingInfoDto,
                                            com.chinamobile.iot.sc.request.order2c.SkuOfferingInfoDTO skuInfoDto, String atomVersion,
                                            AtomOfferingInfo originAtomInfo, Order2cAtomInfo order2cAtomInfo) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {

                // 原子结算金额(物联网收入) 排除A06H 和A13A的 再加上卡+x增值服务的
                Long atomAmountCollected = 0L;
                if (spuOfferingInfoDto.getOfferingClass().equals("A06")) {

                    if (!order2cAtomInfo.getAtomOfferingClass().equals("H")) {

                        atomAmountCollected = order2cAtomInfo.getAtomPrice() == null ? 0L : (long) (order2cAtomInfo.getAtomPrice() / 1000.00);
                    }

                } else if (spuOfferingInfoDto.getOfferingClass().equals("A13")) {

                    if (!order2cAtomInfo.getAtomOfferingClass().equals("A")) {

                        atomAmountCollected = order2cAtomInfo.getAtomPrice() == null ? 0L : (long) (order2cAtomInfo.getAtomPrice() / 1000.00);
                    }

                } else {

                    atomAmountCollected = order2cAtomInfo.getAtomPrice() == null ? 0L : (long) (order2cAtomInfo.getAtomPrice() / 1000.00);

                }

                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
                Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
                String provinceName = "";
                if (orderInfo.getCustInfo() != null && StringUtils.isNotBlank(orderInfo.getCustInfo().getBeId())) {
                    provinceName = "471".equals(orderInfo.getCustInfo().getBeId()) ? "内蒙古"
                            : (String) provinceCodeNameMap.get(orderInfo.getCustInfo().getBeId());
                }
                String cityName = "";
                if (orderInfo.getCustInfo() != null && StringUtils.isNotBlank(orderInfo.getCustInfo().getLocation())) {
                    cityName = (String) locationCodeNameMap.get(orderInfo.getCustInfo().getLocation());
                }
                String regionName = "";

                if (orderInfo.getCustInfo() != null && StringUtils.isNotEmpty(orderInfo.getCustInfo().getRegionID())
                        && "10003329".equals(orderInfo.getCustInfo().getRegionID())) {
                    regionName = "綦江区";
                } else {
                    regionName = orderInfo.getCustInfo() != null && StringUtils.isNotEmpty(orderInfo.getCustInfo().getRegionID())
                            ? (String) regionCodeNameMap.get(orderInfo.getCustInfo().getRegionID())
                            : null;

                }
                String channelPartnerPhone = "";

                if (orderInfo.getAgentInfo() != null) {

                    channelPartnerPhone = orderInfo.getAgentInfo().size() == 0 ? null :extractAgentPhonesFromAgentInfoDTO(orderInfo.getAgentInfo()) ;

                } else {
                    List<Order2cAgentInfo> order2cAgentInfos = order2cAgentInfoMapper
                            .selectByExample(new Order2cAgentInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    if (CollectionUtils.isNotEmpty(order2cAgentInfos)) {

                        channelPartnerPhone = extractAgentPhonesFromAgentInfo(order2cAgentInfos);

                    }
                }
                String customerManagerPhone = "";
                String customerManagerCode = "";
                customerManagerCode = orderInfo.getEmployeeNum() == null ? null : orderInfo.getEmployeeNum();
                customerManagerPhone = orderInfo.getCustomerManagerPhone() == null ? null : IOTEncodeUtils.decryptSM4(orderInfo.getCustomerManagerPhone(), iotSm4Key, iotSm4Iv);

                String userId = orderInfo.getCreateOperUserID() == null ? orderInfo.getCustInfo() != null && orderInfo.getCustInfo().getCustUserID() != null ? orderInfo.getCustInfo().getCustUserID() : null
                        : orderInfo.getCreateOperUserID();
                userId = userId == null ? orderInfo.getCustInfo() != null && orderInfo.getCustInfo().getCustCode() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key, iotSm4Iv) : null : userId;
                // 事件行为消息体，anonymousId 和 loginUserId 参数，不能同时为空
                long eventTime = System.currentTimeMillis();
                if (orderInfo.getCreateTime() != null) {
                    Date parsedDate = DateTimeUtil.parseCreateTimeToDateSafely(orderInfo.getCreateTime());
                    if (parsedDate != null) {
                        eventTime = parsedDate.getTime();
                    } else {
                        log.warn("解析订单创建时间失败，使用当前时间: {}", orderInfo.getCreateTime());
                    }
                }
                Order2cInfo order2cInfo1 = order2cInfoMapper.selectByPrimaryKey(orderInfo.getOrderId());
                String[] locations;
                if (orderInfo.getOrderOrgBizInfo() != null) {
                    if (orderInfo.getOrderOrgBizInfo().getOrgName() == null
                            && orderInfo.getOrderOrgBizInfo().getProvinceOrgName() == null) {
                        if (order2cInfo1 != null) {
                            locations = (order2cInfo1.getOrgName() == null || order2cInfo1.getOrgName().equals(""))
                                    ? order2cInfo1.getProvinceOrgName().split("-")
                                    : order2cInfo1.getOrgName().split("-");
                        } else {
                            locations = null;
                        }
                    } else {
                        locations = (orderInfo.getOrderOrgBizInfo().getOrgName() == null
                                || orderInfo.getOrderOrgBizInfo().getOrgName().equals(""))
                                ? orderInfo.getOrderOrgBizInfo().getProvinceOrgName().split("-")
                                : orderInfo.getOrderOrgBizInfo().getOrgName().split("-");
                    }
                } else{
                    locations = null;
                }

                // 解析订单归属省市区
                String orderProvinceName = locations != null && locations.length > 0 ? locations[0] : null;
                String orderCityName = locations != null && locations.length > 1 ? locations[1] : null;
                String orderAreaName = locations != null && locations.length > 2 ? locations[2] : null;
                List<CouponInfoDTO> decryptOrderInfoCouponList = decryptOrderInfoCouponList(orderInfo.getCouponInfo());
                GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
                        .eventTime(eventTime) // 使用订单创建时间 (选填)
                        .eventKey("H5_ProductOrderCreate") // 埋点事件标识 (必填)
                        .loginUserId(userId) // 登陆用户ID (选填)
                        .addEventVariable("platformName_var", "中后台") // 事件属性 (选填)
                        .addEventVariable("orderId_var", orderInfo.getOrderId())
                        .addEventVariable("orderType_var", orderInfo.getOrderType() == null ? null : OrderTypeEnum.getDescByCode(orderInfo.getOrderType()))
                        .addEventVariable("spu_version_var", spuOfferingInfoDto.getSpuOfferingVersion())
                        .addEventVariable("sku_version_var", skuInfoDto.getSkuOfferingVersion())
                        .addEventVariable("offering_version_var", atomVersion)
                        .addEventVariable("skuCode_var", skuInfoDto.getOfferingCode())
                        .addEventVariable("productPrice_var",
                                order2cAtomInfo.getSkuPrice() == null ? null :
                                        new BigDecimal(order2cAtomInfo.getSkuPrice() / 1000.00).setScale(2, RoundingMode.HALF_UP)
                                                .doubleValue())
                        .addEventVariable("quantity_var", skuInfoDto.getQuantity() == null ? null : Math.toIntExact(skuInfoDto.getQuantity()))
                        .addEventVariable("totalPrice_var",
                                order2cAtomInfo.getSkuQuantity() == null || order2cAtomInfo.getAtomQuantity() == null || order2cAtomInfo.getAtomQuantity() == null ? null :
                                        order2cAtomInfo.getSkuQuantity() * order2cAtomInfo.getAtomQuantity() * order2cAtomInfo.getAtomPrice() / 1000.00)
                        .addEventVariable("offeringId_var", originAtomInfo.getId())
                        .addEventVariable("offeringCode_var", originAtomInfo.getOfferingCode())
                        .addEventVariable("offeringPrice_var",
                                order2cAtomInfo.getAtomPrice() == null ? null :
                                        new BigDecimal(order2cAtomInfo.getAtomPrice() / 1000.00)
                                                .setScale(2, RoundingMode.HALF_UP)
                                                .doubleValue())
                        .addEventVariable("offeringsettlePrice_var",
                                order2cAtomInfo.getAtomSettlePrice() == null ? null :
                                        new BigDecimal(order2cAtomInfo.getAtomSettlePrice() / 1000.00)
                                                .setScale(2, RoundingMode.HALF_UP)
                                                .doubleValue())
                        .addEventVariable("offeringtotalPrice_var", Math.toIntExact(atomAmountCollected)) // 原子商品计收金额，后面算
                        .addEventVariable("offeringQuantity_var", order2cAtomInfo.getAtomQuantity() == null || order2cAtomInfo.getSkuQuantity() == null ? null : Math.toIntExact(order2cAtomInfo.getAtomQuantity() * order2cAtomInfo.getSkuQuantity()))
                        .addEventVariable("cusProvinceCode_var", orderInfo.getCustInfo().getBeId())
                        .addEventVariable("cusCityCode_var", orderInfo.getCustInfo().getLocation())
                        .addEventVariable("cusAreaCode_var", orderInfo.getCustInfo().getRegionID())
                        .addEventVariable("cusProvince_var", provinceName)
                        .addEventVariable("cusCity_var", cityName)
                        .addEventVariable("cusArea_var", regionName)
                        .addEventVariable("channelPartnerPhone_var", channelPartnerPhone)
                        .addEventVariable("customerManagerPhone_var", customerManagerPhone)
                        .addEventVariable("customerManagerCode_var", customerManagerCode)
                        // 新增字段
                        .addEventVariable("orderProvince_var", orderProvinceName)
                        .addEventVariable("orderCity_var", orderCityName)
                        .addEventVariable("orderArea_var", orderAreaName)
                        .addEventVariable("charge_code_var", originAtomInfo.getChargeId())
                        .addEventVariable("charge_code_name_var", originAtomInfo.getChargeCode() != null && originAtomInfo.getChargeCode().contains("_") ?
                                originAtomInfo.getChargeCode().split("_")[originAtomInfo.getChargeCode().split("_").length - 1] : originAtomInfo.getChargeCode())
                        .addEventVariable("busiPersonJobNumber_var", extractJobNumbersFromOrderInfo(decryptOrderInfoCouponList))
                        .addEventVariable("busiPersonPhoneNum_var", extractPhoneNumbersFromOrderInfo(decryptOrderInfoCouponList))
                        .addEventVariable("channelPartnerCode_var", orderInfo.getAgentInfo() != null && orderInfo.getAgentInfo().size() > 0 ? extractAgentNumbersFromAgentInfoDTO(orderInfo.getAgentInfo())  : null)
                        .addEventVariable("customerType_var", orderInfo.getBusinessCode().equals("SyncIndividualOrderInfo") || orderInfo.getBusinessCode().equals("SyncOSExtOrderInfo") ? "个人客户" :
                                (orderInfo.getBusinessCode().equals("SyncGrpOrderInfo") || orderInfo.getBusinessCode().equals("SyncValetOrderInfo") ? "集团客户" : "-"))
                        .addEventVariable("cusCode_var", orderInfo.getCustInfo() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key, iotSm4Iv) : null)
                        .addEventVariable("cusName_var", orderInfo.getCustInfo() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustName(), iotSm4Key, iotSm4Iv) : null)
                        .addEventVariable("prductType", SPUOfferingClassEnum.getDisplay(spuOfferingInfoDto.getOfferingClass()))
                        .addEventVariable("offering_class_var", AtomOfferingClassEnum.getDescribe( order2cAtomInfo.getAtomOfferingClass()))
                        .addEventVariable("couponInfo_var", formatCouponInfoDTOToString(decryptOrderInfoCouponList))
                        .build();
                // 上传事件行为消息到服务器
                growingIOConfig.getProject().send(eventMessage);


            } catch (Exception e) {
                log.error("H5_ProductOrderCreate埋点失败,订单id:{},失败原因:{}",orderInfo.getOrderId() ,e.getMessage());
            }
        });

    }

    @Override
    public void sendOrderDimensionalityMsg(OrderInfoDTO orderInfo, Order2cInfo order2cInfo, Integer innerStatus) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                List<LogisticsInfo> logisticsInfos = logisticsInfoMapper.selectByExample(
                        new LogisticsInfoExample().createCriteria().andOrderIdEqualTo(orderInfo.getOrderId())
                                .example());

                String logisticsNumber = "";
                List<LogisticsInfoDTO> supplierInfoList = supplierInfo.getList();
                if (logisticsInfos != null && logisticsInfos.size() > 0) {
                    for (LogisticsInfo l : logisticsInfos) {
                        LogisticsInfoDTO logisticsInfoDTO = supplierInfoList.stream()
                                .filter(b -> b.getSupplierName().equals(l.getSupplierName())).findFirst()
                                .orElse(new LogisticsInfoDTO());
                        logisticsNumber += logisticsInfoDTO == null ? ""
                                : logisticsInfoDTO.getName() + l.getLogisCode() + ";";

                    }
                }
                if (!logisticsNumber.equals("")) {
                    logisticsNumber = logisticsNumber.substring(0, logisticsNumber.length() - 1);
                }
                // 分销员
                DistributorInfoDTO distributorInfoDTO1 = new DistributorInfoDTO();
                DistributorInfoDTO distributorInfoDTO2 = new DistributorInfoDTO();
                // 0,1,3必传
                if (orderInfo.getDistributorInfo() != null && orderInfo.getDistributorInfo().size() > 0) {
                    for (DistributorInfoDTO d : orderInfo.getDistributorInfo()) {
                        if (d.getDistributorLevel().equals("1")) {
                            BeanUtils.copyProperties(d, distributorInfoDTO1);
                            distributorInfoDTO1.setDistributorPhone(
                                    IOTEncodeUtils.decryptSM4(d.getDistributorPhone(), iotSm4Key, iotSm4Iv));
                        } else {
                            BeanUtils.copyProperties(d, distributorInfoDTO2);
                            distributorInfoDTO2.setDistributorPhone(
                                    IOTEncodeUtils.decryptSM4(d.getDistributorPhone(), iotSm4Key, iotSm4Iv));
                        }
                    }
                } else {
                    List<Order2cDistributorInfo> order2cDistributorInfos = order2cDistributorInfoMapper
                            .selectByExample(new Order2cDistributorInfoExample().createCriteria()
                                    .andOrderIdEqualTo(order2cInfo.getOrderId()).example());
                    if (CollectionUtils.isNotEmpty(order2cDistributorInfos)) {
                        for (Order2cDistributorInfo d : order2cDistributorInfos) {
                            if (d.getDistributorLevel().equals("1")) {
                                BeanUtils.copyProperties(d, distributorInfoDTO1);
                            } else {
                                BeanUtils.copyProperties(d, distributorInfoDTO2);
                            }
                        }
                    }
                }


                // 获取数据库订单完成信息
                Order2cInfo order2cInfo1 = order2cInfoMapper.selectByPrimaryKey(orderInfo.getOrderId());
                // 归属省市区
//                String[] locations;

//                if (orderInfo.getOrderOrgBizInfo() != null) {
//                    if (orderInfo.getOrderOrgBizInfo().getOrgName() == null
//                            && orderInfo.getOrderOrgBizInfo().getProvinceOrgName() == null) {
//                        if (order2cInfo1 != null) {
//                            locations = (order2cInfo1.getOrgName() == null || order2cInfo1.getOrgName().equals(""))
//                                    ? order2cInfo1.getProvinceOrgName().split("-")
//                                    : order2cInfo1.getOrgName().split("-");
//                        } else {
//                            locations = null;
//                        }
//
//
//                    } else {
//                        locations = (orderInfo.getOrderOrgBizInfo().getOrgName() == null
//                                || orderInfo.getOrderOrgBizInfo().getOrgName().equals(""))
//                                ? orderInfo.getOrderOrgBizInfo().getProvinceOrgName().split("-")
//                                : orderInfo.getOrderOrgBizInfo().getOrgName().split("-");
//
//                    }
//
//                } else {
//                    if (order2cInfo.getOrgName() == null && order2cInfo.getProvinceOrgName() == null) {
//                        if (order2cInfo1 != null) {
//                            locations = (order2cInfo1.getOrgName() == null || order2cInfo1.getOrgName().equals(""))
//                                    ? order2cInfo1.getProvinceOrgName().split("-")
//                                    : order2cInfo1.getOrgName().split("-");
//                        } else {
//                            locations = null;
//                        }
//                    } else {
//                        locations = (order2cInfo.getOrgName() == null || order2cInfo.getOrgName().equals(""))
//                                ? order2cInfo.getProvinceOrgName().split("-")
//                                : order2cInfo.getOrgName().split("-");
//                    }
//
//                }

                List<OrderExportDO> list = order2cAtomInfoMapperExt.selectOrderExportList(orderInfo.getOrderId());
                OrderExportDO orderExportDO = null;

                if (list != null && list.size() > 0) {
                    orderExportDO = list.get(0);
                }
                // 渠道商信息：统一处理逻辑（优先使用清洗后的值）
                String channelPartnerName = "-";
                String channelPartnerPhone = "";
                String channelPartnerCode = "-";
                String channelPartnerUserId = "-";
                String channelTag = null;
                String channelCategory = null;
                String busiPersonCode = null;
                String busiPersonName = null;

                // 获取渠道商信息列表
                List<Order2cAgentInfo> order2cAgentInfos = null;
                if (orderInfo.getAgentInfo() != null && !orderInfo.getAgentInfo().isEmpty()) {
                    // 如果有AgentInfo，需要查询对应的Order2cAgentInfo获取清洗数据
                    order2cAgentInfos = order2cAgentInfoMapper
                            .selectByExample(new Order2cAgentInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                } else {
                    // 直接从数据库查询
                    order2cAgentInfos = order2cAgentInfoMapper
                            .selectByExample(new Order2cAgentInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                }

                // 处理所有渠道商信息（优先使用清洗后的值）
                if (CollectionUtils.isNotEmpty(order2cAgentInfos)) {
                    List<String> channelPartnerNames = new ArrayList<>();
                    List<String> channelPartnerPhones = new ArrayList<>();
                    List<String> channelPartnerCodes = new ArrayList<>();
                    List<String> channelPartnerUserIds = new ArrayList<>();
                    List<String> channelTags = new ArrayList<>();
                    List<String> channelCategories = new ArrayList<>();

                    for (Order2cAgentInfo agentInfo : order2cAgentInfos) {
                        // 渠道商名称：优先使用清洗后的值，如果没有则使用原始值
                        String agentName = agentInfo.getAgentNameWash() != null ? agentInfo.getAgentNameWash() : agentInfo.getAgentName();
                        if (agentName != null) {
                            channelPartnerNames.add(agentName);
                        }

                        // 渠道商手机号（需要解密）
                        if (agentInfo.getAgentPhone() != null) {
                            if(orderInfo.getAgentInfo() != null && !orderInfo.getAgentInfo().isEmpty()){
                                String decryptedPhone = IOTEncodeUtils.decryptSM4(agentInfo.getAgentPhone(), iotSm4Key, iotSm4Iv);
                                channelPartnerPhones.add(decryptedPhone);
                            }else{
                                channelPartnerPhones.add(agentInfo.getAgentPhone());
                            }

                        }

                        // 渠道商编号：优先使用清洗后的值，如果没有则使用原始值
                        String agentNumber = agentInfo.getAgentNumberWash() != null ? agentInfo.getAgentNumberWash() : agentInfo.getAgentNumber();
                        if (agentNumber != null) {
                            channelPartnerCodes.add(agentNumber);
                        }

                        // 渠道商用户ID
                        if (agentInfo.getAgentUserId() != null) {
                            channelPartnerUserIds.add(agentInfo.getAgentUserId());
                        }

                        // 渠道商标签（清洗后的值）
                        if (agentInfo.getAgentLabelWash() != null) {
                            channelTags.add(agentInfo.getAgentLabelWash());
                        }

                        // 渠道商类别（清洗后的值）
                        if (agentInfo.getAgentCategoryWash() != null) {
                            channelCategories.add(agentInfo.getAgentCategoryWash());
                        }
                    }

                    // 将列表转换为管道分隔的字符串
                    channelPartnerName = channelPartnerNames.isEmpty() ? "-" : String.join("|", channelPartnerNames);
                    channelPartnerPhone = channelPartnerPhones.isEmpty() ? "" : String.join("|", channelPartnerPhones);
                    channelPartnerCode = channelPartnerCodes.isEmpty() ? "-" : String.join("|", channelPartnerCodes);
                    channelPartnerUserId = channelPartnerUserIds.isEmpty() ? "-" : String.join("|", channelPartnerUserIds);
                    channelTag = channelTags.isEmpty() ? null : String.join("|", channelTags);
                    channelCategory = channelCategories.isEmpty() ? null : String.join("|", channelCategories);
                }
                // 优化：优先从orderInfo获取优惠券信息，没有再从数据库获取
                String listCouponStr = "";
                List<CouponInfoDTO> decryptOrderInfoCouponList=new ArrayList<>();
                if (orderInfo.getCouponInfo() != null && !orderInfo.getCouponInfo().isEmpty()) {
                    log.debug("从orderInfo获取优惠券信息用于订单维度表，共{}条记录", orderInfo.getCouponInfo().size());
                     decryptOrderInfoCouponList = decryptOrderInfoCouponList(orderInfo.getCouponInfo());
                     listCouponStr = formatCouponInfoDTOToString(decryptOrderInfoCouponList);
                } else {
                    log.debug("orderInfo中无优惠券信息，从数据库查询用于订单维度表");
                    List<CouponInfo> couponInfoList = couponInfoMapper.selectByExample(
                            new CouponInfoExample().createCriteria().andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    listCouponStr = formatCouponInfoToString(couponInfoList);
                    // 将数据库查询的CouponInfo转换为CouponInfoDTO格式，保持后续逻辑一致
                    decryptOrderInfoCouponList = convertCouponInfoToCouponInfoDTO(couponInfoList);
                }
                // 业务人员信息：从coupon_info表获取（参考getOrderCreated方法）

                busiPersonCode = extractSalesmanCodesFromCouponInfoDTO(decryptOrderInfoCouponList);
                busiPersonName = extractSalesmanNamesFromCouponInfoDTO(decryptOrderInfoCouponList);

                // 如果渠道商字段没有值，使用以前的逻辑（不从文杰清洗获取）
                // 渠道商字段保持原有逻辑，不从rise_order_2c_grid表获取

                // 网格字段：优先从rise_order_2c_grid表获取
                String gridProvince = null;
                String gridCity = null;
                String gridDistrict = null;
                String orderGridFromRise = null;
                String gridName = "";

                // 从rise_order_2c_grid表获取网格字段
                List<RiseOrder2cGrid> riseOrder2cGrids = riseOrder2cGridMapper.selectByExample(
                        new RiseOrder2cGridExample().createCriteria()
                                .andOrderIdEqualTo(orderInfo.getOrderId())
                                .example());
                if (!CollectionUtils.isEmpty(riseOrder2cGrids)) {
                    RiseOrder2cGrid riseOrder2cGrid = riseOrder2cGrids.get(0);
                    gridProvince = riseOrder2cGrid.getProvince(); // 网格省
                    gridCity = riseOrder2cGrid.getCity(); // 网格市
                    gridDistrict = riseOrder2cGrid.getDistrict(); // 网格区
                    orderGridFromRise = riseOrder2cGrid.getGridName(); // 网格名称
                    gridName = orderGridFromRise; // 使用清洗后的网格名称
                }

                // sn号
                List<Order2cAtomInfo> order2cAtomInfos = order2cAtomInfoMapper
                        .selectByExample(new Order2cAtomInfoExample()
                                .createCriteria().andOrderIdEqualTo(order2cInfo.getOrderId()).example());
                List<Order2cAtomSn> snListAll = new ArrayList<>();
                String snListStr = "";
                List<String> atomOrderIds = new ArrayList<>();
                Integer innerStatuIn = null;
                if (order2cAtomInfos != null && order2cAtomInfos.size() > 0) {

                    innerStatuIn = innerStatus == null ? order2cAtomInfos.get(0).getOrderStatus() : innerStatus;
                    for (Order2cAtomInfo order2cAtomInfo : order2cAtomInfos) {
                        List<Order2cAtomSn> snList = new ArrayList<>();
                        snList = order2cAtomSnMapper.selectByExample(new Order2cAtomSnExample().createCriteria()
                                .andAtomOrderIdEqualTo(order2cAtomInfo.getId()).example());
                        snListAll.addAll(snList);
                        atomOrderIds.add(order2cAtomInfo.getId());
                    }

                }
                if (snListAll != null && snListAll.size() > 0) {
                    for (Order2cAtomSn order2cAtomSn : snListAll) {
                        snListStr += order2cAtomSn.getSn() + ";";
                    }
                    snListStr = snListStr.substring(0, snListStr.length() - 1);
                }
                // 如果rise_order_2c_grid表中没有网格名称，则使用原有逻辑获取
                if (gridName == null || gridName.isEmpty()) {
                    String custMgPhone = "";
                    if (orderInfo.getCustInfo() == null) {
                        custMgPhone = order2cInfo.getCustMgPhone() == null ? "" : order2cInfo.getCustMgPhone();
                    }

                    if (custMgPhone.equals("")) {
                        // 优先从已获取的渠道商信息中获取手机号
                        if (CollectionUtils.isNotEmpty(order2cAgentInfos)) {
                            custMgPhone = order2cAgentInfos.get(0).getAgentPhone();
                            if (custMgPhone != null) {
                                List<ShopCustomerInfo> shopCustomerInfos = shopCustomerInfoMapper.selectByExample(
                                        new ShopCustomerInfoExample().createCriteria().andCustIdEqualTo(custMgPhone).example());
                                if (shopCustomerInfos != null && shopCustomerInfos.size() > 0) {
                                    gridName = shopCustomerInfos.get(0).getGriddingName();
                                }
                            }
                        }
                    } else {
                        List<ShopManagerInfo> shopManagerInfos = shopManagerInfoMapper.selectByExample(
                                new ShopManagerInfoExample().createCriteria().andCreateOperPhoneEqualTo(custMgPhone).example());
                        if (shopManagerInfos != null && shopManagerInfos.size() > 0) {
                            gridName = shopManagerInfos.get(0).getGriddingName();
                        }
                    }
                }

                GioCdpItemMessage msg = new GioCdpItemMessage.Builder()
                        .id(orderInfo.getOrderId()) // 维度表模型ID(记录ID) (必填)
                        .key("Orderid") // 维度表标识符 (必填) // 登陆用户ID (选填)
                        .addItemVariable("item_id", orderInfo.getOrderId())
                        .addItemVariable("order_status", OrderStatusInnerEnum.getDescribe(innerStatuIn))
                        .addItemVariable("order_status_change_time",orderInfo.getOrderStatusTime()==null?null:
                                String.valueOf(DateTimeUtil.toTimestamp(orderInfo.getOrderStatusTime())))
                        .addItemVariable("create_time", order2cInfo.getCreateTime() == null
                                ?order2cInfo1.getCreateTime()==null?null:
                                DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo1.getCreateTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo1.getCreateTime()))
                                : DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getCreateTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getCreateTime())))
                        .addItemVariable("shipping_time",
                                orderExportDO == null || orderExportDO.getSendGoodsTime() == null ? null
                                        : DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getSendGoodsTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getSendGoodsTime())))
                        .addItemVariable("accept_time",
                                orderExportDO == null || orderExportDO.getReceiveOrderTime() == null ? order2cInfo.getPayTime() == null ? order2cInfo.getCreateTime() == null ? null :
                                        DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getCreateTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getCreateTime()))
                                        : String.valueOf(DateTimeUtil.toTimestamp(order2cInfo.getPayTime()))
                                        : DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getReceiveOrderTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getReceiveOrderTime())))
                        .addItemVariable("billing_time",
                                orderExportDO == null || orderExportDO.getValetOrderCompleteTime() == null ? order2cInfo.getValetOrderCompleteTime() == null ? null :
                                        DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getValetOrderCompleteTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getValetOrderCompleteTime()))
                                        : DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getValetOrderCompleteTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getValetOrderCompleteTime())))
                        .addItemVariable("arrival_time",
                                orderExportDO == null || orderExportDO.getCreateTime() == null ? order2cInfo.getCreateTime() == null ? null :
                                        DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getCreateTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(order2cInfo.getCreateTime()))
                                        : DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getCreateTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getCreateTime())))
                        // .addItemVariable("refund_amount", "-")
                        // .addItemVariable("received_amount", "-")
                        .addItemVariable("tracking_number", logisticsNumber)
                        .addItemVariable("chack_time", orderExportDO == null || orderExportDO.getBillNoTime() == null
                                ? null
                                : DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getBillNoTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(orderExportDO.getBillNoTime())))
                        .addItemVariable("billing_order_type",
                                orderExportDO == null || orderExportDO.getSaleOrderType() == null ? null
                                        : orderExportDO.getSaleOrderType())
                        // .addItemVariable("purchase_order_code", purchase_order_code)
                        // .addItemVariable("settlement_status", settlement_status)
                        .addItemVariable("distributor_l1_user_id",
                                distributorInfoDTO1.getDistributorUserID() == null ? null
                                        : distributorInfoDTO1.getDistributorUserID())
                        .addItemVariable("distributor_l2_user_id",
                                distributorInfoDTO2.getDistributorUserID() == null ? null
                                        : distributorInfoDTO2.getDistributorUserID())
                        .addItemVariable("share_code_l1",
                                distributorInfoDTO1.getDistributorShareCode() == null ? null
                                        : distributorInfoDTO1.getDistributorShareCode())
                        .addItemVariable("share_code_l2",
                                distributorInfoDTO2.getDistributorShareCode() == null ? null
                                        : distributorInfoDTO2.getDistributorShareCode())
                        .addItemVariable("distributor_l1_phone",
                                distributorInfoDTO1.getDistributorPhone() == null ? null
                                        : distributorInfoDTO1.getDistributorPhone())
                        .addItemVariable("distributor_l2_phone",
                                distributorInfoDTO2.getDistributorPhone() == null ? null
                                        : distributorInfoDTO2.getDistributorPhone())
                        .addItemVariable("customer_manager_id",
                                orderInfo.getCreateOperUserID() == null
                                        ? (order2cInfo.getCreateOperUserId() == null
                                        ? order2cInfo1 != null && order2cInfo1.getCreateOperUserId() != null ? order2cInfo1.getCreateOperUserId() : null
                                        : order2cInfo.getCreateOperUserId())
                                        : orderInfo.getCreateOperUserID())
                        .addItemVariable("customer_manager_code",
                                orderInfo.getEmployeeNum() == null
                                        ? (order2cInfo.getEmployeeNum() == null ?
                                        order2cInfo1 != null && order2cInfo1.getEmployeeNum() != null ? order2cInfo1.getEmployeeNum() : null
                                        : order2cInfo.getEmployeeNum())
                                        : orderInfo.getEmployeeNum())
                        .addItemVariable("customer_manager_name",
                                orderInfo.getCustomerManagerName() == null
                                        ? (order2cInfo.getCustMgName() == null ? order2cInfo1 != null && order2cInfo1.getCustMgName() != null ? order2cInfo1.getCustMgName() : null
                                        : order2cInfo.getCustMgName())
                                        : orderInfo.getCustomerManagerName())
                        .addItemVariable("customer_manager_phone", orderInfo.getCustomerManagerPhone() == null
                                ? (order2cInfo.getCustMgPhone() == null ? order2cInfo1 != null && order2cInfo1.getCustMgPhone() != null ? order2cInfo1.getCustMgPhone() : null
                                : order2cInfo.getCustMgPhone())
                                : IOTEncodeUtils.decryptSM4(orderInfo.getCustomerManagerPhone(), iotSm4Key, iotSm4Iv))
                        .addItemVariable("channel_partner_name", channelPartnerName)
                        .addItemVariable("channel_partner_phone",channelPartnerPhone)
                        .addItemVariable("channel_partner_code", channelPartnerCode)
                        .addItemVariable("channel_partner_user_id", channelPartnerUserId)
                        .addItemVariable("order_channel_name", orderInfo.getOrderingChannelName() == null
                                ? (order2cInfo.getOrderingChannelName() == null ? order2cInfo1 != null && order2cInfo1.getOrderingChannelName() != null ? order2cInfo1.getOrderingChannelName() : null
                                : order2cInfo.getOrderingChannelName())
                                : orderInfo.getOrderingChannelName())
                        .addItemVariable("coupon_info", listCouponStr)
                        .addItemVariable("shipping_province",
                                orderInfo.getContactInfo() == null
                                        ? (order2cInfo.getAddr1() == null
                                        ? order2cInfo1 != null && order2cInfo1.getAddr1() != null ? IOTEncodeUtils.decryptSM4(order2cInfo1.getAddr1(), iotSm4Key,
                                        iotSm4Iv) : null
                                        : IOTEncodeUtils.decryptSM4(order2cInfo.getAddr1(), iotSm4Key,
                                        iotSm4Iv))
                                        : IOTEncodeUtils.decryptSM4(
                                        orderInfo.getContactInfo().getAddresstInfo().getAddr1(),
                                        iotSm4Key, iotSm4Iv))
                        .addItemVariable("shipping_city",
                                orderInfo.getContactInfo() == null
                                        ? (order2cInfo.getAddr1() == null
                                        ? order2cInfo1 != null && order2cInfo1.getAddr1() != null ? IOTEncodeUtils.decryptSM4(order2cInfo1.getAddr1(), iotSm4Key,
                                        iotSm4Iv) : null
                                        : IOTEncodeUtils.decryptSM4(order2cInfo.getAddr2(), iotSm4Key,
                                        iotSm4Iv))
                                        : IOTEncodeUtils.decryptSM4(
                                        orderInfo.getContactInfo().getAddresstInfo().getAddr2(),
                                        iotSm4Key, iotSm4Iv))
                        .addItemVariable("shipping_area",
                                orderInfo.getContactInfo() == null
                                        ? (order2cInfo.getAddr1() == null
                                        ? order2cInfo1 != null && order2cInfo1.getAddr1() != null ? IOTEncodeUtils.decryptSM4(order2cInfo1.getAddr1(), iotSm4Key,
                                        iotSm4Iv) : null
                                        : IOTEncodeUtils.decryptSM4(order2cInfo.getAddr3(), iotSm4Key,
                                        iotSm4Iv))
                                        : IOTEncodeUtils.decryptSM4(
                                        orderInfo.getContactInfo().getAddresstInfo().getAddr3(),
                                        iotSm4Key, iotSm4Iv))
                        .addItemVariable("sn_var", snListStr)
//                        .addItemVariable("order_province", locations.length >= 1 ? locations[0] : null)
//                        .addItemVariable("order_city", locations.length >= 2 ? locations[1] : null)
//                        .addItemVariable("order_area", locations.length >= 3 ? locations[2] : null)
                        .addItemVariable("order_grid", StringUtils.isNotBlank(orderGridFromRise) ? orderGridFromRise : gridName)
                        .addItemVariable("cus_code",
                                orderInfo.getCustInfo() == null
                                        ? (order2cInfo.getCustCode() == null
                                        ? order2cInfo1 != null && order2cInfo1.getCustCode() != null ? IOTEncodeUtils.decryptSM4(order2cInfo1.getCustCode(), iotSm4Key,
                                        iotSm4Iv) : null
                                        : IOTEncodeUtils.decryptSM4(order2cInfo.getCustCode(), iotSm4Key,
                                        iotSm4Iv))
                                        : IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key,
                                        iotSm4Iv))
                        .addItemVariable("cus_name",
                                orderInfo.getCustInfo() == null
                                        ? (order2cInfo.getCustName() == null
                                        ? order2cInfo1 != null && order2cInfo1.getCustCode() != null ? IOTEncodeUtils.decryptSM4(order2cInfo1.getCustCode(), iotSm4Key,
                                        iotSm4Iv) : null
                                        : IOTEncodeUtils.decryptSM4(order2cInfo.getCustName(), iotSm4Key,
                                        iotSm4Iv))
                                        : IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustName(), iotSm4Key,
                                        iotSm4Iv))
                        .addItemVariable("business_code",orderInfo.getBusinessCode()==null?order2cInfo.getBusinessCode()==null?
                                order2cInfo1!=null&&order2cInfo1.getBusinessCode()!=null? BusinessCodeEnum.getChnName(order2cInfo1.getBusinessCode()) :null
                                :BusinessCodeEnum.getChnName(order2cInfo.getBusinessCode()):BusinessCodeEnum.getChnName(orderInfo.getBusinessCode()))
                        // 新增字段
                        .addItemVariable("channelTag", channelTag)
                        .addItemVariable("channelCategory", channelCategory)
                        .addItemVariable("busiPersonCode", busiPersonCode)
                        .addItemVariable("busiPersonName", busiPersonName)
                        // 网格字段（从文杰清洗获取）
                        .addItemVariable("gridProvince", gridProvince)
                        .addItemVariable("gridCity", gridCity)
                        .addItemVariable("gridDistrict", gridDistrict)
                        .build();
                growingIOConfig.getProject().send(msg);
                log.info("订单维度表埋点信息:{}", msg);
            } catch (Exception e) {
                log.error("订单维度表埋点失败,订单id:{},失败原因:{}",orderInfo.getOrderId(), e);
            }
        });

    }

    @Override
    public void sendOrderDimensionalityMsg(List<ChannelDataWashSucceedDTO> channelDataList) {
        if (CollectionUtils.isEmpty(channelDataList)) {
            return;
        }

        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            for (ChannelDataWashSucceedDTO channelData : channelDataList) {
                try {
                    String orderId = channelData.getOrderId();
                    if (StringUtils.isBlank(orderId)) {
                        continue;
                    }

                    // 构建埋点消息，只使用ChannelDataWashSucceedDTO中的字段
                    String channelPartnerName = StringUtils.isNotBlank(channelData.getAgentNameWash()) ?
                            channelData.getAgentNameWash() : "-";
                    String channelPartnerCode = StringUtils.isNotBlank(channelData.getAgentNumberWash()) ?
                            channelData.getAgentNumberWash() : "-";
                    String channelTag = channelData.getAgentLabelWash();
                    String channelCategory = channelData.getAgentCategoryWash();

                    // 构建并发送埋点消息（只包含ChannelDataWashSucceedDTO中的字段）
                    GioCdpItemMessage msg = new GioCdpItemMessage.Builder()
                            .id(orderId)
                            .key("Orderid")
                            .addItemVariable("item_id", orderId)
                            .addItemVariable("channel_partner_name", channelPartnerName)
                            .addItemVariable("channel_partner_code", channelPartnerCode)
                            .addItemVariable("channelTag", channelTag)
                            .addItemVariable("channelCategory", channelCategory)
                            .build();

                    growingIOConfig.getProject().send(msg);
                    log.info("渠道数据清洗订单维度表埋点信息 - 订单ID: {}, 渠道商名称: {}, 渠道商编号: {}, 渠道商类别: {}, 渠道商标签: {}",
                            orderId, channelPartnerName, channelPartnerCode, channelCategory, channelTag);

                } catch (Exception e) {
                    log.error("渠道数据清洗订单维度表埋点失败,订单id:{},失败原因:{}", channelData.getOrderId(), e);
                }
            }
        });
    }

    @Override
    public void sendOrderDimensionalityMsgFromGrid(String orderId, String gridProvince, String gridCity, String gridDistrict, String gridName) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                // 构建并发送埋点消息（只更新网格相关字段）
                GioCdpItemMessage msg = new GioCdpItemMessage.Builder()
                        .id(orderId)
                        .key("Orderid")
                        .addItemVariable("item_id", orderId)
                        // 网格字段（从文杰清洗获取）
                        .addItemVariable("gridProvince", gridProvince)
                        .addItemVariable("gridCity", gridCity)
                        .addItemVariable("gridDistrict", gridDistrict)
                        .addItemVariable("order_grid", gridName)
                        .build();

                growingIOConfig.getProject().send(msg);
                log.info("网格数据订单维度表埋点信息 - 订单ID: {}, 网格省份: {}, 网格城市: {}, 网格区域: {}, 网格名称: {}",
                        orderId, gridProvince, gridCity, gridDistrict, gridName);

            } catch (Exception e) {
                log.error("网格数据订单维度表埋点失败,订单id:{},失败原因:{}", orderId, e);
            }
        });
    }

    @Override
    public void getUserInfo() throws IOException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<String> userIds = new ArrayList<>();
        // userIds.add("1728629888200300729");
        // userIds.add("1663640655750632899");
        UserMiniProgramExample userMiniProgramExample = new UserMiniProgramExample();
        // userMiniProgramExample.createCriteria().andUserIdIn(userIds);
        long currentNum = 0L;

        List<GioUserOriginDTO> list = gioBurialPointMapperExt.getUserList();

        List<GioUserDTO> userDTOS = new ArrayList<>();
        for (GioUserOriginDTO userMiniProgram : list) {
            String roleLevel = "";
            if (Objects.equals(userMiniProgram.getRoleType(), "1")) {
                roleLevel = "一级";
            } else if (Objects.equals(userMiniProgram.getRoleType(), "2")) {
                roleLevel = "二级";
            }

            String distributorReferralcode = null;
            if (Objects.equals(userMiniProgram.getRoleType(), "1")
                    || Objects.equals(userMiniProgram.getRoleType(), "2")) {
                distributorReferralcode = userMiniProgram.getCode();
            }
            GioUserDTO userDTO = new GioUserDTO();
            userDTO.setUserId(userMiniProgram.getUserId());
            GioUserDTO.UserAttr attr = new GioUserDTO.UserAttr();

            attr.setWlw_UserID(userMiniProgram.getUserId());
            attr.setWlw_ustCode(userMiniProgram.getCode());
            attr.setWlw_custID(userMiniProgram.getPhone());
            attr.setWlw_cusName(userMiniProgram.getName());
            attr.setWlw_cusRegistDate(
                    DateTimeUtil.formatDate(userMiniProgram.getCreateTime(), DateTimeUtil.STANDARD_DAY));
            attr.setWlw_roleType(RoleTypeMiniEnum.getName(userMiniProgram.getRoleType()));
            attr.setWlw_roleLevel(roleLevel);
            attr.setWlw_UserStauts(ManagerStatusEnum.getName(userMiniProgram.getStatus()));
            attr.setWlw_cusProvince(userMiniProgram.getProvinceName());
            attr.setWlw_cusCity(userMiniProgram.getCityName());
            attr.setWlw_cusRegion(userMiniProgram.getRegionName());
            attr.setWlw_cusProvinceCode(userMiniProgram.getBeId());
            attr.setWlw_cusCityCode(userMiniProgram.getLocation());
            attr.setWlw_cusRegionCode(userMiniProgram.getRegionId());
            attr.setWlw_GridCity(userMiniProgram.getShopCityName() == null ? null : userMiniProgram.getShopCityName());
            attr.setWlw_GridRegion(
                    userMiniProgram.getShopRegionName() == null ? null : userMiniProgram.getShopRegionName());
            attr.setWlw_GridName(userMiniProgram.getGriddingName() == null ? null : userMiniProgram.getGriddingName());
            // userDTO.setDistributorMrglnf(userMiniProgram);
            // userDTO.setDistributorMrgCode(userMiniProgram);
            attr.setDistributorReferralcode(distributorReferralcode);
            userDTO.setAttrs(attr);
            userDTOS.add(userDTO);

        }

        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=userDTOS.json");

        // 将 JSON 内容写入响应流

        StringBuffer jsonString = new StringBuffer();
        for (GioUserDTO userDTO : userDTOS) {
            jsonString.append(JSON.toJSONString(userDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }
    }

    @Override
    public void getSkuMsg() throws IOException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<GioSkuDTO> gioSkuDTOs = new ArrayList<>();
        // 查询sku规格商品信息 修改销售目录价
        // List<String> skuIds = new ArrayList<>();
        // skuIds.add("1284464975329923072");
        // skuIds.add("1310636026389684224");
        // skuIds.add("1034780437731127296");
        //
        // SkuOfferingInfoExample skuOfferingInfoExample = new SkuOfferingInfoExample();
        // SkuOfferingInfoExample.Criteria criteria =
        // skuOfferingInfoExample.createCriteria();
        // criteria.andIdIn(skuIds).example();
        // List<SkuOfferingInfo> skuOfferingInfos =
        // skuOfferingInfoMapper.selectByExample(skuOfferingInfoExample);
        List<GioSkuOriginDTO> skuOfferingInfos = gioBurialPointMapperExt.getSkuList();

        for (GioSkuOriginDTO gioSkuOriginDTO : skuOfferingInfos) {
            // List<SkuOfferingInfoHistory> skuOfferingInfoHistories =
            // skuOfferingInfoHistoryMapper.selectByExample(new
            // SkuOfferingInfoHistoryExample().createCriteria().andSkuIdEqualTo(skuOfferingInfo.getId()).example());
            //
            // SkuOfferingInfoHistory skuOfferingInfoHistory =
            // skuOfferingInfoHistories.get(0);
            // 获取发布省市
            // List<SkuReleaseTarget> releaseTargets =
            // skuReleaseTargetMapper.selectByExample(
            // new SkuReleaseTargetExample().createCriteria()
            // .andSkuOfferingCodeEqualTo(skuOfferingInfo.getOfferingCode())
            // .example()
            // );

            StringBuilder cityStr = new StringBuilder();
            if (!CollectionUtils.isEmpty(gioSkuOriginDTO.getSkuReleaseTargetList())) {
                for (SkuReleaseTarget releaseTarget : gioSkuOriginDTO.getSkuReleaseTargetList()) {
                    String proCity = "";
                    Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                    Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();

                    if (StringUtils.isNotBlank(releaseTarget.getProvinceCode())) {
                        if ("471".equals(releaseTarget.getProvinceCode())) {
                            proCity = "内蒙古";

                        } else {
                            proCity = (String) provinceCodeNameMap.get(releaseTarget.getProvinceCode());

                        }
                    }
                    if (StringUtils.isNotBlank(releaseTarget.getCityCode())) {
                        proCity = proCity + "|" + (String) locationCodeNameMap.get(releaseTarget.getCityCode());

                    }
                    cityStr.append(proCity).append("||");

                }
            }

            // 去除最后一个||
            if (cityStr.length() > 0) {
                cityStr.delete(cityStr.length() - 2, cityStr.length());
            }
            // 获取spu

            // SpuOfferingInfo spuOfferingInfo =
            // spuOfferingInfoMapper.selectByPrimaryKey(skuOfferingInfo.getSpuId());
            // CategoryInfo categoryInfo = null;
            // if (!CollectionUtils.isNotEmpty(gioSkuOriginDTO.getCategoryInfoList())) {
            //
            //
            // categoryInfo = gioSkuOriginDTO.getCategoryInfoList().get(0);
            // }

            // 积分
            // SkuRoleRelation skuRoleRelation = null;
            //// List<SkuRoleRelation> skuRoleRelations =
            // skuRoleRelationMapper.selectByExample(new
            // SkuRoleRelationExample().createCriteria().andSkuIdEqualTo(skuOfferingInfo.getId()).example());
            //
            // if (!CollectionUtils.isNotEmpty(gioSkuOriginDTO.getSkuRoleRelationList())) {
            // skuRoleRelation = gioSkuOriginDTO.getSkuRoleRelationList().get(0);
            // }
            Optional<Long> priceOpt = Optional.ofNullable(gioSkuOriginDTO.getPrice());
            long price = priceOpt.orElse(0L); // 若为 null，返回默认值 0
            GioSkuDTO gioSkuDTO = new GioSkuDTO();
            gioSkuDTO.setItem_id(gioSkuOriginDTO.getOfferingCode());
            GioSkuDTO.GioSkuAttr gioSkuAttr = new GioSkuDTO.GioSkuAttr();
            gioSkuAttr.setSku_name(gioSkuOriginDTO.getOfferingName());
            gioSkuAttr.setSku_price(String.valueOf(new BigDecimal(price / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioSkuAttr.setSku_status(gioSkuOriginDTO.getOfferingStatus() == null ? null
                    : OfferingStatusEnum.fromCode(gioSkuOriginDTO.getOfferingStatus()).name);
            gioSkuAttr.setSku_province(cityStr.toString());
            gioSkuAttr.setSpu_name(gioSkuOriginDTO.getSpuOfferingName());
            gioSkuAttr.setSpu_code(gioSkuOriginDTO.getSpuOfferingCode());
            // gioSkuAttr.setSpu_version(gioSkuOriginDTO.getSpuOfferingVersion());
            gioSkuAttr.setSpu_type(gioSkuOriginDTO.getSpuOfferingClass() == null ? null
                    : SPUOfferingClassEnum.getDisplay(gioSkuOriginDTO.getSpuOfferingClass()));
            gioSkuAttr.setSpu_create(gioSkuOriginDTO.getSpuCreateTime() == null ? null
                    : DateTimeUtil.formatDate(gioSkuOriginDTO.getSpuCreateTime(), DateTimeUtil.STANDARD_DAY));
            gioSkuAttr.setSpu_sales_type(gioSkuOriginDTO.getSpuOfferingStatus() == null ? null
                    : OfferingStatusEnum.fromCode(gioSkuOriginDTO.getSpuOfferingStatus()).name);
            gioSkuAttr.setSupplier_code("-");
            gioSkuAttr.setSupplier_name(gioSkuOriginDTO.getSupplierName());
            gioSkuAttr.setSupplier_contact("-");
            gioSkuAttr.setContact_phone("-");
            gioSkuAttr.setCommission_rate(gioSkuOriginDTO.getPointPercent() == null ? null
                    : String.valueOf(
                    new BigDecimal(gioSkuOriginDTO.getPointPercent() / 100)
                            .setScale(4, RoundingMode.HALF_UP)
                            .doubleValue()));
            gioSkuAttr.setIs_delete(gioSkuOriginDTO.getDeleteTime() == null ? "否" : "是");

            // 设置新增字段
            gioSkuAttr.setFirstLevelNavCatalog(gioSkuOriginDTO.getFirstLevelNavCatalog());
            gioSkuAttr.setSecondLevelNavCatalog(gioSkuOriginDTO.getSecondLevelNavCatalog());
            gioSkuAttr.setThirdLevelNavCatalog(gioSkuOriginDTO.getThirdLevelNavCatalog());
            gioSkuAttr.setProductKeyword(gioSkuOriginDTO.getProductKeyword());
            gioSkuAttr.setMainSaleTag(gioSkuOriginDTO.getMainSaleTag());
            gioSkuAttr.setSubSaleTag(gioSkuOriginDTO.getSubSaleTag());

            gioSkuDTO.setAttrs(gioSkuAttr);
            gioSkuDTOs.add(gioSkuDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=gioSkuDTOs.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioSkuDTO gioSkuDTO : gioSkuDTOs) {

            jsonString.append(JSON.toJSONString(gioSkuDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }

    }

    @Override
    public void getAtomMsg() throws IOException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<GioAtomDTO> gioAtomDTOs = new ArrayList<>();
        // 查询sku规格商品信息 修改销售目录价
        // List<String> atomIds = new ArrayList<>();
        // atomIds.add("1074730414667272192");
        // atomIds.add("1000067846646951936");
        // atomIds.add("1000067846676312064");
        //
        // AtomOfferingInfoExample atomOfferingInfoExample = new
        // AtomOfferingInfoExample();
        // AtomOfferingInfoExample.Criteria criteria =
        // atomOfferingInfoExample.createCriteria();
        // criteria.andIdIn(atomIds).example();
        // List<AtomOfferingInfo> atomOfferingInfos =
        // atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
        List<GioAtomOriginDTO> atomOfferingInfos = gioBurialPointMapperExt.getAtomList();
        for (GioAtomOriginDTO atomOfferingInfo : atomOfferingInfos) {
            // 获取版本号
            // List<AtomOfferingInfoHistory> atomOfferingInfoHistories =
            // atomOfferingInfoHistoryMapper.selectByExample(new
            // AtomOfferingInfoHistoryExample().createCriteria().andAtomIdEqualTo(atomOfferingInfo.getId()).example());
            // AtomOfferingInfoHistory atomOfferingInfoHistorie =
            // atomOfferingInfoHistories.get(0);
            // 获取账目项名称
            String chargeCodeName = atomOfferingInfo.getChargeCode();
            if (StringUtils.isNotEmpty(atomOfferingInfo.getChargeCode())) {
                String[] chargeCodeSplit = atomOfferingInfo.getChargeCode().split("_");
                // 取最后一个
                chargeCodeName = chargeCodeSplit[chargeCodeSplit.length - 1];
            }
            // 获取标准服务配置
            // StdServiceConfigDTO stdServiceConfig =
            // productHandlerMapper.selectStdServiceConfig(atomOfferingInfo.getId());
            GioAtomDTO gioAtomDTO = new GioAtomDTO();
            gioAtomDTO.setItem_id(atomOfferingInfo.getId());
            GioAtomDTO.GioAtomAttr gioAtomAttr = new GioAtomDTO.GioAtomAttr();
            gioAtomAttr.setOffering_code(atomOfferingInfo.getOfferingCode());
            gioAtomAttr.setOffering_name(atomOfferingInfo.getOfferingName());

            gioAtomAttr.setOffering_class(AtomOfferingClassEnum.getDescribe(atomOfferingInfo.getOfferingClass()));
            gioAtomAttr.setCharge_code_name(chargeCodeName);
            gioAtomAttr.setCharge_code(atomOfferingInfo.getChargeCode());
            gioAtomAttr.setExt_soft_offering_code(atomOfferingInfo.getExtSoftOfferingCode());
            gioAtomAttr.setExt_hard_offering_code(atomOfferingInfo.getExtHardOfferingCode());
            gioAtomAttr.setOffering_quantity(String.valueOf(atomOfferingInfo.getQuantity()));
            gioAtomAttr.setOffering_unit(atomOfferingInfo.getUnit());
            gioAtomAttr.setOffering_model(atomOfferingInfo.getModel());
            gioAtomAttr.setOffering_color(atomOfferingInfo.getColor());
            gioAtomAttr.setOffering_price(atomOfferingInfo.getAtomSalePrice() == null ? null
                    : String.valueOf(new BigDecimal(atomOfferingInfo.getAtomSalePrice() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioAtomAttr.setOffering_settlement_price(atomOfferingInfo.getSettlePrice() == null ? null
                    : String.valueOf(new BigDecimal(atomOfferingInfo.getSettlePrice() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioAtomAttr.setService_code(
                    atomOfferingInfo.getServiceCode() == null ? "-" : atomOfferingInfo.getServiceCode());
            gioAtomAttr.setService_name(
                    atomOfferingInfo.getServiceName() == null ? "-" : atomOfferingInfo.getServiceName());
            gioAtomAttr.setProduct_name(
                    atomOfferingInfo.getProductName() == null ? "-" : atomOfferingInfo.getProductName());
            gioAtomAttr.setProduct_department(
                    atomOfferingInfo.getProductDepartment() == null ? "-" : atomOfferingInfo.getProductDepartment());
            gioAtomAttr.setProduct_attributes(
                    atomOfferingInfo.getProductAttributes() == null ? "-" : atomOfferingInfo.getProductAttributes());
            gioAtomAttr.setIs_delete_offering(atomOfferingInfo.getDeleteTime() == null ? "否" : "是");
            gioAtomDTO.setAttrs(gioAtomAttr);
            gioAtomDTOs.add(gioAtomDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=gioAtomDTOs.json");
        StringBuffer jsonString = new StringBuffer();

        for (GioAtomDTO gioAtomDTO : gioAtomDTOs) {
            log.info(JSON.toJSONString(gioAtomDTO));
            jsonString.append(JSON.toJSONString(gioAtomDTO).replace("\\\\", "|")).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            log.info(jsonString.toString());
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }

    }

    @Override
    public void getOrderMsg(String startTime,  String endTime) throws IOException, ParseException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<GioAtomDTO> gioAtomDTOs = new ArrayList<>();
        // 查询sku规格商品信息 修改销售目录价
        List<String> orderIds = new ArrayList<>();
        // orderIds.add("390190000001620015");
        // orderIds.add("100190000000673037");
        // orderIds.add("380100000000489006");

        // Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        // Order2cInfoExample.Criteria criteria = order2cInfoExample.createCriteria();
        // criteria.andOrderIdIn(orderIds).example();
        List<GioOrderOriginDTO> GioOrderOriginDTOs = gioBurialPointMapperExt.getOrderList(startTime,endTime);
        // List<Order2cInfo> order2cInfos =
        // order2cInfoMapper.selectByExample(order2cInfoExample);
        List<GioOrderDTO> gioOrderDTOs = new ArrayList<>();

        for (GioOrderOriginDTO GioOrderOriginDTO : GioOrderOriginDTOs) {
            // List<OrderExportDO> list =
            // order2cAtomInfoMapperExt.selectOrderExportList(GioOrderOriginDTO.getOrderId());
            // OrderExportDO orderExportDO = null;
            //
            // if (list != null && list.size() > 0) {
            // orderExportDO = list.get(0);
            // }
            // 分销员
            DistributorInfoDTO distributorInfoDTO1 = new DistributorInfoDTO();
            DistributorInfoDTO distributorInfoDTO2 = new DistributorInfoDTO();

            // List<Order2cDistributorInfo> order2cDistributorInfos =
            // order2cDistributorInfoMapper.selectByExample(new
            // Order2cDistributorInfoExample().createCriteria().andOrderIdEqualTo(GioOrderOriginDTO.getOrderId()).example());

            if (CollectionUtils.isNotEmpty(GioOrderOriginDTO.getOrder2cDistributorInfoList())) {
                for (Order2cDistributorInfo d : GioOrderOriginDTO.getOrder2cDistributorInfoList()) {
                    if (d.getDistributorLevel().equals("1")) {
                        BeanUtils.copyProperties(d, distributorInfoDTO1);
                    } else {
                        BeanUtils.copyProperties(d, distributorInfoDTO2);
                    }
                }
            }
            // 渠道商
            String channelPartnerName = null;
            String channelPartnerPhone = null;
            String channelPartnerCode = null;
            String channelPartnerUserId = null;

            // List<Order2cAgentInfo> order2cAgentInfos =
            // order2cAgentInfoMapper.selectByExample(new
            // Order2cAgentInfoExample().createCriteria().andOrderIdEqualTo(GioOrderOriginDTO.getOrderId()).example());

            // 新增字段：渠道商标签、类别、业务人员信息
            String channelTag = null;
            String channelCategory = null;
            String busiPersonCode = null;
            String busiPersonName = null;

            // 渠道商字段：直接从 order2cAgentInfoList 中获取所有数据（优先使用清洗后的值）
            if (CollectionUtils.isNotEmpty(GioOrderOriginDTO.getOrder2cAgentInfoList())) {
                // 从代理商信息列表中提取所有渠道商信息
                List<String> channelPartnerNames = new ArrayList<>();
                List<String> channelPartnerPhones = new ArrayList<>();
                List<String> channelPartnerCodes = new ArrayList<>();
                List<String> channelPartnerUserIds = new ArrayList<>();
                List<String> channelTags = new ArrayList<>();
                List<String> channelCategories = new ArrayList<>();

                for (Order2cAgentInfo agentInfo : GioOrderOriginDTO.getOrder2cAgentInfoList()) {
                    // 渠道商名称：优先使用清洗后的值，如果没有则使用原始值
                    String agentName = agentInfo.getAgentNameWash() != null ? agentInfo.getAgentNameWash() : agentInfo.getAgentName();
                    if (agentName != null) {
                        channelPartnerNames.add(agentName);
                    }

                    // 渠道商手机号
                    if (agentInfo.getAgentPhone() != null) {
                        channelPartnerPhones.add(agentInfo.getAgentPhone());
                    }

                    // 渠道商编号：优先使用清洗后的值，如果没有则使用原始值
                    String agentNumber = agentInfo.getAgentNumberWash() != null ? agentInfo.getAgentNumberWash() : agentInfo.getAgentNumber();
                    if (agentNumber != null) {
                        channelPartnerCodes.add(agentNumber);
                    }

                    // 渠道商用户ID
                    if (agentInfo.getAgentUserId() != null) {
                        channelPartnerUserIds.add(agentInfo.getAgentUserId());
                    }

                    // 渠道商标签（清洗后的值）
                    if (agentInfo.getAgentLabelWash() != null) {
                        channelTags.add(agentInfo.getAgentLabelWash());
                    }

                    // 渠道商类别（清洗后的值）
                    if (agentInfo.getAgentCategoryWash() != null) {
                        channelCategories.add(agentInfo.getAgentCategoryWash());
                    }
                }

                // 将列表转换为管道分隔的字符串
                channelPartnerName = channelPartnerNames.isEmpty() ? null : String.join("|", channelPartnerNames);
                channelPartnerPhone = channelPartnerPhones.isEmpty() ? null : String.join("|", channelPartnerPhones);
                channelPartnerCode = channelPartnerCodes.isEmpty() ? null : String.join("|", channelPartnerCodes);
                channelPartnerUserId = channelPartnerUserIds.isEmpty() ? null : String.join("|", channelPartnerUserIds);
                channelTag = channelTags.isEmpty() ? null : String.join("|", channelTags);
                channelCategory = channelCategories.isEmpty() ? null : String.join("|", channelCategories);
            }

            // 业务人员信息：从已有的 couponInfoList 中获取（已通过 LEFT JOIN 查询）
            busiPersonCode = extractSalesmanCodes(GioOrderOriginDTO.getCouponInfoList());
            busiPersonName = extractSalesmanNames(GioOrderOriginDTO.getCouponInfoList());

            // 网格字段：直接从 DTO 中获取（已通过 LEFT JOIN 查询）
            String gridProvince = GioOrderOriginDTO.getGridProvince();
            String gridCity = GioOrderOriginDTO.getGridCity();
            String gridDistrict = GioOrderOriginDTO.getGridDistrict();
            String orderGrid = GioOrderOriginDTO.getOrderGrid(); // 先取原有值

            // 如果文杰清洗有网格名称值，则使用文杰清洗的
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getGridName())) {
                orderGrid = GioOrderOriginDTO.getGridName();
            }

            // 领货码
            String listCouponStr = formatCouponInfoDTOToString(GioOrderOriginDTO.getCouponInfoList());

            String snListStr = "";
           
            if (CollectionUtils.isNotEmpty(GioOrderOriginDTO.getOrder2cAtomSnList())) {
                for (String sn : GioOrderOriginDTO.getOrder2cAtomSnList()) {
                    snListStr += sn + ";";
                }
                snListStr = snListStr.substring(0, snListStr.length() - 1);
            }
            // 归属省市区
//            String[] locations = null;
//            if (GioOrderOriginDTO.getProvinceOrgName() != null) {
//                if (GioOrderOriginDTO.getProvinceOrgName() != null) {
//                    locations = GioOrderOriginDTO.getProvinceOrgName().split("-");
//                }
//
//            }
            // 发货物流
            // List<LogisticsInfo> logisticsInfos = logisticsInfoMapper.selectByExample(new
            // LogisticsInfoExample().createCriteria().andOrderIdEqualTo(GioOrderOriginDTO.getOrderId()).example());

            String logisticsNumber = "";
            List<LogisticsInfoDTO> supplierInfoList = supplierInfo.getList();
            if (CollectionUtils.isNotEmpty(GioOrderOriginDTO.getLogisticsInfoList())) {
                for (LogisticsInfo l : GioOrderOriginDTO.getLogisticsInfoList()) {
                    LogisticsInfoDTO logisticsInfoDTO = supplierInfoList.stream()
                            .filter(b -> b.getSupplierName().equals(l.getSupplierName())).findFirst()
                            .orElse(new LogisticsInfoDTO());
                    logisticsNumber += logisticsInfoDTO == null ? ""
                            : logisticsInfoDTO.getName() + l.getLogisCode() + ";";

                }
            }
            if (!logisticsNumber.equals("")) {
                logisticsNumber = logisticsNumber.substring(0, logisticsNumber.length() - 1);
            }
            GioOrderDTO gioOrderDTO = new GioOrderDTO();
            gioOrderDTO.setItem_id(GioOrderOriginDTO.getOrderId());
            GioOrderDTO.GioOrderAttr gioOrderAttr = new GioOrderDTO.GioOrderAttr();
            gioOrderAttr.setOrder_status(OrderStatusInnerEnum.getDescribe(GioOrderOriginDTO.getOrderStatus()));
            gioOrderAttr.setOrder_status_change_time(GioOrderOriginDTO.getOrderStatusTime() == null ? null
                    : DateTimeUtil.formatDate(GioOrderOriginDTO.getOrderStatusTime(), DateTimeUtil.STANDARD_DAY));
            gioOrderAttr.setCreate_time(
                    DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getCreateTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getCreateTime())));
            gioOrderAttr.setShipping_time(GioOrderOriginDTO.getSendGoodsTime() == null ? null
                    : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getSendGoodsTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getSendGoodsTime())));
            gioOrderAttr.setAccept_time(GioOrderOriginDTO.getReceiveOrderTime() == null ? null
                    : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getReceiveOrderTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getReceiveOrderTime())));
            gioOrderAttr.setBilling_time(GioOrderOriginDTO.getValetOrderCompleteTime() == null ? null
                    : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getValetOrderCompleteTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getValetOrderCompleteTime())));
            gioOrderAttr.setArrival_time(GioOrderOriginDTO.getCreateTime() == null ? null
                    : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getCreateTime()) == null ? null : String.valueOf(DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getCreateTime())));
            // gioOrderAttr.setRefund_amount("-");
            // gioOrderAttr.setReceived_amount("-");
            gioOrderAttr.setTracking_number(logisticsNumber);
            gioOrderAttr.setChack_time(GioOrderOriginDTO.getBillNoTime() == null ? null
                    : DateTimeUtil.formatDate(GioOrderOriginDTO.getBillNoTime(), DateTimeUtil.STANDARD_DAY));
            gioOrderAttr.setBilling_order_type(
                    GioOrderOriginDTO.getSaleOrderType() == null ? null : GioOrderOriginDTO.getSaleOrderType());
            // gioOrderAttr.setPurchase_order_code();
            // gioOrderAttr.setSettlement_status();
            gioOrderAttr.setDistributor_l1_user_id(distributorInfoDTO1.getDistributorUserID() == null ? null
                    : distributorInfoDTO1.getDistributorUserID());
            gioOrderAttr.setDistributor_l2_user_id(distributorInfoDTO2.getDistributorShareCode() == null ? null
                    : distributorInfoDTO2.getDistributorShareCode());
            gioOrderAttr.setShare_code_l1(distributorInfoDTO1.getDistributorShareCode() == null ? null
                    : distributorInfoDTO1.getDistributorShareCode());
            gioOrderAttr.setShare_code_l2(distributorInfoDTO2.getDistributorShareCode() == null ? null
                    : distributorInfoDTO2.getDistributorShareCode());
            gioOrderAttr.setDistributor_l1_phone(distributorInfoDTO1.getDistributorPhone() == null ? null
                    : distributorInfoDTO1.getDistributorPhone());
            gioOrderAttr.setDistributor_l2_phone(distributorInfoDTO2.getDistributorPhone() == null ? null
                    : distributorInfoDTO2.getDistributorPhone());
            gioOrderAttr.setCustomer_manager_id(
                    GioOrderOriginDTO.getCreateOperUserId() == null ? null : GioOrderOriginDTO.getCreateOperUserId());
            gioOrderAttr.setCustomer_manager_code(
                    GioOrderOriginDTO.getCreateOperCode() == null ? null : GioOrderOriginDTO.getCreateOperCode());
            gioOrderAttr.setCustomer_manager_name(
                    GioOrderOriginDTO.getCustName() == null ? null : GioOrderOriginDTO.getCustName());
            gioOrderAttr.setCustomer_manager_phone(
                    GioOrderOriginDTO.getCustMgPhone() == null ? null : GioOrderOriginDTO.getCustMgPhone());
            gioOrderAttr.setChannel_partner_name(channelPartnerName);
            gioOrderAttr.setChannel_partner_phone(channelPartnerPhone);
            gioOrderAttr.setChannel_partner_code(channelPartnerCode);
            gioOrderAttr.setChannel_partner_user_id(channelPartnerUserId);
            gioOrderAttr.setOrder_channel_name(GioOrderOriginDTO.getOrderingChannelName() == null ? null
                    : GioOrderOriginDTO.getOrderingChannelName());
            gioOrderAttr.setCoupon_info(listCouponStr);
            gioOrderAttr.setShipping_province(GioOrderOriginDTO.getAddr1() == null ? null
                    : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getAddr1(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setShipping_city(GioOrderOriginDTO.getAddr2() == null ? null
                    : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getAddr2(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setShipping_area(GioOrderOriginDTO.getAddr3() == null ? null
                    : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getAddr3(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setSn_var(snListStr);
//            gioOrderAttr.setOrder_province((locations != null && locations.length >= 1) ? locations[0] : null);
//            gioOrderAttr.setOrder_city((locations != null && locations.length >= 2) ? locations[1] : null);
//            gioOrderAttr.setOrder_area((locations != null && locations.length >= 3) ? locations[2] : null);
            gioOrderAttr.setOrder_grid(orderGrid); // 使用文杰清洗的值（如果有的话）
            gioOrderAttr.setCus_code(GioOrderOriginDTO.getCustCode() == null ? null
                    : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setCus_name(GioOrderOriginDTO.getCustName() == null ? null
                    : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustName(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setBusiness_code(GioOrderOriginDTO.getBusinessCode() == null ? null: BusinessCodeEnum.getChnName(GioOrderOriginDTO.getBusinessCode() ));

            // 设置新增字段
            gioOrderAttr.setChannelTag(channelTag);
            gioOrderAttr.setChannelCategory(channelCategory);
            gioOrderAttr.setBusiPersonCode(busiPersonCode);
            gioOrderAttr.setBusiPersonName(busiPersonName);

            // 设置网格字段（从文杰清洗获取）
            gioOrderAttr.setGridProvince(gridProvince);
            gioOrderAttr.setGridCity(gridCity);
            gioOrderAttr.setGridDistrict(gridDistrict);

            gioOrderDTO.setAttrs(gioOrderAttr);
            gioOrderDTOs.add(gioOrderDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=gioOrderDTOs.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioOrderDTO gioOrderDTO : gioOrderDTOs) {

            jsonString.append(JSON.toJSONString(gioOrderDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }

    }

    /**
     * 优化业务人员信息获取逻辑，提取coupon_code作为业务人员编码
     * 注意：此方法用于处理非orderInfo来源的数据，不需要解密
     */
    private String extractJobNumbers(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }
        List<String> employeeIds = couponInfoList.stream()
                .map(coupon -> coupon.getEmployeeId())
                .filter(employeeId -> employeeId != null && !employeeId.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return employeeIds.isEmpty() ? null : String.join("|", employeeIds);
    }

    /**
     * 从orderInfo获取的优惠券信息中提取业务人员编码，需要解密处理
     * @param couponInfoList 从orderInfo获取的优惠券信息列表
     * @return 业务人员编码字符串
     */
    private String extractJobNumbersFromOrderInfo(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }

        List<String> employeeIds = new ArrayList<>();
        for (CouponInfoDTO coupon : couponInfoList) {
            String employeeId = coupon.getEmployeeId();
            if (employeeId != null && !employeeId.trim().isEmpty()) {
                employeeIds.add(employeeId);
            }
        }

        return employeeIds.isEmpty() ? null : String.join("|", employeeIds);
    }

    /**
     * 提取销售员手机号，用于处理非orderInfo来源的数据，不需要解密
     */
    private String extractPhoneNumbers(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }
        List<String> phoneNumbers = couponInfoList.stream()
                .map(coupon -> {
                    if (coupon.getSalesmanPhone() == null) return null;
                    try {
                        String phone = coupon.getSalesmanPhone();
                        return (phone != null && !phone.trim().isEmpty()) ? phone : null;
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(phone -> phone != null)
                .collect(java.util.stream.Collectors.toList());
        return phoneNumbers.isEmpty() ? null : String.join("|", phoneNumbers);
    }

    /**
     * 从orderInfo获取的优惠券信息中提取销售员手机号，需要解密处理
     * @param couponInfoList 从orderInfo获取的优惠券信息列表
     * @return 销售员手机号字符串
     */
    private String extractPhoneNumbersFromOrderInfo(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }

        List<String> phoneNumbers = new ArrayList<>();
        for (CouponInfoDTO coupon : couponInfoList) {
            String phone = coupon.getSalesmanPhone();
            if (phone != null && !phone.trim().isEmpty()) {
                phoneNumbers.add(phone);
            }
        }

        return phoneNumbers.isEmpty() ? null : String.join("|", phoneNumbers);
    }

    /**
     * 从优惠券信息中提取业务人员姓名（参考getOrderCreated方法）
     * 注意：会先对加密的销售员信息进行解密处理
     */
    private String extractSalesmanNames(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }

        List<String> salesmanNames = new ArrayList<>();
        for (CouponInfoDTO coupon : couponInfoList) {
            // 先解密销售员信息
//            decryptCouponInfoSalesmanFields(coupon);

            String salesmanName = coupon.getSalesmanName();
            if (salesmanName != null && !salesmanName.trim().isEmpty()) {
                salesmanNames.add(salesmanName);
            }
        }

        return salesmanNames.isEmpty() ? null : String.join("|", salesmanNames);
    }

    /**
     * 从CouponInfoDTO列表中提取业务人员编码（salesman_code）
     * 注意：会先对加密的销售员信息进行解密处理
     */
    private String extractSalesmanCodes(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }

        List<String> salesmanCodes = new ArrayList<>();
        for (CouponInfoDTO coupon : couponInfoList) {
            // 先解密销售员信息
//            decryptCouponInfoSalesmanFields(coupon);

            String salesmanCode = coupon.getSalesmanCode();
            if (salesmanCode != null && !salesmanCode.trim().isEmpty()) {
                salesmanCodes.add(salesmanCode);
            }
        }

        return salesmanCodes.isEmpty() ? null : String.join("|", salesmanCodes);
    }

    /**
     * 从CouponInfo实体类中提取业务人员编码（coupon_code）（用于getOrderMsg和sendOrderDimensionalityMsg）
     */
    private String extractJobNumbersFromCouponInfo(List<CouponInfo> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }
        List<String> couponCodes = couponInfoList.stream()
                .map(coupon -> coupon.getCouponCode())
                .filter(couponCode -> couponCode != null && !couponCode.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return couponCodes.isEmpty() ? null : String.join("|", couponCodes);
    }

    /**
     * 从CouponInfo实体类中提取业务人员姓名（用于getOrderMsg和sendOrderDimensionalityMsg）
     */
    private String extractSalesmanNamesFromCouponInfo(List<CouponInfo> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }
        List<String> salesmanNames = couponInfoList.stream()
                .map(coupon -> coupon.getSalesmanName())
                .filter(salesmanName -> salesmanName != null && !salesmanName.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return salesmanNames.isEmpty() ? null : String.join("|", salesmanNames);
    }

    /**
     * 从CouponInfo实体类中提取业务人员编码（salesman_code）（用于getOrderMsg和sendOrderDimensionalityMsg）
     */
    private String extractSalesmanCodesFromCouponInfo(List<CouponInfo> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }
        List<String> salesmanCodes = couponInfoList.stream()
                .map(coupon -> coupon.getSalesmanCode())
                .filter(salesmanCode -> salesmanCode != null && !salesmanCode.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return salesmanCodes.isEmpty() ? null : String.join("|", salesmanCodes);
    }

    /**
     * 从CouponInfoDTO列表中提取业务人员编码（salesman_code）
     */
    private String extractSalesmanCodesFromCouponInfoDTO(List<CouponInfoDTO> couponInfoDTOList) {
        if (couponInfoDTOList == null || couponInfoDTOList.isEmpty()) {
            return null;
        }
        List<String> salesmanCodes = couponInfoDTOList.stream()
                .map(coupon -> coupon.getSalesmanCode())
                .filter(salesmanCode -> salesmanCode != null && !salesmanCode.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return salesmanCodes.isEmpty() ? null : String.join("|", salesmanCodes);
    }

    /**
     * 从CouponInfoDTO列表中提取业务人员姓名
     */
    private String extractSalesmanNamesFromCouponInfoDTO(List<CouponInfoDTO> couponInfoDTOList) {
        if (couponInfoDTOList == null || couponInfoDTOList.isEmpty()) {
            return null;
        }
        List<String> salesmanNames = couponInfoDTOList.stream()
                .map(coupon -> coupon.getSalesmanName())
                .filter(salesmanName -> salesmanName != null && !salesmanName.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return salesmanNames.isEmpty() ? null : String.join("|", salesmanNames);
    }

    /**
     * 从CouponInfoDTO列表中提取优惠券代码（用于couponInfo_var字段）
     */
    private String extractCouponCodesFromCouponInfoDTO(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }
        List<String> couponCodes = couponInfoList.stream()
                .map(coupon -> coupon.getCouponCode())
                .filter(couponCode -> couponCode != null && !couponCode.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return couponCodes.isEmpty() ? null : String.join("|", couponCodes);
    }

    /**
     * 格式化CouponInfo实体类列表为字符串格式：(code,amount)||
     * @param couponInfoList CouponInfo实体类列表
     * @return 格式化后的字符串，如："(code1,10.00)||(code2,20.00)"
     */
    private String formatCouponInfoToString(List<CouponInfo> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return "";
        }

        StringBuilder listCouponStr = new StringBuilder();
        for (CouponInfo couponInfo : couponInfoList) {
            String couponCode = couponInfo.getCouponCode();
            String couponAmount = couponInfo.getCouponAmount();
            if (couponCode != null && couponAmount != null) {
                String couponString = "(" + couponCode + "," +
                    String.format("%.2f", Long.valueOf(couponAmount) / 1000.00) + ")";
                listCouponStr.append(couponString).append("||");
            }
        }

        // 移除最后的"||"
        if (listCouponStr.length() > 0) {
            listCouponStr.setLength(listCouponStr.length() - 2);
        }

        return listCouponStr.toString();
    }

    /**
     * 格式化CouponInfoDTO列表为字符串格式：(code,amount)||
     * 注意：在格式化之前会对加密的销售员信息进行解密处理
     * @param couponInfoDTOList CouponInfoDTO列表
     * @return 格式化后的字符串，如："(code1,10.00)||(code2,20.00)"
     */
    private String formatCouponInfoDTOToString(List<CouponInfoDTO> couponInfoDTOList) {
        if (couponInfoDTOList == null || couponInfoDTOList.isEmpty()) {
            return "";
        }

        StringBuilder listCouponStr = new StringBuilder();
        for (CouponInfoDTO couponInfo : couponInfoDTOList) {
            String couponCode = couponInfo.getCouponCode();
            String couponAmount = couponInfo.getCouponAmount();
            if (couponCode != null && couponAmount != null) {
                String couponString = "(" + couponCode + "," +
                    String.format("%.2f", Long.valueOf(couponAmount) / 1000.00) + ")";
                listCouponStr.append(couponString).append("||");
            }
        }

        // 移除最后的"||"
        if (listCouponStr.length() > 0) {
            listCouponStr.setLength(listCouponStr.length() - 2);
        }

        return listCouponStr.toString();
    }

    /**
     * 将CouponInfo实体类列表转换为CouponParam列表
     * @param couponInfoList CouponInfo实体类列表
     * @return CouponParam列表
     */
    private List<CouponParam> convertCouponInfoToCouponParams(List<CouponInfo> couponInfoList) {
        List<CouponParam> listCoupon = new ArrayList<>();
        if (couponInfoList != null && !couponInfoList.isEmpty()) {
            for (CouponInfo couponInfo : couponInfoList) {
                String couponCode = couponInfo.getCouponCode();
                String couponAmount = couponInfo.getCouponAmount();
                if (couponCode != null && couponAmount != null) {
                    CouponParam couponParam = new CouponParam(couponCode,
                            String.format("%.2f", Long.valueOf(couponAmount) / 1000.00));
                    listCoupon.add(couponParam);
                }
            }
        }
        return listCoupon;
    }

    /**
     * 将CouponInfoDTO列表转换为CouponParam列表
     * 注意：CouponInfoDTO中的数据已经是解密后的数据
     * @param couponInfoDTOList CouponInfoDTO列表
     * @return CouponParam列表
     */
    private List<CouponParam> convertCouponInfoDTOToCouponParams(List<CouponInfoDTO> couponInfoDTOList) {
        List<CouponParam> listCoupon = new ArrayList<>();
        if (couponInfoDTOList != null && !couponInfoDTOList.isEmpty()) {
            for (CouponInfoDTO couponInfo : couponInfoDTOList) {
                String couponCode = couponInfo.getCouponCode();
                String couponAmount = couponInfo.getCouponAmount();
                if (couponCode != null && couponAmount != null) {
                    CouponParam couponParam = new CouponParam(couponCode,
                            String.format("%.2f", Long.valueOf(couponAmount) / 1000.00));
                    listCoupon.add(couponParam);
                }
            }
        }
        return listCoupon;
    }

    /**
     * 将数据库查询的CouponInfo列表转换为CouponInfoDTO列表
     * 注意：数据库中的CouponInfo数据是未加密的，不需要解密处理
     * @param couponInfoList 数据库查询的CouponInfo列表
     * @return CouponInfoDTO列表
     */
    private List<CouponInfoDTO> convertCouponInfoToCouponInfoDTO(List<CouponInfo> couponInfoList) {
        List<CouponInfoDTO> couponInfoDTOList = new ArrayList<>();
        if (couponInfoList != null && !couponInfoList.isEmpty()) {
            for (CouponInfo couponInfo : couponInfoList) {
                CouponInfoDTO couponInfoDTO = new CouponInfoDTO();
                // 直接复制字段，数据库中的数据是未加密的
                couponInfoDTO.setCouponCode(couponInfo.getCouponCode());
                couponInfoDTO.setCouponAmount(couponInfo.getCouponAmount());
                couponInfoDTO.setSalesmanCode(couponInfo.getSalesmanCode());
                couponInfoDTO.setSalesmanPhone(couponInfo.getSalesmanPhone());
                couponInfoDTO.setEmployeeId(couponInfo.getEmployeeId());
                couponInfoDTO.setSalesmanName(couponInfo.getSalesmanName());
                couponInfoDTOList.add(couponInfoDTO);
            }
        }
        return couponInfoDTOList;
    }

    /**
     * 将代理电话列表转换为管道符分隔的字符串
     */
    private String extractAgentPhones(List<String> agentPhoneList) {
        if (agentPhoneList == null || agentPhoneList.isEmpty()) {
            return null;
        }
        List<String> validPhones = agentPhoneList.stream()
                .filter(phone -> phone != null && !phone.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return validPhones.isEmpty() ? null : String.join("|", validPhones);
    }

    /**
     * 将代理编号列表转换为管道符分隔的字符串
     */
    private String extractAgentNumbers(List<String> agentNumberList) {
        if (agentNumberList == null || agentNumberList.isEmpty()) {
            return null;
        }
        List<String> validNumbers = agentNumberList.stream()
                .filter(number -> number != null && !number.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return validNumbers.isEmpty() ? null : String.join("|", validNumbers);
    }

    /**
     * 从代理信息列表中提取电话号码
     */
    private String extractAgentPhonesFromAgentInfo(List<Order2cAgentInfo> agentInfoList) {
        if (agentInfoList == null || agentInfoList.isEmpty()) {
            return null;
        }
        List<String> validPhones = agentInfoList.stream()
                .map(Order2cAgentInfo::getAgentPhone)
                .filter(phone -> phone != null && !phone.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return validPhones.isEmpty() ? null : String.join("|", validPhones);
    }
    /**
     * 从代理信息列表中提取手机号
     */
    private String extractAgentPhonesFromAgentInfoDTO(List<AgentInfoDTO> agentInfoList) {
        if (agentInfoList == null || agentInfoList.isEmpty()) {
            return null;
        }
        List<String> validNumbers = agentInfoList.stream()
                .map(agent -> {
                    if (agent.getAgentPhone() == null) return null;
                    try {
                        String decrypted = IOTEncodeUtils.decryptSM4(agent.getAgentPhone(), iotSm4Key, iotSm4Iv);
                        return (decrypted != null && !decrypted.trim().isEmpty()) ? decrypted : null;
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(phone -> phone != null)
                .collect(java.util.stream.Collectors.toList());
        return validNumbers.isEmpty() ? null : String.join("|", validNumbers);
    }
    /**
     * 从AgentInfoDTO列表中提取代理编号
     */
    private String extractAgentNumbersFromAgentInfoDTO(List<AgentInfoDTO> agentInfoList) {
        if (agentInfoList == null || agentInfoList.isEmpty()) {
            return null;
        }
        List<String> validNumbers = agentInfoList.stream()
                .map(AgentInfoDTO::getAgentNumber)
                .filter(number -> number != null && !number.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return validNumbers.isEmpty() ? null : String.join("|", validNumbers);
    }

    /**
     * 从代理信息列表中提取代理编号
     */
    private String extractAgentNumbersFromAgentInfo(List<Order2cAgentInfo> agentInfoList) {
        if (agentInfoList == null || agentInfoList.isEmpty()) {
            return null;
        }
        List<String> validNumbers = agentInfoList.stream()
                .map(Order2cAgentInfo::getAgentNumber)
                .filter(number -> number != null && !number.trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
        return validNumbers.isEmpty() ? null : String.join("|", validNumbers);
    }

    @Override
    public void getOrderCreated(String startTime, String endTime) throws IOException, ParseException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<GioAtomDTO> gioAtomDTOs = new ArrayList<>();
        // 查询sku规格商品信息 修改销售目录价
        List<String> orderIds = new ArrayList<>();
        // orderIds.add("390190000001620015");
        // orderIds.add("100190000000673037");
        // orderIds.add("380100000000489006");

        // Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        // Order2cInfoExample.Criteria criteria = order2cInfoExample.createCriteria();
        // criteria.andOrderIdIn(orderIds).example();
        List<GioOrderCreatedOriginDTO> GioOrderOriginDTOs = gioBurialPointMapperExt
                .getGioOrderCreatedOriginDTOList(startTime, endTime, 0);

        List<GioOrderH5OrderCreatedHDDTO> gioOrderDTOs = new ArrayList<>();

        for (GioOrderCreatedOriginDTO GioOrderOriginDTO : GioOrderOriginDTOs) {


            // 计算订单结算金额(物联网收入) 排除A06H 和A13A的 再加上卡+x增值服务的
            AtomicLong orderSettlePrice = new AtomicLong(0L);
            if (GioOrderOriginDTO.getSpuOfferingClass().equals("A06")) {
                GioOrderOriginDTO.getOrder2cAtomInfoList().forEach(order2cAtomInfoItem -> {
                    if (order2cAtomInfoItem != null && !GioOrderOriginDTO.getAtomOfferingClass().equals("H")) {
                        orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                                * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                    }
                });
            } else if (GioOrderOriginDTO.getSpuOfferingClass().equals("A13")) {
                GioOrderOriginDTO.getOrder2cAtomInfoList().forEach(order2cAtomInfoItem -> {
                    if (order2cAtomInfoItem != null && !GioOrderOriginDTO.getAtomOfferingClass().equals("A")) {
                        orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                                * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                    }
                });
            } else {
                GioOrderOriginDTO.getOrder2cAtomInfoList().forEach(order2cAtomInfoItem -> {

                    orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                            * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                });
                // 获取卡+x增值服务价格

                if (GioOrderOriginDTO.getOrderQuantity() != null) {

                    orderSettlePrice.addAndGet(Long.valueOf(GioOrderOriginDTO.getExpensesPrice())
                            * Long.valueOf(GioOrderOriginDTO.getOrderQuantity())
                            * Long.valueOf(GioOrderOriginDTO.getExpensesTerm()));
                }
            }
            String orgType = "-";
            if (GioOrderOriginDTO.getBusinessCode().equals("SyncIndividualOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncOSExtOrderInfo")) {
                orgType = "个人客户";
            } else if (GioOrderOriginDTO.getBusinessCode().equals("SyncGrpOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncValetOrderInfo")) {
                orgType = "集团客户";
            }
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
            Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
            String provinceName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getBeId())) {
                provinceName = "471".equals(GioOrderOriginDTO.getBeId()) ? "内蒙古"
                        : (String) provinceCodeNameMap.get(GioOrderOriginDTO.getBeId());
            }
            String cityName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getLocation())) {
                cityName = (String) locationCodeNameMap.get(GioOrderOriginDTO.getLocation());
            }
            String regionName = "";

            if (StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                    && "10003329".equals(GioOrderOriginDTO.getRegionID())) {
                regionName = "綦江区";
            } else {
                regionName = StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                        ? (String) regionCodeNameMap.get(GioOrderOriginDTO.getRegionID())
                        : null;

            }
            String userId = GioOrderOriginDTO.getCreateOperUserId() == null ? GioOrderOriginDTO.getCustUserId() == null ? null : GioOrderOriginDTO.getCustUserId() : GioOrderOriginDTO.getCreateOperUserId();
            userId = userId == null ? GioOrderOriginDTO.getCustCode() == null ? null : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv) : userId;
            GioOrderH5OrderCreatedHDDTO gioOrderH5OrderCreatedHDDTO = new GioOrderH5OrderCreatedHDDTO();
            gioOrderH5OrderCreatedHDDTO.setEvent("H5_orderCreated_HD");
            gioOrderH5OrderCreatedHDDTO.setUserId(userId);
            gioOrderH5OrderCreatedHDDTO.setTimestamp(GioOrderOriginDTO.getCreateTime() == null ? null : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getCreateTime()));
            GioOrderH5OrderCreatedHDDTO.GioOrderH5OrderCreatedHDAttr gioOrderAttr = new GioOrderH5OrderCreatedHDDTO.GioOrderH5OrderCreatedHDAttr();
            gioOrderAttr.setPlatformName_var("中后台");
            gioOrderAttr.setOrderId_var(GioOrderOriginDTO.getOrderId());
            gioOrderAttr.setOrderType_var(
                    GioOrderOriginDTO.getOrderType() == null ? null : OrderTypeEnum.getDescByCode(GioOrderOriginDTO.getOrderType()));
            gioOrderAttr.setPayAmount_var(GioOrderOriginDTO.getTotalPrice() == null ? null : String.valueOf(new BigDecimal(Long.valueOf(
                    IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getTotalPrice(), iotSm4Key, iotSm4Iv))
                    / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setAmountDue_var(String.valueOf(new BigDecimal(orderSettlePrice.doubleValue() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setOrdersource_var(
                    OrderingChannelNameEnum.fromCode(GioOrderOriginDTO.getOrderingChannelName()) == null
                            ? GioOrderOriginDTO.getOrderingChannelName()
                            : OrderingChannelNameEnum.fromCode(GioOrderOriginDTO.getOrderingChannelName())
                            .getMessage());
            gioOrderAttr.setCustomerType_var(orgType);
            gioOrderAttr.setCusCode_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setCusName_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustName(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setCusProvinceCode_var(GioOrderOriginDTO.getBeId());
            gioOrderAttr.setCusCityCode_var(GioOrderOriginDTO.getLocation());
            gioOrderAttr.setCusAreaCode_var(GioOrderOriginDTO.getRegionID());
            gioOrderAttr.setCusProvince_var(provinceName);
            gioOrderAttr.setCusCity_var(cityName);
            gioOrderAttr.setCusArea_var(regionName);
            gioOrderAttr.setChannelPartnerPhone_var(extractAgentPhonesFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));
            gioOrderAttr.setCustomerManagerPhone_var(GioOrderOriginDTO.getCustMgPhone());
            gioOrderAttr.setCustomerManagerCode_var(GioOrderOriginDTO.getCreateOperCode());

            // 新增字段赋值
            // 订单归属信息 - 从provinceOrgName获取
            String[] orderLocations = null;
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getProvinceOrgName())) {
                orderLocations = GioOrderOriginDTO.getProvinceOrgName().split("-");
            }
            gioOrderAttr.setOrderProvince_var(orderLocations != null && orderLocations.length > 0 ? orderLocations[0] : null);
            gioOrderAttr.setOrderCity_var(orderLocations != null && orderLocations.length > 1 ? orderLocations[1] : null);
            gioOrderAttr.setOrderArea_var(orderLocations != null && orderLocations.length > 2 ? orderLocations[2] : null);

            // 业务人员信息 - 从优惠券信息中获取
            String busPersonJobNumbers = extractJobNumbers(GioOrderOriginDTO.getCouponInfoList());
            String busPersonPhoneNums = extractPhoneNumbers(GioOrderOriginDTO.getCouponInfoList());
            gioOrderAttr.setBusiPersonJobNumber_var(busPersonJobNumbers);
            gioOrderAttr.setBusiPersonPhoneNum_var(busPersonPhoneNums);
            gioOrderAttr.setDeductAmount(String.valueOf(GioOrderOriginDTO.getDeductPrice() == null ? null :
                    new BigDecimal(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getDeductPrice(), iotSm4Key, iotSm4Iv))
                            .divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP)
                            .doubleValue()));
            gioOrderAttr.setChannelPartnerCode_var(extractAgentNumbersFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));

            // 商品类型
            String spuType = SPUOfferingClassEnum.getDisplay(GioOrderOriginDTO.getSpuOfferingClass());
            gioOrderAttr.setPrductType(spuType);

            // 领码信息
            String listCouponStr = formatCouponInfoDTOToString(GioOrderOriginDTO.getCouponInfoList());
            gioOrderAttr.setCouponInfo_var(listCouponStr);

            gioOrderH5OrderCreatedHDDTO.setAttrs(gioOrderAttr);
            gioOrderDTOs.add(gioOrderH5OrderCreatedHDDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=GioOrderH5OrderCreatedHD.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioOrderH5OrderCreatedHDDTO gioOrderDTO : gioOrderDTOs) {

            jsonString.append(JSON.toJSONString(gioOrderDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }

    }

    @Override
    public void getOrderPnding(String startTime, String endTime) throws IOException, ParseException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<GioAtomDTO> gioAtomDTOs = new ArrayList<>();
        // 查询sku规格商品信息 修改销售目录价
        List<String> orderIds = new ArrayList<>();
        // orderIds.add("390190000001620015");
        // orderIds.add("100190000000673037");
        // orderIds.add("380100000000489006");

        // Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        // Order2cInfoExample.Criteria criteria = order2cInfoExample.createCriteria();
        // criteria.andOrderIdIn(orderIds).example();
        //待出帐
        List<GioOrderCreatedOriginDTO> GioOrderOriginDTOs = gioBurialPointMapperExt
                .getGioOrderCreatedOriginDTOList(startTime, endTime, 1);

        List<GioOrderPndingInvoiceDTO> gioOrderDTOs = new ArrayList<>();

        for (GioOrderCreatedOriginDTO GioOrderOriginDTO : GioOrderOriginDTOs) {


            // 领货码
            String listCouponStr = formatCouponInfoDTOToString(GioOrderOriginDTO.getCouponInfoList());

            String snListStr = "";


            // 计算订单结算金额(物联网收入) 排除A06H 和A13A的 再加上卡+x增值服务的
            AtomicLong orderSettlePrice = new AtomicLong(0L);
            if (GioOrderOriginDTO.getSpuOfferingClass().equals("A06")) {
                GioOrderOriginDTO.getOrder2cAtomInfoList().forEach(order2cAtomInfoItem -> {
                    if (order2cAtomInfoItem != null && !GioOrderOriginDTO.getAtomOfferingClass().equals("H")) {
                        orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                                * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                    }
                });
            } else if (GioOrderOriginDTO.getSpuOfferingClass().equals("A13")) {
                GioOrderOriginDTO.getOrder2cAtomInfoList().forEach(order2cAtomInfoItem -> {
                    if (order2cAtomInfoItem != null && !GioOrderOriginDTO.getAtomOfferingClass().equals("A")) {
                        orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                                * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                    }
                });
            } else {
                GioOrderOriginDTO.getOrder2cAtomInfoList().forEach(order2cAtomInfoItem -> {

                    orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                            * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                });
                // 获取卡+x增值服务价格

                if (GioOrderOriginDTO.getOrderQuantity() != null) {

                    orderSettlePrice.addAndGet(Long.valueOf(GioOrderOriginDTO.getExpensesPrice())
                            * Long.valueOf(GioOrderOriginDTO.getOrderQuantity())
                            * Long.valueOf(GioOrderOriginDTO.getExpensesTerm()));
                }
            }

            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
            Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
            String provinceName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getBeId())) {
                provinceName = "471".equals(GioOrderOriginDTO.getBeId()) ? "内蒙古"
                        : (String) provinceCodeNameMap.get(GioOrderOriginDTO.getBeId());
            }
            String cityName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getLocation())) {
                cityName = (String) locationCodeNameMap.get(GioOrderOriginDTO.getLocation());
            }
            String regionName = "";

            if (StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                    && "10003329".equals(GioOrderOriginDTO.getRegionID())) {
                regionName = "綦江区";
            } else {
                regionName = StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                        ? (String) regionCodeNameMap.get(GioOrderOriginDTO.getRegionID())
                        : null;

            }
            String userId = GioOrderOriginDTO.getCreateOperUserId() == null ? GioOrderOriginDTO.getCustUserId() == null ? null : GioOrderOriginDTO.getCustUserId() : GioOrderOriginDTO.getCreateOperUserId();
            userId = userId == null ? GioOrderOriginDTO.getCustCode() == null ? null : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv) : userId;
            GioOrderPndingInvoiceDTO gioOrderH5OrderCreatedHDDTO = new GioOrderPndingInvoiceDTO();

            gioOrderH5OrderCreatedHDDTO.setEvent("H5_orderPndingInvoice");
            gioOrderH5OrderCreatedHDDTO.setUserId(userId);
            gioOrderH5OrderCreatedHDDTO.setTimestamp(GioOrderOriginDTO.getValetOrderCompleteTime() == null ? null : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getValetOrderCompleteTime()));
            GioOrderPndingInvoiceDTO.GioOrderPndingInvoiceAttr gioOrderAttr = new GioOrderPndingInvoiceDTO.GioOrderPndingInvoiceAttr();
            gioOrderAttr.setPlatformName_var("中后台");
            gioOrderAttr.setOrderId_var(GioOrderOriginDTO.getOrderId());
            gioOrderAttr.setOrderType_var(
                    GioOrderOriginDTO.getOrderType() == null ? null : OrderTypeEnum.getDescByCode(GioOrderOriginDTO.getOrderType()));
            gioOrderAttr.setPayAmount_var(GioOrderOriginDTO.getTotalPrice() == null ? null : String.valueOf(new BigDecimal(Long.valueOf(
                    IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getTotalPrice(), iotSm4Key, iotSm4Iv))
                    / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setAmountDue_var(String.valueOf(new BigDecimal(orderSettlePrice.doubleValue() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setOrdersource_var(
                    OrderingChannelNameEnum.fromCode(GioOrderOriginDTO.getOrderingChannelName()) == null
                            ? GioOrderOriginDTO.getOrderingChannelName()
                            : OrderingChannelNameEnum.fromCode(GioOrderOriginDTO.getOrderingChannelName())
                            .getMessage());
            gioOrderAttr.setOrderTime_var(GioOrderOriginDTO.getCreateTime());
            gioOrderAttr.setCouponInfo_var(listCouponStr);
//            gioOrderAttr.setCusCode_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv));
//            gioOrderAttr.setCusName_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustName(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setCusProvinceCode_var(GioOrderOriginDTO.getBeId());
            gioOrderAttr.setCusCityCode_var(GioOrderOriginDTO.getLocation());
            gioOrderAttr.setCusAreaCode_var(GioOrderOriginDTO.getRegionID());
            gioOrderAttr.setCusProvince_var(provinceName);
            gioOrderAttr.setCusCity_var(cityName);
            gioOrderAttr.setCusArea_var(regionName);
            gioOrderAttr.setChannelPartnerPhone_var(extractAgentPhonesFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));
            gioOrderAttr.setCustomerManagerPhone_var(GioOrderOriginDTO.getCustMgPhone());
            gioOrderAttr.setCustomerManagerCode_var(GioOrderOriginDTO.getCreateOperCode());

            // 新增字段赋值
            // 订单归属信息 - 从provinceOrgName获取
            String[] orderLocations = null;
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getProvinceOrgName())) {
                orderLocations = GioOrderOriginDTO.getProvinceOrgName().split("-");
            }
            gioOrderAttr.setOrderProvince_var(orderLocations != null && orderLocations.length > 0 ? orderLocations[0] : null);
            gioOrderAttr.setOrderCity_var(orderLocations != null && orderLocations.length > 1 ? orderLocations[1] : null);
            gioOrderAttr.setOrderArea_var(orderLocations != null && orderLocations.length > 2 ? orderLocations[2] : null);


            // 业务人员信息 - 从优惠券信息中获取
            String busPersonJobNumbers = extractJobNumbers(GioOrderOriginDTO.getCouponInfoList());
            String busPersonPhoneNums = extractPhoneNumbers(GioOrderOriginDTO.getCouponInfoList());
            gioOrderAttr.setBusiPersonJobNumber_var(busPersonJobNumbers);
            gioOrderAttr.setBusiPersonPhoneNum_var(busPersonPhoneNums);
            gioOrderAttr.setDeductAmount(String.valueOf(GioOrderOriginDTO.getDeductPrice() == null ? null :
                    new BigDecimal(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getDeductPrice(), iotSm4Key, iotSm4Iv))
                            .divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP)
                            .doubleValue()));

            gioOrderAttr.setChannelPartnerCode_var(extractAgentNumbersFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));

            // 商品类型
            String spuType = SPUOfferingClassEnum.getDisplay(GioOrderOriginDTO.getSpuOfferingClass());
            gioOrderAttr.setPrductType(spuType);


            // 客户类型
            String orgType = "-";
            if (GioOrderOriginDTO.getBusinessCode().equals("SyncIndividualOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncOSExtOrderInfo")) {
                orgType = "个人客户";
            } else if (GioOrderOriginDTO.getBusinessCode().equals("SyncGrpOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncValetOrderInfo")) {
                orgType = "集团客户";
            }
            gioOrderAttr.setCustomerType_var(orgType);

            // 客户编码和客户名称
            gioOrderAttr.setCusCode_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setCusName_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustName(), iotSm4Key, iotSm4Iv));

            gioOrderH5OrderCreatedHDDTO.setAttrs(gioOrderAttr);
            gioOrderDTOs.add(gioOrderH5OrderCreatedHDDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=GioOrderPndingInvoice.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioOrderPndingInvoiceDTO gioOrderDTO : gioOrderDTOs) {

            jsonString.append(JSON.toJSONString(gioOrderDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }
    }

    @Override
    public void getPrudctOrderPnding(String startTime, String endTime) throws IOException, ParseException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<GioAtomDTO> gioAtomDTOs = new ArrayList<>();
        // 查询sku规格商品信息 修改销售目录价
        List<String> orderIds = new ArrayList<>();
        // orderIds.add("390190000001620015");
        // orderIds.add("100190000000673037");
        // orderIds.add("380100000000489006");

        // Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        // Order2cInfoExample.Criteria criteria = order2cInfoExample.createCriteria();
        // criteria.andOrderIdIn(orderIds).example();
        List<GioPrudctOrderPndingOriginDTO> GioOrderOriginDTOs = gioBurialPointMapperExt
                .getPrudctOrderPndingOriginList(startTime, endTime, 1);

        List<GioPrudctOrderPndingDTO> gioOrderDTOs = new ArrayList<>();

        for (GioPrudctOrderPndingOriginDTO GioOrderOriginDTO : GioOrderOriginDTOs) {


            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
            Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
            String provinceName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getBeId())) {
                provinceName = "471".equals(GioOrderOriginDTO.getBeId()) ? "内蒙古"
                        : (String) provinceCodeNameMap.get(GioOrderOriginDTO.getBeId());
            }
            String cityName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getLocation())) {
                cityName = (String) locationCodeNameMap.get(GioOrderOriginDTO.getLocation());
            }
            String regionName = "";

            if (StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                    && "10003329".equals(GioOrderOriginDTO.getRegionID())) {
                regionName = "綦江区";
            } else {
                regionName = StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                        ? (String) regionCodeNameMap.get(GioOrderOriginDTO.getRegionID())
                        : null;

            }
            // 原子结算金额(物联网收入) 排除A06H 和A13A的 再加上卡+x增值服务的
            Long atomAmountCollected = 0L;
            if (GioOrderOriginDTO.getSpuOfferingClass().equals("A06")) {

                if (!GioOrderOriginDTO.getSpuOfferingClass().equals("H")) {

                    atomAmountCollected = GioOrderOriginDTO.getAtomSalePrice() == null ? 0L : (long) (GioOrderOriginDTO.getAtomSalePrice() / 1000.00);
                }

            } else if (GioOrderOriginDTO.getSpuOfferingClass().equals("A13")) {

                if (!GioOrderOriginDTO.getSpuOfferingClass().equals("A")) {

                    atomAmountCollected = GioOrderOriginDTO.getAtomSalePrice() == null ? 0L : (long) (GioOrderOriginDTO.getAtomSalePrice() / 1000.00);
                }

            } else {

                atomAmountCollected = GioOrderOriginDTO.getAtomSalePrice() == null ? null : (long) (GioOrderOriginDTO.getAtomSalePrice() / 1000.00);

            }
            String userId = GioOrderOriginDTO.getCreateOperUserId() == null ? GioOrderOriginDTO.getCustUserId() == null ? null : GioOrderOriginDTO.getCustUserId() : GioOrderOriginDTO.getCreateOperUserId();
            userId = userId == null ? GioOrderOriginDTO.getCustCode() == null ? null : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv) : userId;
            GioPrudctOrderPndingDTO gioOrderH5OrderCreatedHDDTO = new GioPrudctOrderPndingDTO();
            gioOrderH5OrderCreatedHDDTO.setEvent("H5_prudctOrderPndingInvoice");
            gioOrderH5OrderCreatedHDDTO.setUserId(userId);
            gioOrderH5OrderCreatedHDDTO.setTimestamp(GioOrderOriginDTO.getValetOrderCompleteTime() == null ? null : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getValetOrderCompleteTime()));
            GioPrudctOrderPndingDTO.GioPrudctOrderPndingAttr gioOrderAttr = new GioPrudctOrderPndingDTO.GioPrudctOrderPndingAttr();
            gioOrderAttr.setOrderId_var(GioOrderOriginDTO.getOrderId());
            gioOrderAttr.setOrderType_var(
                    GioOrderOriginDTO.getOrderType() == null ? null : OrderTypeEnum.getDescByCode(GioOrderOriginDTO.getOrderType()));
            gioOrderAttr.setSkuCode_var(GioOrderOriginDTO.getSkuCode());
            gioOrderAttr.setSpu_version_var(GioOrderOriginDTO.getSpuVersion());
            gioOrderAttr.setSku_version_var(GioOrderOriginDTO.getSkuVersion());
            gioOrderAttr.setOffering_version_var(GioOrderOriginDTO.getOfferingVersion());
            gioOrderAttr.setQuantity_var(GioOrderOriginDTO.getQuantity());
            gioOrderAttr.setOfferingId_var(GioOrderOriginDTO.getOfferingId());
            gioOrderAttr.setProductPrice_var(GioOrderOriginDTO.getSkuPrice() == null ? null : String.valueOf(new BigDecimal(GioOrderOriginDTO.getSkuPrice() / 1000.00).setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setOfferingQuantity_var(String.valueOf(GioOrderOriginDTO.getQuantity() * GioOrderOriginDTO.getAtomQuantity()));
            gioOrderAttr.setOfferingsettlePrice_var(GioOrderOriginDTO.getAtomSettlePrice() == null ? null : String.valueOf(new BigDecimal(GioOrderOriginDTO.getAtomSettlePrice() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setOfferingPrice_var(GioOrderOriginDTO.getAtomSalePrice() == null ? null : String.valueOf(new BigDecimal(GioOrderOriginDTO.getAtomSalePrice() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setTotalPrice_var(
                    GioOrderOriginDTO.getQuantity() == null || GioOrderOriginDTO.getAtomQuantity() == null || GioOrderOriginDTO.getAtomPrice() == null ? null :
                            String.valueOf(
                                    GioOrderOriginDTO.getQuantity() * GioOrderOriginDTO.getAtomQuantity() * GioOrderOriginDTO.getAtomPrice() / 1000.00));

            gioOrderAttr.setOfferingtotalPrice_var(String.valueOf(atomAmountCollected));
            gioOrderAttr.setCusProvinceCode_var(GioOrderOriginDTO.getBeId());
            gioOrderAttr.setCusCityCode_var(GioOrderOriginDTO.getLocation());
            gioOrderAttr.setCusAreaCode_var(GioOrderOriginDTO.getRegionID());
            gioOrderAttr.setCusProvince_var(provinceName);
            gioOrderAttr.setCusCity_var(cityName);
            gioOrderAttr.setCusArea_var(regionName);
            gioOrderAttr.setChannelPartnerPhone_var(extractAgentPhonesFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));
            gioOrderAttr.setCustomerManagerPhone_var(GioOrderOriginDTO.getCustMgPhone());
            gioOrderAttr.setCustomerManagerCode_var(GioOrderOriginDTO.getCreateOperCode());

            // 新增字段赋值
            // 订单归属信息 - 从provinceOrgName获取
            String[] orderLocations = null;
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getProvinceOrgName())) {
                orderLocations = GioOrderOriginDTO.getProvinceOrgName().split("-");
            }
            gioOrderAttr.setOrderProvince_var(orderLocations != null && orderLocations.length > 0 ? orderLocations[0] : null);
            gioOrderAttr.setOrderCity_var(orderLocations != null && orderLocations.length > 1 ? orderLocations[1] : null);
            gioOrderAttr.setOrderArea_var(orderLocations != null && orderLocations.length > 2 ? orderLocations[2] : null);

            // 账目项信息 - 直接从getPrudctOrderPndingOriginList获取，避免单独查询数据库
            String chargeCode = GioOrderOriginDTO.getChargeCode();
            String chargeId = GioOrderOriginDTO.getChargeId();
            String chargeCodeName = null;
            if (StringUtils.isNotEmpty(chargeCode)) {
                String[] chargeCodeSplit = chargeCode.split("_");
                // 取最后一个作为计费代码名称
                chargeCodeName = chargeCodeSplit[chargeCodeSplit.length - 1];
            }
            gioOrderAttr.setCharge_code_var(chargeId != null ? chargeId : "-");
            gioOrderAttr.setCharge_code_name_var(chargeCodeName != null ? chargeCodeName : "-");

            // 设置offeringCode_var
            gioOrderAttr.setOfferingCode_var(GioOrderOriginDTO.getOfferingCode());

            // 业务人员信息 - 从优惠券信息中获取（参考getOrderCreated方法）
            String busPersonJobNumbers = extractJobNumbers(GioOrderOriginDTO.getCouponInfoList());
            String busPersonPhoneNums = extractPhoneNumbers(GioOrderOriginDTO.getCouponInfoList());
            gioOrderAttr.setBusiPersonJobNumber_var(busPersonJobNumbers);
            gioOrderAttr.setBusiPersonPhoneNum_var(busPersonPhoneNums);

            gioOrderAttr.setChannelPartnerCode_var(extractAgentNumbersFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));

            // 商品类型
            String spuType = SPUOfferingClassEnum.getDisplay(GioOrderOriginDTO.getSpuOfferingClass());

            gioOrderAttr.setPrductType(spuType);


            // 客户类型
            String orgType = "-";
            if (GioOrderOriginDTO.getBusinessCode().equals("SyncIndividualOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncOSExtOrderInfo")) {
                orgType = "个人客户";
            } else if (GioOrderOriginDTO.getBusinessCode().equals("SyncGrpOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncValetOrderInfo")) {
                orgType = "集团客户";
            }
            gioOrderAttr.setCustomerType_var(orgType);

            // 客户编码和客户名称
            gioOrderAttr.setCusCode_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setCusName_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustName(), iotSm4Key, iotSm4Iv));

            // 原子商品类型
            gioOrderAttr.setOffering_class_var(AtomOfferingClassEnum.getDescribe(GioOrderOriginDTO.getAtomOfferingClass()));

            // 领券码信息 - 从优惠券信息中获取
            String couponInfoStr = formatCouponInfoDTOToString(GioOrderOriginDTO.getCouponInfoList());
            gioOrderAttr.setCouponInfo_var(couponInfoStr);

            gioOrderH5OrderCreatedHDDTO.setAttrs(gioOrderAttr);
            gioOrderDTOs.add(gioOrderH5OrderCreatedHDDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=GioPrudctOrderPnding.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioPrudctOrderPndingDTO gioOrderDTO : gioOrderDTOs) {

            jsonString.append(JSON.toJSONString(gioOrderDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }
    }

    @Override
    public void getProductOrderCreate(String startTime, String endTime) throws IOException, ParseException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        List<GioAtomDTO> gioAtomDTOs = new ArrayList<>();
        // 查询sku规格商品信息 修改销售目录价
        List<String> orderIds = new ArrayList<>();
        // orderIds.add("390190000001620015");
        // orderIds.add("100190000000673037");
        // orderIds.add("380100000000489006");

        // Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        // Order2cInfoExample.Criteria criteria = order2cInfoExample.createCriteria();
        // criteria.andOrderIdIn(orderIds).example();
        List<GioPrudctOrderPndingOriginDTO> GioOrderOriginDTOs = gioBurialPointMapperExt
                .getPrudctOrderPndingOriginList(startTime, endTime, 0);

        List<GioProductOrderCreateDTO> gioOrderDTOs = new ArrayList<>();

        for (GioPrudctOrderPndingOriginDTO GioOrderOriginDTO : GioOrderOriginDTOs) {

            Long atomAmountCollected = 0L;
            if (GioOrderOriginDTO.getSpuOfferingClass().equals("A06")) {
//
                if (!GioOrderOriginDTO.getSpuOfferingClass().equals("H")) {

                    atomAmountCollected = GioOrderOriginDTO.getAtomSalePrice() == null ? null : (long) (GioOrderOriginDTO.getAtomSalePrice() / 1000.00);
                }

            } else if (GioOrderOriginDTO.getSpuOfferingClass().equals("A13")) {

                if (!GioOrderOriginDTO.getSpuOfferingClass().equals("A")) {

                    atomAmountCollected = GioOrderOriginDTO.getAtomSalePrice() == null ? null : (long) (GioOrderOriginDTO.getAtomSalePrice() / 1000.00);
                }

            } else {

                atomAmountCollected = GioOrderOriginDTO.getAtomSalePrice() == null ? null : (long) (GioOrderOriginDTO.getAtomSalePrice() / 1000.00);

            }

            String orgType = "-";
            if (GioOrderOriginDTO.getBusinessCode().equals("SyncIndividualOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncOSExtOrderInfo")) {
                orgType = "个人客户";
            } else if (GioOrderOriginDTO.getBusinessCode().equals("SyncGrpOrderInfo")
                    || GioOrderOriginDTO.getBusinessCode().equals("SyncValetOrderInfo")) {
                orgType = "集团客户";
            }
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
            Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
            String provinceName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getBeId())) {
                provinceName = "471".equals(GioOrderOriginDTO.getBeId()) ? "内蒙古"
                        : (String) provinceCodeNameMap.get(GioOrderOriginDTO.getBeId());
            }
            String cityName = "";
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getLocation())) {
                cityName = (String) locationCodeNameMap.get(GioOrderOriginDTO.getLocation());
            }
            String regionName = "";

            if (StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                    && "10003329".equals(GioOrderOriginDTO.getRegionID())) {
                regionName = "綦江区";
            } else {
                regionName = StringUtils.isNotEmpty(GioOrderOriginDTO.getRegionID())
                        ? (String) regionCodeNameMap.get(GioOrderOriginDTO.getRegionID())
                        : null;

            }
            String userId = GioOrderOriginDTO.getCreateOperUserId() == null ? GioOrderOriginDTO.getCustUserId() == null ? null : GioOrderOriginDTO.getCustUserId() : GioOrderOriginDTO.getCreateOperUserId();
            userId = userId == null ? GioOrderOriginDTO.getCustCode() == null ? null : IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv) : userId;
            GioProductOrderCreateDTO gioOrderH5OrderCreatedHDDTO = new GioProductOrderCreateDTO();
            gioOrderH5OrderCreatedHDDTO.setEvent("H5_ProductOrderCreate");
            gioOrderH5OrderCreatedHDDTO.setUserId(userId);
            gioOrderH5OrderCreatedHDDTO.setTimestamp(GioOrderOriginDTO.getCreateTime() == null ? null : DateTimeUtil.parseCreateTimeToTimestampSafely(GioOrderOriginDTO.getCreateTime()));
            GioProductOrderCreateDTO.GioProductOrderCreateAttr gioOrderAttr = new GioProductOrderCreateDTO.GioProductOrderCreateAttr();
            gioOrderAttr.setOrderId_var(GioOrderOriginDTO.getOrderId());
            gioOrderAttr.setOfferingId_var(GioOrderOriginDTO.getOfferingId());

            gioOrderAttr.setPlatformName_var("中后台");
            gioOrderAttr.setOrderType_var(
                    GioOrderOriginDTO.getOrderType() == null ? null : OrderTypeEnum.getDescByCode(GioOrderOriginDTO.getOrderType()));
            gioOrderAttr.setSpu_version_var(GioOrderOriginDTO.getSpuVersion());
            gioOrderAttr.setSku_version_var(GioOrderOriginDTO.getSkuVersion());
            gioOrderAttr.setOffering_version_var(GioOrderOriginDTO.getOfferingVersion());
            gioOrderAttr.setSkuCode_var(GioOrderOriginDTO.getSkuCode());
            gioOrderAttr.setProductPrice_var(GioOrderOriginDTO.getSkuPrice() == null ? null : String.valueOf(new BigDecimal(GioOrderOriginDTO.getSkuPrice() / 1000.00).setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setQuantity_var(String.valueOf(GioOrderOriginDTO.getQuantity()));
            gioOrderAttr.setOfferingQuantity_var(GioOrderOriginDTO.getAtomQuantity() == null || GioOrderOriginDTO.getQuantity() == null ? null : String.valueOf(GioOrderOriginDTO.getQuantity() * GioOrderOriginDTO.getAtomQuantity()));
            gioOrderAttr.setOfferingsettlePrice_var(GioOrderOriginDTO.getAtomSettlePrice() == null ? null : String.valueOf(new BigDecimal(GioOrderOriginDTO.getAtomSettlePrice() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));

            gioOrderAttr.setTotalPrice_var(
                    GioOrderOriginDTO.getQuantity() == null || GioOrderOriginDTO.getAtomQuantity() == null || GioOrderOriginDTO.getAtomPrice() == null ? null :
                            String.valueOf(
                                    GioOrderOriginDTO.getQuantity() * GioOrderOriginDTO.getAtomQuantity() * GioOrderOriginDTO.getAtomPrice() / 1000.00));

            gioOrderAttr.setOfferingtotalPrice_var(String.valueOf(atomAmountCollected));
            gioOrderAttr.setOfferingPrice_var(GioOrderOriginDTO.getAtomSalePrice() == null ? null : String.valueOf(new BigDecimal(GioOrderOriginDTO.getAtomSalePrice() / 1000.00)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()));
            gioOrderAttr.setCusProvinceCode_var(GioOrderOriginDTO.getBeId());
            gioOrderAttr.setCusCityCode_var(GioOrderOriginDTO.getLocation());
            gioOrderAttr.setCusAreaCode_var(GioOrderOriginDTO.getRegionID());
            gioOrderAttr.setCusProvince_var(provinceName);
            gioOrderAttr.setCusCity_var(cityName);
            gioOrderAttr.setCusArea_var(regionName);
            gioOrderAttr.setChannelPartnerPhone_var(extractAgentPhonesFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));
            gioOrderAttr.setCustomerManagerPhone_var(GioOrderOriginDTO.getCustMgPhone());
            gioOrderAttr.setCustomerManagerCode_var(GioOrderOriginDTO.getCreateOperCode());

            // 新增字段赋值
            // 订单归属信息 - 从provinceOrgName获取
            String[] orderLocations = null;
            if (StringUtils.isNotBlank(GioOrderOriginDTO.getProvinceOrgName())) {
                orderLocations = GioOrderOriginDTO.getProvinceOrgName().split("-");
            }
            gioOrderAttr.setOrderProvince_var(orderLocations != null && orderLocations.length > 0 ? orderLocations[0] : null);
            gioOrderAttr.setOrderCity_var(orderLocations != null && orderLocations.length > 1 ? orderLocations[1] : null);
            gioOrderAttr.setOrderArea_var(orderLocations != null && orderLocations.length > 2 ? orderLocations[2] : null);

            // 账目项信息 - 直接从getPrudctOrderPndingOriginList获取，避免单独查询数据库
            String chargeCode = GioOrderOriginDTO.getChargeCode();
            String chargeId = GioOrderOriginDTO.getChargeId();
            String chargeCodeName = null;
            if (StringUtils.isNotEmpty(chargeCode)) {
                String[] chargeCodeSplit = chargeCode.split("_");
                // 取最后一个作为计费代码名称
                chargeCodeName = chargeCodeSplit[chargeCodeSplit.length - 1];
            }
            gioOrderAttr.setCharge_code_var(chargeId != null ? chargeId : "-");
            gioOrderAttr.setCharge_code_name_var(chargeCodeName != null ? chargeCodeName : "-");

            // 设置offeringCode_var
            gioOrderAttr.setOfferingCode_var(GioOrderOriginDTO.getOfferingCode());

            // 业务人员信息 - 从优惠券信息中获取（参考getOrderCreated方法）
            String busPersonJobNumbers = extractJobNumbers(GioOrderOriginDTO.getCouponInfoList());
            String busPersonPhoneNums = extractPhoneNumbers(GioOrderOriginDTO.getCouponInfoList());
            gioOrderAttr.setBusiPersonJobNumber_var(busPersonJobNumbers);
            gioOrderAttr.setBusiPersonPhoneNum_var(busPersonPhoneNums);

            // 渠道伙伴编码 - 暂时设置为空，需要根据业务逻辑补充
            gioOrderAttr.setChannelPartnerCode_var(extractAgentNumbersFromAgentInfo(GioOrderOriginDTO.getAgentInfoList()));


            gioOrderAttr.setCustomerType_var(orgType);

            // 客户编码和客户名称
            gioOrderAttr.setCusCode_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustCode(), iotSm4Key, iotSm4Iv));
            gioOrderAttr.setCusName_var(IOTEncodeUtils.decryptSM4(GioOrderOriginDTO.getCustName(), iotSm4Key, iotSm4Iv));

            // 原子商品类型
            gioOrderAttr.setPrductType(SPUOfferingClassEnum.getDisplay(GioOrderOriginDTO.getSpuOfferingClass()));
            gioOrderAttr.setOffering_class_var(AtomOfferingClassEnum.getDescribe(GioOrderOriginDTO.getAtomOfferingClass()));
            // 领券码信息 - 从优惠券信息中获取
            String couponInfoStr = formatCouponInfoDTOToString(GioOrderOriginDTO.getCouponInfoList());
            gioOrderAttr.setCouponInfo_var(couponInfoStr);

            gioOrderH5OrderCreatedHDDTO.setAttrs(gioOrderAttr);
            gioOrderDTOs.add(gioOrderH5OrderCreatedHDDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=GioProductOrderCreateDTO.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioProductOrderCreateDTO gioOrderDTO : gioOrderDTOs) {

            jsonString.append(JSON.toJSONString(gioOrderDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }
    }

    @Override
    public void getH5ProductLaunch(String userId) throws IOException, ParseException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<GioAtomOriginAllDTO> atomOfferingInfos = gioBurialPointMapperExt.getAtomAllList();
        List<GioH5ProductLaunchDTO> gioOrderDTOs = new ArrayList<>();
        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        for (GioAtomOriginAllDTO gioAtomOriginAllDTO : atomOfferingInfos) {
            String province = "";
            String city = "";

            if (StringUtils.isNotBlank(gioAtomOriginAllDTO.getSkuProvinceCode())) {
                if ("471".equals(gioAtomOriginAllDTO.getSkuProvinceCode())) {
                    province = "内蒙古";

                } else {
                    province = (String) provinceCodeNameMap.get(gioAtomOriginAllDTO.getSkuProvinceCode());

                }
            }
            if (StringUtils.isNotBlank(gioAtomOriginAllDTO.getSkuCityCode())) {
                city = (String) locationCodeNameMap.get(gioAtomOriginAllDTO.getSkuCityCode());

            }

            GioH5ProductLaunchDTO gioOrderH5OrderCreatedHDDTO = new GioH5ProductLaunchDTO();
            gioOrderH5OrderCreatedHDDTO.setEvent("H5_productLaunch");
            gioOrderH5OrderCreatedHDDTO.setUserId(userId);
            gioOrderH5OrderCreatedHDDTO.setTimestamp(DateTimeUtil.toTimestampMs(gioAtomOriginAllDTO.getCreateTime()));
            GioH5ProductLaunchDTO.GioH5ProductLaunchDTOAttr gioOrderAttr = new GioH5ProductLaunchDTO.GioH5ProductLaunchDTOAttr();
            gioOrderAttr.setSkuCode_var(gioAtomOriginAllDTO.getSkuCode());
            gioOrderAttr.setOfferingId_var(gioAtomOriginAllDTO.getOfferingId());
            gioOrderAttr.setOfferingCode_var(gioAtomOriginAllDTO.getAtomOfferingCode());
            gioOrderAttr.setSku_province(province);
            gioOrderAttr.setSku_city(city);
            gioOrderAttr.setCusProvinceCode_var(gioAtomOriginAllDTO.getSkuProvinceCode());
            gioOrderAttr.setCusCityCode_var(gioAtomOriginAllDTO.getSkuCityCode());
            gioOrderH5OrderCreatedHDDTO.setAttrs(gioOrderAttr);
            gioOrderDTOs.add(gioOrderH5OrderCreatedHDDTO);

        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=H5_productLaunch.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioH5ProductLaunchDTO gioOrderDTO : gioOrderDTOs) {

            jsonString.append(JSON.toJSONString(gioOrderDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }
    }

    @Override
    public void sendH5OrderCreatedHDMsg(OrderInfoDTO orderInfo, Order2cInfo order2cInfo,
                                        List<Order2cAtomInfo> atomInfos) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            // H5_orderCreated_HD埋点
            try {
                // 计算金额

                // 计算订单结算金额(物联网收入) 排除A06H 和A13A的 再加上卡+x增值服务的
                AtomicLong orderSettlePrice = new AtomicLong(0L);
                if (orderInfo.getSpuOfferingInfo().getOfferingClass().equals("A06")) {
                    atomInfos.forEach(order2cAtomInfo -> {
                        if (order2cAtomInfo != null && !order2cAtomInfo.getAtomOfferingClass().equals("H")) {
                            orderSettlePrice
                                    .addAndGet(order2cAtomInfo.getSkuQuantity() * order2cAtomInfo.getAtomQuantity()
                                            * order2cAtomInfo.getAtomPrice());
                        }
                    });
                } else if (orderInfo.getSpuOfferingInfo().getOfferingClass().equals("A13")) {
                    atomInfos.forEach(order2cAtomInfo -> {
                        if (order2cAtomInfo != null && !order2cAtomInfo.getAtomOfferingClass().equals("A")) {
                            orderSettlePrice
                                    .addAndGet(order2cAtomInfo.getSkuQuantity() * order2cAtomInfo.getAtomQuantity()
                                            * order2cAtomInfo.getAtomPrice());
                        }
                    });
                } else {
                    atomInfos.forEach(order2cAtomInfo -> {

                        orderSettlePrice.addAndGet(order2cAtomInfo.getSkuQuantity() * order2cAtomInfo.getAtomQuantity()
                                * order2cAtomInfo.getAtomPrice());
                    });
                    // 获取卡+x增值服务价格
                    List<CardValueAddedInfo> cardValueAddedInfo = cardValueAddedInfoMapper
                            .selectByExample(new CardValueAddedInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    if (cardValueAddedInfo != null && cardValueAddedInfo.size() > 0) {
                        CardValueAddedInfo cardValueAddedInfo1 = cardValueAddedInfo.get(0);
                        orderSettlePrice.addAndGet(Long.valueOf(cardValueAddedInfo1.getExpensesPrice())
                                * Long.valueOf(cardValueAddedInfo1.getOrderQuantity())
                                * Long.valueOf(cardValueAddedInfo1.getExpensesTerm()));
                    }
                }
                // 获取订单归属省市区（参考sendOrderDimensionalityMsg方法）
                Order2cInfo order2cInfo1 = order2cInfoMapper.selectByPrimaryKey(orderInfo.getOrderId());
                String[] locations;
                if (orderInfo.getOrderOrgBizInfo() != null) {
                    if (orderInfo.getOrderOrgBizInfo().getOrgName() == null
                            && orderInfo.getOrderOrgBizInfo().getProvinceOrgName() == null) {
                        if (order2cInfo1 != null) {
                            locations = (order2cInfo1.getOrgName() == null || order2cInfo1.getOrgName().equals(""))
                                    ? order2cInfo1.getProvinceOrgName().split("-")
                                    : order2cInfo1.getOrgName().split("-");
                        } else {
                            locations = null;
                        }
                    } else {
                        locations = (orderInfo.getOrderOrgBizInfo().getOrgName() == null
                                || orderInfo.getOrderOrgBizInfo().getOrgName().equals(""))
                                ? orderInfo.getOrderOrgBizInfo().getProvinceOrgName().split("-")
                                : orderInfo.getOrderOrgBizInfo().getOrgName().split("-");
                    }
                } else {
                    if (order2cInfo.getOrgName() == null && order2cInfo.getProvinceOrgName() == null) {
                        if (order2cInfo1 != null) {
                            locations = (order2cInfo1.getOrgName() == null || order2cInfo1.getOrgName().equals(""))
                                    ? order2cInfo1.getProvinceOrgName().split("-")
                                    : order2cInfo1.getOrgName().split("-");
                        } else {
                            locations = null;
                        }
                    } else {
                        locations = (order2cInfo.getOrgName() == null || order2cInfo.getOrgName().equals(""))
                                ? order2cInfo.getProvinceOrgName().split("-")
                                : order2cInfo.getOrgName().split("-");
                    }
                }

                // 解析订单归属省市区
                String orderProvinceName = locations != null && locations.length > 0 ? locations[0] : null;
                String orderCityName = locations != null && locations.length > 1 ? locations[1] : null;
                String orderAreaName = locations != null && locations.length > 2 ? locations[2] : null;

                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
                Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
                String provinceName = "";
                if (StringUtils.isNotBlank(orderInfo.getCustInfo().getBeId())) {
                    provinceName = "471".equals(orderInfo.getCustInfo().getBeId()) ? "内蒙古"
                            : (String) provinceCodeNameMap.get(orderInfo.getCustInfo().getBeId());
                }
                String cityName = "";
                if (StringUtils.isNotBlank(orderInfo.getCustInfo().getLocation())) {
                    cityName = (String) locationCodeNameMap.get(orderInfo.getCustInfo().getLocation());
                }
                String regionName = "";

                if (StringUtils.isNotEmpty(orderInfo.getCustInfo().getRegionID())
                        && "10003329".equals(orderInfo.getCustInfo().getRegionID())) {
                    regionName = "綦江区";
                } else {
                    regionName = StringUtils.isNotEmpty(orderInfo.getCustInfo().getRegionID())
                            ? (String) regionCodeNameMap.get(orderInfo.getCustInfo().getRegionID())
                            : null;

                }
                String orgType = "-";
                if (orderInfo.getBusinessCode().equals("SyncIndividualOrderInfo")
                        || orderInfo.getBusinessCode().equals("SyncOSExtOrderInfo")) {
                    orgType = "个人客户";
                } else if (orderInfo.getBusinessCode().equals("SyncGrpOrderInfo")
                        || orderInfo.getBusinessCode().equals("SyncValetOrderInfo")) {
                    orgType = "集团客户";
                }
                String userId = orderInfo.getCreateOperUserID() == null ? orderInfo.getCustInfo().getCustUserID()
                        : orderInfo.getCreateOperUserID();
                userId = userId == null ? orderInfo.getCustInfo() != null && orderInfo.getCustInfo().getCustCode() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key, iotSm4Iv) : null : userId;
                // 渠道商

                String channelPartnerPhone = "";

                if (orderInfo.getAgentInfo() != null) {

                    channelPartnerPhone = orderInfo.getAgentInfo().size()==0 ? null : extractAgentPhonesFromAgentInfoDTO(orderInfo.getAgentInfo());

                } else {
                    List<Order2cAgentInfo> order2cAgentInfos = order2cAgentInfoMapper
                            .selectByExample(new Order2cAgentInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    if (CollectionUtils.isNotEmpty(order2cAgentInfos)) {

                        channelPartnerPhone = extractAgentPhonesFromAgentInfo(order2cAgentInfos);

                    }
                }
                String customerManagerPhone = "";
                String customerManagerCode = "";
                customerManagerCode = orderInfo.getEmployeeNum() == null ? order2cInfo.getEmployeeNum() : orderInfo.getEmployeeNum();
                customerManagerPhone = orderInfo.getCustomerManagerPhone() == null ? order2cInfo.getCustMgPhone() : IOTEncodeUtils.decryptSM4(orderInfo.getCustomerManagerPhone(), iotSm4Key, iotSm4Iv);

                // 商品类型
                String spuType = SPUOfferingClassEnum.getDisplay(orderInfo.getSpuOfferingInfo().getOfferingClass());



                // 事件行为消息体，anonymousId 和 loginUserId 参数，不能同时为空
                long eventTime = System.currentTimeMillis();
                if (orderInfo.getCreateTime() != null) {
                    Date parsedDate = DateTimeUtil.parseCreateTimeToDateSafely(orderInfo.getCreateTime());
                    if (parsedDate != null) {
                        eventTime = parsedDate.getTime();
                    } else {
                        log.warn("解析订单创建时间失败，使用当前时间: {}", orderInfo.getCreateTime());
                    }
                }
                List<CouponInfoDTO> decryptOrderInfoCouponList = decryptOrderInfoCouponList(orderInfo.getCouponInfo());
                // 领码信息
                String listCouponStr = formatCouponInfoDTOToString(decryptOrderInfoCouponList);
                GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
                        .eventTime(eventTime) // 使用订单创建时间 (选填)
                        .eventKey("H5_orderCreated_HD") // 埋点事件标识 (必填)
                        .loginUserId(userId) // 登陆用户ID (选填)
                        .addEventVariable("platformName_var", "中后台") // 事件属性 (选填)
                        .addEventVariable("orderId_var", orderInfo.getOrderId()) // 事件属性 (选填)
                        .addEventVariable("orderType_var", OrderTypeEnum.getDescByCode(orderInfo.getOrderType()))
                        .addEventVariable("payAmount_var",
                                new BigDecimal(Long.valueOf(
                                        IOTEncodeUtils.decryptSM4(orderInfo.getTotalPrice(), iotSm4Key, iotSm4Iv))
                                        / 1000.00)
                                        .setScale(2, RoundingMode.HALF_UP)
                                        .doubleValue())
                        .addEventVariable("amountDue_var",
                                new BigDecimal(orderSettlePrice.doubleValue() / 1000.00)
                                        .setScale(2, RoundingMode.HALF_UP)
                                        .doubleValue())

                        .addEventVariable("ordersource_var",
                                OrderingChannelNameEnum.fromCode(orderInfo.getOrderingChannelName()) == null
                                        ? orderInfo.getOrderingChannelName()
                                        : OrderingChannelNameEnum.fromCode(orderInfo.getOrderingChannelName())
                                        .getMessage())
                        .addEventVariable("customerType_var", orgType)
                        .addEventVariable("cusCode_var", IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key, iotSm4Iv))
                        .addEventVariable("cusName_var", IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustName(), iotSm4Key, iotSm4Iv))
                        .addEventVariable("cusProvince_var", provinceName)
                        .addEventVariable("cusCity_var", cityName)
                        .addEventVariable("cusArea_var", regionName)
                        .addEventVariable("cusProvinceCode_var", orderInfo.getCustInfo().getBeId())
                        .addEventVariable("cusCityCode_var", orderInfo.getCustInfo().getLocation())
                        .addEventVariable("cusAreaCode_var", orderInfo.getCustInfo().getRegionID())
                        .addEventVariable("channelPartnerPhone_var", channelPartnerPhone)
                        .addEventVariable("customerManagerPhone_var", customerManagerPhone)
                        .addEventVariable("customerManagerCode_var", customerManagerCode)
                        // 新增字段
                        .addEventVariable("orderProvince_var", orderProvinceName)
                        .addEventVariable("orderCity_var", orderCityName)
                        .addEventVariable("orderArea_var", orderAreaName)
                        .addEventVariable("busiPersonJobNumber_var", extractJobNumbersFromOrderInfo(decryptOrderInfoCouponList))
                        .addEventVariable("busiPersonPhoneNum_var", extractPhoneNumbersFromOrderInfo(decryptOrderInfoCouponList))
                        .addEventVariable("deductAmount", orderInfo.getDeductPrice() == null ? null :
                                        new BigDecimal(IOTEncodeUtils.decryptSM4(orderInfo.getDeductPrice(), iotSm4Key, iotSm4Iv))
                                                .divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP)
                                                .doubleValue()
                                ) // 抵扣金额（优惠券总金额）
                        .addEventVariable("channelPartnerCode_var", orderInfo.getAgentInfo() != null && orderInfo.getAgentInfo().size() > 0 ?extractAgentNumbersFromAgentInfoDTO(orderInfo.getAgentInfo())  : null)
                        .addEventVariable("prductType", spuType)
                        .addEventVariable("couponInfo_var", listCouponStr)
                        .build();
                // 上传事件行为消息到服务器
                growingIOConfig.getProject().send(eventMessage);
            } catch (Exception e) {
                log.error("H5_orderCreated_HD埋点失败,订单id:{},失败原因:{}", orderInfo.getOrderId(),e.getMessage());
            }
        });

    }

    @Override
    public void sendH5OrderPndingInvoice(OrderInfoDTO orderInfo, String time) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                List<Order2cInfo> order2cInfoList = order2cInfoMapper.selectByExample(
                        new Order2cInfoExample().createCriteria().andOrderIdEqualTo(orderInfo.getOrderId()).example());
                Order2cInfo order2cInfoQuery = order2cInfoList.get(0);
                // H5_orderPndingInvoice 代客下单的订单，状态变更为待出账时触发，订单待出账时间，用于收入计算
                List<Order2cAtomInfo> order2cAtomInfos = atomOrderInfoMapper.selectByExample(
                        new Order2cAtomInfoExample().createCriteria().andOrderIdEqualTo(orderInfo.getOrderId())
                                .example());
                // 计算订单结算金额(物联网收入) 排除A06H 和A13A的 再加上卡+x增值服务的
                AtomicLong orderSettlePrice = new AtomicLong(0L);
                if (order2cInfoQuery.getSpuOfferingClass().equals("A06")) {
                    order2cAtomInfos.forEach(order2cAtomInfoItem -> {
                        if (order2cAtomInfoItem != null && !order2cAtomInfoItem.getAtomOfferingClass().equals("H")) {
                            orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                                    * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                        }
                    });
                } else if (order2cInfoQuery.getSpuOfferingClass().equals("A13")) {
                    order2cAtomInfos.forEach(order2cAtomInfoItem -> {
                        if (order2cAtomInfoItem != null && !order2cAtomInfoItem.getAtomOfferingClass().equals("A")) {
                            orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                                    * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                        }
                    });
                } else {
                    order2cAtomInfos.forEach(order2cAtomInfoItem -> {

                        orderSettlePrice.addAndGet(order2cAtomInfoItem.getSkuQuantity()
                                * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice());
                    });
                    // 获取卡+x增值服务价格
                    List<CardValueAddedInfo> cardValueAddedInfo = cardValueAddedInfoMapper
                            .selectByExample(new CardValueAddedInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    if (cardValueAddedInfo != null && cardValueAddedInfo.size() > 0) {
                        CardValueAddedInfo cardValueAddedInfo1 = cardValueAddedInfo.get(0);
                        orderSettlePrice.addAndGet(Long.valueOf(cardValueAddedInfo1.getExpensesPrice())
                                * Long.valueOf(cardValueAddedInfo1.getOrderQuantity())
                                * Long.valueOf(cardValueAddedInfo1.getExpensesTerm()));
                    }
                }

                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
                Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
                String provinceName = "";
                if (orderInfo.getCustInfo() != null && StringUtils.isNotBlank(orderInfo.getCustInfo().getBeId())) {
                    provinceName = "471".equals(orderInfo.getCustInfo().getBeId()) ? "内蒙古"
                            : (String) provinceCodeNameMap.get(orderInfo.getCustInfo().getBeId());
                }
                String cityName = "";
                if (orderInfo.getCustInfo() != null && StringUtils.isNotBlank(orderInfo.getCustInfo().getLocation())) {
                    cityName = (String) locationCodeNameMap.get(orderInfo.getCustInfo().getLocation());
                }
                String regionName = "";

                if (orderInfo.getCustInfo() != null && StringUtils.isNotEmpty(orderInfo.getCustInfo().getRegionID())
                        && "10003329".equals(orderInfo.getCustInfo().getRegionID())) {
                    regionName = "綦江区";
                } else {
                    regionName = orderInfo.getCustInfo() != null && StringUtils.isNotEmpty(orderInfo.getCustInfo().getRegionID())
                            ? (String) regionCodeNameMap.get(orderInfo.getCustInfo().getRegionID())
                            : null;

                }


                // 获取订单归属省市区（参考sendOrderDimensionalityMsg方法）
                Order2cInfo order2cInfo1 = order2cInfoMapper.selectByPrimaryKey(orderInfo.getOrderId());
                String[] locations;
                if (orderInfo.getOrderOrgBizInfo() != null) {
                    if (orderInfo.getOrderOrgBizInfo().getOrgName() == null
                            && orderInfo.getOrderOrgBizInfo().getProvinceOrgName() == null) {
                        if (order2cInfo1 != null) {
                            locations = (order2cInfo1.getOrgName() == null || order2cInfo1.getOrgName().equals(""))
                                    ? order2cInfo1.getProvinceOrgName().split("-")
                                    : order2cInfo1.getOrgName().split("-");
                        } else {
                            locations = null;
                        }
                    } else {
                        locations = (orderInfo.getOrderOrgBizInfo().getOrgName() == null
                                || orderInfo.getOrderOrgBizInfo().getOrgName().equals(""))
                                ? orderInfo.getOrderOrgBizInfo().getProvinceOrgName().split("-")
                                : orderInfo.getOrderOrgBizInfo().getOrgName().split("-");
                    }
                } else {
                    if (order2cInfoQuery.getOrgName() == null && order2cInfoQuery.getProvinceOrgName() == null) {
                        if (order2cInfo1 != null) {
                            locations = (order2cInfo1.getOrgName() == null || order2cInfo1.getOrgName().equals(""))
                                    ? order2cInfo1.getProvinceOrgName().split("-")
                                    : order2cInfo1.getOrgName().split("-");
                        } else {
                            locations = null;
                        }
                    } else {
                        locations = (order2cInfoQuery.getOrgName() == null || order2cInfoQuery.getOrgName().equals(""))
                                ? order2cInfoQuery.getProvinceOrgName().split("-")
                                : order2cInfoQuery.getOrgName().split("-");
                    }
                }

                // 解析订单归属省市区
                String orderProvinceName = locations != null && locations.length > 0 ? locations[0] : null;
                String orderCityName = locations != null && locations.length > 1 ? locations[1] : null;
                String orderAreaName = locations != null && locations.length > 2 ? locations[2] : null;

                String channelPartnerPhone = "";

                if (orderInfo.getAgentInfo() != null) {

                    channelPartnerPhone = orderInfo.getAgentInfo().size() == 0 ? null :extractAgentPhonesFromAgentInfoDTO(orderInfo.getAgentInfo()) ;

                } else {
                    List<Order2cAgentInfo> order2cAgentInfos = order2cAgentInfoMapper
                            .selectByExample(new Order2cAgentInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    if (CollectionUtils.isNotEmpty(order2cAgentInfos)) {

                        channelPartnerPhone = extractAgentPhonesFromAgentInfo(order2cAgentInfos);

                    }
                }
                String customerManagerPhone = "";
                String customerManagerCode = "";
                customerManagerCode = orderInfo.getEmployeeNum() == null ? order2cInfoQuery.getEmployeeNum() : orderInfo.getEmployeeNum();
                customerManagerPhone = orderInfo.getCustomerManagerPhone() == null ? order2cInfoQuery.getCustMgPhone() : IOTEncodeUtils.decryptSM4(orderInfo.getCustomerManagerPhone(), iotSm4Key, iotSm4Iv);

                // 商品类型
                String spuType = SPUOfferingClassEnum.getDisplay(order2cInfoQuery.getSpuOfferingClass());

                // 客户类型
                String orgType = "-";
                if (orderInfo.getBusinessCode().equals("SyncIndividualOrderInfo")
                        || orderInfo.getBusinessCode().equals("SyncOSExtOrderInfo")) {
                    orgType = "个人客户";
                } else if (orderInfo.getBusinessCode().equals("SyncGrpOrderInfo")
                        || orderInfo.getBusinessCode().equals("SyncValetOrderInfo")) {
                    orgType = "集团客户";
                }

                String userId = orderInfo.getCreateOperUserID() == null ? orderInfo.getCustInfo() != null && orderInfo.getCustInfo().getCustUserID() != null ? orderInfo.getCustInfo().getCustUserID() : null
                        : orderInfo.getCreateOperUserID();
                userId = userId == null ? orderInfo.getCustInfo() != null && orderInfo.getCustInfo().getCustCode() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key, iotSm4Iv) : null : userId;
                // 事件行为消息体，anonymousId 和 loginUserId 参数，不能同时为空
                long eventTime = System.currentTimeMillis();
                if (orderInfo.getCreateTime() != null) {
                    Date parsedDate = DateTimeUtil.parseCreateTimeToDateSafely(orderInfo.getCreateTime());
                    if (parsedDate != null) {
                        eventTime = parsedDate.getTime();
                    } else {
                        log.warn("解析订单创建时间失败，使用当前时间: {}", orderInfo.getCreateTime());
                    }
                }
                // 优化：优先从orderInfo获取优惠券信息，没有再从数据库获取
                List<CouponParam> listCoupon = new ArrayList<>();
                List<CouponInfoDTO> decryptOrderInfoCouponList = null;

                // 先尝试从orderInfo获取优惠券信息
                if (orderInfo.getCouponInfo() != null && !orderInfo.getCouponInfo().isEmpty()) {
                    log.debug("从orderInfo获取优惠券信息，共{}条记录", orderInfo.getCouponInfo().size());
                    decryptOrderInfoCouponList = decryptOrderInfoCouponList(orderInfo.getCouponInfo());
                    listCoupon = convertCouponInfoDTOToCouponParams(decryptOrderInfoCouponList);
                } else {
                    log.debug("orderInfo中无优惠券信息，从数据库查询");
                    // 如果orderInfo中没有优惠券信息，则从数据库查询
                    List<CouponInfo> couponInfoList = couponInfoMapper.selectByExample(
                            new CouponInfoExample().createCriteria().andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    listCoupon = convertCouponInfoToCouponParams(couponInfoList);
                    // 将数据库查询的CouponInfo转换为CouponInfoDTO格式，保持后续逻辑一致
                    decryptOrderInfoCouponList = convertCouponInfoToCouponInfoDTO(couponInfoList);
                }
                GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
                        .eventTime(eventTime) // 使用订单创建时间 (选填)
                        .eventKey("H5_orderPndingInvoice") // 埋点事件标识 (必填)
                        .loginUserId(userId)
                        .addEventVariable("platformName_var", "中后台")
                        .addEventVariable("orderId_var", orderInfo.getOrderId())
                        .addEventVariable("orderType_var", orderInfo.getOrderType() == null ? null : OrderTypeEnum.getDescByCode(orderInfo.getOrderType()))
                        .addEventVariable("payAmount_var",
                                orderInfo.getTotalPrice() == null ? null :
                                        new BigDecimal(Long.valueOf(
                                                IOTEncodeUtils.decryptSM4(orderInfo.getTotalPrice(), iotSm4Key, iotSm4Iv))
                                                / 1000.00)
                                                .setScale(2, RoundingMode.HALF_UP)
                                                .doubleValue())
                        .addEventVariable("amountDue_var",
                                new BigDecimal(orderSettlePrice.doubleValue() / 1000.00)
                                        .setScale(2, RoundingMode.HALF_UP)
                                        .doubleValue())
                        .addEventVariable("ordersource_var",
                                orderInfo.getOrderingChannelName() == null ? null : (OrderingChannelNameEnum.fromCode(orderInfo.getOrderingChannelName()) == null
                                        ? orderInfo.getOrderingChannelName()
                                        : OrderingChannelNameEnum.fromCode(orderInfo.getOrderingChannelName())
                                        .getMessage()))
                        .addEventVariable("orderTime_var", orderInfo.getCreateTime().toString())
                        // .addEventVariable("shareCode_var", orderInfo.getDistributorInfo() == null ?
                        // "-" : orderInfo.getDistributorInfo().get(0).getDistributorShareCode())
                        .addEventVariable("couponInfo_var", listCoupon)
                        .addEventVariable("cusProvinceCode_var", orderInfo.getCustInfo() == null ? null : orderInfo.getCustInfo().getBeId())
                        .addEventVariable("cusCityCode_var", orderInfo.getCustInfo() == null ? null : orderInfo.getCustInfo().getLocation())
                        .addEventVariable("cusAreaCode_var", orderInfo.getCustInfo() == null ? null : orderInfo.getCustInfo().getRegionID())
                        .addEventVariable("cusProvince_var", provinceName)
                        .addEventVariable("cusCity_var", cityName)
                        .addEventVariable("cusArea_var", regionName)
                        .addEventVariable("channelPartnerPhone_var", channelPartnerPhone)
                        .addEventVariable("customerManagerPhone_var", customerManagerPhone)
                        .addEventVariable("customerManagerCode_var", customerManagerCode)
                        // 新增字段
                        .addEventVariable("orderProvince_var", orderProvinceName)
                        .addEventVariable("orderCity_var", orderCityName)
                        .addEventVariable("orderArea_var", orderAreaName)
                        .addEventVariable("busiPersonJobNumber_var", extractJobNumbersFromOrderInfo(decryptOrderInfoCouponList))
                        .addEventVariable("busiPersonPhoneNum_var", extractPhoneNumbersFromOrderInfo(decryptOrderInfoCouponList))
                        .addEventVariable("deductAmount", orderInfo.getDeductPrice() == null ? null :
                                        new BigDecimal(IOTEncodeUtils.decryptSM4(orderInfo.getDeductPrice(), iotSm4Key, iotSm4Iv))
                                                .divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP)
                                                .doubleValue()
                                )
                        .addEventVariable("channelPartnerCode_var", orderInfo.getAgentInfo() != null && orderInfo.getAgentInfo().size() > 0 ? extractAgentNumbersFromAgentInfoDTO(orderInfo.getAgentInfo()) : null)
                        .addEventVariable("prductType",spuType )
                        .addEventVariable("customerType_var", orgType)
                        .addEventVariable("cusCode_var", orderInfo.getCustInfo() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key, iotSm4Iv) : null)
                        .addEventVariable("cusName_var", orderInfo.getCustInfo() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustName(), iotSm4Key, iotSm4Iv) : null)
                        .build();

                growingIOConfig.getProject().send(eventMessage);

                for (Order2cAtomInfo order2cAtomInfoItem : order2cAtomInfos) {
                    Long atomAmountCollected = 0L;

                    if (order2cInfoQuery.getSpuOfferingClass().equals("A06")) {

                        if (order2cAtomInfoItem != null && !order2cAtomInfoItem.getAtomOfferingClass().equals("H")) {

                            atomAmountCollected = order2cAtomInfoItem.getSkuQuantity()
                                    * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice();
                        }

                    } else if (order2cInfoQuery.getSpuOfferingClass().equals("A13")) {

                        if (order2cAtomInfoItem != null && !order2cAtomInfoItem.getAtomOfferingClass().equals("A")) {

                            atomAmountCollected = order2cAtomInfoItem.getSkuQuantity()
                                    * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice();
                        }

                    } else {

                        atomAmountCollected = order2cAtomInfoItem.getSkuQuantity()
                                * order2cAtomInfoItem.getAtomQuantity()
                                * order2cAtomInfoItem.getAtomPrice();

                    }
                    // 获取卡+x增值服务价格
                    List<CardValueAddedInfo> cardValueAddedInfo = cardValueAddedInfoMapper
                            .selectByExample(new CardValueAddedInfoExample().createCriteria()
                                    .andOrderIdEqualTo(orderInfo.getOrderId()).example());
                    if (cardValueAddedInfo != null && cardValueAddedInfo.size() > 0) {
                        CardValueAddedInfo cardValueAddedInfo1 = cardValueAddedInfo.get(0);
                        atomAmountCollected = atomAmountCollected + Long.valueOf(cardValueAddedInfo1.getExpensesPrice())
                                * Long.valueOf(cardValueAddedInfo1.getOrderQuantity())
                                * Long.valueOf(cardValueAddedInfo1.getExpensesTerm());
                    }
                    // 获取原子商品
                    List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper
                            .selectByExample(new AtomOfferingInfoExample().createCriteria()
                                    .andSpuCodeEqualTo(order2cAtomInfoItem.getSpuOfferingCode())
                                    .andSkuCodeEqualTo(order2cAtomInfoItem.getSkuOfferingCode())
                                    .andOfferingCodeEqualTo(order2cAtomInfoItem.getAtomOfferingCode())
                                    .example());
                    AtomOfferingInfo atomOfferingInfo = null;
                    if (!CollectionUtils.isEmpty(atomOfferingInfos)) {
                        atomOfferingInfo = atomOfferingInfos.get(0);
                    }

                    // H5_prudctOrderPndingInvoice 代客下单的订单，状态变更为待出账时触发，订单待出账时间，用于原子商品收入计算
                    GioCdpEventMessage eventMessage1 = new GioCdpEventMessage.Builder()
                            .eventTime(DateTimeUtil.parseCreateTimeToDateSafely(time) == null ? System.currentTimeMillis() : DateTimeUtil.toTimestampMs(DateTimeUtil.parseCreateTimeToDateSafely(time))) // 默认为系统当前时间 (选填)
                            .eventKey("H5_prudctOrderPndingInvoice") // 埋点事件标识 (必填)
                            .loginUserId(userId) // 登陆用户ID (选填)
                            .addEventVariable("orderId_var", orderInfo.getOrderId())
                            .addEventVariable("orderType_var", orderInfo.getOrderType() == null ? null : OrderTypeEnum.getDescByCode(orderInfo.getOrderType()))
                            .addEventVariable("skuCode_var", order2cAtomInfoItem.getSkuOfferingCode())
                            .addEventVariable("quantity_var", Math.toIntExact(order2cAtomInfoItem.getSkuQuantity()))
                            .addEventVariable("spu_version_var", order2cAtomInfoItem.getSpuOfferingVersion())
                            .addEventVariable("sku_version_var", order2cAtomInfoItem.getSkuOfferingVersion())
                            .addEventVariable("offering_version_var", order2cAtomInfoItem.getAtomOfferingVersion())
                            .addEventVariable("offeringId_var", atomOfferingInfo.getId())
                            .addEventVariable("offeringCode_var", order2cAtomInfoItem.getAtomOfferingCode())
                            .addEventVariable("productPrice_var", order2cAtomInfoItem.getSkuPrice() == null ? null :
                                    new BigDecimal(order2cAtomInfoItem.getSkuPrice() / 1000.00)
                                            .setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue())
                            .addEventVariable("offeringQuantity_var", order2cAtomInfoItem.getAtomQuantity() == null || order2cAtomInfoItem.getSkuQuantity() == null ? null :
                                    Math.toIntExact(order2cAtomInfoItem.getSkuQuantity() * order2cAtomInfoItem.getAtomQuantity()))
                            .addEventVariable("offeringsettlePrice_var", order2cAtomInfoItem.getAtomSettlePrice() == null ? null :
                                    new BigDecimal(order2cAtomInfoItem.getAtomSettlePrice() / 1000.00)
                                            .setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue())
                            .addEventVariable("offeringPrice_var", order2cAtomInfoItem.getAtomPrice() == null ? null :
                                    new BigDecimal(order2cAtomInfoItem.getAtomPrice() / 1000.00)
                                            .setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue())
                            .addEventVariable("totalPrice_var",
                                    order2cAtomInfoItem.getSkuQuantity() == null || order2cAtomInfoItem.getAtomQuantity() == null || order2cAtomInfoItem.getAtomPrice() == null ? null :
                                            String.valueOf(
                                                    order2cAtomInfoItem.getSkuQuantity() * order2cAtomInfoItem.getAtomQuantity() * order2cAtomInfoItem.getAtomPrice() / 1000.00))
                            .addEventVariable("offeringtotalPrice_var", new BigDecimal(atomAmountCollected / 1000.00)
                                    .setScale(2, RoundingMode.HALF_UP)
                                    .doubleValue())
                            .addEventVariable("cusProvinceCode_var", orderInfo.getCustInfo() == null ? null : orderInfo.getCustInfo().getBeId())
                            .addEventVariable("cusCityCode_var", orderInfo.getCustInfo() == null ? null : orderInfo.getCustInfo().getLocation())
                            .addEventVariable("cusAreaCode_var", orderInfo.getCustInfo() == null ? null : orderInfo.getCustInfo().getRegionID())
                            .addEventVariable("cusProvince_var", provinceName)
                            .addEventVariable("cusCity_var", cityName)
                            .addEventVariable("cusArea_var", regionName)
                            .addEventVariable("channelPartnerPhone_var", channelPartnerPhone)
                            .addEventVariable("customerManagerPhone_var", customerManagerPhone)
                            .addEventVariable("customerManagerCode_var", customerManagerCode)
                            // 新增字段
                            .addEventVariable("orderProvince_var", orderProvinceName)
                            .addEventVariable("orderCity_var", orderCityName)
                            .addEventVariable("orderArea_var", orderAreaName )
                            .addEventVariable("charge_code_var", atomOfferingInfo != null ? atomOfferingInfo.getChargeId() : null)
                            .addEventVariable("charge_code_name_var", atomOfferingInfo != null && atomOfferingInfo.getChargeCode() != null && atomOfferingInfo.getChargeCode().contains("_") ?
                                    atomOfferingInfo.getChargeCode().split("_")[atomOfferingInfo.getChargeCode().split("_").length - 1] : atomOfferingInfo != null ? atomOfferingInfo.getChargeCode() : null)
                            .addEventVariable("busiPersonJobNumber_var", extractJobNumbersFromOrderInfo(decryptOrderInfoCouponList ))
                            .addEventVariable("busiPersonPhoneNum_var", extractPhoneNumbersFromOrderInfo(decryptOrderInfoCouponList ))
                            .addEventVariable("channelPartnerCode_var", orderInfo.getAgentInfo() != null && orderInfo.getAgentInfo().size() > 0 ? extractAgentPhonesFromAgentInfoDTO(orderInfo.getAgentInfo())  : null)
                            .addEventVariable("prductType", spuType)
                            .addEventVariable("customerType_var", orgType)
                            .addEventVariable("cusCode_var", orderInfo.getCustInfo() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustCode(), iotSm4Key, iotSm4Iv) : null)
                            .addEventVariable("cusName_var", orderInfo.getCustInfo() != null ? IOTEncodeUtils.decryptSM4(orderInfo.getCustInfo().getCustName(), iotSm4Key, iotSm4Iv) : null)
                            .addEventVariable("offering_class_var",AtomOfferingClassEnum.getDescribe(order2cAtomInfoItem.getAtomOfferingClass() ))
                            .addEventVariable("couponInfo_var", formatCouponInfoDTOToString(decryptOrderInfoCouponList ))
                            .build();
                    growingIOConfig.getProject().send(eventMessage1);
                }
            } catch (Exception e) {
                log.error("订单待出帐埋点失败,订单id:{},失败原因:{}", orderInfo.getOrderId(),e);
            }

        });

    }

    @Override
    public void sendUserMsg(UserMiniProgram userMiniProgram) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                String distributorReferralcode = null;
                if (Objects.equals(userMiniProgram.getRoleType(), "1")
                        || Objects.equals(userMiniProgram.getRoleType(), "2")) {
                    distributorReferralcode = userMiniProgram.getCode();
                }
                String roleLevel = null;
                if (userMiniProgram.getRoleType() == "1") {
                    roleLevel = "一级";
                } else if (userMiniProgram.getRoleType() == "2") {
                    roleLevel = "二级";
                }
                // 获取客户经理清洗数据 shopManagerInfo
                List<ShopManagerInfo> shopManagerInfos = userMiniProgram.getPhone() == null ? null : shopManagerInfoMapper
                        .selectByExample(new ShopManagerInfoExample().createCriteria()
                                .andCreateOperPhoneEqualTo(userMiniProgram.getPhone()).example());
                ShopManagerInfo shopManagerInfo = null;
                if (CollectionUtils.isNotEmpty(shopManagerInfos)) {
                    shopManagerInfo = shopManagerInfos.get(0);
                }
                // 获取其他用户清洗数据 shopCustomerInfo
                List<ShopCustomerInfo> customerManagerInfos = userMiniProgram.getPhone() == null ? null : shopCustomerInfoMapper
                        .selectByExample(new ShopCustomerInfoExample().createCriteria()
                                .andCustIdEqualTo(userMiniProgram.getPhone()).example());
                ShopCustomerInfo customerManagerInfo = null;
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(customerManagerInfos)) {
                    customerManagerInfo = customerManagerInfos.get(0);
                }
                // 事件行为消息体，anonymousId 和 loginUserId 参数，不能同时为空
                GioCdpUserMessage msg = new GioCdpUserMessage.Builder()
                        .time(System.currentTimeMillis()) // 默认为系统当前时间 (选填)
                        .loginUserId(userMiniProgram.getUserId()) // 登录用户ID的 (选填)
                        .addUserVariable("wlw_UserID", userMiniProgram.getUserId()) // 用户ID
                        .addUserVariable("wlw_ustCode", userMiniProgram.getCode()) // ustCode
                        .addUserVariable("wlw_custID", userMiniProgram.getPhone()) // custID
                        .addUserVariable("wlw_cusName", userMiniProgram.getName()) // 客户名称
                        .addUserVariable("wlw_cusRegistDate", userMiniProgram.getCreateTime() == null ? null :
                                DateTimeUtil.formatDate(userMiniProgram.getCreateTime(), DateTimeUtil.STANDARD_DAY)) // 客户注册日期
                        .addUserVariable("wlw_roleType", RoleTypeMiniEnum.getName(userMiniProgram.getRoleType())) // 角色类型
                        .addUserVariable("wlw_roleLevel", roleLevel == null ? null : roleLevel) // 角色级别
                        .addUserVariable("wlw_UserStauts", userMiniProgram.getStatus() != null ? null : ManagerStatusEnum.getName(userMiniProgram.getStatus())) // 用户状态
                        .addUserVariable("wlw_cusProvince", userMiniProgram.getProvinceName()) // 客户省份
                        .addUserVariable("wlw_cusCity", userMiniProgram.getCityName()) // 客户城市
                        .addUserVariable("wlw_cusRegion", userMiniProgram.getRegionName()) // 客户区域
                        .addUserVariable("wlw_cusProvinceCode", userMiniProgram.getBeId()) // 客户省份编码
                        .addUserVariable("wlw_cusCityCode", userMiniProgram.getLocation()) // 客户城市编码
                        .addUserVariable("wlw_cusRegionCode", userMiniProgram.getRegionId()) // 客户区域编码
                        .addUserVariable("wlw_GridCity",
                                shopManagerInfo == null
                                        ? (customerManagerInfo == null ? null : customerManagerInfo.getProvinceName())
                                        : shopManagerInfo.getCityName()) // 网格城市
                        .addUserVariable("wlw_GridRegion",
                                shopManagerInfo == null
                                        ? (customerManagerInfo == null ? null : customerManagerInfo.getRegionName())
                                        : shopManagerInfo.getRegionName()) // 网格区域
                        .addUserVariable("wlw_GridName",
                                shopManagerInfo == null
                                        ? (customerManagerInfo == null ? null : customerManagerInfo.getGriddingName())
                                        : shopManagerInfo.getGriddingName()) // 网格名称
                        // .addUserVariable("wlw_distributorMrglnf", "-") // 分销商利润率
                        // .addUserVariable("wlw_distributorMrgUserid", "-") // 分销商经理用户ID
                        // .addUserVariable("wlw_distributorMrgCode", "-") // 分销商经理代码
                        // .addUserVariable("wlw_distributorL1Name", "-") // 一级分销商名称
                        // .addUserVariable("wlw_distributorL2Name", "-") // 二级分销商名称
                        .addUserVariable("wlw_distributorReferralcode", distributorReferralcode) // 分销商推荐码
                        .build();
                // 上传事件行为消息到服务器
                growingIOConfig.getProject().send(msg);
            } catch (Exception e) {
                log.error("用户属性埋点失败,用户id:{},失败原因:{}", userMiniProgram.getUserId() ,e.getMessage());
            }
        });

    }

    @Override
    public void getH5Userregistration(String userId) throws IOException, ParseException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<UserMiniProgram> userMiniPrograms = gioBurialPointMapperExt.getUserIdList();
        List<GioUserH5UserregistrationDTO> userMiniProgramDTOList = new ArrayList<>();
        for (UserMiniProgram userMiniProgram : userMiniPrograms) {
            GioUserH5UserregistrationDTO userMiniProgramDTO = new GioUserH5UserregistrationDTO();
            userMiniProgramDTO.setEvent("H5_userregistration");
            userMiniProgramDTO.setUserId(userMiniProgram.getUserId());
            userMiniProgramDTO.setTimestamp(DateTimeUtil.toTimestampMs(userMiniProgram.getCreateTime()));


            userMiniProgramDTOList.add(userMiniProgramDTO);
        }
        // 设置响应头以指示浏览器下载文件
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=H5_userregistration.json");
        StringBuffer jsonString = new StringBuffer();
        for (GioUserH5UserregistrationDTO gioUserH5UserregistrationDTO : userMiniProgramDTOList) {

            jsonString.append(JSON.toJSONString(gioUserH5UserregistrationDTO)).append("\n");
        }

        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(jsonString.toString().getBytes("UTF-8"));
        }

    }

    @Override
    public void sendH5Userregistration(UserMiniProgram userMiniProgram) {
        ThreadExecutorConfig.executorService.execute(() -> {
                    try {

                        Date date = userMiniProgram.getCreateTime();
                        if (userMiniProgram.getCreateTime() == null) {
                            UserMiniProgram userMiniProgram1 = userMiniProgramMapper.selectByPrimaryKey(userMiniProgram.getId());
                            date = userMiniProgram1.getCreateTime();
                        }
                        // 事件行为消息体，anonymousId 和 loginUserId 参数，不能同时为空
                        GioCdpEventMessage eventMessage = new GioCdpEventMessage.Builder()
//                        .eventTime(userMiniProgram.getCreateTime()==null?DateTimeUtil.toTimestamp(new Date()): DateTimeUtil.toTimestamp(userMiniProgram.getCreateTime())) // 默认为系统当前时间 (选填)
                                .eventTime(DateTimeUtil.toTimestampMs(date))
                                .eventKey("H5_userregistration") // 埋点事件标识 (必填)
                                .loginUserId(userMiniProgram.getUserId())
                                .build();

                        growingIOConfig.getProject().send(eventMessage);
                    } catch (Exception e) {
                        log.info("用户注册埋点失败,用户id:{},失败原因:{}", userMiniProgram.getUserId(),e.getMessage());
                    }
                }
        );

    }

    /**
     * 监听 rise_order_2c_grid 表的 Kafka 消息
     * 处理网格数据变化，发送到订单维度表
     */
    @KafkaListener(topics = {"supply_chain.rise_order_2c_grid"})
    public void listenerRiseOrder2cGrid(ConsumerRecord<String, byte[]> record) {
        try {
            log.info("收到 rise_order_2c_grid 表 Kafka 消息: {}", new String(record.value()));

            // 解析 Kafka 消息
            JSONObject jsonObject = JSON.parseObject(new String(record.value()));
            JSONObject after = jsonObject.getJSONObject("after");

            if (after == null) {
                // 删除事件，暂不处理
                log.info("rise_order_2c_grid 删除事件，跳过处理");
                return;
            }

            // 提取网格数据字段
            String orderId = after.getString("order_id");
            String gridProvince = after.getString("province");
            String gridCity = after.getString("city");
            String gridDistrict = after.getString("district");
            String gridName = after.getString("grid_name");
            if (orderId == null || orderId.trim().isEmpty()) {
                log.warn("rise_order_2c_grid 消息中订单ID为空，跳过处理");
                return;
            }

            log.info("处理网格数据 - 订单ID: {}, 省份: {}, 城市: {}, 区域: {}, 网格名称: {}",
                    orderId, gridProvince, gridCity, gridDistrict, gridName);

            // 发送到订单维度表
            sendOrderDimensionalityMsgFromGrid(orderId, gridProvince, gridCity, gridDistrict, gridName);

        } catch (Exception e) {
            log.error("处理 rise_order_2c_grid Kafka 消息失败: {}", new String(record.value()), e);
        }
    }

    @Override
    public void sendSkuMsg(SpuOfferingInfo spuOfferingInfo, List<SpuSaleLabelHistory> spuSaleLabelHistoryList) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                // 使用 MapperExt 一次性查询所有相关的 SKU 信息
                List<GioSkuOriginDTO> gioSkuOriginDTOs = gioBurialPointMapperExt.getSkuListBySpuCode(spuOfferingInfo.getOfferingCode());

                if (CollectionUtils.isEmpty(gioSkuOriginDTOs)) {
                    log.warn("未找到 SPU {} 对应的 SKU 信息", spuOfferingInfo.getOfferingCode());
                    return;
                }

                // 预处理 SPU 信息，优先使用传入的 spuOfferingInfo 参数
                String spuName = spuOfferingInfo.getOfferingName();
                String spuCode = spuOfferingInfo.getOfferingCode();
                String spuCreateTime = spuOfferingInfo.getCreateTime() == null ? null :
                        DateTimeUtil.formatDate(spuOfferingInfo.getCreateTime(), DateTimeUtil.STANDARD_DAY);
                String spuSalesType = spuOfferingInfo.getOfferingStatus() == null ? null :
                        OfferingStatusEnum.fromCode(spuOfferingInfo.getOfferingStatus()).name;
                String productKeyword = spuOfferingInfo.getProductKeywords();

                // 处理销售标签 - 优先从参数 spuSaleLabelHistoryList 中获取
                String mainSaleTag = null;
                String subSaleTag = null;
                if (!CollectionUtils.isEmpty(spuSaleLabelHistoryList)) {
                    // 获取主标签（type="0"）
                    mainSaleTag = spuSaleLabelHistoryList.stream()
                            .filter(label -> "0".equals(label.getType()))
                            .map(SpuSaleLabelHistory::getLabel)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(","));

                    // 获取副标签（type="1"）
                    subSaleTag = spuSaleLabelHistoryList.stream()
                            .filter(label -> "1".equals(label.getType()))
                            .map(SpuSaleLabelHistory::getLabel)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(","));
                }

                // 为每个 SKU 发送埋点消息
                for (GioSkuOriginDTO gioSkuOriginDTO : gioSkuOriginDTOs) {
                    try {

                        // 构建并发送埋点消息 - 包含 SPU 相关信息、标签、目录和 item_id
                        GioCdpItemMessage msg = new GioCdpItemMessage.Builder()
                                .id(gioSkuOriginDTO.getOfferingCode()) // 维度表模型ID(记录ID) (必填)
                                .key("Sku") // 维度表标识符 (必填)
                                .addItemVariable("item_id", gioSkuOriginDTO.getOfferingCode())
                                // SPU 相关信息（优先从 spuOfferingInfo 参数获取）
                                .addItemVariable("spu_name", spuName)
                                .addItemVariable("spu_code", spuCode)
                                .addItemVariable("spu_type", gioSkuOriginDTO.getSpuOfferingClass() == null ? null :
                                        SPUOfferingClassEnum.getDisplay(gioSkuOriginDTO.getSpuOfferingClass()))
                                .addItemVariable("spu_create", spuCreateTime != null ? spuCreateTime :
                                        (gioSkuOriginDTO.getSpuCreateTime() == null ? null :
                                                DateTimeUtil.formatDate(gioSkuOriginDTO.getSpuCreateTime(), DateTimeUtil.STANDARD_DAY)))
                                .addItemVariable("spu_sales_type", spuSalesType)
                                // 商品关键字（优先从 spuOfferingInfo 获取）
                                .addItemVariable("productKeyword", productKeyword != null ? productKeyword : gioSkuOriginDTO.getProductKeyword())
                                // 销售标签（优先从参数获取，如果为空则从查询结果获取）
                                .addItemVariable("mainSaleTag", mainSaleTag != null ? mainSaleTag : gioSkuOriginDTO.getMainSaleTag())
                                .addItemVariable("subSaleTag", subSaleTag != null ? subSaleTag : gioSkuOriginDTO.getSubSaleTag())
                                .build();

                        growingIOConfig.getProject().send(msg);
                        log.info("成功发送 SKU 埋点消息，SKU编码: {}", gioSkuOriginDTO.getOfferingCode());

                    } catch (Exception e) {
                        log.error("发送 SKU 埋点消息失败，SKU编码: {}, 错误: {}", gioSkuOriginDTO.getOfferingCode(), e.getMessage(), e);
                    }
                }

                log.info("完成 SPU {} 的所有 SKU 埋点消息发送，共处理 {} 个 SKU", spuCode, gioSkuOriginDTOs.size());

            } catch (Exception e) {
                log.error("根据 SPU 发送 SKU 埋点消息失败，SPU编码: {}, 错误: {}", spuOfferingInfo.getOfferingCode(), e.getMessage(), e);
            }
        });
    }

    @Override
    public void sendSkuMsg(String spuOfferingCode, List<NavigationInfo> navigationInfoList) {
        // 异步执行
        ThreadExecutorConfig.executorService.execute(() -> {
            try {
                // 根据 SPU 编码查询所有相关的 SKU 信息
                List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(
                        new SkuOfferingInfoExample().createCriteria()
                                .andSpuCodeEqualTo(spuOfferingCode)
                                .andDeleteTimeIsNull()
                                .example());

                if (CollectionUtils.isEmpty(skuOfferingInfos)) {
                    log.warn("未找到 SPU {} 对应的 SKU 信息", spuOfferingCode);
                    return;
                }

                // 处理导航目录信息 - 从参数 navigationInfoList 中获取
                String firstLevelNavCatalog = null;
                String secondLevelNavCatalog = null;
                String thirdLevelNavCatalog = null;

                if (!CollectionUtils.isEmpty(navigationInfoList)) {
                    NavigationInfo navigationInfo = navigationInfoList.get(0); // 取第一个导航信息

                    // 使用重试机制查询导航目录名称
                    firstLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel1NavigationCode(), "一级");
                    secondLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel2NavigationCode(), "二级");
                    thirdLevelNavCatalog = queryNavigationDirectoryWithRetry(navigationInfo.getLevel3NavigationCode(), "三级");
                }

                // 为每个 SKU 发送埋点消息
                for (SkuOfferingInfo skuOfferingInfo : skuOfferingInfos) {
                    try {
                        // 构建并发送埋点消息 - 只包含导航目录字段和 item_id
                        GioCdpItemMessage msg = new GioCdpItemMessage.Builder()
                                .id(skuOfferingInfo.getOfferingCode()) // 维度表模型ID(记录ID) (必填)
                                .key("Sku") // 维度表标识符 (必填)
                                .addItemVariable("item_id", skuOfferingInfo.getOfferingCode())
                                // 导航目录信息（从参数获取）
                                .addItemVariable("firstLevelNavCatalog", firstLevelNavCatalog)
                                .addItemVariable("secondLevelNavCatalog", secondLevelNavCatalog)
                                .addItemVariable("thirdLevelNavCatalog", thirdLevelNavCatalog)
                                .build();

                        growingIOConfig.getProject().send(msg);
                        log.info("成功发送 SKU 导航目录埋点消息，SKU编码: {}", skuOfferingInfo.getOfferingCode());

                    } catch (Exception e) {
                        log.error("发送 SKU 导航目录埋点消息失败，SKU编码: {}, 错误: {}", skuOfferingInfo.getOfferingCode(), e.getMessage(), e);
                    }
                }

                log.info("完成 SPU {} 的所有 SKU 导航目录埋点消息发送，共处理 {} 个 SKU", spuOfferingCode, skuOfferingInfos.size());

            } catch (Exception e) {
                log.error("根据 SPU 编码发送导航目录埋点消息失败，SPU编码: {}, 错误: {}", spuOfferingCode, e.getMessage(), e);
            }
        });
    }

    /**
     * 带重试机制的导航目录查询
     * @param navigationCode 导航编码
     * @param level 导航级别（用于日志）
     * @return 导航目录名称，如果查询失败返回null
     */
    private String queryNavigationDirectoryWithRetry(String navigationCode, String level) {
        if (StringUtils.isBlank(navigationCode)) {
            return null;
        }

        int maxRetries = 3;
        int retryDelay = 1000; // 1秒

        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                ProductNavigationDirectory directory = productNavigationDirectoryMapper.selectByPrimaryKey(navigationCode);
                if (directory != null) {
                    log.info("成功查询到{}导航目录，编码: {}, 名称: {}, 重试次数: {}",
                            level, navigationCode, directory.getName(), retry);
                    return directory.getName();
                } else if (retry < maxRetries - 1) {
                    log.warn("{}导航目录暂未找到，编码: {}, 第 {} 次重试，等待 {}ms 后重试",
                            level, navigationCode, retry + 1, retryDelay);
                    Thread.sleep(retryDelay);
                    retryDelay *= 2; // 指数退避
                } else {
                    log.warn("{}导航目录在 {} 次重试后仍未找到，编码: {}",
                            level, maxRetries, navigationCode);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("查询{}导航目录时线程被中断，编码: {}", level, navigationCode);
                break;
            } catch (Exception e) {
                log.error("查询{}导航目录时发生异常，编码: {}, 重试次数: {}, 错误: {}",
                        level, navigationCode, retry, e.getMessage(), e);
                if (retry == maxRetries - 1) {
                    break;
                }
            }
        }
        return null;
    }

    /**
     * 对CouponInfoDTO中的销售员信息进行解密处理
     * 图片显示需要对加密的销售员字段进行解密
     * @param couponInfo 优惠券信息DTO
     * @return 解密后的新CouponInfoDTO对象，如果输入为null则返回null
     */
    private CouponInfoDTO decryptCouponInfoSalesmanFields(CouponInfoDTO couponInfo) {
        if (couponInfo == null) {
            return null;
        }

        // 创建新的CouponInfoDTO对象
        CouponInfoDTO decryptedCouponInfo = new CouponInfoDTO();

        try {
            // 复制所有原始字段
            decryptedCouponInfo.setSalesmanCode(couponInfo.getSalesmanCode());
            decryptedCouponInfo.setEmployeeId(couponInfo.getEmployeeId());

            // 关联领货码金额
            if (StringUtils.isNotEmpty(couponInfo.getCouponAmount())) {
                String decrypteCouponAmount = IOTEncodeUtils.decryptSM4(couponInfo.getCouponAmount(), iotSm4Key, iotSm4Iv);
                decryptedCouponInfo.setCouponAmount(decrypteCouponAmount);
            } else {
                decryptedCouponInfo.setCouponAmount(couponInfo.getCouponAmount());
            }

            // 对销售员编码进行SM4解密
            if (StringUtils.isNotEmpty(couponInfo.getCouponCode())) {
                String decryptedCode = IOTEncodeUtils.decryptSM4(couponInfo.getCouponCode(), iotSm4Key, iotSm4Iv);
                decryptedCouponInfo.setCouponCode(decryptedCode);
            } else {
                decryptedCouponInfo.setCouponCode(couponInfo.getCouponCode());
            }

            // 对销售员手机号进行SM4解密
            if (StringUtils.isNotEmpty(couponInfo.getSalesmanPhone())) {
                String decryptedPhone = IOTEncodeUtils.decryptSM4(couponInfo.getSalesmanPhone(), iotSm4Key, iotSm4Iv);
                decryptedCouponInfo.setSalesmanPhone(decryptedPhone);
            } else {
                decryptedCouponInfo.setSalesmanPhone(couponInfo.getSalesmanPhone());
            }

            // 对业务员姓名进行SM4解密
            if (StringUtils.isNotEmpty(couponInfo.getSalesmanName())) {
                String decryptedName = IOTEncodeUtils.decryptSM4(couponInfo.getSalesmanName(), iotSm4Key, iotSm4Iv);
                decryptedCouponInfo.setSalesmanName(decryptedName);
            } else {
                decryptedCouponInfo.setSalesmanName(couponInfo.getSalesmanName());
            }

            return decryptedCouponInfo;
        } catch (Exception e) {
            log.error("解密CouponInfoDTO销售员信息失败", e);
            // 解密失败时返回原对象的副本
            CouponInfoDTO fallbackCouponInfo = new CouponInfoDTO();
            fallbackCouponInfo.setCouponCode(couponInfo.getCouponCode());
            fallbackCouponInfo.setCouponAmount(couponInfo.getCouponAmount());
            fallbackCouponInfo.setSalesmanCode(couponInfo.getSalesmanCode());
            fallbackCouponInfo.setSalesmanPhone(couponInfo.getSalesmanPhone());
            fallbackCouponInfo.setEmployeeId(couponInfo.getEmployeeId());
            fallbackCouponInfo.setSalesmanName(couponInfo.getSalesmanName());
            return fallbackCouponInfo;
        }
    }

    /**
     * 批量解密orderInfo.getCouponInfo()列表中的优惠券信息
     * 专门用于处理从orderInfo获取的CouponInfoDTO列表，避免重复解密
     * @param couponInfoList 从orderInfo.getCouponInfo()获取的优惠券信息列表
     * @return 解密后的新优惠券信息列表
     */
    private List<CouponInfoDTO> decryptOrderInfoCouponList(List<CouponInfoDTO> couponInfoList) {
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return couponInfoList;
        }

        log.debug("开始解密orderInfo.getCouponInfo()列表，共{}条记录", couponInfoList.size());

        // 创建新的列表来存储解密后的对象
        List<CouponInfoDTO> decryptedList = new ArrayList<>();

        for (CouponInfoDTO couponInfo : couponInfoList) {
            if (couponInfo != null) {
                try {
                    // 创建新的CouponInfoDTO对象
                    CouponInfoDTO decryptedCouponInfo = new CouponInfoDTO();

                    // 复制不需要解密的字段
                    decryptedCouponInfo.setSalesmanCode(couponInfo.getSalesmanCode());
                    decryptedCouponInfo.setEmployeeId(couponInfo.getEmployeeId());

                    // 对优惠券码进行SM4解密
                    if (StringUtils.isNotEmpty(couponInfo.getCouponCode())) {
                        String decryptedCode = IOTEncodeUtils.decryptSM4(couponInfo.getCouponCode(), iotSm4Key, iotSm4Iv);
                        decryptedCouponInfo.setCouponCode(decryptedCode);
                    } else {
                        decryptedCouponInfo.setCouponCode(couponInfo.getCouponCode());
                    }

                    // 对优惠券金额进行SM4解密
                    if (StringUtils.isNotEmpty(couponInfo.getCouponAmount())) {
                        String decryptedAmount = IOTEncodeUtils.decryptSM4(couponInfo.getCouponAmount(), iotSm4Key, iotSm4Iv);
                        decryptedCouponInfo.setCouponAmount(decryptedAmount);
                    } else {
                        decryptedCouponInfo.setCouponAmount(couponInfo.getCouponAmount());
                    }

                    // 对销售员手机号进行SM4解密
                    if (StringUtils.isNotEmpty(couponInfo.getSalesmanPhone())) {
                        String decryptedPhone = IOTEncodeUtils.decryptSM4(couponInfo.getSalesmanPhone(), iotSm4Key, iotSm4Iv);
                        decryptedCouponInfo.setSalesmanPhone(decryptedPhone);
                    } else {
                        decryptedCouponInfo.setSalesmanPhone(couponInfo.getSalesmanPhone());
                    }

                    // 对业务员姓名进行SM4解密
                    if (StringUtils.isNotEmpty(couponInfo.getSalesmanName())) {
                        String decryptedName = IOTEncodeUtils.decryptSM4(couponInfo.getSalesmanName(), iotSm4Key, iotSm4Iv);
                        decryptedCouponInfo.setSalesmanName(decryptedName);
                    } else {
                        decryptedCouponInfo.setSalesmanName(couponInfo.getSalesmanName());
                    }

                    decryptedList.add(decryptedCouponInfo);
                    log.debug("成功解密优惠券信息，券码: {}", decryptedCouponInfo.getCouponCode());
                } catch (Exception e) {
                    log.error("解密优惠券信息失败，券码: {}, 错误: {}", couponInfo.getCouponCode(), e.getMessage(), e);
                    // 解密失败时添加原对象的副本，继续处理下一条记录
                    CouponInfoDTO fallbackCouponInfo = new CouponInfoDTO();
                    fallbackCouponInfo.setCouponCode(couponInfo.getCouponCode());
                    fallbackCouponInfo.setCouponAmount(couponInfo.getCouponAmount());
                    fallbackCouponInfo.setSalesmanCode(couponInfo.getSalesmanCode());
                    fallbackCouponInfo.setSalesmanPhone(couponInfo.getSalesmanPhone());
                    fallbackCouponInfo.setEmployeeId(couponInfo.getEmployeeId());
                    fallbackCouponInfo.setSalesmanName(couponInfo.getSalesmanName());
                    decryptedList.add(fallbackCouponInfo);
                }
            } else {
                // 如果原对象为null，在新列表中也添加null
                decryptedList.add(null);
            }
        }

        log.debug("完成orderInfo.getCouponInfo()列表解密");
        return decryptedList;
    }

}
