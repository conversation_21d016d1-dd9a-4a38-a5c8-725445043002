package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

/**
 * @Author: dgj
 * @Description:售后订单商品信息
 */
@Data
public class AfterMarketOfferingInfoRequestDTO {
    /**
     * 售后服务类型
     * 1：OneNET/OnePark属地化服务
     * 2：铁通增值服务
     */
    private Integer afterMarketType;
    /**
     * 售后商品编码
     * 1：新增
     * 2：修改
     */
    private String afterMarketCode;
    /**
     * 订购数量
     */
    private Long quantity;

    /**
     * 关联商品信息
     * */
    private AfterMarketMallOfferingInfoDTO offeringinfo;

    /**
     * 售后商品版本号
     */
    private String afterMarketVersion;

}
