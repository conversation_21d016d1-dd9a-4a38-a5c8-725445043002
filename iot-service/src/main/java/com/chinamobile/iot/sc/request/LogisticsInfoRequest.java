package com.chinamobile.iot.sc.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/11 10:51
 * @Description:
 */
@Data
public class LogisticsInfoRequest {
    /**
     * 原子订单ID
     */
//    private String id;
    /**
     * 订单ID
     */
    private String orderId;

    private List<LogisticsMsg> logisticsMsgs;

    private List<String> snList;

    @Data
    public static class LogisticsMsg {
        /**
         * 物流单号
         */
        @NotBlank(message = "物流单号不能为空")
        private String logisCode;
        /**
         * 物流服务商编码
         */
        @NotBlank(message = "物流服务商编码不能为空")
        private String supplierName;
        /**
         * 备注信息
         */
        private String description;
    }

}
