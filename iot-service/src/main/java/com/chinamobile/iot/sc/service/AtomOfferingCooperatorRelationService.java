package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.pojo.dto.AtomCooperatorInfoByGroupDTO;
import com.chinamobile.iot.sc.pojo.dto.AtomCooperatorInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.AtomOfferingCooperatorRelation;
import com.chinamobile.iot.sc.pojo.entity.AtomOfferingCooperatorRelationExample;
import com.chinamobile.iot.sc.pojo.param.AtomCooperatorInfoByGroupParam;
import com.chinamobile.iot.sc.pojo.param.AtomCooperatorInfoParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/26
 * @description 商品和从合作伙伴的关系service接口类
 */
public interface AtomOfferingCooperatorRelationService {

    /**
     * 批量新增商品和从合作伙伴的关系
     * @param relationList
     */
    void batchAddAtomOfferingCooperatorRelation(List<AtomOfferingCooperatorRelation> relationList);

    /**
     * 根据需要删除商品和从合作伙伴的关系
     * @param atomOfferingCooperatorRelationExample
     */
    void deleteAtomOfferingCooperatorRelationByExample(AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample);

    /**
     * 根据需要获取商品和从合作伙伴的关系
     * @param atomOfferingCooperatorRelationExample
     * @return
     */
    List<AtomOfferingCooperatorRelation> listAtomOfferingCooperatorRelationByExample(AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample);


    /**
     * 获取组装后的原子商品和合作伙伴列表
     * @param atomCooperatorInfoByGroupParam
     * @return
     */
    List<AtomCooperatorInfoByGroupDTO>  listCooperatorInfoByGroup(AtomCooperatorInfoByGroupParam atomCooperatorInfoByGroupParam);

    /**
     * 获取原子商品和合作伙伴列表
     * @param atomCooperatorInfoParam
     * @return
     */
    List<AtomCooperatorInfoDTO> listCooperatorInfo(AtomCooperatorInfoParam atomCooperatorInfoParam);

    /**
     * 获取原子商品从合作伙伴信息
     * @param atomOfferingId
     * @return
     */
    List<Data4User> listCooperatorUserInfo(String atomOfferingId);

}
