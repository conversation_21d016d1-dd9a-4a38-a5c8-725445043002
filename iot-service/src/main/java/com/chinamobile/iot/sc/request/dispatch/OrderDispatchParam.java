package com.chinamobile.iot.sc.request.dispatch;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/12/13 16:48
 * @description: 订单派遣装维请求参数
 **/
@Data
public class OrderDispatchParam {

    /**
     * 售后订单主键id
     */
    @NotEmpty(message = "售后订单主键id不能为空")
    private String serviceOrderId;

    /**
     * 售后订单与商品关联主键id
     */
    @NotEmpty(message = "订单与商品关联主键id不能为空")
    private String orderOfferingRelationId;

    /**
     * 装维人员用户id
     */
    @NotEmpty(message = "装维人员用户id不能为空")
    private String installUserId;

    @NotEmpty(message = "装维人员用户名称不能为空")
    private String installUserName;

    @NotEmpty(message = "装维人员用户电话不能为空")
    private String installUserPhone;


    @NotEmpty(message = "装维人员用户公司不能为空")
    private String installPartnerName;
}
