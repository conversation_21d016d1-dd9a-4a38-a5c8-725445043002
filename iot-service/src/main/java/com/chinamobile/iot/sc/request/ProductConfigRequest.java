package com.chinamobile.iot.sc.request;

import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.pojo.dto.ProMaterialDTO;
import com.chinamobile.iot.sc.pojo.dto.StdServiceConfigDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ProductConfigRequest {
    /**
     * sku id
     */
    @NotBlank(message = "规格商品ID不能为空")
    private String id;

    /**
     * spu类型
     */
    @NotBlank(message = "spu类型不能为空")
    private String spuOfferingClass;

    /**
     * 合伙人id,不传递或者传递空字符串-> 不更新合伙人。 传递-1 -> 清除合伙人设置
     * 修改为主合作伙伴id 20250526
     */
    private String cooperatorId;

    /**
     * 从合作伙伴id集合
     */
    private List<String> downCooperatorIdList;

    /**
     * 接单方式  1--OS接单   2--省内接单
     */
    private Integer getOrderWay;

    /**
     * 物料
     */
    @Valid
    private List<ProMaterialDTO> proMaterials;

    /**
     * 标准服务
     */
    @Valid
    @Size(min = 1,message = "标准服务不能为空")
    private List<StdServiceItem> stdServiceList;

    /**
     * A13 产品类型ID为0004  云瞳资源配置
     */
  /*  @Valid
    private List<HeMuResourceConfig> heMuResourceConfigList;*/


    @Data
    public static class StdServiceItem{
        @NotEmpty(message = "原子商品id不能为空")
        private String atomId;

        @NotEmpty(message = "标准服务id不能为空")
        private String stdServiceId;

        /**
         * 专合结算价
         */
        private String specialCooperativeSettlementPrice;
    }


/*    @Data
    public static class HeMuResourceConfig{
        @NotEmpty(message = "原子商品id不能为空")
        private String atomId;

        @NotEmpty(message = "套餐编码不能为空")
        private String packageNumber;

        @NotNull(message = "存储属性不能为空")
        private Integer storageType;

        @NotNull(message = "存储时长不能为空")
        private Integer storageDays;

        @NotNull(message = "套餐属性不能为空")
        private Integer packageProperty;

        @NotNull(message = "资费类型不能为空")
        private Integer packageCost;

        @NotNull(message = "服务时长不能为空")
        private Integer serviceMonths;


    }*/

}
