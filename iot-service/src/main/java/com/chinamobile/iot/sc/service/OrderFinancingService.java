package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.BasePageQuery;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.FinancingRepaymentBaseVO;
import com.chinamobile.iot.sc.pojo.vo.FinancingRepaymentHistoryVO;
import com.chinamobile.iot.sc.pojo.vo.FinancingRepaymentListVO;
import com.chinamobile.iot.sc.pojo.vo.FinancingResponseVO;

public interface OrderFinancingService {
    BaseAnswer<String> getUrl(QueryFinancingUrlParam param);

    FinancingResponseVO authenticationAndActivationStatus(AuthenticationAndActivationStatusParam param);

    BaseAnswer<Void> startFinancing(StartFinancingParam param, String userId,String ip);

    BaseAnswer<Void> cancelFinancing(CancelFinancingParam param, String userId,String ip);

    FinancingResponseVO orderConfirmStatus(OrderConfirmStatusParam param);

    FinancingResponseVO orderFinancingStatus(OrderFinancingStatusParam param);

    FinancingResponseVO repayment(FinancingRepaymentParam param);

    BaseAnswer<PageData<FinancingRepaymentListVO>> repaymentList(FinancingRepaymentListParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<FinancingRepaymentBaseVO> repaymentBaseInfo(String orderCode);

    BaseAnswer<PageData<FinancingRepaymentHistoryVO>> repaymentHistory(String orderCode, BasePageQuery pageQuery);

    void sendStartFinancingMask(String tradeOrderId);

    BaseAnswer<Boolean> judgeUserIfHaveEffectiveTrade(String userId);
}
