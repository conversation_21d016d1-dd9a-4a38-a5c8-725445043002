package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceInfo;
import com.chinamobile.iot.sc.pojo.invoice.InvoiceReverseInfo;
import com.chinamobile.iot.sc.request.invoice.InvoiceRevNewRequest;
import com.chinamobile.iot.sc.request.invoice.Request4InvoRecPage;
import com.chinamobile.iot.sc.request.invoice.Request4InvoRevPage;
import com.chinamobile.iot.sc.response.web.invoice.Data4ApplyInvoiceRec;
import com.chinamobile.iot.sc.response.web.invoice.Data4InvoiceRevRec;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IInvoiceService
 * @description: 发票相关Service
 * @author: zyj
 * @create: 2021/11/30 15:01
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IInvoiceService {

    IOTAnswer<Void> InvoiceRequest2OS(IOTRequest baseRequest);

    BaseAnswer<Void> invoicingResult2IOTMall(String invoiceEntryListJSON, String orderReq
            , MultipartFile[] files, String result,String userId,String ip);

    BaseAnswer<Void> invoiceVoidCallback(InvoiceRevNewRequest invoiceRevRequest, String result, String errorDesc,String ip,String userId);

    IOTAnswer<Void> InvoiceVoid(IOTRequest baseRequest);

    BaseAnswer<PageData<Data4ApplyInvoiceRec>> findPageByInvoiceIdOrOrderId(Request4InvoRecPage request
            , String userId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<List<ApplyInvoiceInfo>> findInvoicesByOrderId(String orderId, String orderReq, String userId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<Boolean> isSuccInvoiceByAtomOrderId(String atomOrderId);

    BaseAnswer<PageData<Data4InvoiceRevRec>> findPageByRevIdOrOrderId(Request4InvoRevPage request
            , String userId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<List<InvoiceReverseInfo>> findRevInfoByOrderId(String orderId, String orderReq, String userId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<Boolean> isSuccInvoiceByOrderId(String orderId);

    void exportNotInvoiceExcel(LoginIfo4Redis loginIfo4Redis, String userId);

    void exportNotRedFlushExcel(LoginIfo4Redis loginIfo4Redis, String userId);

    Void invoicingResult2IOTMall1(String orderSeq);

    String getRevenueToken();

    BaseAnswer<Void> revenueInvoiceApply(String recId);
}
