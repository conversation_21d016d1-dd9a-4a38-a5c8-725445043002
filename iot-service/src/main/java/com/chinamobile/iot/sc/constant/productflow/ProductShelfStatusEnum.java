package com.chinamobile.iot.sc.constant.productflow;

public enum ProductShelfStatusEnum {

    SHELF_IN_PROGRESS(1,"上架中"),
    SHELF_CANCEL(2,"上架取消"),
    SHELF_SUCCESS(3,"已上架"),
    OFF_SHELF_IN_PROGRESS(4,"下架中"),
    OFF_SHELF_CANCEL(5,"下架取消"),
    OFF_SHELF_SUCCESS(6,"已下架"),
    ;


    public Integer code;
    public String name;

    ProductShelfStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
