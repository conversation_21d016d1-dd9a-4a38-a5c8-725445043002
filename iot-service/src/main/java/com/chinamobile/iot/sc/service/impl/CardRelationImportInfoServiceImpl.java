package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.dao.CardRelationImportInfoMapper;
import com.chinamobile.iot.sc.dao.ext.CardRelationImportInfoMapperExt;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfo;
import com.chinamobile.iot.sc.pojo.param.CardRelationImportInfoParam;
import com.chinamobile.iot.sc.pojo.vo.CardRelationImportInfoVO;
import com.chinamobile.iot.sc.service.CardRelationImportInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12
 * @description 卡+X终端导入批次相关信息service实现类
 */
@Service
@Slf4j
public class CardRelationImportInfoServiceImpl implements CardRelationImportInfoService {

    @Resource
    private CardRelationImportInfoMapper cardRelationImportInfoMapper;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private CardRelationImportInfoMapperExt cardRelationImportInfoMapperExt;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Override
    public void batchAddCardRelationImportInfo(List<CardRelationImportInfo> cardRelationImportInfoList) {
        cardRelationImportInfoMapper.batchInsert(cardRelationImportInfoList);
    }

    @Override
    public PageData<CardRelationImportInfoVO> pageCardRelationImportInfo(CardRelationImportInfoParam cardRelationImportInfoParam,
                                                                         LoginIfo4Redis loginIfo4Redis) {
        PageData<CardRelationImportInfoVO> pageData = new PageData<>();
        Integer pageNum = cardRelationImportInfoParam.getPageNum();
        Integer pageSize = cardRelationImportInfoParam.getPageSize();

        // 处理用户权限
        handleCardInfoXParam(cardRelationImportInfoParam, loginIfo4Redis);

        Page<CardRelationImportInfoVO> page = new Page<>(pageNum, pageSize);
        List<CardRelationImportInfoVO> cardRelationImportInfoVOList
                = cardRelationImportInfoMapperExt.listCardRelationImport(page, cardRelationImportInfoParam);
        if (CollectionUtils.isNotEmpty(cardRelationImportInfoVOList)) {
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            cardRelationImportInfoVOList.stream().forEach(cardRelationImportInfoVO -> {
                cardRelationImportInfoVO.setProvinceName(provinceCodeNameMap.get(cardRelationImportInfoVO.getBeId())+"");
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(cardRelationImportInfoVOList);

        return pageData;
    }

    private void handleCardInfoXParam(CardRelationImportInfoParam cardRelationImportInfoParam,
                                      LoginIfo4Redis loginIfo4Redis) {

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = BaseConstant.PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = BaseConstant.PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
            String beId = cardRelationImportInfoParam.getBeId();
            List<String> beIdList = cardRelationImportInfoParam.getBeIdList();

            if (!isProvinceUser) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
            }

            if (CollectionUtils.isEmpty(beIdList)) {
                cardRelationImportInfoParam.setBeId(data4User.getBeIdPartner());
            }

            /*// 省公司合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole && isProvinceUser && StringUtils.isEmpty(beId)) {
                cardRelationImportInfoParam.setBeId(data4User.getBeIdPartner());
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince)
                    && isProvinceUser && StringUtils.isEmpty(location)) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    cardRelationXParam.setBeId(data4User.getBeIdPartner());
                } else {
                    cardRelationXParam.setLocation(userLocation);
                }

            }*/
        }
    }
}
