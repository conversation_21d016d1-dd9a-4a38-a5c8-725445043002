package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/5/5 14:28
 * @description: 订单催单请求参数
 **/
@Data
public class OrderReminderParam {

    /**
     * 订单主键
     */
    /*@NotEmpty(message = "订单主键id不能为空")
    private String id;*/

    /**
     * 合作伙伴id
     */
//    @NotEmpty(message = "合作伙伴id不能为空")
    private String cooperatorId;

    /**
     * 订单编码号
     */
    @NotEmpty(message = "订单编号不能为空")
    private String orderId;

    /**
     * 原子商品名称
     */
   /* private String atomOfferingName;*/


    /**
     * 订购数
     */
  /*  private String quantity;*/

    /**
     * 退货单号
     */
/*    private String refundOrderId;*/

    /**
     * 订单状态 在售订单(待发货：0,待接单:10,待交付:16)，售后退换货订单 （用户申请退款；1，用户申请退货退款；7，用户申请验货；10）
     */
//    @NotNull(message = "订单状态不能为空")
    private Integer orderStatus;


    /**
     * 短信模板标识 1.系统模板 2.自定义模板
     */
   /* @NotEmpty(message = "模板标识不能为空")
    private String templateSign;*/

    /**
     * 订单标识：1.待发货订单  2.售后退换货订单 3.发票开票 4.发票冲红
     */
    @NotEmpty(message = "订单标识不能为空")
    private String orderSign;

}
