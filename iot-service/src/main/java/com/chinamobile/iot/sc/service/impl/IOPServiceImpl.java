package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.ProductFlowSmsConfig;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.dao.ext.IOPMapperExt;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.dto.IOPUploadDTO;
import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;
import com.chinamobile.iot.sc.request.IopMonitorRequest;
import com.chinamobile.iot.sc.response.IopMonitorResponse;
import com.chinamobile.iot.sc.service.BaseSmsService;
import com.chinamobile.iot.sc.service.IOPService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.SFTPUtil;
import com.jcraft.jsch.ChannelSftp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
@Service
@Slf4j
public class IOPServiceImpl implements IOPService {
    @Value("${iop.ftp.name}")
    private String sftpUserName;
    @Value("${iop.ftp.password}")
    private String sftpPassword;
    @Value("${iop.ftp.host}")
    private String sftpHost;
    @Value("${iop.ftp.port}")
    private Integer sftpPort;

    @Value("${iop.ftp.report}")
    private String sftpReport;

    @Value("${iop.ftp.upload}")
    private String sftpUpload;

    @Value("${iop.sms.templateId:108279}")
    private String iopMessageId;

    @Value("${iop.monitor.url}")
    private String iopMonitorUrl;

    @Value("${iop.monitor.key}")
    private String iopMonitorKey;

    @Value("${iop.monitor.secret}")
    private String iopMonitorSecret;

    @Resource
    private IOPMapperExt iopMapperExt;

    @Autowired
    private ProductFlowSmsConfig productFlowSmsConfig;

    @Autowired
    private BaseSmsService baseSmsService;
    private static final DateTimeFormatter FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");

    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);
    // 获取iop校验报告
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sftpIOPReport() {
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接iopsftp获取校验报告，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpReport);
        List<String> phones = new ArrayList<>();
        phones.add("15823524720");
        phones.add("18223126515");
        phones.add("18323299093");

        List<String> successPhones = new ArrayList<>();
        successPhones.add("18223126515");
        successPhones.add("18323299093");
        try {
            Boolean noIop = true;
            if (!sftpUtil.login()) {
                log.info("FTP登录失败！");
                return;
            }
            log.info("FTP登录成功！");
            Vector files = sftpUtil.listFiles(sftpReport);
            if (files == null || files.isEmpty()) {
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("date", "iop校验文件夹为空");
                paramMap.put("result", "失败");
                log.info("iop校验文件夹为空");
                executor.execute(() ->  monitorResult(0,"iop校验文件夹为空"));
                baseSmsService.sendMsg(phones, iopMessageId, paramMap);
                return;
            }
            for (Iterator<ChannelSftp.LsEntry> it = files.iterator(); it.hasNext(); ) {
                ChannelSftp.LsEntry str = it.next();
                String filename = str.getFilename();
                if (filename.equals(".") || filename.equals("..") || !filename.contains("S0055")) {
                    continue;
                }
                noIop = false;
                log.info("开始下载文件原名:{}", filename);
                InputStream is = sftpUtil.getInputStream(sftpReport, filename);

                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String readStr;
                //Date date = new Date();
//                //获取文件名里时间 作为数据创建时间
//                String dateStr = StringUtils.substringBetween(filename, "_");
//                Date updateDate = DateUtils.strToDate(dateStr, DATE_FORMAT_NO_SYMBOL);
//                Date date = DateTimeUtil.addDay(updateDate, -1);
                boolean success = true;
                String errMsg = "";
                while ((readStr = br.readLine()) != null) {
                    String part1 = null;
                    String part2 = null;
                    String part3 = null;
                    // 文件级
                    if(filename.startsWith("f")){
                         part1 = readStr.substring(0, 50);
                         part2 = readStr.substring(50, 50 + 14);
                         part3 = readStr.substring(50 + 14);
                    }else if (filename.startsWith("r")){
                        //记录级
                         part1 = readStr.substring(0, 50);
                         part2 = readStr.substring(50);
                         part3 =  reverseSubstring(readStr, 8);
                    }else{
                        log.info("iop sftp获取校验报告 该条记录格式异常，无法解析,文件名:{}", filename);
                        success = false;
                    }

                    if(part3.matches("^[0]+$")){
                        //成功了好像不用处理 或者看要不要搞个成功消息
                    }else{
                        if(filename.startsWith("r")){
                            part3 =  reverseSubstring(readStr, 9);
                        }
                        errMsg = errMsg.concat("\n文件名为").concat(filename).concat("处理时间或文件行号为").concat(part2)
                                .concat("错误码为").concat(part3);
                        success = false;
                    }
                    log.info("iop校验报告readStr:{}", readStr);
                }
                if (success) {
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("date", "iop校验拉取校验文件并校验成功");
                    paramMap.put("result", "成功");
                    executor.execute(() ->  monitorResult(1,"iop校验拉取校验文件并校验成功"));
//                    baseSmsService.sendMsg(successPhones, iopMessageId, paramMap);
                }else{
                    // 只发给骆杨 发文件名，报错信息到时候看日志，怕有很多短信发不过去
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("date", "iop校验校验文件出错");
                    paramMap.put("result", "失败");
                    log.info("iop校验出错:{}",errMsg);
                    executor.execute(() ->  monitorResult(0,"iop校验校验文件出错"));
                    baseSmsService.sendMsg(phones, iopMessageId, paramMap);
                }
                // 无论成功失败都删除
                log.info("删除文件:{}", filename);
                sftpUtil.delete(sftpReport, filename);
                log.info("删除成功");
                br.close();
            }
            if(noIop){
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("date", "iop校验文件未获取到");
                paramMap.put("result", "失败");
                log.info("iop校验文件未获取到");
                executor.execute(() ->  monitorResult(0,"iop校验文件未获取到"));
                baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            }
        } catch (Exception e) {
            log.info("拉取iop获取校验报告错误:{}", e.toString());
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("date", "拉取iop获取校验报告错误");
            paramMap.put("result", "失败");
            executor.execute(() ->  monitorResult(0,"拉取iop获取校验报告错误"));
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
//            Map<String, String> paramMap = new HashMap<>();
//            paramMap.put("date", "拉取iop获取校验报告失败");
//            paramMap.put("result", "");
//
//            baseSmsService.sendMsg("15823524720", iopMessageId, paramMap);
        } finally {
            sftpUtil.logout();
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sftpUploadIOP(IOPUploadParam param) {
        List<String> phones = new ArrayList<>();
        phones.add("15823524720");
        phones.add("18223126515");
        phones.add("18323299093");
        List<String> successPhones = new ArrayList<>();
        successPhones.add("18223126515");
        successPhones.add("18323299093");

        String datFilePath =  "a_20006_S0055_07001_" + DateTimeUtil.getDayStr(param.getEndTime().getTime())  + "_"+ param.getRetry() + "_001.dat";
        String verfFilePath =  "a_20006_S0055_07001_" + DateTimeUtil.getDayStr(param.getEndTime().getTime())  + "_"+ param.getRetry() + ".verf";
        String gzFilePath =  "a_20006_S0055_07001_" + DateTimeUtil.getDayStr(param.getEndTime().getTime())  + "_"+ param.getRetry() + "_001.dat.gz";;

        // 生成.dat文件
        generateDatFile(datFilePath, param.getStartTime(), param.getEndTime(), param.getType());

        // 将.dat文件压缩为.gz文件
        compressToGz(datFilePath, gzFilePath);

        // 生成.verf文件
        generateVerfFile(verfFilePath, gzFilePath);


        FileInputStream verfFileInputStream =null;
        FileInputStream gzFileInputStream =null;

        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        log.info("连接iopsftp上传接口文件，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpUpload);
        try {
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");
                log.info("开始上传文件原名:{}", gzFilePath);
                log.info("开始上传文件原名:{}", verfFilePath);
                File gzFile = new File(gzFilePath);
                File verfFile = new File(verfFilePath);
                verfFileInputStream = new FileInputStream(verfFile);
                gzFileInputStream = new FileInputStream(gzFile);
                sftpUtil.upload(sftpUpload, gzFilePath, gzFileInputStream);
                sftpUtil.upload(sftpUpload, verfFilePath, verfFileInputStream);

                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("date", "iop上传文件成功");
                paramMap.put("result", "成功");
                executor.execute(() ->  monitorResult(1,"iop上传文件成功"));
//                baseSmsService.sendMsg(successPhones, iopMessageId, paramMap);
            }
        } catch (Exception e) {
            log.error("SFTP上传文件失败！:{}", e.getMessage());
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("date", "iop上传文件失败");
            paramMap.put("result", "失败");
            executor.execute(() ->  monitorResult(0,"iop上传文件失败"));
            baseSmsService.sendMsg(phones, iopMessageId, paramMap);
            throw new BusinessException(StatusConstant.OSS_UPLOAD_ERR.getStateCode(), StatusConstant.OSS_UPLOAD_ERR.getMessage());
        } finally {
            sftpUtil.logout();
            if (verfFileInputStream !=null){
                try {
                    verfFileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (gzFileInputStream !=null){
                try {
                    gzFileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            log.info("登出sftp服务！");
        }

    }

    private void generateDatFile(String datFilePath, Date startTime, Date endTime, String type) {
//        IOPUploadDTO[] array = new IOPUploadDTO[10]; // 假设有一个 IOPUploadDTO 类型的数组对象
        String formatStartTime;
        String formatEndTime;
        // type为a时为非首次同步，用updateTime yyyy-MM-dd HH:mm:ss
        // 不为a时 yyyyMMddHHmmss
        if(type.equals("a")){
            formatStartTime =  DateTimeUtil.formatDate(startTime, DateTimeUtil.DEFAULT_DATE_DEFAULT);
            formatEndTime = DateTimeUtil.formatDate(endTime, DateTimeUtil.DEFAULT_DATE_DEFAULT);
        }else{
            formatStartTime =  DateTimeUtil.formatDate(startTime, DateTimeUtil.DB_TIME_STR);
            formatEndTime = DateTimeUtil.formatDate(endTime, DateTimeUtil.DB_TIME_STR);
        }
        List<IOPUploadDTO> array = iopMapperExt.IOPUploadList(formatStartTime, formatEndTime, type);
        // 假设数组对象已经被填充

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(datFilePath))) {
            int count = 1;
            for (IOPUploadDTO item : array) {
                String line = count + "€€" +
                        item.getBeId() + "€€" +
                        item.getOrderId() + "€€" +
                        item.getPhone() + "€€" +
                        item.getName() + "€€" +
                        item.getTimeStr() + "€€" +
                        item.getOrderStatus() + "€€" +
                        item.getTotalPrice() + "€€" +
                        item.getSkuOfferingCode() + "€€" +
                        item.getSkuOfferingName() + "€€" +
                        item.getSkuQuantity() + "€€" +
                        item.getAtomOfferingCode() + "€€" +
                        item.getAtomOfferingName() + "€€" +
                        item.getAtomQuantity() + "\r\n";
                count++;
                writer.write(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void generateVerfFile(String verfFilePath, String gzFilePath) {
        try (GZIPInputStream gzInputStream = new GZIPInputStream(new FileInputStream(gzFilePath));
             BufferedWriter writer = new BufferedWriter(new FileWriter(verfFilePath))) {

            File gzFile = new File(gzFilePath);
            String fileName = gzFile.getName();
            long fileSize = gzFile.length();
            int recordCount = getRecordCount(gzInputStream);
            long fileTime = gzFile.lastModified();


                // 获取当前日期和时间
                // 这里应该取数据账期，但是需要保证当日重传所以无所谓了
                // YYYYMMDD
                LocalDate currentDate = LocalDate.now().minusDays(1);
//                // YYYYMMDDHH24MISS
//                LocalDateTime currentDateTime = LocalDateTime.now();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                String formattedFileTime = dateFormat.format(new Date(fileTime));
                // 构建.verf文件内容
                String verfContent = String.format("%-50s", fileName) +
                        String.format("%-20s", fileSize) +
                        String.format("%-20s", recordCount) +
                        currentDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) +
//                        currentDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
                        formattedFileTime
                        + "\r\n";

                writer.write(verfContent);
            } catch(IOException e){
                e.printStackTrace();
            }

    }


    private static int getRecordCount(GZIPInputStream gzInputStream) throws IOException {
        // 根据具体情况提取记录数
        // 例如，假设记录数位于压缩包中文件内容的行数，可以使用以下代码提取记录数：
        int count = 0;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(gzInputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 根据需要计算记录数的逻辑进行处理
                count++;
            }
        }
        return count;
    }

    private static void compressToGz(String sourceFilePath, String gzFilePath) {
        Path sourcePath = Paths.get(sourceFilePath);
        Path gzPath = Paths.get(gzFilePath);

        try (
                FileInputStream fis = new FileInputStream(sourcePath.toFile());
                FileOutputStream fos = new FileOutputStream(gzPath.toFile());
                GZIPOutputStream gzipOS = new GZIPOutputStream(fos)
        ) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                gzipOS.write(buffer, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 从字符串末尾截取指定长度的子字符串
    private static String reverseSubstring(String str, int length) {
        int startIndex = str.length() - length;
        if (startIndex < 0) {
            startIndex = 0;
        }

        return str.substring(startIndex);
    }
    public BaseAnswer<Void> monitorResult(Integer result, String content) {
        BaseAnswer baseAnswer = new BaseAnswer<>();
        IopMonitorRequest iopMonitorRequest = new IopMonitorRequest();
        iopMonitorRequest.setUpResult(result);

        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 定义格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 格式化输出
        String formattedDate = yesterday.format(formatter);
        iopMonitorRequest.setUpPeriod(formattedDate);

        if(StringUtils.isNotEmpty(content)){
            String contentResult = "接口编号:07001, 上报周期:" + formattedDate + " 结果为: " + content;
            iopMonitorRequest.setAlarmContent(contentResult);
        }

        LocalDateTime now = LocalDateTime.now();
        String formattedNow = now.format(FORMATTER);

        iopMonitorRequest.setPushTime(formattedNow);


        iopMonitorRequest.setInterfaceCode("07001");

        Long timestamp = Instant.now().getEpochSecond();;
        String nonceStr = BaseServiceUtils.getId();
        Map<String, Object> params = new HashMap<>();
        params.put("nonce_str", nonceStr);       // 随机字符串
        params.put("app_key", iopMonitorKey);    // 应用Key（示例值）
        params.put("timestamp", timestamp);    // Unix时间戳 10位

        // 2. 调用签名方法
        String sign = md5Signature(params, iopMonitorSecret);

        String url = iopMonitorUrl + "?timestamp=" + timestamp + "&nonce_str="
                    + nonceStr + "&app_key=" + iopMonitorKey + "&sign=" + sign;

        log.info("iopMontior数据上传 url:{}",url);
        // 请求iopmontior
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(iopMonitorRequest), headers);


            log.info("iopMontior数据上传request:{}",JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<String> response = restTemplateHttps.postForEntity(url, requestEntity, String.class);
            log.info("iopMontior数据上传response:{}",JSON.toJSONString(response));
            IopMonitorResponse response1 = JSON.parseObject(response.getBody(), IopMonitorResponse.class);
            if(!response1.getCode().equals(0)){
                log.info("iopMontior数据上传失败:{}", response1.getMessage());
                baseAnswer.setMessage(response1.getMessage());
                baseAnswer.setStateCode("10004");
            }else{
            }
        }catch (Exception e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }


        return baseAnswer;
    }

    /**
     * 二行制转字符串
     */
    private static String byte2hex(byte[] b) {
        StringBuffer hs = new StringBuffer();
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs.append("0").append(stmp);
            } else {
                hs.append(stmp);
            }
        }
        return hs.toString().toUpperCase();
    }
    /**
     *
     * @param params 未排序的参数
     * @param secret
     * @return
     */
    public static String md5Signature(Map<String, Object> params, String secret) {
        String result = "";
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        query.append(secret);
        for (String key : keys) {
            Object value = params.get(key);
            if (StringUtils.isNotEmpty(key) && !ObjectUtils.isEmpty(value)) {
                query.append(key).append(value);
            }
        }
        query.append(secret);
        // 第三步：使用MD5加密
        try {
            //MD5加密，输出一个定长信息摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            result = byte2hex(md.digest(query.toString().getBytes(StandardCharsets.UTF_8)));

        } catch (Exception e) {
            throw new RuntimeException("sign error !");
        }
        return result;
    }

}
