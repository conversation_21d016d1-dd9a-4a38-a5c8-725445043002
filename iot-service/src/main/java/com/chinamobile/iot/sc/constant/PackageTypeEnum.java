package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/27
 * @description 商品套餐类型枚举类
 */
public enum PackageTypeEnum {

    CONTINUOUS_MONTH("0","连续包月"),
    ONLY_ONE("1","一次性包");

    /**
     * 套餐类型
     */
    private String type;

    /**
     * 套餐类型描述
     */
    private String desc;

    PackageTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type) {
        for (PackageTypeEnum value : PackageTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public static Boolean containDesc(String desc) {
        for (PackageTypeEnum value : PackageTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
