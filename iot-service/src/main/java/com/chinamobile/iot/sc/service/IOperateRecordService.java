package com.chinamobile.iot.sc.service;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.*;
import com.chinamobile.iot.sc.entity.iot.CreateOperateRecordParam;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.OperateRecordDTO;
import com.chinamobile.iot.sc.pojo.param.OperateRecordQueryParam;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;


/**
 * 日志管理
 */
public interface IOperateRecordService {

    BaseAnswer<PageData<OperateRecordDTO>> getLogList(OperateRecordQueryParam param);

    void createLog(CreateOperateRecordParam param);
}
