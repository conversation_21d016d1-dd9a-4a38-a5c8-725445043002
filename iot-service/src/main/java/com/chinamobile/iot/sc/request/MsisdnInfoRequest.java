package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/7
 * @description 接单反馈到商城的实体类
 */
@Data
public class MsisdnInfoRequest {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 接单结果
     * 1:成功
     * 2:失败
     */
    private String deliverResult;

    /**
     * 失败原因
     */
    private String reason;

    private List<MsisdnInfo> msisdnInfos;

    @Data
    public static class MsisdnInfo{
        /**
         * 正式服务号码
         */
        private String msisdn;

        /**
         * 临时iccid
         */
        private String iccid;

        /**
         * 终端IMEI
         */
        private String imei;
    }

}
