package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/2
 * @description 行车卫士产品编码枚举类
 */
public enum CarSecurityProductCodeEnum {

    XCWS_01_0001("xcws-01-0001","100条短信告警包","01短信告警"),
    XCWS_01_0002("xcws-01-0002","1000条短信告警包","01短信告警"),
    XCWS_01_0003("xcws-01-0003","1800条短信告警包","01短信告警")
    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String desc;

    CarSecurityProductCodeEnum(String code,String name,String desc){
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static String getName(String code) {
        for(CarSecurityProductCodeEnum value : CarSecurityProductCodeEnum.values()){
            if(value.code.equals(code)){
                return value.name;
            }
        }
        return "";
    }
}
