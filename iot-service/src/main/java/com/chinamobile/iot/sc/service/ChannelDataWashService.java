package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Order2cAgentInfo;
import com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig;
import com.chinamobile.iot.sc.pojo.param.OrderChannelDataWashParam;
import com.chinamobile.iot.sc.pojo.param.UpdateOrderChannelParam;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> xiemaohua
 * @date : 2025/4/29 14:45
 * @description: 渠道商数据清洗接口类
 **/
public interface ChannelDataWashService {


    /**
     * 导入渠道商信息清洗
     * @param loginIfo4Redis
     * @param file
     */
    void importChannelDataWash(LoginIfo4Redis loginIfo4Redis, MultipartFile file);

    void exportOrderChannelDataWash(OrderChannelDataWashParam param);

    /**
     * 分页查询订单渠道商信息
     * @param param
     * @return
     */
    BaseAnswer<PageData<Order2cAgentInfo>> getOrderChannelDataWashList(OrderChannelDataWashParam param);

    /**
     * 单个订单渠道商信息清洗
     * @param param
     */
    void orderAccurateAgentWash(LoginIfo4Redis loginIfo4Redis,UpdateOrderChannelParam param);

}
