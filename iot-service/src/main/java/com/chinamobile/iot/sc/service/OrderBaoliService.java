package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.InfoToImportBaoliNotUseValidDTO;
import com.chinamobile.iot.sc.pojo.entity.OrderBaoli;
import com.chinamobile.iot.sc.pojo.entity.OrderBaoliExample;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.BaoliOrderVO;
import com.chinamobile.iot.sc.pojo.vo.OrderBaoliVO;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13
 * @description 保理订单service接口类
 */
public interface OrderBaoliService {

    /**
     * 获取保理订单标记列表
     * @param baoliOrderParam
     * @return
     */
    List<BaoliOrderVO> listBaoliOrder(BaoliOrderParam baoliOrderParam);

    /**
     * 分页获取保理订单标记列表
     * @param baoliOrderParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<BaoliOrderVO> pageBaoliOrder(BaoliOrderParam baoliOrderParam,
                                          LoginIfo4Redis loginIfo4Redis);

    /**
     * 获取导出保理不可用订单
     * @param baoliOrderExportParam
     * @param response
     */
    void exportBaoliOrderCanNotUse(BaoliOrderExportParam baoliOrderExportParam,
                                   HttpServletResponse response) throws IOException;

    /**
     * 改变为可用的保理订单信息
     * @param orderBaoliStatusChangeParam
     */
    void changeBaoliOrderStatus(OrderBaoliStatusChangeParam orderBaoliStatusChangeParam,String userId,String ip);

    /**
     * 导入保理不可用订单
     * @param file
     * @param request
     * @param response
     * @throws Exception
     */
    void importBaoliOrderCanNotUse(MultipartFile file,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception ;

    /**
     * 获取用于验证导入保理不可用订单数据
     * @param orderId
     * @return
     */
    List<InfoToImportBaoliNotUseValidDTO> getInfoToImportBaoliNotUseValid(String orderId);

    /**
     * 分页查询保理订单列表
     * @param orderBaoliParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<OrderBaoliVO> pageOrderBaoli(OrderBaoliParam orderBaoliParam,
                                          @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis);


    /**
     * 生成贸易订单
     * @param generateTradeOrderInfoParam
     * @param loginIfo4Redis
     * @return
     */
    List<String> generateTradeOrderInfo(GenerateTradeOrderInfoParam generateTradeOrderInfoParam,
                                        LoginIfo4Redis loginIfo4Redis,
                                        Boolean addLog,String ip);

    /**
     * 获取从合作伙伴用户id
     * @param userId
     * @return
     */
    List<String> listDownUserId(String userId);

    /**
     * 获取订单保理信息
     * @param orderBaoliExample
     * @return
     */
    List<OrderBaoli> listOrderBaoli(OrderBaoliExample orderBaoliExample);

    void exportBaoliOrder(LoginIfo4Redis loginIfo4Redis);

    void batchGenerateTradeOrder(MultipartFile file, LoginIfo4Redis loginIfo4Redis,String ip);
}
