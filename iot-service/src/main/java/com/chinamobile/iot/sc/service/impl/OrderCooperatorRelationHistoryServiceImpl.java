package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.dao.OrderCooperatorRelationHistoryMapper;
import com.chinamobile.iot.sc.dao.ext.OrderCooperatorRelationHistoryMapperExt;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryByGroupDTO;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryDTO;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationExample;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationHistory;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationHistoryExample;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoHistoryByGroupParam;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoHistoryParam;
import com.chinamobile.iot.sc.service.OrderCooperatorRelationHistoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 * @description 订单和从合作伙伴关系历史service实现类
 */
@Service
public class OrderCooperatorRelationHistoryServiceImpl implements OrderCooperatorRelationHistoryService {

    @Resource
    private OrderCooperatorRelationHistoryMapper orderCooperatorRelationHistoryMapper;

    @Resource
    private OrderCooperatorRelationHistoryMapperExt orderCooperatorRelationHistoryMapperExt;

    @Resource
    private UserFeignClient userFeignClient;

    @Override
    public void batchAddOrderCooperatorRelationHistory(List<OrderCooperatorRelationHistory> orderCooperatorRelationHistoryList) {
        orderCooperatorRelationHistoryMapper.batchInsert(orderCooperatorRelationHistoryList);
    }

    @Override
    public List<OrderCooperatorRelationHistory> listOrderCooperatorRelationHistoryByNeed(OrderCooperatorRelationHistoryExample orderCooperatorRelationHistoryExample) {
        return orderCooperatorRelationHistoryMapper.selectByExample(orderCooperatorRelationHistoryExample);
    }

    @Override
    public List<Data4User> listCooperatorHistoryUserInfo(String atomOrderId, String orderId) {

        if (StringUtils.isNotEmpty(atomOrderId)
                && StringUtils.isEmpty(orderId)){
            throw new RuntimeException("原子订单id和订单id不能同时为空");
        }

        OrderCooperatorRelationHistoryExample orderCooperatorRelationHistoryExample = new OrderCooperatorRelationHistoryExample();
        OrderCooperatorRelationHistoryExample.Criteria criteria = orderCooperatorRelationHistoryExample.createCriteria();
        if (StringUtils.isNotEmpty(atomOrderId)){
            criteria.andAtomOrderIdEqualTo(atomOrderId);
        }

        if (StringUtils.isNotEmpty(orderId)){
            criteria.andOrderIdEqualTo(orderId);
        }
        List<OrderCooperatorRelationHistory> orderCooperatorRelationHistoryList = listOrderCooperatorRelationHistoryByNeed(orderCooperatorRelationHistoryExample);
        if (CollectionUtils.isNotEmpty(orderCooperatorRelationHistoryList)){
            List<String> cooperatorIdList = orderCooperatorRelationHistoryList.stream()
                .map(OrderCooperatorRelationHistory::getCooperatorId)
                    .distinct()
                .collect(Collectors.toList());
            BaseAnswer<List<Data4User>> cooperatorBaseAnswer = userFeignClient.partnerInfoByIds(cooperatorIdList);

            if (cooperatorBaseAnswer == null
                    || !cooperatorBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                    || cooperatorBaseAnswer.getData() == null) {
                throw new RuntimeException("调用获取从合作伙伴信息失败。从合作伙伴ID:" + String.join(",",cooperatorIdList));
            }

            return cooperatorBaseAnswer.getData();
        }

        return null;
    }

    @Override
    public List<OrderCooperatorInfoHistoryByGroupDTO> listCooperatorInfoHistoryByGroup(OrderCooperatorInfoHistoryByGroupParam orderCooperatorInfoHistoryByGroupParam) {
        return orderCooperatorRelationHistoryMapperExt.listCooperatorInfoHistoryByGroup(orderCooperatorInfoHistoryByGroupParam);
    }

    @Override
    public List<OrderCooperatorInfoHistoryDTO> listCooperatorInfoHistory(OrderCooperatorInfoHistoryParam orderCooperatorInfoHistoryParam) {
        return orderCooperatorRelationHistoryMapperExt.listCooperatorInfoHistory(orderCooperatorInfoHistoryParam);
    }
}
