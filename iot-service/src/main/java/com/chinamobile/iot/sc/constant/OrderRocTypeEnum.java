package com.chinamobile.iot.sc.constant;

/**
 * @Author: YSC
 * @Date: 2021/12/20 18:04
 * @Description: OS系统内部状态，供前端使用
 */
public enum OrderRocTypeEnum {
    ORDER_REFUND(1, "用户申请退款", "用户申请", "", 0),
    REFUND_AGREE(2, "商家同意退款", "商家审核", "同意。", 1),
    REFUND_SUCCESS(3, "退款成功", "退款结果", "退款完成。", 2),
    REFUND_REJECT(4, "商家拒绝退款", "商家审核", "", 1),
    REFUND_CANCEL(5, "退款取消", "退款结果", "退款取消。", 2),
    REFUND_FAIL(6, "退款失败", "退款结果", "退款失败。", 2),
    ORDER_RETURN(7, "用户申请退货退款", "用户申请", "", 0),
    RETURN_AGREE(8, "商家同意退货退款", "商家审核", "同意。", 1),
    RETURN_REJECT(9, "商家拒绝退货退款", "商家审核", "", 1),
    ORDER_INSPECT(10, "用户申请验货", "用户发货", "已发货。", 2),
    INSPECT_AGREE(11, "商家验货成功", "商家验货", "验收成功。", 3),
    INSPECT_DISAGREE(12, "商家验货失败", "商家验货", "验收失败。", 3),
    RETURN_CANCEL(13, "退货退款取消", "退款结果", "退货退款取消。", 4),
    RETURN_SUCCESS(14, "退货退款成功", "退款结果", "退货退款成功。", 4),
    RETURN_FAIL(15, "退货退款失败", "退款结果", "退货退款失败。", 4),
    PART_SUCCESS(16, "部分退款成功", "退款结果", "部分退款成功。", 4);
    /**
     * 订单状态码
     */
    private final Integer status;
    /**
     * 订单状态信息
     */
    private final String message;
    /**
     * 售后进程
     */
    private final String process;
    /**
     * 订单历史描述
     */
    private final String history;
    /**
     * 售后进程码
     */
    private final Integer processStatus;

    OrderRocTypeEnum(int status, String message, String process, String history, Integer processStatus) {
        this.status = status;
        this.message = message;
        this.process = process;
        this.history = history;
        this.processStatus = processStatus;
    }


    public Integer getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public static String getDescribe(Integer status) {
        for (OrderRocTypeEnum value : OrderRocTypeEnum.values()) {
            if (value.status.equals(status)) {
                return value.message;
            }
        }
        return null;
    }

    public static String getHistoryDescribe(Integer status) {
        for (OrderRocTypeEnum value : OrderRocTypeEnum.values()) {
            if (value.status.equals(status)) {
                return value.history;
            }
        }
        return null;
    }

    public static String getProcessDescribe(Integer status) {
        for (OrderRocTypeEnum value : OrderRocTypeEnum.values()) {
            if (value.status.equals(status)) {
                return value.process;
            }
        }
        return null;
    }

    public static Integer getProcessStatus(Integer status) {
        for (OrderRocTypeEnum value : OrderRocTypeEnum.values()) {
            if (value.status.equals(status)) {
                return value.processStatus;
            }
        }
        return null;
    }
}
