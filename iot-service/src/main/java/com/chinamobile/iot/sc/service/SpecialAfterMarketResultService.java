package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.SpecialAfterMarketResult;
import com.chinamobile.iot.sc.pojo.vo.SpecialAfterMarketResultVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/11
 * @description 特殊售后服务历史记录service接口类
 */
public interface SpecialAfterMarketResultService {

    /**
     * 新增特殊售后服务历史记录
     * @param specialAfterMarketResult
     */
    void addSpecialAfterMarketResult(SpecialAfterMarketResult specialAfterMarketResult);

    /**
     * 获取特殊售后列表
     * @param orderId
     * @return
     */
    List<SpecialAfterMarketResultVO> listSpecialAfterMarketResult(String orderId);

}
