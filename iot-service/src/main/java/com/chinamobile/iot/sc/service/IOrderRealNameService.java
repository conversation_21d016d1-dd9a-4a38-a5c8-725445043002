package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.param.OrderRealNameUpdateParam;
import com.chinamobile.iot.sc.pojo.vo.OrderRealNameVO;
import org.springframework.web.bind.annotation.RequestAttribute;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2025/3/4 17:29
 * @description: 订单实名接口类
 **/
public interface IOrderRealNameService {

    /**
     * 根据订单id获取订单收货信息
     * @param orderId
     * @return
     */
   BaseAnswer<OrderRealNameVO> getOrderRealName(String orderId);

    /**
     * 修改订单收货信息
     * @param param
     * @return
     */
   BaseAnswer<Void> updateOrderRealName(OrderRealNameUpdateParam param,LoginIfo4Redis loginIfo4Redis);
}
