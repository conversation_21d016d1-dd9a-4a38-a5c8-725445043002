package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfoExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/10
 * @description 代客下单卡+X库存信息表service接口类
 */
public interface DkcardxInventoryInfoService {

    /**
     * 根据需要获取库存信息表
     * @param example
     * @return
     */
    List<DkcardxInventoryInfo> getDkcardxInventoryInfoByNeed(DkcardxInventoryInfoExample example);

    /**
     * 根据id更新库存信息表
     * @param dkcardxInventoryInfo
     */
    void updateDkcardxInventoryInfoById(DkcardxInventoryInfo dkcardxInventoryInfo);

    /**
     * 新增库存信息表
     * @param dkcardxInventoryInfo
     */
    void addDkcardxInventoryInfo(DkcardxInventoryInfo dkcardxInventoryInfo);

    /**
     * 批量新增库存信息表
     * @param dkcardxInventoryInfoList
     */
    void batchAddDkcardxInventoryInfo(List<DkcardxInventoryInfo> dkcardxInventoryInfoList);

    /**
     * 根据需要更新库存信息
     * @param dkcardxInventoryInfo
     * @param example
     */
    void updateDkcardxInventoryInfoByNeed(DkcardxInventoryInfo dkcardxInventoryInfo,
                                          DkcardxInventoryInfoExample example);
}
