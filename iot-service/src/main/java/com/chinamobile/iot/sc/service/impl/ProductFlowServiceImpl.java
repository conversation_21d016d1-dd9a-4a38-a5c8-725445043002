package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.productflow.ProductFlowLimitEnum;
import com.chinamobile.iot.sc.constant.productflow.ProductFlowTypeEnum;
import com.chinamobile.iot.sc.constant.productflow.ProductOperateTypeEnum;
import com.chinamobile.iot.sc.dao.ProductFlowMapper;
import com.chinamobile.iot.sc.dao.ProductFlowStepMapper;
import com.chinamobile.iot.sc.entity.user.RoleInfo;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.ProductManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.SystemInstallEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.ProductFlow;
import com.chinamobile.iot.sc.pojo.ProductFlowExample;
import com.chinamobile.iot.sc.pojo.ProductFlowStep;
import com.chinamobile.iot.sc.pojo.ProductFlowStepExample;
import com.chinamobile.iot.sc.pojo.param.EditFlowParam;
import com.chinamobile.iot.sc.pojo.param.ProductFlowListParam;
import com.chinamobile.iot.sc.pojo.vo.FlowRoleListVO;
import com.chinamobile.iot.sc.pojo.vo.FlowTypeListVO;
import com.chinamobile.iot.sc.pojo.vo.LimitListVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowDetailVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowListVO;
import com.chinamobile.iot.sc.pojo.vo.productFlowInfo.ProductFlowVO;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.ProductFlowInstanceService;
import com.chinamobile.iot.sc.service.ProductFlowService;
import com.chinamobile.iot.sc.util.ProductFlowUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2024/3/8 10:57
 */
@Service
@Slf4j
public class ProductFlowServiceImpl implements ProductFlowService {

    @Resource
    private ProductFlowMapper productFlowMapper;

    @Resource
    private ProductFlowStepMapper productFlowStepMapper;

    @Autowired
    private ProductFlowUtil productFlowUtil;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private ProductFlowInstanceService productFlowInstanceService;

    @Autowired
    private LogService logService;
    private ExecutorService executorService = new ThreadPoolExecutor(8, 16, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(100000));


    @Override
    public BaseAnswer<PageData<ProductFlowListVO>> getFlowList(ProductFlowListParam param, LoginIfo4Redis loginIfo4Redis) {
        PageData<ProductFlowListVO> pageData = new PageData<>();
        String flowNumber = param.getFlowNumber();
        Integer flowType = param.getFlowType();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum,pageSize);
        ProductFlowExample.Criteria criteria = new ProductFlowExample().createCriteria();
        if(StringUtils.isNotEmpty(flowNumber)){
            criteria.andNumberLike("%"+flowNumber+"%");
        }
        if(flowType != null){
            criteria.andFlowTypeEqualTo(flowType);
        }
        ProductFlowExample example = criteria.example();
        example.orderBy("create_time DESC");
        List<ProductFlow> productFlows = productFlowMapper.selectByExample(example);
        if(productFlows.isEmpty()){
            return BaseAnswer.success(pageData);
        }
        Map<String,String> creatorIdNameMap = new HashMap<>();
        for (ProductFlow productFlow : productFlows) {
            String creatorId = productFlow.getCreatorId();
            if(StringUtils.isEmpty(creatorId)){
                continue;
            }
            if(!creatorIdNameMap.containsKey(creatorId)){
                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(creatorId);
                if(data4UserBaseAnswer == null || data4UserBaseAnswer.getData() == null){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"用户id["+ creatorId +"]不存在");
                }
                creatorIdNameMap.put(creatorId,data4UserBaseAnswer.getData().getName());
            }
        }
        PageInfo<ProductFlow> pageInfo = new PageInfo<>(productFlows);
        List<ProductFlowListVO> collect = productFlows.stream().map(p -> {
            ProductFlowListVO vo = new ProductFlowListVO();
            BeanUtils.copyProperties(p, vo);
            String creatorId = p.getCreatorId();
            if(StringUtils.isEmpty(creatorId)){
                vo.setCreatorName("系统生成");
            }else {
                vo.setCreatorName(creatorIdNameMap.get(creatorId));
            }

            return vo;
        }).collect(Collectors.toList());
        pageData.setData(collect);
        pageData.setCount(pageInfo.getTotal());
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer<ProductFlowDetailVO> getFlowDetail(String flowId, LoginIfo4Redis loginIfo4Redis,Boolean addLog) {
        ProductFlow productFlow = productFlowMapper.selectByPrimaryKey(flowId);
        if(productFlow == null){
            logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,"【查看流程】", LogResultEnum.LOG_FAIL.code, "流程不存在");
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"流程不存在");
        }
        ProductFlowDetailVO vo = new ProductFlowDetailVO();
        BeanUtils.copyProperties(productFlow,vo);
        if(StringUtils.isNotEmpty(productFlow.getCreatorId())){
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(productFlow.getCreatorId());
            if(data4UserBaseAnswer == null || data4UserBaseAnswer.getData()== null){
                logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,"【查看流程】", LogResultEnum.LOG_FAIL.code, "创建人用户不存在");

                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"创建人用户不存在");
            }
            vo.setCreatorName(data4UserBaseAnswer.getData().getName());
        }else {
            vo.setCreatorName("系统生成");
        }
        //查询流程步骤
        ProductFlowStepExample example = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(flowId).example();
        example.orderBy("step_index ASC");
        List<ProductFlowStep> productFlowSteps = productFlowStepMapper.selectByExample(example);
        if(productFlowSteps != null){
            List<ProductFlowDetailVO.StepInfo> collect = productFlowSteps.stream().map(p -> {
                ProductFlowDetailVO.StepInfo stepInfo = new ProductFlowDetailVO.StepInfo();
                BeanUtils.copyProperties(p, stepInfo);
                if (stepInfo.getRejectNextStepId() != null) {
                    ProductFlowStep productFlowStep = productFlowStepMapper.selectByPrimaryKey(stepInfo.getRejectNextStepId());
                    if (productFlowStep == null) { logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,"【查看流程】", LogResultEnum.LOG_FAIL.code,  "环节" + stepInfo.getStepName() + "驳回后环节不存在");


                        throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "环节" + stepInfo.getStepName() + "驳回后环节不存在");
                    }
                    stepInfo.setRejectNextStepName(productFlowStep.getStepName());
                }
                //设置角色名称(逗号分隔多个)
                if(stepInfo.getAssigneeRoleId() != null){
                    String[] roleList = stepInfo.getAssigneeRoleId().split(",");
                    List<String> assigneeRoleName = new ArrayList<>();
                    for (String roleID : roleList) {
                        BaseAnswer<RoleInfo> roleInfo = userFeignClient.getRoleInfoByRoleIdMessage(roleID);
                        if(roleInfo == null || roleInfo.getData() == null){
                            logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,"【查看流程】", LogResultEnum.LOG_FAIL.code, "角色id:"+roleID+"不存在");

                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"角色id:"+roleID+"不存在");
                        }
                        assigneeRoleName.add(roleInfo.getData().getName());
                    }
                    stepInfo.setAssigneeRoleName(String.join(",",assigneeRoleName));
                }
                if(stepInfo.getKnownRoleId() != null){
                    String[] roleList = stepInfo.getKnownRoleId().split(",");
                    List<String> knownRoleName = new ArrayList<>();
                    for (String roleID : roleList) {
                        BaseAnswer<RoleInfo> roleInfo = userFeignClient.getRoleInfoByRoleIdMessage(roleID);
                        if(roleInfo == null || roleInfo.getData() == null){
                            logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,"【查看流程】", LogResultEnum.LOG_FAIL.code, "角色id:"+roleID+"不存在");

                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"角色id:"+roleID+"不存在");
                        }
                        knownRoleName.add(roleInfo.getData().getName());
                    }
                    stepInfo.setKnownRoleName(String.join(",",knownRoleName));
                }
                if(stepInfo.getRedirectRoleId() != null){
                    String[] roleList = stepInfo.getRedirectRoleId().split(",");
                    List<String> redirectRoleName = new ArrayList<>();
                    for (String roleID : roleList) {
                        BaseAnswer<RoleInfo> roleInfo = userFeignClient.getRoleInfoByRoleIdMessage(roleID);
                        if(roleInfo == null || roleInfo.getData() == null){
                            logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,"【查看流程】", LogResultEnum.LOG_FAIL.code, "角色id:"+roleID+"不存在");

                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"角色id:"+roleID+"不存在");
                        }
                        redirectRoleName.add(roleInfo.getData().getName());
                    }
                    stepInfo.setRedirectRoleName(String.join(",",redirectRoleName));
                }

                if (stepInfo.getLimitId() != null) {
                    ProductFlowLimitEnum productFlowLimitEnum = ProductFlowLimitEnum.fromCode(stepInfo.getLimitId());
                    if (productFlowLimitEnum == null) {
                        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,"【查看流程】", LogResultEnum.LOG_FAIL.code, "限制数据不存在:" + stepInfo.getLimitId());

                        throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "限制数据不存在:" + stepInfo.getLimitId());
                    }
                    stepInfo.setLimitName(productFlowLimitEnum.name);
                    stepInfo.setLimitResult(productFlowLimitEnum.desc);
                }
                return stepInfo;
            }).collect(Collectors.toList());
            vo.setStepInfoList(collect);
        }


        if(addLog){
            //记录日志
            String content = productFlowInstanceService.getLogContent("【查看流程】",vo.getNumber(),null,null,null,null,null,null,null,null,null);
            if(StringUtils.isNotEmpty(content)){
                logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,content, LogResultEnum.LOG_SUCESS.code, null);
            }
        }
        return BaseAnswer.success(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer init() {
        //省业管角色,是手动创建
        BaseAnswer<RoleInfo> provinceRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("省业管员", "province");
        if(provinceRoleInfo == null || provinceRoleInfo.getData() == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"省业管员 角色不存在");
        }
        String provinceRoleId = provinceRoleInfo.getData().getId();
        //运营支撑角色
        BaseAnswer<RoleInfo> operateAssistRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("运营支撑管理员", "os");
        if(operateAssistRoleInfo == null || operateAssistRoleInfo.getData() == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"运营支撑管理员 角色不存在");
        }
        String operateAssistRoleId = operateAssistRoleInfo.getData().getId();
        //产品经理
        BaseAnswer<RoleInfo> productManagerRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("产品经理", "os");
        if(productManagerRoleInfo == null || productManagerRoleInfo.getData() == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"产品经理 角色不存在");
        }
        String productManager = productManagerRoleInfo.getData().getId();
        //备案管理员
        BaseAnswer<RoleInfo> saveFileRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("备案管理员", "os");
        if(saveFileRoleInfo == null || saveFileRoleInfo.getData() == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"备案管理员 角色不存在");
        }
        String saveFileRoleId = saveFileRoleInfo.getData().getId();
        //产品运营管理员
        BaseAnswer<RoleInfo> productOperateAdminRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("产品运营管理员", "os");
        if(productOperateAdminRoleInfo == null || productOperateAdminRoleInfo.getData() == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"产品运营管理员 角色不存在");
        }
        String productOperateAdminRoleId = productOperateAdminRoleInfo.getData().getId();
        //产品运营负责人
        BaseAnswer<RoleInfo> productOperateResponsibleRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("产品运营负责人", "os");
        if(productOperateResponsibleRoleInfo == null || productOperateResponsibleRoleInfo.getData() == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"产品运营负责人 角色不存在");
        }
        String productOperateResponsibleRoleId = productOperateResponsibleRoleInfo.getData().getId();
        //配置专员
        BaseAnswer<RoleInfo> configWorkerRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("配置专员", "os");
        if(configWorkerRoleInfo == null || configWorkerRoleInfo.getData() == null){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"配置专员 角色不存在");
        }
        String configWorkerRoleId = configWorkerRoleInfo.getData().getId();

        List<ProductFlowStep> stepList =new ArrayList<>();
        List<ProductFlow> flowList =new ArrayList<>();

        //分省运营上架
        Date now = new Date();
        Integer operateType = ProductOperateTypeEnum.PROVINCE.code;
        Integer flowType = ProductFlowTypeEnum.PRODUCT_SHELF.code;
        ProductFlowExample provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        List<ProductFlow> provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请填报除销售价/计收外所有配置信息，请确认产品通路、供给合规正常");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("省业管审核");
            step2.setTip("请填写产品销售价、计收科目，请审批是否同意产品在本省上架");
            step2.setAssigneeRoleId(provinceRoleId);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(true);
            step2.setRedirectRoleId(provinceRoleId);
            step2.setAllowKnown(false);
            step2.setAllowLimit(false);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("运营支撑审核");
            step3.setTip("请核实计收项是否有依据,审核配置表正确性和完整性");
            step3.setAssigneeRoleId(operateAssistRoleId);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("备案审核");
            step4.setTip("请核实核心部件与备案信息一致性");
            step4.setAssigneeRoleId(saveFileRoleId);
            step4.setRejectNextStepId(step1.getId());
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);

            ProductFlowStep step5 = new ProductFlowStep();
            step5.setId(BaseServiceUtils.getId());
            step5.setFlowId(productFlow.getId());
            step5.setStepIndex(5);
            step5.setStepName("产品运营审核");
            step5.setTip("请审核产品命名合规性（SPU\\SKU定义），请审核产品上架素材合规性");
            step5.setAssigneeRoleId(productOperateAdminRoleId);
            step5.setRejectNextStepId(step1.getId());
            step5.setAllowRedirect(false);
            step5.setAllowKnown(false);
            step5.setAllowLimit(false);
            step5.setCreateTime(now);
            step5.setUpdateTime(now);
            stepList.add(step5);

            ProductFlowStep step6 = new ProductFlowStep();
            step6.setId(BaseServiceUtils.getId());
            step6.setFlowId(productFlow.getId());
            step6.setStepIndex(6);
            step6.setStepName("专员配置");
            step6.setTip("请完成配置并反馈信息");
            step6.setAssigneeRoleId(configWorkerRoleId);
            step6.setRejectNextStepId(null);
            step6.setAllowRedirect(false);
            step6.setAllowKnown(false);
            step6.setAllowLimit(false);
            step6.setCreateTime(now);
            step6.setUpdateTime(now);
            stepList.add(step6);
        }
        //为了排序的有序性，拉开时间
        now = new Date(now.getTime()+1000L);

        //分省运营下架
        operateType = ProductOperateTypeEnum.PROVINCE.code;
        flowType = ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code;
        provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请填写下架原因，发起下架流程");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("省业管审核");
            step2.setTip("请确认产品可下架");
            step2.setAssigneeRoleId(provinceRoleId);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(false);
            step2.setAllowKnown(true);
            step2.setKnownRoleId(productManager);
            step2.setAllowLimit(true);
            step2.setLimitId(1);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("运营支撑审核");
            step3.setTip("请确认信息完整，依据有效，商城可调整");
            step3.setAssigneeRoleId(operateAssistRoleId);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("专员配置");
            step4.setTip("请完成配置并反馈信息");
            step4.setAssigneeRoleId(configWorkerRoleId);
            step4.setRejectNextStepId(null);
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);
        }

        //分省运营-销售价变更
        now = new Date(now.getTime()+1000L);
        operateType = ProductOperateTypeEnum.PROVINCE.code;
        flowType = ProductFlowTypeEnum.SALE_PRICE_UPDATE.code;
        provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请修改产品销售价后发起流程");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("产品经理审核");
            step2.setTip("请确认产品销售价对产品体系无影响");
            step2.setAssigneeRoleId(productManager);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(false);
            step2.setAllowKnown(false);
            step2.setAllowLimit(false);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("运营支撑审核");
            step3.setTip("请核对产品信息修改完整性");
            step3.setAssigneeRoleId(operateAssistRoleId);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("专员配置");
            step4.setTip("请完成配置并反馈信息");
            step4.setAssigneeRoleId(configWorkerRoleId);
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);
        }

        //分省运营-结算价变更
        now = new Date(now.getTime()+1000L);
        operateType = ProductOperateTypeEnum.PROVINCE.code;
        flowType = ProductFlowTypeEnum.SETTLE_PRICE_UPDATE.code;
        provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请修改产品结算价后发起流程");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("省业管审核");
            step2.setTip("请确认产品结算可调整");
            step2.setAssigneeRoleId(provinceRoleId);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(false);
            step2.setAllowKnown(false);
            step2.setAllowLimit(false);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("运营支撑审核");
            step3.setTip("请确认信息完整，商城可调整");
            step3.setAssigneeRoleId(operateAssistRoleId);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("备案审核");
            step4.setTip("请确认修改后的结算条目备案信息与服务实际核心部件一致");
            step4.setAssigneeRoleId(saveFileRoleId);
            step4.setRejectNextStepId(step1.getId());
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);

            ProductFlowStep step5 = new ProductFlowStep();
            step5.setId(BaseServiceUtils.getId());
            step5.setFlowId(productFlow.getId());
            step5.setStepIndex(5);
            step5.setStepName("专员配置");
            step5.setTip("请完成配置并反馈信息");
            step5.setAssigneeRoleId(configWorkerRoleId);
            step5.setRejectNextStepId(null);
            step5.setAllowRedirect(false);
            step5.setAllowKnown(false);
            step5.setAllowLimit(false);
            step5.setCreateTime(now);
            step5.setUpdateTime(now);
            stepList.add(step5);
        }

        //分省运营-非价格信息变更
        now = new Date(now.getTime()+1000L);
        operateType = ProductOperateTypeEnum.PROVINCE.code;
        flowType = ProductFlowTypeEnum.OTHER_INFO_UPDATE.code;
        provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请修改非价格信息后发起流程");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("省业管审核");
            step2.setTip("请确认产品信息可更改");
            step2.setAssigneeRoleId(provinceRoleId);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(false);
            step2.setAllowKnown(false);
            step2.setAllowLimit(false);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("产品经理审核");
            step3.setTip("请确认产品信息更改对产品体系无影响");
            step3.setAssigneeRoleId(productManager);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("运营支撑审核");
            step4.setTip("请确认信息完整，商城可调整");
            step4.setAssigneeRoleId(operateAssistRoleId);
            step4.setRejectNextStepId(step1.getId());
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);

            ProductFlowStep step5 = new ProductFlowStep();
            step5.setId(BaseServiceUtils.getId());
            step5.setFlowId(productFlow.getId());
            step5.setStepIndex(5);
            step5.setStepName("产品运营审核");
            step5.setTip("请审核差评信息更改合规性，包括图片素材、名称等");
            step5.setAssigneeRoleId(productOperateAdminRoleId);
            step5.setRejectNextStepId(step1.getId());
            step5.setAllowRedirect(false);
            step5.setAllowKnown(false);
            step5.setAllowLimit(false);
            step5.setCreateTime(now);
            step5.setUpdateTime(now);
            stepList.add(step5);

            ProductFlowStep step6 = new ProductFlowStep();
            step6.setId(BaseServiceUtils.getId());
            step6.setFlowId(productFlow.getId());
            step6.setStepIndex(6);
            step6.setStepName("专员配置");
            step6.setTip("请完成配置并反馈信息");
            step6.setAssigneeRoleId(configWorkerRoleId);
            step6.setRejectNextStepId(null);
            step6.setAllowRedirect(false);
            step6.setAllowKnown(false);
            step6.setAllowLimit(false);
            step6.setCreateTime(now);
            step6.setUpdateTime(now);
            stepList.add(step6);
        }

        //统一运营-上架
        now = new Date(now.getTime()+1000L);
        operateType = ProductOperateTypeEnum.UNIFICATION.code;
        flowType = ProductFlowTypeEnum.PRODUCT_SHELF.code;
        provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请填报所有配置信息，请确认产品通路、供给合规正常");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("产品运营审核");
            step2.setTip("请审核产品命名合规性（SPU /SKU定义）、审核产品上架素材合规性及、确认上架依据有效");
            step2.setAssigneeRoleId(productOperateAdminRoleId);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(false);
            step2.setAllowKnown(true);
            step2.setKnownRoleId(operateAssistRoleId);
            step2.setAllowLimit(false);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("产品运营负责人审核");
            step3.setTip("请审核确认产品可上架商城");
            step3.setAssigneeRoleId(productOperateResponsibleRoleId);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("专员配置");
            step4.setTip("请完成配置并反馈信息");
            step4.setAssigneeRoleId(configWorkerRoleId);
            step4.setRejectNextStepId(null);
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);

        }

        //统一运营-下架
        now = new Date(now.getTime()+1000L);
        operateType = ProductOperateTypeEnum.UNIFICATION.code;
        flowType = ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code;
        provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请说明下架原因发起流程");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("运营支撑审核");
            step2.setTip("请审核产品下架依据合规");
            step2.setAssigneeRoleId(operateAssistRoleId);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(false);
            step2.setAllowKnown(false);
            step2.setAllowLimit(false);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("产品运营负责人审核");
            step3.setTip("请审核确认产品可下架");
            step3.setAssigneeRoleId(productOperateResponsibleRoleId);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("专员配置");
            step4.setTip("请完成配置并反馈信息");
            step4.setAssigneeRoleId(configWorkerRoleId);
            step4.setRejectNextStepId(null);
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);

        }

        //统一运营-所有信息变更
        now = new Date(now.getTime()+1000L);
        operateType = ProductOperateTypeEnum.UNIFICATION.code;
        flowType = ProductFlowTypeEnum.ALL_INFO_UPDATE.code;
        provinceShelfExample = new ProductFlowExample().createCriteria().andOperateTypeEqualTo(operateType).andFlowTypeEqualTo(flowType).andStatusEqualTo(0).example();
        provinceShelfList  = productFlowMapper.selectByExample(provinceShelfExample);
        if(CollectionUtils.isEmpty(provinceShelfList)){
            //同类型的有效流程只能有一条
            ProductFlow productFlow = new ProductFlow();
            productFlow.setId(BaseServiceUtils.getId());
            productFlow.setFlowType(flowType);
            productFlow.setOperateType(operateType);
            productFlow.setStatus(0);
            productFlow.setNumber(productFlowUtil.generateFlowNumber());
            productFlow.setCreatorId(null);
            productFlow.setCreateTime(now);
            productFlow.setUpdateTime(now);
            flowList.add(productFlow);

            //保存流程步骤
            ProductFlowStep step1 = new ProductFlowStep();
            step1.setId(BaseServiceUtils.getId());
            step1.setFlowId(productFlow.getId());
            step1.setStepIndex(1);
            step1.setStepName("流程发起");
            step1.setTip("请修改产品信息后发起流程");
            step1.setAllowRedirect(false);
            step1.setAllowKnown(false);
            step1.setAllowLimit(false);
            step1.setCreateTime(now);
            step1.setUpdateTime(now);
            stepList.add(step1);

            ProductFlowStep step2 = new ProductFlowStep();
            step2.setId(BaseServiceUtils.getId());
            step2.setFlowId(productFlow.getId());
            step2.setStepIndex(2);
            step2.setStepName("运营支撑审核");
            step2.setTip("请审核产品信息修改后符合合规性要求，产品信息修改依据合规");
            step2.setAssigneeRoleId(operateAssistRoleId);
            step2.setRejectNextStepId(step1.getId());
            step2.setAllowRedirect(false);
            step2.setAllowKnown(false);
            step2.setAllowLimit(false);
            step2.setCreateTime(now);
            step2.setUpdateTime(now);
            stepList.add(step2);

            ProductFlowStep step3 = new ProductFlowStep();
            step3.setId(BaseServiceUtils.getId());
            step3.setFlowId(productFlow.getId());
            step3.setStepIndex(3);
            step3.setStepName("产品运营负责人审核");
            step3.setTip("请审核确认产品信息可修改");
            step3.setAssigneeRoleId(productOperateResponsibleRoleId);
            step3.setRejectNextStepId(step1.getId());
            step3.setAllowRedirect(false);
            step3.setAllowKnown(false);
            step3.setAllowLimit(false);
            step3.setCreateTime(now);
            step3.setUpdateTime(now);
            stepList.add(step3);

            ProductFlowStep step4 = new ProductFlowStep();
            step4.setId(BaseServiceUtils.getId());
            step4.setFlowId(productFlow.getId());
            step4.setStepIndex(4);
            step4.setStepName("专员配置");
            step4.setTip("请完成配置并反馈信息");
            step4.setAssigneeRoleId(configWorkerRoleId);
            step4.setRejectNextStepId(null);
            step4.setAllowRedirect(false);
            step4.setAllowKnown(false);
            step4.setAllowLimit(false);
            step4.setCreateTime(now);
            step4.setUpdateTime(now);
            stepList.add(step4);

        }

        if(!flowList.isEmpty()){
            productFlowMapper.batchInsert(flowList);
        }
        if(!stepList.isEmpty()){
            productFlowStepMapper.batchInsert(stepList);
        }

        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<List<FlowTypeListVO>> getFlowTypeList() {
        ProductFlowTypeEnum[] values = ProductFlowTypeEnum.values();
        List<FlowTypeListVO> collect = Arrays.stream(values).map(p -> {
            FlowTypeListVO vo = new FlowTypeListVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer editFlow(EditFlowParam param, LoginIfo4Redis loginIfo4Redis) {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String ip = org.apache.commons.lang3.StringUtils.isNotEmpty(request.getHeader(Constant.IP))? request.getHeader(Constant.IP) : "127.0.0.1";
        Date now = new Date();
        String id = param.getId();
        MultipartFile imageFile = param.getImageFile();
        InputStream inputStream = null;
        try {
            if(imageFile != null){
                checkFileSuffix(imageFile);
                try {
                    inputStream = imageFile.getInputStream();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            List<ProductFlowDetailVO.StepInfo> stepInfoList = param.getStepInfoList();
            ProductFlow productFlow = productFlowMapper.selectByPrimaryKey(id);
            if(productFlow == null){
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,
                            "【修改流程】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"流程不存在");
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"流程不存在");
            }
            if(productFlow.getStatus() == 1){
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,
                            "【修改流程】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"流程已失效，不允许修改");
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"流程已失效，不允许修改");
            }

            //新建流程
            ProductFlow newFlow = new ProductFlow();
            BeanUtils.copyProperties(productFlow,newFlow);
            newFlow.setId(BaseServiceUtils.getId());
            newFlow.setCreatorId(loginIfo4Redis.getUserId());
            newFlow.setCreateTime(now);
            newFlow.setStatus(0);
            newFlow.setNumber(productFlowUtil.generateFlowNumber());
            newFlow.setUpdateTime(now);
            productFlowMapper.insert(newFlow);

            if(CollectionUtils.isNotEmpty(stepInfoList)){
                //新建流程步骤
                List<ProductFlowStep> stepList = stepInfoList.stream().map(stepInfo -> {
                    if(stepInfo.getStepIndex().intValue() != 1 && stepInfo.getAssigneeRoleId() == null){
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,
                                    "【修改流程】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"除了第一个环节外，其他环节必须指定处理人角色");
                        });
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"除了第一个环节外，其他环节必须指定处理人角色");
                    }
                    if(stepInfo.getAllowRedirect() != null && stepInfo.getAllowRedirect() && StringUtils.isEmpty(stepInfo.getRedirectRoleId())){
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,
                                    "【修改流程】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"转办角色不能为空");
                        });
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"转办角色不能为空");
                    }
                    if(stepInfo.getAllowKnown() != null && stepInfo.getAllowKnown() && StringUtils.isEmpty(stepInfo.getKnownRoleId())){
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,
                                    "【修改流程】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"知悉角色不能为空");
                        });
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"知悉角色不能为空");
                    }
                    if(stepInfo.getAllowLimit() != null && stepInfo.getAllowLimit() && stepInfo.getLimitId() == null){
                        executorService.execute(() -> {
                            logService.recordOperateLogAsync(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,
                                    "【修改流程】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,"限制条件不能为空");
                        });
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"限制条件不能为空");
                    }
                    ProductFlowStep step = new ProductFlowStep();
                    BeanUtils.copyProperties(stepInfo,step);
                    step.setCreateTime(now);
                    step.setUpdateTime(now);
                    step.setId(BaseServiceUtils.getId());
                    step.setFlowId(newFlow.getId());
                    return step;
                }).collect(Collectors.toList());
                //驳回后环节，只能是流程发起环节，也就是index=1的环节
                ProductFlowStep firstStep = stepList.stream().filter(s -> {
                    return s.getStepIndex().intValue() == 1;
                }).collect(Collectors.toList()).get(0);
                String firstStepId = firstStep.getId();
                for (ProductFlowStep flowStep : stepList) {
                    flowStep.setRejectNextStepId(firstStepId);
                }
                productFlowStepMapper.batchInsert(stepList);
            }

            //将之前的流程设置为失效
            productFlow.setStatus(1);
            productFlow.setUpdateTime(now);
            productFlowMapper.updateByPrimaryKey(productFlow);

            //记录日志
            String content = productFlowInstanceService.getLogContent("【修改流程】",null,null,null,null,null,null,null,null,productFlow.getNumber(),newFlow.getNumber());
            if(StringUtils.isNotEmpty(content)){
                logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.FLOW_CENTER.code,content,LogResultEnum.LOG_SUCESS.code, null);
            }
        } finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }


        return BaseAnswer.success(null);
    }

    private void checkFileSuffix(MultipartFile imageFile) {
        String filename = imageFile.getOriginalFilename();
        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
        if(StringUtils.isEmpty(suffix)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"文件格式错误");
        }
        String[] suffixWhiteList = {"png"};
        boolean fileSuffixRight = Arrays.stream(suffixWhiteList).anyMatch(s -> {
            return s.equalsIgnoreCase(suffix);
        });
        if(!fileSuffixRight){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"文件格式只允许"+String.join(",",suffixWhiteList));
        }
    }

    @Override
    public BaseAnswer<List<LimitListVO>> getLimitList() {
        ProductFlowLimitEnum[] values = ProductFlowLimitEnum.values();
        List<LimitListVO> collect = Arrays.stream(values).map(p -> {
            LimitListVO vo = new LimitListVO();
            BeanUtils.copyProperties(p, vo);
            vo.setId(p.getCode());
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<List<FlowRoleListVO>> getFlowRoleList() {
        List<FlowRoleListVO> list = new ArrayList<>();
        //省业管角色,是手动创建
        BaseAnswer<RoleInfo> provinceRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("省业管员", "province");
        if (provinceRoleInfo == null || provinceRoleInfo.getData() == null) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "省业管员 角色不存在");
        }
        String provinceRoleId = provinceRoleInfo.getData().getId();
        list.add(new FlowRoleListVO(provinceRoleId, "省业管员"));
        //运营支撑角色
        BaseAnswer<RoleInfo> operateAssistRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("运营支撑管理员", "os");
        if (operateAssistRoleInfo == null || operateAssistRoleInfo.getData() == null) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "运营支撑管理员 角色不存在");
        }
        String operateAssistRoleId = operateAssistRoleInfo.getData().getId();
        list.add(new FlowRoleListVO(operateAssistRoleId, "运营支撑管理员"));
        //产品经理
        BaseAnswer<RoleInfo> productManagerRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("产品经理", "os");
        if (productManagerRoleInfo == null || productManagerRoleInfo.getData() == null) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "产品经理 角色不存在");
        }
        String productManager = productManagerRoleInfo.getData().getId();
        list.add(new FlowRoleListVO(productManager, "产品经理"));
        //备案管理员
        BaseAnswer<RoleInfo> saveFileRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("备案管理员", "os");
        if (saveFileRoleInfo == null || saveFileRoleInfo.getData() == null) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "备案管理员 角色不存在");
        }
        String saveFileRoleId = saveFileRoleInfo.getData().getId();
        list.add(new FlowRoleListVO(saveFileRoleId, "备案管理员"));
        //产品运营管理员
        BaseAnswer<RoleInfo> productOperateAdminRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("产品运营管理员", "os");
        if (productOperateAdminRoleInfo == null || productOperateAdminRoleInfo.getData() == null) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "产品运营管理员 角色不存在");
        }
        String productOperateAdminRoleId = productOperateAdminRoleInfo.getData().getId();
        list.add(new FlowRoleListVO(productOperateAdminRoleId, "产品运营管理员"));
        //产品运营负责人
        BaseAnswer<RoleInfo> productOperateResponsibleRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("产品运营负责人", "os");
        if (productOperateResponsibleRoleInfo == null || productOperateResponsibleRoleInfo.getData() == null) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "产品运营负责人 角色不存在");
        }
        String productOperateResponsibleRoleId = productOperateResponsibleRoleInfo.getData().getId();
        list.add(new FlowRoleListVO(productOperateResponsibleRoleId, "产品运营负责人"));
        //配置专员
        BaseAnswer<RoleInfo> configWorkerRoleInfo = userFeignClient.getRoleInfoByNameAndSystemMessage("配置专员", "os");
        if (configWorkerRoleInfo == null || configWorkerRoleInfo.getData() == null) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "配置专员 角色不存在");
        }
        String configWorkerRoleId = configWorkerRoleInfo.getData().getId();
        list.add(new FlowRoleListVO(configWorkerRoleId, "配置专员"));
        return BaseAnswer.success(list);
    }


    private byte[] inputStream2ByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        outputStream.close();
        return outputStream.toByteArray();
    }
}
