package com.chinamobile.iot.sc.constant.softService;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/10
 * @description 软件服务开通状态枚举类
 */
public enum SoftServiceOpenStatusEnum {

    OPEN_SUCESS(0,"开通成功"),
    OPEN_FAIL(1,"开通失败"),
    OPENING(2,"开通中");

    /**
     * 开通状态类型
     */
    private Integer type;

    /**
     * 开通状态描述
     */
    private String desc;

    SoftServiceOpenStatusEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(Integer type) {
        for (SoftServiceOpenStatusEnum value : SoftServiceOpenStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
