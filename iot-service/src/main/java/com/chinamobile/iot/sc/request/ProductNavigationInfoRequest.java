package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/1 16:00
 * @Description:
 */
@Data
public class ProductNavigationInfoRequest {
    /**
     * 导航目录菜单,
     * 1:物联网导航目录
     * 2:视联网导航目录
     */
    private String menu;
    /**
     * 一级导航目录信息
     */
    private List<Level1Navigation> level1Navigation;

    @Data
    public static class Level1Navigation {
        /**
         * 一级导航目录编
         */
        private String level1NavigationCode;
        /**
         * 一级导航目录名称
         */
        private String level1NavigationName;

        /**
         * 二级导航目录信息
         */
        private List<Level2Navigation> level2Navigation;
    }


    @Data
    public static class Level2Navigation {
        /**
         * 二级导航目录编码
         * */
        private String level2NavigationCode;
        /**
         * 二级导航目录名称
         * */
        private String level2NavigationName;

        /**
         * 三级级导航目录信息
         */
        private List<Level3Navigation> level3Navigation;
    }

    @Data
    public static class Level3Navigation{
        /**
         * 三级级导航目录编码
         */
        private String level3NavigationCode;

        /**
         * 三级级导航目录名称
         */
        private String level3NavigationName;
    }
}
