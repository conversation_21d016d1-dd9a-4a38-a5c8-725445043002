package com.chinamobile.iot.sc.constant.productflow;

public enum ProductOperateTypeEnum {

    PROVINCE(1,"分省运营类"),
    UNIFICATION(2,"统一运营类"),
    ;


    public Integer code;
    public String name;

    ProductOperateTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
