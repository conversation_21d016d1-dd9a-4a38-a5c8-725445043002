package com.chinamobile.iot.sc.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: YSC
 * @Date: 2021/11/10 10:07
 * @Description: 合作伙伴添加实体类
 */
@Data
public class CooperAddRequest {
    /**
     * atom id
     */
    @NotBlank(message = "原子商品ID不能为空")
    private String atomId;
    /**
     * cooper_id
     */
    @NotBlank(message = "合作伙伴ID不能为空")
    private String cooperatorId;
}
