package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/29 15:10
 *
 * 商品状态数据割接(spu, sku, atom)
 */
@Data
public class SpuStatusCutOver {

    @ExcelProperty(value="OFFERING_ID",index=0)
    private String offeringId;

    @ExcelProperty(value="OFFERING_CODE",index=1)
    private String offeringCode;

    @ExcelProperty(value="OFFERING_NAME",index=2)
    private String offeringName;
    
    @ExcelProperty(value="OFFERING_SHORT_NAME",index=3)
    private String offeringShortName;

    @ExcelProperty(value="STATUS",index=4)
    private String status;

    @ExcelProperty(value="类型",index=5)
    private String type;
}
