package com.chinamobile.iot.sc.constant.softService;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/10
 * @description 软件服务使用中状态枚举类
 */
public enum SoftServiceAtomStatusEnum {

    OPEN_SUCESS(0,"开通成功"),
    OPEN_FAIL(1,"开通失败"),
    OPENING(2,"开通中"),
    RETAIL_SUCESS(3,"退订成功"),
    RETAIL_FAIL(4,"退订失败"),
    RETAILING(5,"退订中"),
    USEING(6,"使用中");

    /**
     * 使用中状态类型
     */
    private Integer type;

    /**
     * 使用中状态描述
     */
    private String desc;

    SoftServiceAtomStatusEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(Integer type) {
        for (SoftServiceAtomStatusEnum value : SoftServiceAtomStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
