package com.chinamobile.iot.sc.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * @Author: YSC
 * @Date: 2021/12/14 10:40
 * @Description:
 */
@Data
public class AuditRefundRequest {

    /**
     * 退款订单请求流水号
     */
    @NotBlank(message = "请输入退款订单请求流水号")
    private String refundOrderId;

    /**
     * 订单ID
     */
    @NotBlank(message = "请输入订单ID")
    private String orderId;

    /**
     * 售后类型 1 仅退款 2 退货退款 3 换货
     */
    private Integer rocType = 1;
    /**
     * 审核结果
     * 1 通过
     * 2 不通过
     */
    @NotBlank(message = "请输入审核结果")
    private String auditResult;

    /**
     * 合作伙伴地址ID
     */
    private String partnerAddressId;
    /**
     * 审核原因
     */
    private String auditResultReason = "";
    /**
     * 销售类型
     */
    private String spuOfferingClass = "";
    /**
     * 确认冲红
     */
    private boolean ignoreInvoice;
}
