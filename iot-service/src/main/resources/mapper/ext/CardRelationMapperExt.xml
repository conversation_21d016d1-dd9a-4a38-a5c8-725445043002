<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.CardRelationMapperExt">

    <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.dto.CardRelationXDTO">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="imei" jdbcType="VARCHAR" property="imei" />
        <result column="temp_iccid" jdbcType="VARCHAR" property="tempIccid" />
        <result column="client_name" jdbcType="VARCHAR" property="clientName" />
        <result column="product_num" jdbcType="VARCHAR" property="productNum" />
        <result column="sn" jdbcType="VARCHAR" property="sn" />
        <result column="order_id" jdbcType="VARCHAR" property="orderId" />
        <result column="order_atom_info_id" jdbcType="VARCHAR" property="orderAtomInfoId" />
        <result column="msisdn" jdbcType="VARCHAR" property="msisdn" />
        <result column="sell_status" jdbcType="VARCHAR" property="sellStatus" />
        <result column="terminal_type" jdbcType="VARCHAR" property="terminalType" />
        <result column="be_id" jdbcType="VARCHAR" property="beId" />
        <result column="location" jdbcType="VARCHAR" property="location" />
        <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
        <result column="mis_num" jdbcType="VARCHAR" property="misNum" />
        <result column="order_source" jdbcType="VARCHAR" property="orderSource" />
        <result column="channel" jdbcType="VARCHAR" property="channel" />
        <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
        <result column="supply" jdbcType="VARCHAR" property="supply" />
        <result column="contract_term" jdbcType="VARCHAR" property="contractTerm" />
        <result column="termianl_owner" jdbcType="VARCHAR" property="termianlOwner" />
        <result column="device_carton_num" jdbcType="VARCHAR" property="deviceCartonNum" />
        <result column="device_attr" jdbcType="VARCHAR" property="deviceAttr" />
        <result column="terminal_condition" jdbcType="VARCHAR" property="terminalCondition" />
        <result column="inventory_flag" jdbcType="VARCHAR" property="inventoryFlag" />
        <result column="use_mode" jdbcType="VARCHAR" property="useMode" />
        <result column="physics_flag" jdbcType="VARCHAR" property="physicsFlag" />
        <result column="condition_time" jdbcType="VARCHAR" property="conditionTime" />
        <result column="teminal_create_time" jdbcType="VARCHAR" property="teminalCreateTime" />
        <result column="teminal_remark" jdbcType="VARCHAR" property="teminalRemark" />
        <result column="recycle_source" jdbcType="VARCHAR" property="recycleSource" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="provinceName" jdbcType="VARCHAR" property="provinceName" />
        <result column="cityName" jdbcType="VARCHAR" property="cityName" />
        <result column="orderCardProvinceName" jdbcType="VARCHAR" property="orderCardProvinceName" />
        <result column="orderCardCityName" jdbcType="VARCHAR" property="orderCardCityName" />
        <result column="templateName" jdbcType="VARCHAR" property="templateName" />
        <result column="cardDeliverTime" jdbcType="TIMESTAMP" property="cardDeliverTime" />
        <result column="logisCode" jdbcType="VARCHAR" property="logisCode" />
        <result column="addr1" jdbcType="VARCHAR" property="addr1" />
        <result column="addr2" jdbcType="VARCHAR" property="addr2" />
        <result column="addr3" jdbcType="VARCHAR" property="addr3" />
        <result column="addr4" jdbcType="VARCHAR" property="addr4" />
        <result column="usaddr" jdbcType="VARCHAR" property="usaddr" />
        <result column="contactPersonName" jdbcType="VARCHAR" property="contactPersonName" />
        <result column="contactPhone" jdbcType="VARCHAR" property="contactPhone" />
        <result column="chaba_msisdn" jdbcType="VARCHAR" property="chabaMsisdn" />
        <result column="importNum" jdbcType="VARCHAR" property="importNum" />
        <result column="createdUserName" jdbcType="VARCHAR" property="createdUserName" />
        <result column="createdUser" jdbcType="VARCHAR" property="createdUser" />
        <result column="deleteTime" jdbcType="TIMESTAMP" property="deleteTime" />
    </resultMap>

    <select id="listCardRelationX" resultMap="BaseResultMap">
        <!--select * from
        (-->
        <!--select
        cr.id, cr.imei, cr.temp_iccid, cr.client_name, cr.product_num, cr.sn, cr.order_id, cr.order_atom_info_id,o2i.order_type,
        case
        when o2i.order_type in ('00','02','03') and cr.terminal_type = 3 then (SELECT GROUP_CONCAT( msisdn ) FROM sku_msisdn_relation where order_id=o2i.order_id)
        when cr.terminal_type = 0 then cr.chaba_msisdn else cr.msisdn end msisdn, cr.sell_status, cr.terminal_type, cr.be_id, cr.location, cr.device_version, cr.mis_num, cr.order_source,
        cr.channel, cr.device_type, cr.supply, cr.contract_term, cr.termianl_owner, cr.device_carton_num, cr.device_attr,
        cr.terminal_condition, cr.inventory_flag, cr.use_mode, cr.physics_flag, cr.condition_time, cr.teminal_create_time,
        cr.teminal_remark, cr.recycle_source, cr.create_time, cr.update_time,cpic.mall_name provinceName,ccic.mall_name cityName,
        cpio.mall_name orderCardProvinceName,ccio.mall_name orderCardCityName,cr.template_name templateName,cr.card_deliver_time cardDeliverTime,
        ( SELECT GROUP_CONCAT( logis_code ) FROM logistics_info WHERE order_id = o2ai.order_id AND order_atom_info_id = o2ai.id AND logistics_type = 0 ) logisCode,
        o2i.addr1,o2i.addr2,o2i.addr3,o2i.addr4,o2i.usaddr,
        o2i.contact_person_name contactPersonName,o2i.contact_phone contactPhone,
        cr.chaba_msisdn,cr.import_num importNum,cr.created_user_name createdUserName,
        cr.created_user createdUser,cr.delete_time deleteTime
        from card_relation cr
        left join contract_province_info cpic on cr.be_id = cpic.mall_code
        left join contract_city_info ccic on cr.location = ccic.mall_code
        left join order_2c_info o2i on cr.order_id = o2i.order_id
        left join order_2c_atom_info o2ai on cr.order_id = o2ai.order_id and cr.order_atom_info_id = o2ai.id
        left join contract_province_info cpio on o2i.reserve_be_id = cpio.mall_code
        left join contract_city_info ccio on o2i.reserve_location = ccio.mall_code
        &lt;!&ndash;下面2个join是用于卡+X库存配置时的过滤条件 &ndash;&gt;
        left join card_mall_sync cms on cr.msisdn = cms.msisdn
        left join card_info ci on cms.card_info_id = ci.id and cr.be_id = ci.be_id
        where 1=1
        and (cr.location is null or cr.location = '')
        and cr.delete_time is null
        union-->
        select
        cr.id, cr.imei, cr.temp_iccid, cr.client_name, cr.product_num, cr.sn, cr.order_id, cr.order_atom_info_id,o2i.order_type,
        case
        when o2i.order_type in ('00','02','03') and cr.terminal_type = 3 then (SELECT GROUP_CONCAT( msisdn ) FROM sku_msisdn_relation where order_id=o2i.order_id)
        when cr.terminal_type = 0 then cr.chaba_msisdn else cr.msisdn end msisdn, cr.sell_status, cr.terminal_type, cr.be_id, cr.location, cr.device_version, cr.mis_num, cr.order_source,
        cr.channel, cr.device_type, cr.supply, cr.contract_term, cr.termianl_owner, cr.device_carton_num, cr.device_attr,
        cr.terminal_condition, cr.inventory_flag, cr.use_mode, cr.physics_flag, cr.condition_time, cr.teminal_create_time,
        cr.teminal_remark, cr.recycle_source, cr.create_time, cr.update_time,cpic.mall_name provinceName,ccic.mall_name cityName,
        cpio.mall_name orderCardProvinceName,ccio.mall_name orderCardCityName,cr.template_name templateName,cr.card_deliver_time cardDeliverTime,
        ( SELECT GROUP_CONCAT( logis_code ) FROM logistics_info WHERE order_id = o2ai.order_id AND order_atom_info_id = o2ai.id AND logistics_type = 0 ) logisCode,
        o2i.addr1,o2i.addr2,o2i.addr3,o2i.addr4,o2i.usaddr,
        o2i.contact_person_name contactPersonName,o2i.contact_phone contactPhone,
        cr.chaba_msisdn,cr.import_num importNum,cr.created_user_name createdUserName,
        cr.created_user createdUser,cr.delete_time deleteTime
        from card_relation cr
        left join contract_province_info cpic on cr.be_id = cpic.mall_code
        left join contract_city_info ccic on cr.location = ccic.mall_code
        left join order_2c_info o2i on cr.order_id = o2i.order_id
        left join order_2c_atom_info o2ai on cr.order_id = o2ai.order_id and cr.order_atom_info_id = o2ai.id
        left join contract_province_info cpio on o2i.reserve_be_id = cpio.mall_code
        left join contract_city_info ccio on o2i.reserve_location = ccio.mall_code
        <!--下面2个join是用于卡+X库存配置时的过滤条件 -->
        left join card_mall_sync cms on cr.msisdn = cms.msisdn
        left join card_info ci on cms.card_info_id = ci.id and cr.be_id = ci.be_id
        where 1=1
        <!--and cr.location is not null and cr.location != ''-->
        and cr.delete_time is null
        <if test="cardRelationXParam.location != null and cardRelationXParam.location != ''">
            and (cr.location = #{cardRelationXParam.location} or (cr.location is null or cr.location = ''))
        </if>
        <!--) cm
        where 1=1-->
        <if test="cardRelationXParam.terminalType != null and cardRelationXParam.terminalType != ''">
            and cr.terminal_type = #{cardRelationXParam.terminalType}
        </if>
        <if test="cardRelationXParam.terminalTypeList != null and cardRelationXParam.terminalTypeList.size() != 0">
            and cr.terminal_type in
            <foreach collection="cardRelationXParam.terminalTypeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationXParam.imei != null and cardRelationXParam.imei != ''">
            and cr.imei = #{cardRelationXParam.imei}
        </if>
        <if test="cardRelationXParam.msisdn != null and cardRelationXParam.msisdn != ''">
            and (case
            when o2i.order_type in ('00','02','03') and cr.terminal_type = 3 then (SELECT GROUP_CONCAT( msisdn ) FROM sku_msisdn_relation where order_id=o2i.order_id)
            when cr.terminal_type = 0 then cr.chaba_msisdn else cr.msisdn end) like '%${cardRelationXParam.msisdn}%'
        </if>
        <if test="cardRelationXParam.tempIccid != null and cardRelationXParam.tempIccid != ''">
            and cr.temp_iccid like '%${cardRelationXParam.tempIccid}%'
        </if>
        <if test="cardRelationXParam.beId != null and cardRelationXParam.beId != ''">
            and cr.be_id = #{cardRelationXParam.beId}
        </if>
        <if test="cardRelationXParam.beIdList != null and cardRelationXParam.beIdList.size() != 0">
            and cr.be_id in
            <foreach collection="cardRelationXParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationXParam.sellStatus != null and cardRelationXParam.sellStatus != ''">
            and cr.sell_status = #{cardRelationXParam.sellStatus}
        </if>
        <if test="cardRelationXParam.sellStatusList != null and cardRelationXParam.sellStatusList.size() != 0">
            and cr.sell_status in
            <foreach collection="cardRelationXParam.sellStatusList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationXParam.deviceVersion != null and cardRelationXParam.deviceVersion != ''">
            and cr.device_version = #{cardRelationXParam.deviceVersion}
        </if>
        <if test="cardRelationXParam.templateName != null and cardRelationXParam.templateName != ''">
            and cr.template_name = #{cardRelationXParam.templateName}
        </if>
        <if test="cardRelationXParam.pageTemplateName != null and cardRelationXParam.pageTemplateName != ''">
            and cr.template_name like '%${cardRelationXParam.pageTemplateName}%'
        </if>
        <if test="cardRelationXParam.configInventory != null and cardRelationXParam.configInventory == 1">
            and cr.imei not in (select imei from dkcardx_inventory_config)
        </if>
        <if test="cardRelationXParam.clientName != null and cardRelationXParam.clientName != ''">
            and cr.client_name like '%${cardRelationXParam.clientName}%'
        </if>
        <if test="cardRelationXParam.beginDate != null and cardRelationXParam.beginDate !=  ''">
            and cr.teminal_create_time <![CDATA[ >= ]]> #{cardRelationXParam.beginDate}
        </if>
        <if test="cardRelationXParam.endDate != null and cardRelationXParam.endDate !=  ''">
            and cr.teminal_create_time <![CDATA[ <= ]]> #{cardRelationXParam.endDate}
        </if>
        <if test="cardRelationXParam.importNum != null and cardRelationXParam.importNum != ''">
            and cr.import_num like '%${cardRelationXParam.importNum}%'
        </if>
        <if test="cardRelationXParam.createdUserName != null and cardRelationXParam.createdUserName != ''">
            and cr.created_user_name like '%${cardRelationXParam.createdUserName}%'
        </if>
        <if test="cardRelationXParam.beginCreateTime != null and cardRelationXParam.beginCreateTime !=  ''">
            and DATE_FORMAT(cr.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationXParam.beginCreateTime}
        </if>
        <if test="cardRelationXParam.endCreateTime != null and cardRelationXParam.endCreateTime !=  ''">
            and DATE_FORMAT(cr.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationXParam.endCreateTime}
        </if>
        order by cr.create_time desc
    </select>

  <select id="listCardRelation" resultType="com.chinamobile.iot.sc.pojo.vo.CardRelationVO">
    SELECT
        imei,
        temp_iccid tempIccid,
        client_name clientName,
        product_num productNum,
        sn,
        create_time createTime
    FROM
        card_relation
    where 1 = 1
    and delete_time is null
    <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
      and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
    </if>
    <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
      and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.endDate}
    </if>
    <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
      and imei like '%${cardRelationParam.imei}%'
    </if>
    <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
      and temp_iccid like '%${cardRelationParam.tempIccid}%'
    </if>
    <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
      and client_name like '%${cardRelationParam.clientName}%'
    </if>
    order by create_time desc
  </select>

    <select id="listOrderCard" resultType="com.chinamobile.iot.sc.pojo.vo.OrderCardVO">
        select
        orderId,orderType,orderTypeName,
        sellMode,cardDeliverTime,logisCode,
        addr1,addr2,addr3,addr4,usaddr,
        contactPersonName,contactPhone,
        terminalType,imei,tempIccid,
        clientName,msisdn,deviceVersion,
        provinceName,cityName,
        orderCardProvinceName,orderCardCityName,
        sellStatus,productNum,sn,orderResult,createTime,
        custName,custCode,iccid
        from
        (
        SELECT
        smr.order_id orderId,
        o2ai.order_type orderType,
        CASE
        WHEN o2ai.order_type in ('00', '02','03') THEN '代客下单'
        WHEN o2ai.order_type = '01' THEN '自主下单'
        ELSE '未知类型'
        END orderTypeName,
        CASE
        WHEN o2ai.order_type = '00' THEN '商城直销'
        WHEN o2ai.order_type = '02' THEN '省内融合集团客户订单'
        WHEN o2ai.order_type = '03' THEN '省内融合个人客户订单'
        ELSE ''
        END sellMode,
        cr.card_deliver_time cardDeliverTime,
        (SELECT
        GROUP_CONCAT(logis_code)
        FROM
        logistics_info
        WHERE
        order_id = o2ai.order_id
        AND order_atom_info_id = o2ai.id
        AND logistics_type = 0) logisCode,
        o2i.addr1,
        o2i.addr2,
        o2i.addr3,
        o2i.addr4,
        o2i.usaddr,
        o2i.contact_person_name contactPersonName,
        o2i.contact_phone contactPhone,
        cr.terminal_type terminalType,
        smr.imei,
        smr.temp_iccid tempIccid,
        cr.client_name clientName,
        smr.msisdn,
        cr.device_version deviceVersion,
        cpic.mall_name provinceName,
        ccic.mall_name cityName,
        cpio.mall_name orderCardProvinceName,
        ccio.mall_name orderCardCityName,
        cr.sell_status sellStatus,
        cr.product_num productNum,
        cr.sn,
        CASE
        WHEN smr.order_result IS NULL THEN '暂无结果'
        WHEN smr.order_result = 1 THEN '成功'
        WHEN smr.order_result = 2 THEN '失败'
        ELSE '暂无结果'
        END orderResult,
        o2i.create_time createTime,
        o2i.be_id beId,
        o2i.location,
        <!--o2ai.cooperator_id cooperatorId,-->
        o2i.cust_name custName,
        o2i.cust_code custCode,
        cms.iccid
        FROM
        sku_msisdn_relation smr
        INNER JOIN order_2c_atom_info o2ai ON smr.order_id = o2ai.order_id
        AND smr.order_atom_info_id = o2ai.id
        AND o2ai.order_type = '01'
        INNER JOIN card_relation cr ON smr.order_id = cr.order_id
        AND smr.order_atom_info_id = cr.order_atom_info_id
        AND smr.imei = cr.imei
        AND smr.temp_iccid = cr.temp_iccid
        AND cr.delete_time IS NULL
        AND cr.terminal_type = 3
        INNER JOIN order_2c_info o2i ON o2ai.order_id = o2i.order_id
        INNER JOIN sku_offering_info_history soih ON o2ai.sku_offering_code = soih.offering_code
        AND o2ai.spu_offering_version = soih.spu_offering_version
        AND o2ai.sku_offering_version = soih.sku_offering_version
        AND soih.product_type IN ('1','2','3')
        LEFT JOIN card_mall_sync cms ON cms.order_id = o2ai.order_id
        AND cms.atom_order_id = o2ai.id
        AND smr.msisdn = cms.msisdn
        LEFT JOIN contract_province_info cpic ON cr.be_id = cpic.mall_code
        LEFT JOIN contract_city_info ccic ON cr.location = ccic.mall_code
        LEFT JOIN contract_province_info cpio ON o2i.reserve_be_id = cpio.mall_code
        LEFT JOIN contract_city_info ccio ON o2i.reserve_location = ccio.mall_code
        <if test="cardRelationParam.cooperatorIdList != null">
            inner join (
            select ocr.atom_order_id,ocr.order_id
            where
            1=1
            and ocr.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        where 1=1
        <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
            and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
        </if>
        <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
            and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
        </if>
        <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
        </if>
        <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
        </if>
        <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
            and cr.imei like '%${cardRelationParam.imei}%'
        </if>
        <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
            and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
        </if>
        <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
            and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
            else cr.msisdn like '%${cardRelationParam.msisdn}%'
            end
        </if>
        <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
            and cr.client_name like '%${cardRelationParam.clientName}%'
        </if>
        <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
            and o2i.be_id = #{cardRelationParam.beId}
        </if>
        <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
            and o2i.location = #{cardRelationParam.location}
        </if>
        <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
            and o2i.location in
            <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            and cms.card_type = #{cardRelationParam.cardType}
        </if>
        <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
            and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
        </if>
        <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
            and o2i.cust_name = #{cardRelationParam.orderCustName}
        </if>
        <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
            and cr.device_version like '%${cardRelationParam.deviceVersion}%'
        </if>
        <!--<if test="cardRelationParam.cooperatorIdList != null">
            and o2ai.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>-->
        union
        SELECT
        smr.order_id orderId,
        o2ai.order_type orderType,
        CASE
        WHEN o2ai.order_type in ('00', '02','03') THEN '代客下单'
        WHEN o2ai.order_type = '01' THEN '自主下单'
        ELSE '未知类型'
        END orderTypeName,
        CASE
        WHEN o2ai.order_type = '00' THEN '商城直销'
        WHEN o2ai.order_type = '02' THEN '省内融合集团客户订单'
        WHEN o2ai.order_type = '03' THEN '省内融合个人客户订单'
        ELSE ''
        END sellMode,
        cr.card_deliver_time cardDeliverTime,
        (SELECT
        GROUP_CONCAT(logis_code)
        FROM
        logistics_info
        WHERE
        order_id = o2ai.order_id
        AND order_atom_info_id = o2ai.id
        AND logistics_type = 0) logisCode,
        o2i.addr1,
        o2i.addr2,
        o2i.addr3,
        o2i.addr4,
        o2i.usaddr,
        o2i.contact_person_name contactPersonName,
        o2i.contact_phone contactPhone,
        cr.terminal_type terminalType,
        smr.imei,
        smr.temp_iccid tempIccid,
        cr.client_name clientName,
        smr.msisdn,
        cr.device_version deviceVersion,
        cpic.mall_name provinceName,
        ccic.mall_name cityName,
        cpio.mall_name orderCardProvinceName,
        ccio.mall_name orderCardCityName,
        cr.sell_status sellStatus,
        cr.product_num productNum,
        cr.sn,
        CASE
        WHEN smr.order_result IS NULL THEN '暂无结果'
        WHEN smr.order_result = 1 THEN '成功'
        WHEN smr.order_result = 2 THEN '失败'
        ELSE '暂无结果'
        END orderResult,
        o2i.create_time createTime,
        o2i.be_id beId,
        o2i.location,
        <!--o2ai.cooperator_id cooperatorId,-->
        o2i.cust_name custName,
        o2i.cust_code custCode,
        cms.iccid
        FROM
        sku_msisdn_relation smr
        INNER JOIN order_2c_atom_info o2ai ON smr.order_id = o2ai.order_id
        AND smr.order_atom_info_id = o2ai.id
        AND o2ai.order_type = '01'
        INNER JOIN card_relation cr ON smr.order_id = cr.order_id
        AND smr.order_atom_info_id = cr.order_atom_info_id
        AND cr.delete_time IS NULL
        AND cr.terminal_type = 3
        INNER JOIN order_2c_info o2i ON o2ai.order_id = o2i.order_id
        INNER JOIN sku_offering_info_history soih ON o2ai.sku_offering_code = soih.offering_code
        AND o2ai.spu_offering_version = soih.spu_offering_version
        AND o2ai.sku_offering_version = soih.sku_offering_version
        AND soih.product_type IN ('4','5','6','7','8','9','10','11','12')
        LEFT JOIN card_mall_sync cms ON cms.order_id = o2ai.order_id
        AND cms.atom_order_id = o2ai.id
        AND smr.msisdn = cms.msisdn
        LEFT JOIN contract_province_info cpic ON cr.be_id = cpic.mall_code
        LEFT JOIN contract_city_info ccic ON cr.location = ccic.mall_code
        LEFT JOIN contract_province_info cpio ON o2i.reserve_be_id = cpio.mall_code
        LEFT JOIN contract_city_info ccio ON o2i.reserve_location = ccio.mall_code
        <if test="cardRelationParam.cooperatorIdList != null">
            inner join (
            select ocr.atom_order_id,ocr.order_id
            where
            1=1
            and ocr.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        where 1=1
        <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
            and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
        </if>
        <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
            and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
        </if>
        <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
        </if>
        <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
        </if>
        <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
            and cr.imei like '%${cardRelationParam.imei}%'
        </if>
        <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
            and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
        </if>
        <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
            and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
            else cr.msisdn like '%${cardRelationParam.msisdn}%'
            end
        </if>
        <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
            and cr.client_name like '%${cardRelationParam.clientName}%'
        </if>
        <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
            and o2i.be_id = #{cardRelationParam.beId}
        </if>
        <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
            and o2i.location = #{cardRelationParam.location}
        </if>
        <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
            and o2i.location in
            <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            and cms.card_type = #{cardRelationParam.cardType}
        </if>
        <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
            and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
        </if>
        <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
            and o2i.cust_name = #{cardRelationParam.orderCustName}
        </if>
        <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
            and cr.device_version like '%${cardRelationParam.deviceVersion}%'
        </if>
        <!--<if test="cardRelationParam.cooperatorIdList != null">
            and o2ai.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>-->
        union
        SELECT
        smr.order_id orderId,
        o2ai.order_type orderType,
        CASE
        WHEN o2ai.order_type in ('00', '02','03') THEN '代客下单'
        WHEN o2ai.order_type = '01' THEN '自主下单'
        ELSE '未知类型'
        END orderTypeName,
        CASE
        WHEN o2ai.order_type = '00' THEN '商城直销'
        WHEN o2ai.order_type = '02' THEN '省内融合集团客户订单'
        WHEN o2ai.order_type = '03' THEN '省内融合个人客户订单'
        ELSE ''
        END sellMode,
        cr.card_deliver_time cardDeliverTime,
        (SELECT
        GROUP_CONCAT(logis_code)
        FROM
        logistics_info
        WHERE
        order_id = o2ai.order_id
        AND order_atom_info_id = o2ai.id
        AND logistics_type = 0) logisCode,
        o2i.addr1,
        o2i.addr2,
        o2i.addr3,
        o2i.addr4,
        o2i.usaddr,
        o2i.contact_person_name contactPersonName,
        o2i.contact_phone contactPhone,
        cr.terminal_type terminalType,
        cr.imei,
        cr.temp_iccid tempIccid,
        cr.client_name clientName,
        smr.msisdn,
        cr.device_version deviceVersion,
        cpic.mall_name provinceName,
        ccic.mall_name cityName,
        cpio.mall_name orderCardProvinceName,
        ccio.mall_name orderCardCityName,
        cr.sell_status sellStatus,
        cr.product_num productNum,
        cr.sn,
        '' orderResult,
        o2i.create_time createTime,
        o2i.be_id beId,
        o2i.location,
        <!--o2ai.cooperator_id cooperatorId,-->
        o2i.cust_name custName,
        o2i.cust_code custCode,
        cms.iccid
        FROM
        <!--card_mall_sync cms, card_info ci,-->
        sku_msisdn_relation smr
        JOIN order_2c_atom_info o2ai ON smr.order_id = o2ai.order_id AND smr.order_atom_info_id = o2ai.id
        JOIN card_relation cr ON smr.order_id = cr.order_id AND smr.order_atom_info_id = cr.order_atom_info_id
        AND cr.delete_time IS NULL
        AND cr.terminal_type = 3
        JOIN order_2c_info o2i ON o2ai.order_id = o2i.order_id
        JOIN sku_offering_info_history soih ON o2ai.sku_offering_code = soih.offering_code
        AND o2ai.spu_offering_version = soih.spu_offering_version
        AND o2ai.sku_offering_version = soih.sku_offering_version
        LEFT JOIN card_mall_sync cms ON cms.order_id = o2ai.order_id AND cms.atom_order_id = o2ai.id
        AND smr.msisdn = cms.msisdn
        LEFT JOIN contract_province_info cpic ON cr.be_id = cpic.mall_code
        LEFT JOIN contract_city_info ccic ON cr.location = ccic.mall_code
        LEFT JOIN contract_province_info cpio ON o2i.reserve_be_id = cpio.mall_code
        LEFT JOIN contract_city_info ccio ON o2i.reserve_location = ccio.mall_code
        <if test="cardRelationParam.cooperatorIdList != null">
            inner join (
            select ocr.atom_order_id,ocr.order_id
            where
            1=1
            and ocr.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        WHERE 1=1
        <!--cms.card_info_id = ci.id
        AND cms.order_id = o2ai.order_id
        AND cms.atom_order_id = o2ai.id
        AND cms.order_id = cr.order_id
        AND cms.atom_order_id = cr.order_atom_info_id
        AND cr.terminal_type = cms.card_type-->
        AND o2ai.order_type IN ('00' , '02', '03')

        <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
            and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
        </if>
        <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
            and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
        </if>
        <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
        </if>
        <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
        </if>
        <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
            and cr.imei like '%${cardRelationParam.imei}%'
        </if>
        <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
            and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
        </if>
        <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
            and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
            else cr.msisdn like '%${cardRelationParam.msisdn}%'
            end
        </if>
        <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
            and cr.client_name like '%${cardRelationParam.clientName}%'
        </if>
        <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
            and o2i.be_id = #{cardRelationParam.beId}
        </if>
        <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
            and o2i.location = #{cardRelationParam.location}
        </if>
        <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
            and o2i.location in
            <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            and cms.card_type = #{cardRelationParam.cardType}
        </if>
        <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
            and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
        </if>
        <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
            and o2i.cust_name = #{cardRelationParam.orderCustName}
        </if>
        <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
            and cr.device_version like '%${cardRelationParam.deviceVersion}%'
        </if>
        <!--<if test="cardRelationParam.cooperatorIdList != null">
            and o2ai.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>-->
        union
        select
        o2ai.order_id orderId,
        o2ai.order_type orderType,
        CASE
        WHEN o2ai.order_type in ('00', '02','03') THEN '代客下单'
        WHEN o2ai.order_type = '01' THEN '自主下单'
        ELSE '未知类型'
        END orderTypeName,
        CASE
        WHEN o2ai.order_type = '00' THEN '商城直销'
        WHEN o2ai.order_type = '02' THEN '省内融合集团客户订单'
        WHEN o2ai.order_type = '03' THEN '省内融合个人客户订单'
        ELSE ''
        END sellMode,
        cr.card_deliver_time cardDeliverTime,
        (SELECT
        GROUP_CONCAT(logis_code)
        FROM
        logistics_info
        WHERE
        order_id = o2ai.order_id
        AND order_atom_info_id = o2ai.id
        AND logistics_type = 0) logisCode,
        o2i.addr1,
        o2i.addr2,
        o2i.addr3,
        o2i.addr4,
        o2i.usaddr,
        o2i.contact_person_name contactPersonName,
        o2i.contact_phone contactPhone,
        cr.terminal_type terminalType,
        cr.imei,
        cr.temp_iccid tempIccid,
        cr.client_name clientName,
        case
        when cr.terminal_type = 0 then cr.chaba_msisdn
        else cr.msisdn
        end msisdn,
        cr.device_version deviceVersion,
        cpic.mall_name provinceName,
        ccic.mall_name cityName,
        cpio.mall_name orderCardProvinceName,
        ccio.mall_name orderCardCityName,
        cr.sell_status sellStatus,
        cr.product_num productNum,
        cr.sn,
        '' orderResult,
        o2i.create_time createTime,
        o2i.be_id beId,
        o2i.location,
        <!--o2ai.cooperator_id cooperatorId,-->
        o2i.cust_name custName,
        o2i.cust_code custCode,
        cms.iccid
        from
        card_relation cr
        LEFT JOIN contract_province_info cpic ON cr.be_id = cpic.mall_code
        LEFT JOIN contract_city_info ccic ON cr.location = ccic.mall_code
        JOIN order_2c_atom_info o2ai ON cr.order_id = o2ai.order_id AND cr.order_atom_info_id = o2ai.id
        LEFT JOIN card_mall_sync cms ON cms.order_id = o2ai.order_id AND cms.atom_order_id = o2ai.id
        and  case
        when cr.terminal_type = 0 then cr.chaba_msisdn
        else cr.msisdn
        end = cms.msisdn
        JOIN sku_offering_info_history soih ON o2ai.sku_offering_code = soih.offering_code
        AND o2ai.spu_offering_version = soih.spu_offering_version
        AND o2ai.sku_offering_version = soih.sku_offering_version
        JOIN order_2c_info o2i ON o2ai.order_id = o2i.order_id
        LEFT JOIN contract_province_info cpio ON o2i.reserve_be_id = cpio.mall_code
        LEFT JOIN contract_city_info ccio ON o2i.reserve_location = ccio.mall_code
        <if test="cardRelationParam.cooperatorIdList != null">
            inner join (
            select ocr.atom_order_id,ocr.order_id
            where
            1=1
            and ocr.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        where
            cr.delete_time is null
        AND cr.terminal_type IN (0 , 1)
        AND (o2ai.order_type IN ('00' , '02','03') or (o2ai.order_type = '01' and soih.product_type in ('4','5','6','7','8','9','10','11','12')) )

        <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
            and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
        </if>
        <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
            and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
        </if>
        <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
        </if>
        <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
        </if>
        <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
            and cr.imei like '%${cardRelationParam.imei}%'
        </if>
        <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
            and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
        </if>
        <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
            and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
            else cr.msisdn like '%${cardRelationParam.msisdn}%'
            end
        </if>
        <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
            and cr.client_name like '%${cardRelationParam.clientName}%'
        </if>
        <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
            and o2i.be_id = #{cardRelationParam.beId}
        </if>
        <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
            and o2i.location = #{cardRelationParam.location}
        </if>
        <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
            and o2i.location in
            <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            and cms.card_type = #{cardRelationParam.cardType}
        </if>
        <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
            and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
        </if>
        <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
            and o2i.cust_name = #{cardRelationParam.orderCustName}
        </if>
        <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
            and cr.device_version like '%${cardRelationParam.deviceVersion}%'
        </if>
        <!--<if test="cardRelationParam.cooperatorIdList != null">
            and o2ai.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>-->
        ) oc
        where 1=1
        order by createTime desc
    </select>

    <select id="getNullCardCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (
        SELECT 1
            FROM sku_msisdn_relation smr
            INNER JOIN order_2c_atom_info o2ai ON smr.order_id = o2ai.order_id
                AND smr.order_atom_info_id = o2ai.id
                AND o2ai.order_type = '01'
            <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
                inner join card_mall_sync cms on cms.order_id = o2ai.order_id and cms.atom_order_id = o2ai.id
            </if>
            <if test="cardRelationParam.cooperatorIdList != null">
                inner join (
                select  ocr.atom_order_id,ocr.order_id
                from order_cooperator_relation ocr
                where
                1=1
                and ocr.cooperator_id in
                <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                    #{item}
                </foreach>
                group by ocr.atom_order_id
                ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
            </if>
            INNER JOIN card_relation cr ON smr.order_id = cr.order_id
                AND smr.order_atom_info_id = cr.order_atom_info_id
                AND (smr.imei = cr.imei OR (smr.imei IS NULL AND cr.imei IS NULL))
                AND (smr.temp_iccid = cr.temp_iccid OR (smr.temp_iccid IS NULL AND cr.temp_iccid IS NULL))
            INNER JOIN order_2c_info o2i ON o2ai.order_id = o2i.order_id
            INNER JOIN sku_offering_info_history soih ON o2ai.sku_offering_code = soih.offering_code
                AND o2ai.spu_offering_version = soih.spu_offering_version
                AND o2ai.sku_offering_version = soih.sku_offering_version
                AND soih.product_type IN ('1','2','3')
            WHERE cr.terminal_type = 3
                AND cr.delete_time IS NULL
            <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
                and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
            </if>
            <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
                and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
            </if>
            <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
                and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
            </if>
            <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
                and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
            </if>
            <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
                and cr.imei like '%${cardRelationParam.imei}%'
            </if>
            <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
                and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
            </if>
            <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
                and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
                else cr.msisdn like '%${cardRelationParam.msisdn}%'
                end
            </if>
            <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
                and cr.client_name like '%${cardRelationParam.clientName}%'
            </if>
            <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
                and o2i.be_id = #{cardRelationParam.beId}
            </if>
            <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
                and o2i.location = #{cardRelationParam.location}
            </if>
            <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
                and o2i.location in
                <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
                and cms.card_type = #{cardRelationParam.cardType}
            </if>
            <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
                and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
            </if>
            <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
                and o2i.cust_name = #{cardRelationParam.orderCustName}
            </if>
            <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
                and cr.device_version like '%${cardRelationParam.deviceVersion}%'
            </if>
            <!--<if test="cardRelationParam.cooperatorIdList != null">
                and o2ai.cooperator_id in
                <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                    #{item}
                </foreach>
            </if>-->
            GROUP BY
            COALESCE(smr.order_id, 'NULL'),
            COALESCE(smr.imei, 'NULL'),
            COALESCE(smr.temp_iccid, 'NULL'),
            COALESCE(smr.msisdn, 'NULL')
        ) AS oc
    </select>

    <select id="getNullCardNewProductTypeCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM (
            SELECT 1
            FROM sku_msisdn_relation smr
            INNER JOIN order_2c_atom_info o2ai ON
                smr.order_id = o2ai.order_id AND
                smr.order_atom_info_id = o2ai.id AND
                o2ai.order_type = '01'
            <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
                inner join card_mall_sync cms on cms.order_id = o2ai.order_id and cms.atom_order_id = o2ai.id
            </if>
            <if test="cardRelationParam.cooperatorIdList != null">
                inner join (
                select ocr.atom_order_id,ocr.order_id
                from order_cooperator_relation ocr
                where
                1=1
                and ocr.cooperator_id in
                <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                    #{item}
                </foreach>
                group by ocr.atom_order_id
                ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
            </if>
            INNER JOIN card_relation cr ON
                smr.order_id = cr.order_id AND
                smr.order_atom_info_id = cr.order_atom_info_id AND
                cr.terminal_type = 3 AND
                cr.delete_time IS NULL
            INNER JOIN order_2c_info o2i ON
                o2ai.order_id = o2i.order_id
            INNER JOIN sku_offering_info_history soih ON
                o2ai.sku_offering_code = soih.offering_code AND
                o2ai.spu_offering_version = soih.spu_offering_version AND
                o2ai.sku_offering_version = soih.sku_offering_version AND
                soih.product_type IN ('4','5','6','7','8','9','10','11','12')
            where 1=1
            <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
                and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
            </if>
            <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
                and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
            </if>
            <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
                and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
            </if>
            <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
                and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
            </if>
            <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
                and cr.imei like '%${cardRelationParam.imei}%'
            </if>
            <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
                and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
            </if>
            <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
                and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
                else cr.msisdn like '%${cardRelationParam.msisdn}%'
                end
            </if>
            <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
                and cr.client_name like '%${cardRelationParam.clientName}%'
            </if>
            <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
                and o2i.be_id = #{cardRelationParam.beId}
            </if>
            <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
                and o2i.location = #{cardRelationParam.location}
            </if>
            <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
                and o2i.location in
                <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
                and cms.card_type = #{cardRelationParam.cardType}
            </if>
            <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
                and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
            </if>
            <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
                and o2i.cust_name = #{cardRelationParam.orderCustName}
            </if>
            <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
                and cr.device_version like '%${cardRelationParam.deviceVersion}%'
            </if>
            <!--<if test="cardRelationParam.cooperatorIdList != null">
                and o2ai.cooperator_id in
                <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                    #{item}
                </foreach>
            </if>-->
            GROUP BY
                COALESCE(smr.order_id, 'NULL'),
                COALESCE(smr.imei, 'NULL'),
                COALESCE(smr.temp_iccid, 'NULL'),
                COALESCE(smr.msisdn, 'NULL')
        ) AS oc
    </select>

    <select id="getNullCardCustomerCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM (
        SELECT
        smr.order_id,
        COALESCE(cr.imei, '') AS imei,
        COALESCE(cr.temp_iccid, '') AS temp_iccid,
        COALESCE(smr.msisdn, '') AS msisdn
        FROM sku_msisdn_relation smr
        INNER JOIN card_relation cr ON smr.order_id = cr.order_id
        AND smr.order_atom_info_id = cr.order_atom_info_id
        AND cr.delete_time IS NULL
        AND cr.terminal_type = 3
        INNER JOIN order_2c_atom_info o2ai ON smr.order_id = o2ai.order_id
        AND smr.order_atom_info_id = o2ai.id
        AND o2ai.order_type IN ('00', '02', '03')
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            inner join card_mall_sync cms on cms.order_id = o2ai.order_id and cms.atom_order_id = o2ai.id
        </if>
        <if test="cardRelationParam.cooperatorIdList != null">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_2c_atom_info o2aii
            inner join order_cooperator_relation ocr on o2aii.id = ocr.atom_order_id and o2aii.order_id = ocr.order_id
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where
            1=1
            and ocr.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
            group by o2aii.id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        INNER JOIN order_2c_info o2i ON o2ai.order_id = o2i.order_id
        INNER JOIN sku_offering_info_history soih ON o2ai.sku_offering_code = soih.offering_code
        AND o2ai.spu_offering_version = soih.spu_offering_version
        AND o2ai.sku_offering_version = soih.sku_offering_version
        where 1=1
        <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
            and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
        </if>
        <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
            and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
        </if>
        <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
        </if>
        <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
        </if>
        <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
            and cr.imei like '%${cardRelationParam.imei}%'
        </if>
        <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
            and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
        </if>
        <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
            and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
            else cr.msisdn like '%${cardRelationParam.msisdn}%'
            end
        </if>
        <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
            and cr.client_name like '%${cardRelationParam.clientName}%'
        </if>
        <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
            and o2i.be_id = #{cardRelationParam.beId}
        </if>
        <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
            and o2i.location = #{cardRelationParam.location}
        </if>
        <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
            and o2i.location in
            <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            and cms.card_type = #{cardRelationParam.cardType}
        </if>
        <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
            and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
        </if>
        <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
            and o2i.cust_name = #{cardRelationParam.orderCustName}
        </if>
        <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
            and cr.device_version like '%${cardRelationParam.deviceVersion}%'
        </if>
        <!--<if test="cardRelationParam.cooperatorIdList != null">
            and o2ai.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>-->
        GROUP BY
        smr.order_id,
        COALESCE(cr.imei, ''),
        COALESCE(cr.temp_iccid, ''),
        COALESCE(smr.msisdn, '')
        ) t
    </select>

    <select id="getNotNullCardCustomerCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (
        SELECT DISTINCT
        cr.order_id,
        cr.imei,
        cr.temp_iccid,
        CASE WHEN cr.terminal_type = 0 THEN cr.chaba_msisdn ELSE cr.msisdn END
        FROM card_relation cr
        INNER JOIN order_2c_atom_info o2ai
        ON cr.order_id = o2ai.order_id
        AND cr.order_atom_info_id = o2ai.id
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            inner join card_mall_sync cms on cms.order_id = o2ai.order_id and cms.atom_order_id = o2ai.id
        </if>
        <if test="cardRelationParam.cooperatorIdList != null">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_2c_atom_info o2aii
            inner join order_cooperator_relation ocr on o2aii.id = ocr.atom_order_id and o2aii.order_id = ocr.order_id
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where
            1=1
            and ocr.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
            group by o2aii.id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        INNER JOIN order_2c_info o2i
        ON o2ai.order_id = o2i.order_id
        INNER JOIN sku_offering_info_history soih
        ON o2ai.sku_offering_code = soih.offering_code
        AND o2ai.spu_offering_version = soih.spu_offering_version
        AND o2ai.sku_offering_version = soih.sku_offering_version
        WHERE cr.delete_time IS NULL
        AND cr.terminal_type IN (0, 1)
        AND (
        o2ai.order_type IN ('00', '02', '03')
        OR (o2ai.order_type = '01' AND soih.product_type IN ('4','5','6','7','8','9','10','11','12'))
        )
        <if test="cardRelationParam.beginDate != null and cardRelationParam.beginDate !=  ''">
            and o2i.create_time <![CDATA[ >= ]]> #{cardRelationParam.beginDate}
        </if>
        <if test="cardRelationParam.endDate != null and cardRelationParam.endDate !=  ''">
            and o2i.create_time <![CDATA[ <= ]]> #{cardRelationParam.endDate}
        </if>
        <if test="cardRelationParam.deliverBeginDate != null and cardRelationParam.deliverBeginDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationParam.deliverBeginDate}
        </if>
        <if test="cardRelationParam.deliverEndDate != null and cardRelationParam.deliverEndDate !=  ''">
            and DATE_FORMAT(cr.card_deliver_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationParam.deliverEndDate}
        </if>
        <if test="cardRelationParam.imei != null and cardRelationParam.imei != ''">
            and cr.imei like '%${cardRelationParam.imei}%'
        </if>
        <if test="cardRelationParam.tempIccid != null and cardRelationParam.tempIccid != ''">
            and cr.temp_iccid like '%${cardRelationParam.tempIccid}%'
        </if>
        <if test="cardRelationParam.msisdn != null and cardRelationParam.msisdn != ''">
            and when cr.terminal_type = 0 then cr.chaba_msisdn like '%${cardRelationParam.msisdn}%'
            else cr.msisdn like '%${cardRelationParam.msisdn}%'
            end
        </if>
        <if test="cardRelationParam.clientName != null and cardRelationParam.clientName != ''">
            and cr.client_name like '%${cardRelationParam.clientName}%'
        </if>
        <if test="cardRelationParam.beId != null and cardRelationParam.beId != ''">
            and o2i.be_id = #{cardRelationParam.beId}
        </if>
        <if test="cardRelationParam.location != null and cardRelationParam.location != ''">
            and o2i.location = #{cardRelationParam.location}
        </if>
        <if test="cardRelationParam.locationList != null and cardRelationParam.locationList.size() != 0">
            and o2i.location in
            <foreach collection="cardRelationParam.locationList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardRelationParam.cardType != null and cardRelationParam.cardType != ''">
            and cms.card_type = #{cardRelationParam.cardType}
        </if>
        <if test="cardRelationParam.skuOfferingName != null and cardRelationParam.skuOfferingName != ''">
            and soih.offering_name like '%${cardRelationParam.skuOfferingName}%'
        </if>
        <if test="cardRelationParam.orderCustName != null and cardRelationParam.orderCustName != ''">
            and o2i.cust_name = #{cardRelationParam.orderCustName}
        </if>
        <if test="cardRelationParam.deviceVersion != null and cardRelationParam.deviceVersion != ''">
            and cr.device_version like '%${cardRelationParam.deviceVersion}%'
        </if>
        <!--<if test="cardRelationParam.cooperatorIdList != null">
            and o2ai.cooperator_id in
            <foreach item ="item" index ="index" collection ="cardRelationParam.cooperatorIdList" open ="(" separator ="," close =")" >
                #{item}
            </foreach>
        </if>-->
        ) t

    </select>

    <select id="listNotUseCard" resultType="com.chinamobile.iot.sc.pojo.vo.NotUseCardVO">
        select
        cr.id,
        CONCAT(cr.imei,'-',cr.temp_iccid) imeiTmepIccid,
        terminal_type terminalType
        from
        card_relation cr
        where 1=1
        and (cr.order_id is null or cr.order_id = '')
        and cr.imei is not null and cr.imei != ''
        and cr.temp_iccid is not null and cr.temp_iccid != ''
        and cr.delete_time is null
        and not exists (select imei from card_choose_delivery where imei = cr.imei)
        <if test="notUseCardParam.imei != null and notUseCardParam.imei != ''">
            and cr.imei like '%${notUseCardParam.imei}%'
        </if>
        <if test="notUseCardParam.orderId != null and notUseCardParam.orderId != ''">
            and exists (select distinct srt.province_code from order_2c_atom_info o2ai,sku_release_target srt
            where o2ai.sku_offering_code = srt.sku_offering_code
            and case when srt.province_code = '000' then srt.province_code = srt.province_code else srt.province_code = cr.be_id end
            and o2ai.order_id = #{notUseCardParam.orderId})
        </if>
    </select>

    <update id="updateCardOrderToNull">
        update
            card_relation
        set
            order_id = null,
            order_atom_info_id = null,
            chaba_msisdn = null,
            msisdn = case
                when terminal_type = 3 then null
                else msisdn
            end,
            sell_status = 1,
            update_time = now()
        where
            delete_time is null
        and  order_id = #{updateCardOrderToNullParam.orderId}
        <if test="updateCardOrderToNullParam.imei != null and updateCardOrderToNullParam.imei != ''">
            and imei = #{updateCardOrderToNullParam.imei}
        </if>
        <if test="updateCardOrderToNullParam.msisdn != null and updateCardOrderToNullParam.msisdn != ''">
            and msisdn = #{updateCardOrderToNullParam.msisdn}
        </if>
        <if test="updateCardOrderToNullParam.chabaMsisdn != null and updateCardOrderToNullParam.chabaMsisdn != ''">
            and chaba_msisdn = #{updateCardOrderToNullParam.chabaMsisdn}
        </if>
    </update>

    <select id="listCardRelationImportInfoDetail" resultType="com.chinamobile.iot.sc.pojo.dto.CardRelationImportInfoDetailDTO">
        SELECT
            terminal_type terminalType,
            imei,
            msisdn,
            temp_iccid tempIccid,
            be_id beId,
            location,
            case
                when delete_time is null then '在库'
                else '不在库'
            end deleteStatusName,
            case
                when delete_time is not null and delete_time != '' then '-'
                else sell_status
            end sellStatus
        FROM
            card_relation
        where
            import_num = #{cardRelationImportInfoDetailParam.importNum}
        and device_version = #{cardRelationImportInfoDetailParam.deviceVersion}
        and be_id = #{cardRelationImportInfoDetailParam.beId}
        <if test="cardRelationImportInfoDetailParam.location != null and cardRelationImportInfoDetailParam.location != ''">
            <if test="cardRelationImportInfoDetailParam.location == '-1'">
                and (location is null or location = '')
            </if>
            <if test="cardRelationImportInfoDetailParam.location != '-1'">
                and location = #{cardRelationImportInfoDetailParam.location}
            </if>

        </if>
        <if test="cardRelationImportInfoDetailParam.sellStatus != null and cardRelationImportInfoDetailParam.sellStatus != ''">
            <if test="cardRelationImportInfoDetailParam.sellStatus == '-1'">
                and delete_time is not null and delete_time != ''
            </if>
            <if test="cardRelationImportInfoDetailParam.sellStatus != '-1'">
                and sell_status = #{cardRelationImportInfoDetailParam.sellStatus}
            </if>

        </if>
        <if test="cardRelationImportInfoDetailParam.imei != null and cardRelationImportInfoDetailParam.imei != ''">
            and imei like '%${cardRelationImportInfoDetailParam.imei}%'
        </if>
    </select>

    <select id="listImportNumXDetailLocation" resultType="com.chinamobile.iot.sc.pojo.dto.ImportNumCardDetailLocationDTO">
        SELECT distinct
          import_num importNum,
          location
        FROM
          card_relation
        where
          import_num = #{cardRelationImportInfoDetailParam.importNum}
        and device_version = #{cardRelationImportInfoDetailParam.deviceVersion}
        and be_id = #{cardRelationImportInfoDetailParam.beId}
    </select>

    <select id="listNoImportNumCard" resultType="com.chinamobile.iot.sc.pojo.dto.NoImportNumCardDTO">
        SELECT
            id,
            be_id beId,
            device_version deviceVersion,
            create_time createTime
        FROM
            card_relation
        WHERE
            (import_num IS NULL OR import_num = '')
        AND delete_time is null
        AND be_id IS NOT NULL
        AND be_id != ''
    </select>

    <select id="orderProductCardRelationList" resultType="com.chinamobile.iot.sc.pojo.mapper.OrderProductCardRelationListDO">
        select imei,iccid,msisdn,terminalType
        from(
        SELECT
        relation.imei,
        relation.temp_iccid iccid,
        relation.msisdn,
        relation.update_time,
        relation.terminal_type terminalType
        FROM
        atom_offering_info atom
        JOIN dkcardx_inventory_main_info main ON main.id = atom.inventory_main_id
        JOIN card_relation relation ON  relation.device_version = main.device_version and relation.terminal_type = main.terminal_type
        and case when relation.cust_code is not null and relation.cust_code != '' then relation.cust_code = main.cust_code
        else 1=1
        end
        and case when relation.template_id is not null and  relation.template_id != '' then relation.template_id = main.template_id
        else 1=1
        end
        and relation.sell_status = '1'
        and relation.delete_time is null
        <!--<if test="reserveBeId != null and reserveBeId != ''">
            AND	relation.be_id = main.be_id and relation.be_id = #{reserveBeId}
        </if>-->
        <if test="reserveLocation != null and reserveLocation != ''">
            and relation.location = #{reserveLocation}
        </if>
        <if test="reserveLocation == null or reserveLocation == ''">
            and relation.location = -1
        </if>
        WHERE atom.spu_code = #{spuOfferingCode}
        AND atom.sku_code = #{skuOfferingCode}
        and  atom.offering_code = #{atomOfferingCode}
        and not exists (select imei from card_choose_delivery where imei = relation.imei)
        <if test="searchWord != null and searchWord != ''">
            AND (relation.imei like concat ('%', #{searchWord},'%') or relation.temp_iccid like concat ('%', #{searchWord},'%') or relation.msisdn like concat ('%', #{searchWord},'%') )
        </if>
        union
        SELECT relation.imei, relation.temp_iccid iccid, relation.msisdn ,
        relation.update_time,
        relation.terminal_type terminalType
        FROM atom_offering_info atom
        JOIN dkcardx_inventory_main_info main ON main.id = atom.inventory_main_id
        JOIN card_relation relation ON relation.device_version = main.device_version and relation.terminal_type = main.terminal_type
        and case when relation.cust_code is not null and relation.cust_code != '' then relation.cust_code = main.cust_code else 1=1 end
        and case when relation.template_id is not null and relation.template_id != '' then relation.template_id = main.template_id else 1=1 end
        and relation.sell_status = '1'
        and relation.delete_time is null
        <if test="reserveBeId != null and reserveBeId != ''">
            AND	relation.be_id = main.be_id
            and (relation.location is null or relation.location = '' )
            and relation.be_id = #{reserveBeId}
        </if>
        <if test="reserveBeId == null or reserveBeId == ''">
            AND	relation.be_id = main.be_id
            and (relation.location is null or relation.location = '' )
            and relation.be_id = -1
        </if>
        WHERE atom.spu_code = #{spuOfferingCode}
        AND atom.sku_code = #{skuOfferingCode}
        and  atom.offering_code = #{atomOfferingCode}
        and not exists (select imei from card_choose_delivery where imei = relation.imei)
        <if test="searchWord != null and searchWord != ''">
            AND (relation.imei like concat ('%', #{searchWord},'%') or relation.temp_iccid like concat ('%', #{searchWord},'%') or relation.msisdn like concat ('%', #{searchWord},'%') )
        </if>
        )rl
        ORDER BY update_time DESC
    </select>


    <select id="cardRelationDeliverList" resultType="com.chinamobile.iot.sc.pojo.mapper.OrderProductCardRelationListDO">
        select imei,iccid,msisdn,terminalType
        from(
        SELECT
        relation.imei,
        relation.temp_iccid iccid,
        relation.msisdn,
        relation.update_time,relation.device_version deviceVersion,
        relation.terminal_type terminalType
        FROM
        atom_offering_info atom
        JOIN dkcardx_inventory_main_info main ON main.id = atom.inventory_main_id
        JOIN card_relation relation ON  relation.device_version = main.device_version and relation.terminal_type = main.terminal_type
        and case when relation.cust_code is not null and relation.cust_code != '' then relation.cust_code = main.cust_code
        else 1=1
        end
        and case when relation.template_id is not null and  relation.template_id != '' then relation.template_id = main.template_id
        else 1=1
        end
        and relation.sell_status = '1'
        and relation.delete_time is null
        <!--<if test="reserveBeId != null and reserveBeId != ''">
            AND	relation.be_id = main.be_id and relation.be_id = #{reserveBeId}
        </if>-->
        <if test="reserveLocation != null and reserveLocation != ''">
            and relation.location = #{reserveLocation}
        </if>
        <if test="reserveLocation == null or reserveLocation == ''">
            and relation.location = -1
        </if>
        WHERE atom.spu_code = #{spuOfferingCode}
        AND atom.sku_code = #{skuOfferingCode}
        and  atom.offering_code = #{atomOfferingCode}
        and not exists (select imei from card_choose_delivery where imei = relation.imei)
        <if test="imei != null and imei != ''">
            AND relation.imei = #{imei}
        </if>
        <if test="deviceVersion != null and deviceVersion != ''">
            AND relation.device_version = #{deviceVersion}
        </if>
        union
        SELECT relation.imei, relation.temp_iccid iccid, relation.msisdn ,
        relation.update_time,relation.device_version deviceVersion,
        relation.terminal_type terminalType
        FROM atom_offering_info atom
        JOIN dkcardx_inventory_main_info main ON main.id = atom.inventory_main_id
        JOIN card_relation relation ON relation.device_version = main.device_version and relation.terminal_type = main.terminal_type
        and case when relation.cust_code is not null and relation.cust_code != '' then relation.cust_code = main.cust_code else 1=1 end
        and case when relation.template_id is not null and relation.template_id != '' then relation.template_id = main.template_id else 1=1 end
        and relation.sell_status = '1'
        and relation.delete_time is null
        <if test="reserveBeId != null and reserveBeId != ''">
            AND	relation.be_id = main.be_id
            and (relation.location is null or relation.location = '' )
            and relation.be_id = #{reserveBeId}
        </if>
        <if test="reserveBeId == null or reserveBeId == ''">
            AND	relation.be_id = main.be_id
            and (relation.location is null or relation.location = '' )
            and relation.be_id = -1
        </if>
        WHERE atom.spu_code = #{spuOfferingCode}
        AND atom.sku_code = #{skuOfferingCode}
        and  atom.offering_code = #{atomOfferingCode}
        and not exists (select imei from card_choose_delivery where imei = relation.imei)
        <if test="imei != null and imei != ''">
            AND relation.imei = #{imei}
        </if>
        <if test="deviceVersion != null and deviceVersion != ''">
            AND relation.device_version = #{deviceVersion}
        </if>
        )rl
        ORDER BY update_time DESC
    </select>
</mapper>