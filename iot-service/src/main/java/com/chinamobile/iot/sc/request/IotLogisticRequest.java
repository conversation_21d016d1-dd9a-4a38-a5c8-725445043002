package com.chinamobile.iot.sc.request;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/11 15:11
 * @Description:
 */
@Data
public class IotLogisticRequest {
    private List<OrderInfoDTO> orderInfo;

    @Data
    public static class OrderInfoDTO{
        private String orderId;
        private List<LogisInfo> logisInfo;
    }

    @Data
    public static class LogisInfo{
        private String logisCode;
        private String supplierName;
        private String signReceiptName;
        private String description;
    }
}
