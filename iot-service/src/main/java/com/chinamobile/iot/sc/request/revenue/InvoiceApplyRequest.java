package com.chinamobile.iot.sc.request.revenue;

import lombok.Data;


@Data
public class InvoiceApplyRequest {

    private String accessToken;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 客户属性(0:一般纳税人,1:小规模纳税人,2:个人)
     */
    private String clientProperty;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税人识别号
     */
    private String taxpayerNumber;
    /**
     * 客户开户行
     */
    private String clientOpeningBank;
    /**
     * 客户账号
     */
    private String clientAccount;
    /**
     * 客户地址
     */
    private String clientAddress;
    /**
     * 客户电话
     */
    private String clientPhone;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 购买方自然人标识 0:否 1:是 个人用户传1
     */
    private Integer isGmfzrrbz;
}
