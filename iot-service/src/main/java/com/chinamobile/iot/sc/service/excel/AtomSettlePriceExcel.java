package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27
 * @description 商品原子信息结算单价+结算明细服务名称割接模板割接
 */
@Data
public class AtomSettlePriceExcel {

    @ExcelProperty(value = "序号", index = 0)
    private String index;

    @ExcelProperty(value = "原子商品编码", index = 1)
    private String atomOfferingCode;

    @ExcelProperty(value = "原子商品名称", index = 2)
    private String atomOfferingName;

    @ExcelProperty(value = "不需结算", index = 3)
    private String notNeedAccount;

    @ExcelProperty(value = "结算明细服务名称", index = 4)
    private String settleServiceName;

    @ExcelProperty(value = "结算单价（省专）", index = 5)
    private String settlePrice;

    @ExcelProperty(value = "交付周期", index = 6)
    private String deliveryCycle;

    @ExcelProperty(value = "原子商品数量", index = 7)
    private Integer quantity;

}
