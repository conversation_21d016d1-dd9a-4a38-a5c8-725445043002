package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.dao.OrderCooperatorRelationMapper;
import com.chinamobile.iot.sc.dao.ext.OrderCooperatorRelationMapperExt;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupDTO;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationExample;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupParam;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoParam;
import com.chinamobile.iot.sc.service.OrderCooperatorRelationService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/27
 * @description 订单和合作伙伴关系service实现类
 */
@Service
public class OrderCooperatorRelationServiceImpl implements OrderCooperatorRelationService {

    @Resource
    private OrderCooperatorRelationMapper orderCooperatorRelationMapper;

    @Resource
    private OrderCooperatorRelationMapperExt orderCooperatorRelationMapperExt;

    @Resource
    private UserFeignClient userFeignClient;

    @Override
    public void batchAddOrderCooperatorRelation(List<OrderCooperatorRelation> orderCooperatorRelationList) {
        orderCooperatorRelationMapper.batchInsert(orderCooperatorRelationList);
    }

    @Override
    public void deleteOrderCooperatorRelationByNeed(OrderCooperatorRelationExample orderCooperatorRelationExample) {
        orderCooperatorRelationMapper.deleteByExample(orderCooperatorRelationExample);
    }

    @Override
    public List<OrderCooperatorRelation> listOrderCooperatorRelationByNeed(OrderCooperatorRelationExample orderCooperatorRelationExample) {
        return orderCooperatorRelationMapper.selectByExample(orderCooperatorRelationExample);
    }

    @Override
    public List<OrderCooperatorInfoByGroupDTO> listCooperatorInfoByGroup(OrderCooperatorInfoByGroupParam orderCooperatorInfoByGroupParam) {
        return orderCooperatorRelationMapperExt.listCooperatorInfoByGroup(orderCooperatorInfoByGroupParam);
    }

    @Override
    public List<OrderCooperatorInfoDTO> listCooperatorInfo(OrderCooperatorInfoParam orderCooperatorInfoParam) {
        return orderCooperatorRelationMapperExt.listCooperatorInfo(orderCooperatorInfoParam);
    }

    @Override
    public List<Data4User> listCooperatorUserInfo(String atomOrderId, String orderId) {

        if (StringUtils.isNotEmpty(atomOrderId)
           && StringUtils.isEmpty(orderId)){
            throw new RuntimeException("原子订单id和订单id不能同时为空");
        }

        OrderCooperatorRelationExample orderCooperatorRelationExample = new OrderCooperatorRelationExample();
        OrderCooperatorRelationExample.Criteria criteria = orderCooperatorRelationExample.createCriteria();
        if (StringUtils.isNotEmpty(atomOrderId)){
            criteria.andAtomOrderIdEqualTo(atomOrderId);
        }

        if (StringUtils.isNotEmpty(orderId)){
            criteria.andOrderIdEqualTo(orderId);
        }
        List<OrderCooperatorRelation> orderCooperatorRelationList = listOrderCooperatorRelationByNeed(orderCooperatorRelationExample);
        if (CollectionUtils.isEmpty(orderCooperatorRelationList)){
            throw new RuntimeException("未找到原子订单id为"+atomOrderId+",订单id为"+orderId+"的从合作伙伴信息");
        }

        List<String> cooperatorIdList = orderCooperatorRelationList.stream()
                .map(OrderCooperatorRelation::getCooperatorId)
                .distinct()
                .collect(Collectors.toList());
        BaseAnswer<List<Data4User>> cooperatorBaseAnswer = userFeignClient.partnerInfoByIds(cooperatorIdList);

        if (cooperatorBaseAnswer == null
                || !cooperatorBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                || cooperatorBaseAnswer.getData() == null) {
            throw new RuntimeException("调用获取从合作伙伴信息失败。从合作伙伴ID:" + String.join(",",cooperatorIdList));
        }

        return cooperatorBaseAnswer.getData();
    }
}
