package com.chinamobile.iot.sc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023/5/4 15:56
 * @description: 定时任务异步配置类
 **/
@Component
public class AsyncScheduledTaskConfig {

    @Bean("asyncTask")
    public Executor myAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 最大线程数
        executor.setMaxPoolSize(100);
        // 核心线程数
        executor.setCorePoolSize(100);
        // 任务队列的大小
        executor.setQueueCapacity(100);
        // 线程前缀名
        executor.setThreadNamePrefix("New-Thread-");
        // 线程存活时间
        executor.setKeepAliveSeconds(60);
        // 拒绝处理策略：直接抛出异常
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy() {
        });
        // 线程初始化
        executor.initialize();
        return executor;
    }


}
