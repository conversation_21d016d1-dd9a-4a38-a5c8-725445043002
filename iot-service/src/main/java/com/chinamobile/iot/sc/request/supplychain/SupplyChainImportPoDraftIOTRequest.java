package com.chinamobile.iot.sc.request.supplychain;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description: iot同步省采参数
 **/
@Data
public class SupplyChainImportPoDraftIOTRequest {

    /**
     * 合同id
     */
    @NotBlank(message = "合同id必填！")
    private String contractId;

    /**
     * 合同号
     */
    @NotBlank(message = "合同号必填！")
    private String contractNum;

    /**
     * 合同类型 0-地市 1-省 3-省地市
     */
    @NotBlank(message = "合同类型必填！")
    private String type;

}
