package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineMaterialMapper;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterial;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterialExample;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineMaterialService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架商品素材service实现类
 */
@Service
public class NewProductRequestOnlineOfflineMaterialServiceImpl implements NewProductRequestOnlineOfflineMaterialService {

    @Resource
    private NewProductRequestOnlineOfflineMaterialMapper newProductRequestOnlineOfflineMaterialMapper;

    @Override
    public void batchSaveMaterial(List<NewProductRequestOnlineOfflineMaterial> materialList) {
        newProductRequestOnlineOfflineMaterialMapper.batchInsert(materialList);
    }

    @Override
    public void deleteMaterialByRequestIdAndComboInfoId(String comboInfoId, String requestId) {
        NewProductRequestOnlineOfflineMaterialExample example = new NewProductRequestOnlineOfflineMaterialExample();
        example.createCriteria()
                .andComboInfoIdEqualTo(comboInfoId)
                .andNewProductRequestIdEqualTo(requestId);
        newProductRequestOnlineOfflineMaterialMapper.deleteByExample(example);
    }

    @Override
    public void batchUpdateMaterialByRequestIdAndComboInfoIdAndFileType(List<NewProductRequestOnlineOfflineMaterial> materialList) {
        materialList.stream().forEach(material ->{
            NewProductRequestOnlineOfflineMaterialExample example = new NewProductRequestOnlineOfflineMaterialExample();
            example.createCriteria()
                    .andComboInfoIdEqualTo(material.getComboInfoId())
                    .andNewProductRequestIdEqualTo(material.getNewProductRequestId())
                    .andFileTypeEqualTo(material.getFileType());
            newProductRequestOnlineOfflineMaterialMapper.updateByExampleSelective(material,example);
        });
    }

    @Override
    public List<NewProductRequestOnlineOfflineMaterial> getMaterialByRequestIdAndComboInfoId(String newProductRequestId, String comboInfoId) {
        NewProductRequestOnlineOfflineMaterialExample example = new NewProductRequestOnlineOfflineMaterialExample();
        example.createCriteria()
                .andNewProductRequestIdEqualTo(newProductRequestId)
                .andComboInfoIdEqualTo(comboInfoId);
        return newProductRequestOnlineOfflineMaterialMapper.selectByExample(example);
    }
}
