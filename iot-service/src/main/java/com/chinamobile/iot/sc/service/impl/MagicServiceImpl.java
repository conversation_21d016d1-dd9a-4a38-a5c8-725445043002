package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.entity.OrderOrganizationRelevancy;
import com.chinamobile.iot.sc.pojo.entity.OrderOrganizationRelevancyExample;
import com.chinamobile.iot.sc.service.MagicService;
import com.chinamobile.iot.sc.service.excel.ProvinceOrgCut;
import com.chinamobile.iot.sc.service.excel.ProvinceOrgCutExcelListener;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
@Slf4j
public class MagicServiceImpl implements MagicService {
    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;
    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private OrderOrganizationRelevancyMapper orderOrgRelevancyMapper;

    @Resource
    private Order2cRocInfoMapper order2cRocInfoMapper;

    @Resource
    private Order2cAtomHistoryMapper order2cAtomHistoryMapper;

    private static final int BATCH_SIZE = 2000; // 批量大小
    @Override
    public void testOrder2cInfoSM4Sql(){
        List<Order2cInfo> order2cInfoList = order2cInfoMapper.selectByExample(new Order2cInfoExample());
        List<String> batchList = new ArrayList<>();
        for(Order2cInfo order2cInfo : order2cInfoList){
            try{
                if(StringUtils.isNotEmpty(order2cInfo.getTotalPrice())){
                    double totalPrice = Double.valueOf(IOTEncodeUtils.decryptSM4(order2cInfo.getTotalPrice(), iotSm4Key,iotSm4Iv)) / 1000;
                }
            }catch (Exception e){
                batchList.add(order2cInfo.getOrderId());
            }
        }
        log.info("orderIdList: {}",batchList);


        List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(new Order2cAtomInfoExample());
        List<String> order2cAtomInfoListArr = new ArrayList<>();
        for(Order2cAtomInfo order2cAtomInfo : order2cAtomInfoList){
            try{
                if(StringUtils.isNotEmpty(order2cAtomInfo.getDeductPrice())){
                    String deductPrice = IOTEncodeUtils.decryptSM4(order2cAtomInfo.getDeductPrice(), iotSm4Key, iotSm4Iv);
                    BigDecimal totalPrice = new BigDecimal(deductPrice).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
                }
            }catch (Exception e){
                order2cAtomInfoListArr.add(order2cAtomInfo.getId());
            }
        }
        log.info("order2cAtomInfoListArr: {}",order2cAtomInfoListArr);
//        try (BufferedWriter writer = new BufferedWriter(new FileWriter("order2cInfo.sql"))) {
//            for (Order2cInfo order2cInfo : order2cInfoList) {
//
//                batchList.add(order2cInfo);
//
//                if (batchList.size() >= BATCH_SIZE) {
//                    writeResultsOrder2cInfo(writer, batchList); // 将 SQL 语句写入文件
//                    batchList.clear(); // 清空批量列表
//                }
//            }
//
//            // 更新剩余的记录
//            if (!batchList.isEmpty()) {
//                writeResultsOrder2cInfo(writer, batchList); // 将 SQL 语句写入文件
//            }
//        }catch (IOException e) {
//            e.printStackTrace();
//        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cutoverOrderProvinceorg(MultipartFile upfile) {
        if (upfile.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = upfile.getOriginalFilename();
        if (oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"))) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }
        log.info("解析provinceOrg割接文件: {}", oldName);
        try{
//            ProvinceOrgCutExcelListener listener = new ProvinceOrgCutExcelListener();
            List<ProvinceOrgCut> list = EasyExcel.read(upfile.getInputStream(), ProvinceOrgCut.class, null)
                    .ignoreEmptyRow(true).sheet(0).headRowNumber(1).doReadSync();
//            List<Order2cInfo> updateList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                log.info("list size = {}",list.size());
                int i = 0;
                for(ProvinceOrgCut item : list){
                    log.info("cutoverOrderProvinceorg i  = {}",i);
                    i++;
                    //这里后面看要不要
                    if(StringUtils.isEmpty(item.getOrgName())){
                        continue;
                    }
                    //先检验次订单的组织机构是否变化，如果没有变化，就不不更新，以防flink自动同步
                    String orderId = item.getOrderId();
                    Order2cInfo oldInfo = order2cInfoMapper.selectByPrimaryKey(item.getOrderId());
                    String oldOrgName = oldInfo.getProvinceOrgName();
                    String cutOrgName = item.getOrgName();
                    log.info("cutoverOrderProvinceorg oldOrgName = {}, cutOrgName = {}",oldOrgName, cutOrgName);
                    Order2cInfo info = new Order2cInfo();

                    if(StringUtils.isNotEmpty(oldOrgName) && StringUtils.isNotEmpty(cutOrgName)){
                        if(oldOrgName.equals(cutOrgName)){
                            //不做任何处理，进入下一循环
                            continue;
                        }else{
                            //组织关系修改了（更新了订单表的），更新了订单表的，会重新同步，新增订单组织关系，要删除以前order_relevency
                            orderOrgRelevancyMapper.deleteByExample(new OrderOrganizationRelevancyExample().createCriteria().andOrderIdEqualTo(orderId).example());
                            info.setOrderId(item.getOrderId());
                            info.setProvinceOrgName(item.getOrgName());
                            order2cInfoMapper.updateByPrimaryKeySelective(info);
                        }
                    }else if(StringUtils.isNotEmpty(oldOrgName) && StringUtils.isEmpty(cutOrgName)){
                        //以前不是空的，可能是乱码，或者其他，但是割接文件是空的，按产品说法，空的都是废单，不用管，但是还是按割接文件割成空的
                        //组织关系修改了（更新了订单表的），更新了订单表的，会重新同步，新增订单组织关系，要删除以前order_relevency
                        orderOrgRelevancyMapper.deleteByExample(new OrderOrganizationRelevancyExample().createCriteria().andOrderIdEqualTo(orderId).example());
                        BeanUtils.copyProperties(oldInfo, info);
                        info.setProvinceOrgName(null);
                        order2cInfoMapper.updateByPrimaryKey(info);

                    }else if(StringUtils.isEmpty(oldOrgName) && StringUtils.isEmpty(cutOrgName)){
                        //do nothing 不更新
                        log.info("cutProvinceOrg order_id = {} , both orgName is Empty!!",orderId);
                        continue;
                    }
                }
                //生成sql文档
                log.info("cutoverOrderProvinceorg update OrgName Over!");
            }else{
                throw new BusinessException(BaseErrorConstant.EMPTY_EXCEL);
            }
        }catch(Exception e){
            e.printStackTrace();
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void duplicateOrderRoc(String prikey) {
        try{
            int count = order2cRocInfoMapper.deleteByPrimaryKey(prikey);
            log.info("duplicateOrderRoc influence count = {}",count);
        }catch (Exception e){
            throw new BusinessException(e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void duplicateOrderRocModiStatus(String prikey, Integer innerStatus, Integer orgStatus, String updateTime) {
        try{
            order2cRocInfoMapper.updateByPrimaryKeySelective(new Order2cRocInfo().withId(prikey).withInnerStatus(innerStatus).withOriginalStatus(orgStatus)
                    .withUpdateTime(DateUtils.strToDate(updateTime, "yyyy-MM-dd HH:mm:ss")));
        }catch(Exception e){
            throw new BusinessException(e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void duplicateOrderRocHistory(String atomId, String refundOrderId, Integer operType, Integer innerStatus, String createTime, String updateTime) {
        try{
            //由于没有主键，无法删除一条，先读取一条备用，删除后再插入
            List<Order2cAtomHistory> hisList = order2cAtomHistoryMapper.selectByExample(new Order2cAtomHistoryExample().createCriteria().andRefundOrderIdEqualTo(refundOrderId)
                    .andOperateTypeEqualTo(operType).andInnerStatusEqualTo(innerStatus).andCreateTimeEqualTo(DateUtils.strToDate(createTime, "yyyy-MM-dd HH:mm:ss"))
                    .andUpdateTimeEqualTo(DateUtils.strToDate(updateTime, "yyyy-MM-dd HH:mm:ss")).example());
            if(CollectionUtils.isEmpty(hisList)){
                throw new BusinessException(BaseErrorConstant.EMPTY_EXCEL);
            }
            Order2cAtomHistory orgHis = hisList.get(0);

            int influenceCount = order2cAtomHistoryMapper.deleteByExample(new Order2cAtomHistoryExample().createCriteria().andRefundOrderIdEqualTo(refundOrderId)
                    .andOperateTypeEqualTo(operType).andInnerStatusEqualTo(innerStatus).andCreateTimeEqualTo(DateUtils.strToDate(createTime, "yyyy-MM-dd HH:mm:ss"))
                    .andUpdateTimeEqualTo(DateUtils.strToDate(updateTime, "yyyy-MM-dd HH:mm:ss")).example());
            log.info("duplicateOrderRocHistory delete duplicate history count = {}",influenceCount);

            order2cAtomHistoryMapper.insert(orgHis);

        }catch(Exception e){
            throw new BusinessException(e);
        }
    }

    // 将 SQL 语句写入文件
    private void writeResultsOrder2cInfo(BufferedWriter writer, List<Order2cInfo> batchList) throws IOException {
        for (Order2cInfo order2cInfo : batchList) {
            String sql = String.format("UPDATE order_2c_info SET cust_code = '%s', cust_name = '%s', total_price = '%s', deduct_price = '%s', contact_person_name = '%s', contact_phone = '%s', usaddr = '%s', addr1 = '%s', addr2 = '%s', addr3 = '%s', addr4 = '%s' WHERE order_id = %s;",
                    desToSM4(order2cInfo.getCustCode()), desToSM4(order2cInfo.getCustName()),
                    desToSM4(order2cInfo.getTotalPrice()), desToSM4(order2cInfo.getDeductPrice()),
                    desToSM4(order2cInfo.getContactPersonName()), desToSM4(order2cInfo.getContactPhone()),
                    desToSM4(order2cInfo.getUsaddr()), desToSM4(order2cInfo.getAddr1()),
                    desToSM4(order2cInfo.getAddr2()), desToSM4(order2cInfo.getAddr3()),
                    desToSM4(order2cInfo.getAddr4()), order2cInfo.getOrderId());
            writer.write(sql);
            writer.newLine();
        }
    }

    private String desToSM4(String desString){
        String decryptedData = IOTEncodeUtils.decryptIOTMessage(desString, encodeKey);
        String newEncryptedData = IOTEncodeUtils.encryptSM4(decryptedData, iotSm4Key, iotSm4Iv);
        return newEncryptedData;
    }



}

