package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupDTO;
import com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation;
import com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationExample;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupParam;
import com.chinamobile.iot.sc.pojo.param.OrderCooperatorInfoParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/27
 * @description 订单和合作伙伴关系service接口类
 */
public interface OrderCooperatorRelationService {

    /**
     * 批量新增订单和合作伙伴关系
     * @param orderCooperatorRelationList
     */
    void batchAddOrderCooperatorRelation(List<OrderCooperatorRelation> orderCooperatorRelationList);

    /**
     * 根据需要删除订单和合作伙伴关系
     * @param orderCooperatorRelationExample
     */
    void deleteOrderCooperatorRelationByNeed(OrderCooperatorRelationExample orderCooperatorRelationExample);

    /**
     * 根据需要获取订单和合作伙伴关系
     * @param orderCooperatorRelationExample
     * @return
     */
    List<OrderCooperatorRelation> listOrderCooperatorRelationByNeed(OrderCooperatorRelationExample orderCooperatorRelationExample);

    /**
     * 获取组装后的原子订单和合作伙伴列表
     * @param orderCooperatorInfoByGroupParam
     * @return
     */
    List<OrderCooperatorInfoByGroupDTO> listCooperatorInfoByGroup(OrderCooperatorInfoByGroupParam orderCooperatorInfoByGroupParam);

    /**
     * 获取原子订单和合作伙伴列表
     * @param orderCooperatorInfoParam
     * @return
     */
    List<OrderCooperatorInfoDTO> listCooperatorInfo(OrderCooperatorInfoParam orderCooperatorInfoParam);

    /**
     * 获取合作伙伴用户信息
     * @param atomOrderId
     * @param orderId
     */
    List<Data4User> listCooperatorUserInfo(String atomOrderId, String orderId);
}
