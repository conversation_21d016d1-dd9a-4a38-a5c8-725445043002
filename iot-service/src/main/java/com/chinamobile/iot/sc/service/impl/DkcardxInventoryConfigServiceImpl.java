package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.DkcardxInventoryConfigMapper;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfig;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfigExample;
import com.chinamobile.iot.sc.service.DkcardxInventoryConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/10
 * @description 代客下单卡+X库存详细配置表（硬件终端详情）service实现类
 */
@Service
public class DkcardxInventoryConfigServiceImpl implements DkcardxInventoryConfigService {

    @Resource
    private DkcardxInventoryConfigMapper dkcardxInventoryConfigMapper;

    @Override
    public List<DkcardxInventoryConfig> getDkcardxInventoryConfigByNeed(DkcardxInventoryConfigExample example) {
        return dkcardxInventoryConfigMapper.selectByExample(example);
    }

    @Override
    public void batchAddDkcardxInventoryConfig(List<DkcardxInventoryConfig> dkcardxInventoryConfigList) {
        dkcardxInventoryConfigMapper.batchInsert(dkcardxInventoryConfigList);
    }

    @Override
    public void updateDkcardxInventoryConfigByNeed(DkcardxInventoryConfig dkcardxInventoryConfig,
                                                   DkcardxInventoryConfigExample example) {
        dkcardxInventoryConfigMapper.updateByExampleSelective(dkcardxInventoryConfig,example);
    }
}
