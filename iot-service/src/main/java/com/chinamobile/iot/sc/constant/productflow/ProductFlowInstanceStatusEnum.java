package com.chinamobile.iot.sc.constant.productflow;

public enum ProductFlowInstanceStatusEnum {

    IN_PROGRESS(0,"进行中"),
    OVER(1,"结束"),
    CANCEL(2,"废止"),
    ;


    public Integer code;
    public String name;

    ProductFlowInstanceStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
