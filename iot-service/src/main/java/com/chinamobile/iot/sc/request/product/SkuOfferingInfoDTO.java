package com.chinamobile.iot.sc.request.product;

import com.chinamobile.iot.sc.pojo.dto.PartnersAcceptingOrdersDTO;
import com.chinamobile.iot.sc.pojo.dto.PartnersAfterSalesDTO;
import com.chinamobile.iot.sc.pojo.dto.PartnersDeliveryDTO;
import com.chinamobile.iot.sc.request.sku.SkuReleaseTargetDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/2 10:18
 * @Description: 在仅刷新销售商品基本信息时本节点非必填，其他场景本节点必填
 */
@Data
public class SkuOfferingInfoDTO {
    /**
     * 规格编码
     */
    private String offeringCode;
    /**
     * 商品名称
     */
    private String offeringName;
    /**
     * Sku商品状态 0-测试 1-发布 2-下架
     */
    private String skuOfferingStatus;
    /**
     * Sku商品状态变更时间
     */
    private Date skuOfferingStatusTime;
    /**
     * Sku商品发布范围
     */
    private List<SkuReleaseTargetDTO> releaseTargetList;
    /**
     * 商品构成
     */
    private String composition;
    /**
     * 型号
     */
    private String model;
    /**
     * 数量
     */
    private Long quantity;
    /**
     * 尺寸
     */
    private String size;
    /**
     * 操作类型
     */
    private String operType;
    /**
     * IOT应用市场未配置“价格面议”时必填;
     * 不传值时，表示“价格面议”场景。
     * 单位：厘;
     */
    private Long recommendPrice;
    /**
     * 销售目录价
     * 单位：厘;
     */
    private Long price;
    /**
     * 计量单位
     * 与价格搭配使用;
     */
    private String unit;
    /**
     * 营销案名称
     */
    private String marketName;
    /**
     * 营销案编码
     */
    private String marketCode;
    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * X产品类型
     * 1:5G CPE
     * 2:5G 快线
     * 3:千里眼
     * 4:合同履约
     * 5:OneNET独立服务
     * 6:标准产品(OneNET）
     * 7:OnePark独立服务
     * 8:标准产品（OnePark）
     */
    private String productType;

    /**
     * 是否需要合作伙伴接单
     * 1：是
     * 2：否
     */
    private String receiveOrder;

    /**
     * 卡服务商EC编码
     */
    private String custCode;

    /**
     * 卡服务商名称
     */
    private String custName;

    /**
     * 卡片类型
     * 0：插拔卡
     * 1：贴片卡
     * 2：M2M芯片非空写卡
     * 3：M2M芯片空写卡
     */
    private String cardType;

    /**
     * 主商品
     * 01：物联卡个人、03：窄带网个人、04：和对讲个人 16：行车卫士个人
     */
    private String mainOfferingCode;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 开卡模板编码
     */
    private String templateId;

    /**
     * 项目信息
     */
    private String project;

    /**
     * 原子商品
     * offeringClass=A06时，必传;
     */
    private List<AtomOfferingInfoDTO> atomOfferingInfo;

    /**
     * sku版本号
     */
    private String skuOfferingVersion;

    /**
     * 销售模式 1：省内融合
     * 2：商城直销
     * 当offeringClass=A11卡+X时必传
     */
    private String saleModel;

    /**
     * 规格简称
     */
    private String skuAbbreviation;

    /**
     * 套餐类型 0：连续包月
     * 1：一次性包
     * 当offeringClass=A13：软件服务时必传
     */
    private String packageType;

    /**
     * 商品轮播图文件名
     */
    private List<String> productCarouselImage;

    /**
     * 商品视频信息文件名
     */
    private List<String> productVideoInformation;

    /**
     * 合作伙伴（接单）,当 offeringClass =A04、A08、A09、A12、A14、A15、A16、A17时，必传，其他不传
     */
    private List<PartnersAcceptingOrdersDTO> partnersAcceptingOrders;

    /**
     * 合作伙伴（交付）,当 offeringClass =A04、A08、A09、A12、A14、A15、A16、A17时，必传，其他不传
     */
    private List<PartnersDeliveryDTO> partnersDelivery;

    /**
     * 合作伙伴（售后）,当 offeringClass =A04、A08、A09、A12、A14、A15、A16、A17时，必传，其他不传
     */
    private List<PartnersAfterSalesDTO> partnersAfterSales;
}
