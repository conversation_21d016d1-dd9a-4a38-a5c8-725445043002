package com.chinamobile.iot.sc.request.order2c;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/23 15:23
 * @description 云视讯服务请求
 */
@Data
public class YunShiXunServiceParam implements Serializable {

    /**
     * 订单号（唯一）,订购服务时传递订单号，退订时传递退订流水号
     */
    private String orderNumber;
    /**
     * AES加解密KEY
     */
    @JSONField(name = "AES_KEY")
    private String aesKey;

    /**
     * AES加解密IV
     */
    @JSONField(name = "AES_IV")
    private String aesIv;

    /**
     * 操作编码：01-订购，02-退订
     */
    private String oprCode;

    private List<UserData> userData;

    @Data
    public static class UserData implements Serializable{
        /**手机号,注：AES加密传输*/
        private String mobile;

        /**用户名称,可选*/
        private String name;

        /**产品编码，参考7产品编码表*/
        private String productCode;

    }

}
