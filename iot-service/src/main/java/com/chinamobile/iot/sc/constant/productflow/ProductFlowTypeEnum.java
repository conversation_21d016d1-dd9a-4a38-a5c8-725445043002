package com.chinamobile.iot.sc.constant.productflow;

public enum ProductFlowTypeEnum {

    PRODUCT_SHELF(1,"商品上架"),
    PRODUCT_OFF_SHELF(2,"商品下架"),
    SALE_PRICE_UPDATE(3,"销售价变更"),
    SETTLE_PRICE_UPDATE(4,"结算价变更"),
    OTHER_INFO_UPDATE(5,"非价格信息变更"),
    ALL_INFO_UPDATE(6,"商品所有信息变更"),
    ;


    public Integer code;
    public String name;

    ProductFlowTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductFlowTypeEnum fromCode(Integer code){
        if(code == null){
            return null;
        }
        ProductFlowTypeEnum[] values = ProductFlowTypeEnum.values();
        for (ProductFlowTypeEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }

    public static boolean containsCode(Integer code) {
        if(code == null){
            return false;
        }
        ProductFlowTypeEnum[] values = ProductFlowTypeEnum.values();
        for (ProductFlowTypeEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return true;
            }
        }
        return false;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
