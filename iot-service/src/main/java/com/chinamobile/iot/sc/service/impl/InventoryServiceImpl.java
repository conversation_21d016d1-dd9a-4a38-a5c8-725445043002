package com.chinamobile.iot.sc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.config.IotConfig;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.constant.InventoryManagementModeKxEnum;
import com.chinamobile.iot.sc.constant.NoticeTypeConstant;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.constant.SellStatusEnum;
import com.chinamobile.iot.sc.constant.limit.DictLimitStatusEnum;
import com.chinamobile.iot.sc.constant.limit.DictLimitTypeEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.DkcardxInventoryMainInfoMapperExt;
import com.chinamobile.iot.sc.dao.handle.InventoryHandlerMapper;
import com.chinamobile.iot.sc.dao.handle.ProductHandlerMapper;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.b2b.Qry3rdInventoryResp;
import com.chinamobile.iot.sc.entity.b2b.QueryInventoryDTO;
import com.chinamobile.iot.sc.entity.b2b.ReserveInventoryDTO;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.GoodsManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.excel.DictLimitExportVO;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.exceptions.ResponseCode;
import com.chinamobile.iot.sc.feign.B2BFeignClient;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.handle.InventoryInfoHandle;
import com.chinamobile.iot.sc.pojo.mapper.CityInfoDO;
import com.chinamobile.iot.sc.pojo.mapper.InventoryExportDO;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.CardRelationImportInfoVO;
import com.chinamobile.iot.sc.pojo.vo.InventoryExportVO;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import com.chinamobile.iot.sc.request.*;
import com.chinamobile.iot.sc.request.inventory.LimitListRequest;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.response.iot.LimitSyncInfoResponse;
import com.chinamobile.iot.sc.response.iot.ReserveInventoryResponse;
import com.chinamobile.iot.sc.response.web.*;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.util.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_LORD_ROLE;
import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_ROLE;
import static com.chinamobile.iot.sc.common.Constant.*;
import static com.chinamobile.iot.sc.constant.CardTypeEnum.NO_INCLUDED_CARD;
import static com.chinamobile.iot.sc.constant.InventoryPatternConstant.*;
import static com.chinamobile.iot.sc.constant.InventoryTypeConstant.OPERATION_ADMIN_STATISTICS;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * @Author: YSC
 * @Date: 2021/11/5 09:47
 * @Description:
 */
@Service
@Slf4j
public class InventoryServiceImpl implements IInventoryService {

    @Resource
    ServicePackLimitSyncMapper servicePackLimitSyncMapper;
    @Resource
    private ServicePackLimitAmountMapper servicePackLimitAmountMapper;
    @Resource
    private InventoryHandlerMapper inventoryHandlerMapper;
    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;
    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;
    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private ProductHandlerMapper productHandlerMapper;

    @Resource
    private SkuReleaseTargetMapper skuReleaseTargetMapper;

    @Resource
    private DkcardxInventoryAtomInfoService dkcardxInventoryAtomInfoService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private UserFeignClient userFeignClient;

    @Autowired
    private B2BFeignClient b2BFeignClient;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Value("${sms.InventoryTemplateId:105980}")
    private String smsInventoryTemplateId;

    @Value("${sms.smsInventoryKxDeficiencyTemplateId:107590}")
    private String smsInventoryKxDeficiencyTemplateId;

    @Value("${tocustomer.orderType}")
    private List toCustomerOrderType;

    @Resource
    private LogService logService;

    @Resource
    private ProvinceCityConfig provinceCityConfig;

    @Autowired
    private RedisUtil redisUtil;


    @Resource
    private K3ProductMaterialMapper k3ProductMaterialMapper;

    @Resource
    private ContractMaterialMapper contractMaterialMapper;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private DkcardxInventoryInfoMapper dkcardxInventoryInfoMapper;

    @Resource
    private DkcardxInventoryConfigMapper dkcardxInventoryConfigMapper;

    @Resource
    private DkcardxInventoryDetailInfoMapper dkcardxInventoryDetailInfoMapper;

    @Resource
    private DkcardxInventoryAtomInfoMapper dkcardxInventoryAtomInfoMapper;

    @Resource
    private CardInventoryMainInfoMapper cardInventoryMainInfoMapper;

    @Resource
    private CardInventoryAtomInfoMapper cardInventoryAtomInfoMapper;

    @Resource
    private ContractCityInfoMapper contractCityInfoMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;
    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;
    @Autowired
    private IotConfig iotConfig;

    @Autowired
    private InventoryServiceImpl inventoryService;

    @Resource
    private UserRefundKxServiceImpl userRefundKxService;

    @Resource
    private DkcardxInventoryConfigService dkcardxInventoryConfigService;

    @Resource
    private DkcardxInventoryInfoService dkcardxInventoryInfoService;

    @Resource
    private DkcardxInventoryMainInfoMapper dkcardxInventoryMainInfoMapper;

    @Resource
    private DkcardxInventoryDetailInfoService dkcardxInventoryDetailInfoService;

    @Resource
    private CardRelationService cardRelationService;

    @Resource
    private OrderBaoliService orderBaoliService;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Resource
    private CategoryInfoMapper categoryInfoMapper;

    @Resource
    private AtomOfferingCooperatorRelationService atomOfferingCooperatorRelationService;

    @Resource
    private DkcardxInventoryMainInfoMapperExt dkcardxInventoryMainInfoMapperExt;

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(10000));

    /**
     * @param spuOfferingName
     * @param skuOfferingName
     * @param atomOfferingName
     * @param spuOfferingClass
     * @param partnerName
     * @param cooperatorName   原子商品名
     * @param inventoryStatus  0 库存充足 1 库存不足
     * @param page
     * @param num
     * @param loginIfo4Redis
     * @return
     */
    // 数据权限-库存配置
    @Override
    public BaseAnswer<PageData<InventoryInfoDTO>> getInventoryList(String spuOfferingName, String skuOfferingName,
                                                                   String spuOfferingCode, String skuOfferingCode,
                                                                   String atomOfferingName, String spuOfferingClass,
                                                                   String partnerName, String cooperatorName,
                                                                   Integer inventoryStatus,
                                                                   String spuOfferingStatus, String skuOfferingStatus, String h5Key,
                                                                   List<String> h5SpuOfferingClasses,
                                                                   LoginIfo4Redis loginIfo4Redis,
                                                                   Integer page, Integer num) {

        StringBuilder content = new StringBuilder();
        content.append("【查看】\n");

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue()
                .get(REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes)
                || (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL))) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        // 商品名称搜索支持反斜杠适配
        if (spuOfferingName != null) {
            spuOfferingName = spuOfferingName.replaceAll("\\\\", "\\\\\\\\");
        }
        if (skuOfferingName != null) {
            skuOfferingName = skuOfferingName.replaceAll("\\\\", "\\\\\\\\");
        }
        if (atomOfferingName != null) {
            atomOfferingName = atomOfferingName.replaceAll("\\\\", "\\\\\\\\");
        }
        if (StringUtils.isNotEmpty(spuOfferingCode)) {
            content.append("商品组/销售组编码").append(spuOfferingCode).append("\n");
        } else {
            content.append("商品组/销售组编码--\n");
        }
        if (StringUtils.isNotEmpty(skuOfferingCode)) {
            content.append("商品规格编码").append(skuOfferingCode).append("\n");
        } else {
            content.append("商品规格编码--\n");
        }
        String userId = loginIfo4Redis.getUserId();
        String roleType = loginIfo4Redis.getRoleType();
        String beId = "";
        String location = "";
        BaseAnswer<PageData<InventoryInfoDTO>> baseAnswer = new BaseAnswer<>();
        PageData<InventoryInfoDTO> pageData = new PageData<>();
        baseAnswer.setData(pageData);
        pageData.setPage(page);
        if (StringUtils.isNotEmpty(spuOfferingClass) && !SPUOfferingClassEnum.contains(spuOfferingClass)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品类型错误");
        }
        // 这里会对人员角色进行判断。如果是超管和运营管理员 客服管理员看到的应该是全部的库存信息，如果是合作伙伴看到的应该只有属于合作伙伴的信息
        List<String> userIdList = new ArrayList<>();
        Long count = null;
        // 从合作
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL)) {
            userIdList.add(userId);
            count = inventoryHandlerMapper.countInventoryByHandleQuery(spuOfferingName, skuOfferingName,
                    spuOfferingCode, skuOfferingCode,
                    atomOfferingName, spuOfferingClass, partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, userIdList, h5Key, h5SpuOfferingClasses,
                    beId, location);
            // 主合作
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)) {

            if (roleType.equals(PARTNER_LORD_ROLE)) {
                BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                    userIdList = downUserIds.getData();
                }
                userIdList.add(userId);
            } else if (roleType.equals(BaseConstant.PARTNER_PROVINCE)) {
                log.info("合作伙伴省管查询库存信息：partnerProvince：{}", roleType);
                // 省管配置权限为主合作伙伴权限
                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
                if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                    throw new BusinessException("10004", "合作伙伴省管账号错误");
                }

                Data4User data4User = data4UserBaseAnswer.getData();
                String companyType = data4User.getCompanyType();
                boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType)
                        && "2".equals(companyType);
                String userLocation = data4User.getLocationIdPartner();
                log.info("登录用户是否是合作伙伴省管：partnerIsProvinceUser:{}", isProvinceUser);
                if (isProvinceUser) {
                    BaseAnswer<Data4User> userPartner = userFeignClient
                            .getUserPartnerPrimaryByPartnerName(data4User.getPartnerName());
                    if (userPartner == null || !SUCCESS.getStateCode().equals(userPartner.getStateCode())) {
                        throw new BusinessException("10004", "合作伙伴省管获取主合作伙伴账号错误");
                    }
                    Data4User userPartnerData = userPartner.getData();
                    if (Optional.ofNullable(userPartnerData).isPresent()) {
                        BaseAnswer<List<String>> downUserIds = userFeignClient
                                .getDownUserIds(userPartnerData.getUserId());
                        if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                            userIdList = downUserIds.getData();
                        }
                        userIdList.add(userPartnerData.getUserId());
                        userIdList.add(userId);
                    }
                    if ("all".equals(userLocation)) {
                        beId = data4User.getBeIdPartner();
                    } else {
                        location = userLocation;
                    }
                } else {
                    throw new BusinessException(StatusConstant.AUTH_ERROR, "账号不属于省公司");
                }
            }
            count = inventoryHandlerMapper.countInventoryByHandleQuery(spuOfferingName, skuOfferingName,
                    spuOfferingCode, skuOfferingCode,
                    atomOfferingName, spuOfferingClass, partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, userIdList, h5Key, h5SpuOfferingClasses,
                    beId, location);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)) {
            count = inventoryHandlerMapper.countInventoryByHandleQuery(spuOfferingName, skuOfferingName,
                    spuOfferingCode, skuOfferingCode,
                    atomOfferingName, spuOfferingClass, partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, null, h5Key, h5SpuOfferingClasses,
                    beId, location);
        } else {
            // 这里考虑后面可能有多种角色。应该也是没有权限的
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }
        pageData.setCount(count);
        if (count == 0) {
            return baseAnswer;
        }
        List<InventoryInfoHandle> inventoryInfoHandles;
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL)) {
            inventoryInfoHandles = inventoryHandlerMapper.selectInventoryByHandleQuery((page - 1) * num, num,
                    spuOfferingName, skuOfferingName, spuOfferingCode, skuOfferingCode, atomOfferingName,
                    spuOfferingClass, partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, userIdList, h5Key, h5SpuOfferingClasses,
                    beId, location);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)) {
            inventoryInfoHandles = inventoryHandlerMapper.selectInventoryByHandleQuery((page - 1) * num, num,
                    spuOfferingName, skuOfferingName, spuOfferingCode, skuOfferingCode, atomOfferingName,
                    spuOfferingClass, partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, userIdList, h5Key, h5SpuOfferingClasses,
                    beId, location);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)) {
            inventoryInfoHandles = inventoryHandlerMapper.selectInventoryByHandleQuery((page - 1) * num, num,
                    spuOfferingName, skuOfferingName, spuOfferingCode, skuOfferingCode, atomOfferingName,
                    spuOfferingClass, partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, null, h5Key, h5SpuOfferingClasses,
                    beId, location);
        } else {
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }
        List<InventoryInfoDTO> inventoryInfoDTOS = new ArrayList<>();
        pageData.setData(inventoryInfoDTOS);
        content.append("原子商品编码");
        inventoryInfoHandles.forEach(x -> {
            content.append(x.getSkuOfferingCode()).append(",");
            InventoryInfoDTO inventoryInfoDTO = new InventoryInfoDTO();
            BeanUtils.copyProperties(x, inventoryInfoDTO);
            if (x.getIsPrimary() != null && !x.getIsPrimary()) {
                inventoryInfoDTO.setHasPartner(true);
            } else {
                inventoryInfoDTO.setHasPartner(false);
            }
            String atomOfferingClass = x.getAtomOfferingClass();
            if ("S".equals(atomOfferingClass)) {
                inventoryInfoDTO.setAtomOfferingClass("软件");
            } else if ("H".equals(atomOfferingClass)) {
                if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(x.getSpuOfferingClass())) {
                    inventoryInfoDTO.setAtomOfferingClass("合同履约类硬件");
                } else {
                    inventoryInfoDTO.setAtomOfferingClass("代销类硬件");
                }
            } else if ("O".equals(atomOfferingClass)) {
                // inventoryInfoDTO.setPartnerName(x.getSupplierName());
                inventoryInfoDTO.setAtomOfferingClass("OneNET独立服务");
            } else if ("D".equals(atomOfferingClass)) {
                // inventoryInfoDTO.setPartnerName(x.getSupplierName());
                inventoryInfoDTO.setAtomOfferingClass("（DICT）产品增值服务包");
            } else if ("P".equals(atomOfferingClass)) {
                // inventoryInfoDTO.setPartnerName(x.getSupplierName());
                inventoryInfoDTO.setAtomOfferingClass("OnePark独立服务");
            } else if ("F".equals(atomOfferingClass)) {
                // inventoryInfoDTO.setPartnerName(x.getSupplierName());
                inventoryInfoDTO.setAtomOfferingClass("行车卫士标准产品");
            } else if ("K".equals(atomOfferingClass)) {
                // inventoryInfoDTO.setPartnerName(x.getSupplierName());
                inventoryInfoDTO.setAtomOfferingClass("OneTraffic独立服务");
            } else if ("X".equals(atomOfferingClass)) {
                inventoryInfoDTO.setAtomOfferingClass("(卡+X类)硬件");
            } else if ("M".equals(atomOfferingClass)) {
                inventoryInfoDTO.setAtomOfferingClass("OneCyber标准产品");
            } else if ("A".equals(atomOfferingClass)) {
                inventoryInfoDTO.setAtomOfferingClass("自营软件服务");
            } else if ("B".equals(atomOfferingClass)) {
                inventoryInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.getDescribe(atomOfferingClass));
            } else if ("E".equals(atomOfferingClass)) {
                inventoryInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.getDescribe(atomOfferingClass));
            } else if ("G".equals(atomOfferingClass)) {
                inventoryInfoDTO.setAtomOfferingClass(AtomOfferingClassEnum.getDescribe(atomOfferingClass));
            }

            // 查询商品信息发布省地市信息
            List<CityInfoDO> cityInfoList = productHandlerMapper.selectCityInfo(x.getSkuOfferingCode());
            List<String> cityCodeList = cityInfoList.stream().map(item -> item.getCity()).collect(Collectors.toList());
            // 这里对库存状态进行判断。
            if (x.getInventory() != null) {
                if ("X".equals(atomOfferingClass)) {
                    // 卡+x
                    if (StringUtils.isNotEmpty(x.getInventoryMainId())) {
                        // List<DkcardxInventoryInfo> dkcardxInventoryInfos =
                        // dkcardxInventoryInfoMapper.selectByExample(new
                        // DkcardxInventoryInfoExample().createCriteria().andInventoryIdEqualTo(x.getInventoryId()).example());
                        // if (CollectionUtils.isNotEmpty(dkcardxInventoryInfos)) {
                        // List<DkcardxInventoryInfo> collect =
                        // dkcardxInventoryInfos.stream().filter(dkcardxInventoryInfo ->
                        // dkcardxInventoryInfo.getInventoryStatus() == 0).collect(Collectors.toList());
                        // inventoryInfoDTO.setInventoryStatus(CollectionUtils.isNotEmpty(collect) ? 1 :
                        // 0);
                        // inventoryInfoDTO.setInventory(dkcardxInventoryInfos.stream().mapToLong(DkcardxInventoryInfo::getCurrentInventory).sum());
                        // inventoryInfoDTO.setReserveInventory(dkcardxInventoryInfos.stream().mapToLong(DkcardxInventoryInfo::getReserveQuatity).sum());
                        // }
                        // 根据id去找对应的库存
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfoList = dkcardxInventoryDetailInfoMapper
                                .selectByExample(
                                        new DkcardxInventoryDetailInfoExample()
                                                .createCriteria()
                                                .andInventoryMainIdEqualTo(x.getInventoryMainId())
                                                .example());
                        Integer provinceTotalInventory = 0;
                        // 是否有省级库存
                        Boolean hasProvince = true;
                        // 是否短缺
                        Boolean hasThreshold = false;
                        Optional<DkcardxInventoryDetailInfo> optional = dkcardxInventoryDetailInfoList.stream().filter(
                                item -> StringUtils.isNotEmpty(item.getProvinceAliasName())).findFirst();
                        if (optional.isPresent()) {
                            provinceTotalInventory = optional.get().getTotalInventory();
                        } else {
                            hasProvince = false;
                        }
                        if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfoList)) {
                            hasThreshold = true;
                        }
                        // 有省级和地市一种处理方式，只有省级或地市一种处理方式
                        // 存在一个短缺就所有都短缺
                        for (DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo : dkcardxInventoryDetailInfoList) {
                            // 当只有1条数据，要么地市要么省份
                            if ((dkcardxInventoryDetailInfoList.size() == 1
                                    && StringUtils.isNotEmpty(dkcardxInventoryDetailInfo.getProvinceAliasName()))
                                    || !hasProvince) {
                                // 总库存小于库存预占则短缺
                                if (dkcardxInventoryDetailInfo.getTotalInventory() < x.getInventoryThreshold()) {
                                    hasThreshold = true;
                                }
                            } else {
                                // 省级地市都有的情况，忽略省级
                                // 比对发布市
                                if (StringUtils.isEmpty(dkcardxInventoryDetailInfo.getProvinceAliasName())) {
                                    // 当前地市不在商品发布地市中，则计算省级库存是否小于库存预警，
                                    // 否则计算省级库存+当前地市库存是否小于库存预警
                                    if (StringUtils.isNotEmpty(dkcardxInventoryDetailInfo.getLocation())
                                            && !cityCodeList.contains(dkcardxInventoryDetailInfo.getLocation())
                                            && !cityCodeList.contains("000")) {
                                        if (provinceTotalInventory < x.getInventoryThreshold()) {
                                            hasThreshold = true;
                                        }
                                    } else {
                                        if ((dkcardxInventoryDetailInfo.getTotalInventory()
                                                + provinceTotalInventory) < x.getInventoryThreshold()) {
                                            hasThreshold = true;
                                        }
                                    }

                                }
                            }
                        }
                        inventoryInfoDTO.setInventoryStatus(hasThreshold ? 0 : 1);
                    }

                } else {
                    // 设置库存状态
                    inventoryInfoDTO.setInventoryStatus(
                            (x.getInventory() + x.getReserveInventory() >= x.getInventoryThreshold()) ? 1 : 0);
                    // 计算库存 现在有总库存显示 不用计算了 直接展示实际库存数
                    inventoryInfoDTO.setInventory(x.getInventory());
                    inventoryInfoDTO.setReserveInventory(x.getReserveInventory());
                }
            }
            inventoryInfoDTO.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(x.getSpuOfferingClass()));
            List<CityInfoDTO> cityInfo = new ArrayList<>();
            Map<String, Set<String>> cityMap = new HashMap<>();
            // provinceCityConfig.getcityCodeNameMap().get(city);
            for (CityInfoDO city : cityInfoList) {
                String province = city.getProvince();
                String provinceName = provinceCityConfig.getProvinceCodeNameMap().get(province);
                String cityName = provinceCityConfig.getcityCodeNameMap().get(city.getCity());
                if (StringUtils.isNotEmpty(provinceName)) {
                    // 如果是河南的商品，则默认省内接单，否则默认OS接单
                    /*
                     * if ("371".equals(province)) {
                     * productConfigDTO.setOrdertakeType(2);
                     * } else {
                     * productConfigDTO.setOrdertakeType(1);
                     * }
                     */
                    if (!cityMap.containsKey(provinceName)) {
                        Set<String> citySet = new HashSet<>();
                        citySet.add(cityName);
                        cityMap.put(provinceName, citySet);
                    } else {
                        Set<String> cityExistSet = cityMap.get(provinceName);
                        cityExistSet.add(cityName);
                    }
                }
            }
            for (String province : cityMap.keySet()) {
                CityInfoDTO cityInfoDTO = new CityInfoDTO();
                cityInfoDTO.setProvince(province);
                Set<String> citySetList = cityMap.get(province);
                List<String> provinceCity = new ArrayList<>();
                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(citySetList)) {
                    provinceCity = new ArrayList<>(citySetList);
                }
                cityInfoDTO.setCity(provinceCity);
                cityInfo.add(cityInfoDTO);
            }
            if (CollectionUtil.isNotEmpty(cityInfoList)){
                inventoryInfoDTO.setBeId(cityInfoList.get(0).getProvince());
            }
            inventoryInfoDTO.setCityInfo(cityInfo);
            inventoryInfoDTOS.add(inventoryInfoDTO);
        });
        String contentStr = content.toString();
        contentStr = contentStr.substring(0, contentStr.length() - 1);
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.INVENTORY_CONFIG.code, contentStr,
                LogResultEnum.LOG_SUCESS.code, null);
        return baseAnswer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configCardXInventory(ConfigCardXInventoryParam configCardXInventoryParam,
                                     LoginIfo4Redis loginIfo4Redis) {
        // 配置kx库存
        String atomId = configCardXInventoryParam.getAtomId();
        String inventoryMainId = configCardXInventoryParam.getInventoryMainId();
        Long threshold = configCardXInventoryParam.getThreshold();
        Boolean isNotice = configCardXInventoryParam.getIsNotice();
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoMapper.selectByPrimaryKey(atomId);
        Long thresholdExist = atomOfferingInfo.getInventoryThreshold();
        String existInventoryMainId = atomOfferingInfo.getInventoryMainId();
        Boolean inventoryChange = StringUtils.isEmpty(existInventoryMainId)
                || !inventoryMainId.equals(existInventoryMainId);
        String spuCode = atomOfferingInfo.getSpuCode();
        String skuCode = atomOfferingInfo.getSkuCode();
        String skuId = atomOfferingInfo.getSkuId();
        String offeringCode = atomOfferingInfo.getOfferingCode();
        Date date = new Date();
        BeanUtils.copyProperties(configCardXInventoryParam, atomOfferingInfo);
        atomOfferingInfo.setInventoryThreshold(threshold);
        DkcardxInventoryMainInfo dkcardxInventoryMainInfo = dkcardxInventoryMainInfoMapper
                .selectByPrimaryKey(inventoryMainId);

        // 同一个sku下，不同原子不允许配置相同设备型号
        int deviceVersionConfigedAtomCount = dkcardxInventoryMainInfoMapperExt.getDeviceVersionConfigedAtomCount(
                spuCode, skuCode, atomId, dkcardxInventoryMainInfo.getDeviceVersion());
        if (deviceVersionConfigedAtomCount >= 1) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,
                    "一个sku下已经有其他原子商品配置了该设备型号:" + dkcardxInventoryMainInfo.getDeviceVersion());
        }

        DkcardxInventoryMainInfo dkcardxInventoryMainInfoExist = null;
        if (inventoryChange) {
            if (StringUtils.isNotEmpty(existInventoryMainId)) {
                dkcardxInventoryMainInfoExist = dkcardxInventoryMainInfoMapper.selectByPrimaryKey(existInventoryMainId);
                if (atomOfferingInfo.getReserveInventory() > 0) {
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "此商品有被预占的订单，无法修改");
                }

                List<Integer> orderStatusList = Stream.of(OrderStatusInnerEnum.ORDER_SUCCESS.getStatus(),
                        OrderStatusInnerEnum.ORDER_FAIL.getStatus(), OrderStatusInnerEnum.VALET_DRAFT.getStatus(),
                        OrderStatusInnerEnum.VALET_DRAFT_DELETE.getStatus()).collect(Collectors.toList());
                List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(
                        new Order2cAtomInfoExample().createCriteria()
                                .andSpuOfferingCodeEqualTo(spuCode)
                                .andSkuOfferingCodeEqualTo(skuCode)
                                .andAtomOfferingCodeEqualTo(offeringCode)
                                .andOrderStatusNotIn(orderStatusList)
                                .example());
                if (CollectionUtils.isNotEmpty(order2cAtomInfoList)) {
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "此商品有未结束的订单，无法修改");
                }
            }
        }

        List<String> phones = new ArrayList<>();

        String cooperatorId = atomOfferingInfo.getCooperatorId();
        // 未配置合作伙伴不允许配置库存
        if (cooperatorId == null) {
            throw new BusinessException(StatusConstant.NO_COOPERATOR);
        }

        AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample = new AtomOfferingCooperatorRelationExample();
        atomOfferingCooperatorRelationExample.createCriteria()
                .andAtomOfferingIdEqualTo(atomId);
        List<AtomOfferingCooperatorRelation> atomOfferingCooperatorRelationList = atomOfferingCooperatorRelationService.listAtomOfferingCooperatorRelationByExample(atomOfferingCooperatorRelationExample);
        if (CollectionUtil.isEmpty(atomOfferingCooperatorRelationList)){
            throw new BusinessException(StatusConstant.AUTH_ERROR, "配置合作伙伴从账号之后才能配置库存");
        }

        // 获取原子商品和从合作伙伴关系
        List<Data4User> data4UserList = atomOfferingCooperatorRelationService.listCooperatorUserInfo(atomOfferingInfo.getId());
        List<String> cooperatorPhoneList = data4UserList.stream()
                .map(Data4User::getPhone)
                .collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cooperatorPhoneList)){
            phones.addAll(cooperatorPhoneList);
        }

        BaseAnswer<Data4User> primaryCooperatorData4UserBaseAnswer = userFeignClient.partnerInfoById(cooperatorId);
        if (primaryCooperatorData4UserBaseAnswer == null
                || !primaryCooperatorData4UserBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                || primaryCooperatorData4UserBaseAnswer.getData() == null) {
            log.error("配置库存时调用获取主合作伙伴信息失败。主合作伙伴ID:{}", cooperatorId);
            throw new RuntimeException("配置库存时调用获取从合作伙伴信息失败。");
        }

        Data4User primaryUserData = primaryCooperatorData4UserBaseAnswer.getData();
        Boolean isSend = primaryUserData.getIsSend();
        if (isSend != null && isSend){
            String primaryCooperatorPhone = primaryUserData.getPhone();
            phones.add(primaryCooperatorPhone);
        }
        /*BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
        Data4User cooperator = data4UserBaseAnswer.getData();
        if (cooperator.getIsPrimary()) {
            throw new BusinessException(StatusConstant.AUTH_ERROR, "配置合作伙伴从账号之后才能配置库存");
        }

        String phone = cooperator.getPhone();
        // 查询主合作伙伴信息
        BaseAnswer<Data4User> userBaseAnswer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
        if (userBaseAnswer == null
                || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                || userBaseAnswer.getData() == null) {
            log.error("配置库存时调用获取主用户信息失败。从合作伙伴ID:{}", cooperatorId);
            throw new RuntimeException("调用获取主用户信息失败。从合作伙伴ID:" + cooperatorId);
        }
        if (userBaseAnswer.getData().getIsSend()) {
            phones.add(userBaseAnswer.getData().getPhone());
        }
        phones.add(phone);*/
        // 对于未配置过库存的需要修改配置了库存
        if (!atomOfferingInfo.getIsInventory()) {
            atomOfferingInfo.setIsInventory(true);
        }

        // 这里对短信通知状态重置 规则也可以修改为订单库存量大于预警数才修改。都可
        if (atomOfferingInfo.getNotified() != null && atomOfferingInfo.getNotified()) {
            atomOfferingInfo.setNotified(false);
        }

        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfoList = dkcardxInventoryDetailInfoMapper
                .selectByExample(
                        new DkcardxInventoryDetailInfoExample()
                                .createCriteria()
                                .andInventoryMainIdEqualTo(inventoryMainId)
                                .example());
        Integer provinceTotalInventory = 0;
        Boolean hasProvince = true;
        Boolean hasThreshold = false;
        Optional<DkcardxInventoryDetailInfo> optional = dkcardxInventoryDetailInfoList.stream().filter(
                item -> StringUtils.isNotEmpty(item.getProvinceAliasName())).findFirst();
        if (optional.isPresent()) {
            provinceTotalInventory = optional.get().getTotalInventory();
        } else {
            hasProvince = false;
        }

        String warehouse = "";
        // 有省级和地市一种处理方式，只有省级或地市一种处理方式
        // 存在一个短缺就所有都短缺
        for (DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo : dkcardxInventoryDetailInfoList) {
            if ((dkcardxInventoryDetailInfoList.size() == 1
                    && StringUtils.isNotEmpty(dkcardxInventoryDetailInfo.getProvinceAliasName())) || !hasProvince) {
                if (dkcardxInventoryDetailInfo.getTotalInventory() < threshold) {
                    hasThreshold = true;
                }
                if (!hasProvince){
                    warehouse = dkcardxInventoryDetailInfo.getCityName();
                }else{
                    warehouse = dkcardxInventoryDetailInfo.getProvinceName();
                }
            } else {
                // 省级地市都有的情况，忽略省级
                if (StringUtils.isEmpty(dkcardxInventoryDetailInfo.getProvinceAliasName())) {
                    if ((dkcardxInventoryDetailInfo.getTotalInventory() + provinceTotalInventory) < threshold) {
                        hasThreshold = true;
                        warehouse = dkcardxInventoryDetailInfo.getCityName();
                    }
                }
            }
        }
        SkuOfferingInfo skuOfferingInfo = skuOfferingInfoMapper.selectByPrimaryKey(skuId);
        String custCode = skuOfferingInfo.getCustCode();
        String templateId = skuOfferingInfo.getTemplateId();
        Optional<CardInventoryMainInfo> inventoryMainInfoOptional = cardInventoryMainInfoMapper
                .selectByExample(new CardInventoryMainInfoExample().createCriteria()
                        .andCustCodeEqualTo(custCode).andTemplateIdEqualTo(templateId).example())
                .stream().findFirst();
        if (!inventoryMainInfoOptional.isPresent()) {
            throw new BusinessException("10004", "sku商品不是X类型商品");
        }
        CardInventoryMainInfo cardInventoryMainInfo = inventoryMainInfoOptional.get();
        String cardInventoryMainInfoId = cardInventoryMainInfo.getId();
        List<CardInventoryAtomInfo> cardInventoryAtomInfos = cardInventoryAtomInfoMapper
                .selectByExample(new CardInventoryAtomInfoExample().createCriteria()
                        .andCardInventoryMainIdEqualTo(cardInventoryMainInfoId).andAtomIdEqualTo(atomId).example());
        String cardContainingTerminal = atomOfferingInfo.getCardContainingTerminal();
        if ("1".equals(cardContainingTerminal)) {
            atomOfferingInfo.setCardInfoInventoryMainId(cardInventoryMainInfoId);
        }
        atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfo);
        // 删除+X终端库存原子详情
        DkcardxInventoryAtomInfoExample dkcardxInventoryAtomInfoExample = new DkcardxInventoryAtomInfoExample();
        dkcardxInventoryAtomInfoExample.createCriteria()
                .andAtomIdEqualTo(atomId);

        Long atomInventory = 0L;
        Map<String, Long> locationInventoryMap = new HashMap<>();
        List<DkcardxInventoryAtomInfo> inventoryAtomInfos = dkcardxInventoryAtomInfoService
                .getInventoryAtomInfoByExample(dkcardxInventoryAtomInfoExample);
        if (CollectionUtil.isNotEmpty(inventoryAtomInfos)) {
            inventoryAtomInfos.forEach(dkcardxInventoryAtomInfo -> {
                String inventoryDetailId = dkcardxInventoryAtomInfo.getInventoryDetailId();
                DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfoMapper
                        .selectByPrimaryKey(inventoryDetailId);
                if (!Optional.ofNullable(dkcardxInventoryDetailInfo).isPresent()) {
                    throw new BusinessException("10004", "原子商品id为：" + dkcardxInventoryAtomInfo.getAtomId()
                            + "库存配置详情id为：" + inventoryDetailId + "未发现");
                }
                String location = dkcardxInventoryDetailInfo.getLocation();
                if (StringUtils.isEmpty(location)) {
                    // 省级的
                    locationInventoryMap.put(dkcardxInventoryDetailInfo.getBeId(),
                            dkcardxInventoryAtomInfo.getAtomInventory());
                } else {
                    // 地市的
                    locationInventoryMap.put(dkcardxInventoryDetailInfo.getLocation(),
                            dkcardxInventoryAtomInfo.getAtomInventory());
                }
            });
        }
        dkcardxInventoryAtomInfoService.deleteInventoryAtomInfoByExample(dkcardxInventoryAtomInfoExample);
        // 获取商品的发布省份地市
        SkuReleaseTargetExample skuReleaseTargetExample = new SkuReleaseTargetExample();
        skuReleaseTargetExample.createCriteria()
                .andSkuOfferingCodeEqualTo(skuCode);
        List<SkuReleaseTarget> skuReleaseTargetList = skuReleaseTargetMapper.selectByExample(skuReleaseTargetExample);
        if (CollectionUtil.isEmpty(skuReleaseTargetList)) {
            throw new BusinessException("10004", "商品未配置发布省份地市信息,请先配置发布省份地市信息");
        }

        List<DkcardxInventoryAtomInfo> inventoryAtomInfoList = new ArrayList<>();
        Set<String> beIdSet = new HashSet<>();
        Set<String> cityCodeSet = new HashSet<>();
        skuReleaseTargetList.forEach(skuReleaseTarget -> {
            String provinceCode = skuReleaseTarget.getProvinceCode();
            String cityCode = skuReleaseTarget.getCityCode();
            // 先处理地市级的库存关联
            if (StringUtils.isNotEmpty(cityCode)) {
                cityCodeSet.add(cityCode);
                addInventoryAtomInfoList(inventoryMainId, provinceCode, cityCode, atomId,
                        spuCode, skuCode, offeringCode, date, locationInventoryMap, inventoryAtomInfoList);
            }
            if (!beIdSet.contains(provinceCode)) {
                beIdSet.add(provinceCode);
            }
        });

        // 处理省级库存关联
        if (!beIdSet.isEmpty() && beIdSet.size() > 0) {
            Iterator<String> iterator = beIdSet.iterator();
            if (iterator.hasNext()) {
                String beId = iterator.next();
                addInventoryAtomInfoList(inventoryMainId, beId, "", atomId,
                        spuCode, skuCode, offeringCode, date, locationInventoryMap, inventoryAtomInfoList);
                // 只关联了省级的时候，需要重建对应终端库存下面的所有市
                if (cityCodeSet.size() == 0) {
                    for (DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo : dkcardxInventoryDetailInfoList) {
                        if (StringUtils.isNotEmpty(dkcardxInventoryDetailInfo.getLocation())
                                && dkcardxInventoryDetailInfo.getBeId().equals(beId)) {
                            addInventoryAtomInfoList(inventoryMainId, beId, dkcardxInventoryDetailInfo.getLocation(),
                                    atomId,
                                    spuCode, skuCode, offeringCode, date, locationInventoryMap, inventoryAtomInfoList);
                        }
                    }
                }
            }
        }

        if (CollectionUtil.isNotEmpty(inventoryAtomInfoList)) {
            dkcardxInventoryAtomInfoService.batchAddInventoryAtomInfo(inventoryAtomInfoList);
        }
        // 初始化码号库存与原子商品关联关系 原子商品是含卡的 才初始化
        if (CollectionUtil.isEmpty(cardInventoryAtomInfos) && "1".equals(cardContainingTerminal)) {
            // 初始化
            CardInventoryAtomInfo cardInventoryAtomInfo = new CardInventoryAtomInfo();
            cardInventoryAtomInfo.setId(BaseServiceUtils.getId());
            cardInventoryAtomInfo.setCardInventoryMainId(cardInventoryMainInfoId);
            cardInventoryAtomInfo.setAtomInventory(0);
            cardInventoryAtomInfo.setAtomId(atomId);
            cardInventoryAtomInfo.setSpuCode(spuCode);
            cardInventoryAtomInfo.setSkuCode(skuCode);
            cardInventoryAtomInfo.setOfferingCode(offeringCode);
            cardInventoryAtomInfo.setCreateTime(date);
            cardInventoryAtomInfo.setUpdateTime(date);
            cardInventoryAtomInfoMapper.insert(cardInventoryAtomInfo);
        } else {
            log.info("原子商品与码号库存已关联，cardInventoryMainInfoId：{}", cardInventoryMainInfoId);
        }

        String deviceVersion = dkcardxInventoryMainInfo.getDeviceVersion();

        String deviceVersionExist = deviceVersion;
        if (Optional.ofNullable(dkcardxInventoryMainInfoExist).isPresent()) {
            deviceVersionExist = dkcardxInventoryMainInfoExist.getDeviceVersion();
        }
        String content = "【配置设备型号等】\n";
        if (StringUtils.isNotEmpty(existInventoryMainId) && !inventoryMainId.equals(existInventoryMainId)) {
            content = "【重新配置设备型号等】\n";
        }
        content = content.concat("商品组/销售组编码").concat(atomOfferingInfo.getSpuCode()).concat("\n")
                .concat("商品规格编码").concat(atomOfferingInfo.getSkuCode()).concat("\n")
                .concat("原子商品编码").concat(atomOfferingInfo.getOfferingCode()).concat("\n");
        if (StringUtils.isEmpty(existInventoryMainId)) {
            content = content.concat("设备型号为").concat(deviceVersion)
                    .concat(",配置库存预警值").concat(threshold + "");
        } else {
            if (!deviceVersion.equals(deviceVersionExist)) {
                content = content.concat("设备型号由").concat(deviceVersionExist)
                        .concat("修改为").concat(deviceVersion);
            }
            if (!threshold.equals(thresholdExist)) {
                content = content.concat(",库存预警值由").concat(thresholdExist + "")
                        .concat("修改为").concat(threshold + "");
            }

        }
        if (isNotice) {
            content = content.concat(",勾选短信提醒");
        } else {
            content = content.concat(",取消短信提醒");
        }

        // 记录日志
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code, GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                content, LogResultEnum.LOG_SUCESS.code, null);

        if (hasThreshold && isNotice) {
            log.info("配置库存发送短信：configInventory inventory={}; reserveInventory={}; threshold={}; isNotice={}",
                    atomOfferingInfo.getInventory(), atomOfferingInfo.getReserveInventory(),
                    atomOfferingInfo.getInventoryThreshold(), isNotice);

            Map<String, String> msgMap = new HashMap<>();
            phones = phones.stream().distinct().collect(Collectors.toList());
            Msg4Request msg4Request = new Msg4Request();
            msg4Request.setMobiles(phones);
            msgMap.put("atomOfferingName", atomOfferingInfo.getOfferingName());
            msgMap.put("offeringCode", offeringCode);
            msgMap.put("warehouse", warehouse);
            msg4Request.setMessage(msgMap);
            msg4Request.setTemplateId(smsInventoryTemplateId);
            smsFeignClient.asySendMessage(msg4Request);
        }
    }

    private void addInventoryAtomInfoList(String inventoryMainId,
                                          String provinceCode,
                                          String cityCode,
                                          String atomId,
                                          String spuCode,
                                          String skuCode,
                                          String offeringCode,
                                          Date date,
                                          Map<String, Long> locationInventoryMap,
                                          List<DkcardxInventoryAtomInfo> inventoryAtomInfoList) {
        Long atomInventory = 0L;
        // 获取库存详情信息
        DkcardxInventoryDetailInfoExample dkcardxInventoryDetailInfoExample = new DkcardxInventoryDetailInfoExample();
        DkcardxInventoryDetailInfoExample.Criteria detailInfoExampleCriteria = dkcardxInventoryDetailInfoExample
                .createCriteria();
        detailInfoExampleCriteria.andInventoryMainIdEqualTo(inventoryMainId)
                .andBeIdEqualTo(provinceCode);

        // 不为空查地市库存，为空查省级库存
        if (StringUtils.isNotEmpty(cityCode)) {
            atomInventory = locationInventoryMap.get(cityCode) == null ? 0L : locationInventoryMap.get(cityCode);
            detailInfoExampleCriteria.andLocationEqualTo(cityCode);
        } else {
            atomInventory = locationInventoryMap.get(provinceCode) == null ? 0L
                    : locationInventoryMap.get(provinceCode);
            detailInfoExampleCriteria.andLocationIsNull();
        }
        List<DkcardxInventoryDetailInfo> inventoryDetailInfoList = dkcardxInventoryDetailInfoMapper
                .selectByExample(dkcardxInventoryDetailInfoExample);
        if (CollectionUtil.isNotEmpty(inventoryDetailInfoList)) {
            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = inventoryDetailInfoList.get(0);
            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = new DkcardxInventoryAtomInfo();
            dkcardxInventoryAtomInfo.setId(BaseServiceUtils.getId());
            dkcardxInventoryAtomInfo.setInventoryDetailId(dkcardxInventoryDetailInfo.getId());
            dkcardxInventoryAtomInfo.setInventoryMainId(inventoryMainId);
            dkcardxInventoryAtomInfo.setAtomId(atomId);
            dkcardxInventoryAtomInfo.setAtomInventory(atomInventory);
            dkcardxInventoryAtomInfo.setSpuCode(spuCode);
            dkcardxInventoryAtomInfo.setSkuCode(skuCode);
            dkcardxInventoryAtomInfo.setOfferingCode(offeringCode);
            dkcardxInventoryAtomInfo.setCreateTime(date);
            dkcardxInventoryAtomInfo.setUpdateTime(date);
            inventoryAtomInfoList.add(dkcardxInventoryAtomInfo);
        }
    }

    /**
     * 设置新的卡+X库存信息
     *
     * @param inventoryId
     * @param beId
     * @param provinceName
     * @param location
     * @param cityName
     * @param inventoryCount
     * @param date
     * @return
     */
    private DkcardxInventoryInfo setDkcardxInventoryInfo(String inventoryId,
                                                         String beId,
                                                         String provinceName,
                                                         String location,
                                                         String cityName,
                                                         Integer inventoryCount,
                                                         Date date) {
        DkcardxInventoryInfo dkcardxInventoryInfo = new DkcardxInventoryInfo();
        dkcardxInventoryInfo.setId(BaseServiceUtils.getId());
        dkcardxInventoryInfo.setInventoryId(inventoryId);
        dkcardxInventoryInfo.setBeId(beId);
        dkcardxInventoryInfo.setProvinceName(provinceName);
        dkcardxInventoryInfo.setLocation(location);
        dkcardxInventoryInfo.setCityName(cityName);
        dkcardxInventoryInfo.setReserveQuatity(0);
        dkcardxInventoryInfo.setCurrentInventory(inventoryCount);
        dkcardxInventoryInfo.setTotalInventory(inventoryCount);
        dkcardxInventoryInfo.setInventoryStatus(1);
        dkcardxInventoryInfo.setInventoryWarn(0);
        dkcardxInventoryInfo.setCreateTime(date);
        dkcardxInventoryInfo.setUpdateTime(date);
        return dkcardxInventoryInfo;
    }

    /**
     * 更新的卡+X库存信息
     *
     * @param dkcardxInventoryInfo
     * @param addInventoryCount
     * @param date
     */
    private void updateDkcardxInventoryInfo(DkcardxInventoryInfo dkcardxInventoryInfo,
                                            Integer addInventoryCount,
                                            Date date) {
        DkcardxInventoryInfo dkcardxInventoryInfoUpdate = new DkcardxInventoryInfo();
        dkcardxInventoryInfoUpdate.setId(dkcardxInventoryInfo.getId());
        Integer newTotalInventory = dkcardxInventoryInfo.getTotalInventory() + addInventoryCount;
        dkcardxInventoryInfoUpdate.setTotalInventory(dkcardxInventoryInfo.getTotalInventory() + addInventoryCount);
        dkcardxInventoryInfoUpdate.setCurrentInventory(dkcardxInventoryInfo.getCurrentInventory() + addInventoryCount);
        dkcardxInventoryInfoUpdate.setUpdateTime(date);
        if (newTotalInventory > dkcardxInventoryInfo.getInventoryWarn()) {
            dkcardxInventoryInfoUpdate.setInventoryStatus(1);
        } else {
            dkcardxInventoryInfoUpdate.setInventoryStatus(0);
        }
        dkcardxInventoryInfoService.updateDkcardxInventoryInfoById(dkcardxInventoryInfoUpdate);
    }

    @Override
    public void exportInventory(InventoryExportParam inventoryExportParam, LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        String userId = loginIfo4Redis.getUserId();

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue()
                .get(REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes)
                || (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL))) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        /*
         * 这里会对人员角色进行判断。
         * 如果是超管和运营管理员 、客服管理员看到的应该是全部的库存信息，
         * 如果是合作伙伴看到的应该只有属于合作伙伴的信息
         */
        List<String> userIdList = new ArrayList<>();
        String roleType = loginIfo4Redis.getRoleType();
        // 从合作
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL)) {
            userIdList.add(userId);
            // 主合作
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)) {

            if (roleType.equals(PARTNER_LORD_ROLE)) {
                BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                    userIdList = downUserIds.getData();
                }
                userIdList.add(userId);
            } else if (roleType.equals(BaseConstant.PARTNER_PROVINCE)) {
                log.info("合作伙伴省管查询库存信息：partnerProvince：{}", roleType);
                // 省管配置权限为主合作伙伴权限
                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
                if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                    throw new BusinessException("10004", "合作伙伴省管账号错误");
                }

                Data4User data4User = data4UserBaseAnswer.getData();
                String companyType = data4User.getCompanyType();
                boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType)
                        && "2".equals(companyType);
                String userLocation = data4User.getLocationIdPartner();
                log.info("登录用户是否是合作伙伴省管：partnerIsProvinceUser:{}", isProvinceUser);
                if (isProvinceUser) {
                    BaseAnswer<Data4User> userPartner = userFeignClient
                            .getUserPartnerPrimaryByPartnerName(data4User.getPartnerName());
                    if (userPartner == null || !SUCCESS.getStateCode().equals(userPartner.getStateCode())) {
                        throw new BusinessException("10004", "合作伙伴省管获取主合作伙伴账号错误");
                    }
                    Data4User userPartnerData = userPartner.getData();
                    if (Optional.ofNullable(userPartnerData).isPresent()) {
                        BaseAnswer<List<String>> downUserIds = userFeignClient
                                .getDownUserIds(userPartnerData.getUserId());
                        if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                            userIdList = downUserIds.getData();
                        }
                        userIdList.add(userPartnerData.getUserId());
                        userIdList.add(userId);
                    }
                    if ("all".equals(userLocation)) {
                        inventoryExportParam.setBeId(data4User.getBeIdPartner());
                    } else {
                        inventoryExportParam.setLocation(userLocation);
                    }
                } else {
                    throw new BusinessException(StatusConstant.AUTH_ERROR, "账号不属于省公司");
                }
            }
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)) {
        } else {
            // 这里考虑后面可能有多种角色。应该也是没有权限的
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }
        inventoryExportParam.setUserIdList(userIdList);
        List<InventoryExportDO> inventoryExportDOList = inventoryHandlerMapper
                .listExportInventory(inventoryExportParam);
        if (CollectionUtil.isEmpty(inventoryExportDOList)) {
            throw new BusinessException(BaseErrorConstant.INVENTORY_NOT_FOUND);
        }

        List<InventoryExportVO> inventoryExportVOList = new ArrayList<>();
        // 用来存储库存信息以方便合并发布省市
        ConcurrentHashMap<String, HashMap> inventoryMap = new ConcurrentHashMap();
        BigDecimal priceBase = new BigDecimal(1000);
        inventoryExportDOList.stream().forEach(inventoryExportDO -> {
            // System.out.println(JSON.toJSON(inventoryExportDO));
            String skuOfferingCode = inventoryExportDO.getSkuOfferingCode();
            if (StringUtils.isEmpty(skuOfferingCode)) {
                skuOfferingCode = "";
            }
            String spuOfferingCode = inventoryExportDO.getSpuOfferingCode();
            if (StringUtils.isEmpty(spuOfferingCode)) {
                spuOfferingCode = "";
            }
            String offeringCode = inventoryExportDO.getOfferingCode();
            if (StringUtils.isEmpty(offeringCode)) {
                offeringCode = "";
            }
            String key = spuOfferingCode
                    .concat("_").concat(skuOfferingCode)
                    .concat("_").concat(offeringCode);
            Object inventoryValue = inventoryMap.get(key);
            String provinceCode = inventoryExportDO.getProvinceCode();
            String cityCode = inventoryExportDO.getCityCode();
            HashMap<String, Set> provCityMap;
            // 如果不存在 ‘商品组/销售商品编码_商品编码（规格）_原子商品编码组成的省市信息，则新增，否则更新
            boolean hasCityCode = StringUtils.isNotEmpty(cityCode);
            if (inventoryValue == null) {
                provCityMap = new HashMap();
                Set citySet = new HashSet();
                if (hasCityCode) {
                    citySet.add(cityCode);
                }
                if (StringUtils.isNotEmpty(provinceCode)) {
                    provCityMap.put(provinceCode, citySet);
                }
                inventoryMap.put(key, provCityMap);

                // 复制数据
                InventoryExportVO inventoryExportVO = new InventoryExportVO();
                BeanUtils.copyProperties(inventoryExportDO, inventoryExportVO);

                inventoryExportVO.setCreateTimeStr(
                        DateUtils.dateToStr(inventoryExportDO.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT));

                BigDecimal settlePrice = inventoryExportDO.getSettlePrice();
                if (settlePrice != null) {
                    inventoryExportVO.setSettlePrice(settlePrice.divide(priceBase, 2, RoundingMode.HALF_UP));
                }
                BigDecimal price = inventoryExportDO.getPrice();
                if (price != null) {
                    inventoryExportVO.setPrice(price.divide(priceBase, 2, RoundingMode.HALF_UP));
                }

                String atomOfferingClass = inventoryExportDO.getAtomOfferingClass();
                String supplierName = inventoryExportDO.getSupplierName();
                if ("S".equals(atomOfferingClass)) {
                    inventoryExportVO.setAtomOfferingClass("软件");
                } else if ("H".equals(atomOfferingClass)) {
                    if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(atomOfferingClass)) {
                        inventoryExportVO.setAtomOfferingClass("合同履约类硬件");
                    } else {
                        inventoryExportVO.setAtomOfferingClass("代销类硬件");
                    }
                } else if ("O".equals(atomOfferingClass)) {
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass("OneNET独立服务");
                } else if ("D".equals(atomOfferingClass)) {
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass("（DICT）产品增值服务包");
                } else if ("P".equals(atomOfferingClass)) {
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass("OnePark独立服务");
                } else if ("F".equals(atomOfferingClass)) {
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass("行车卫士标准产品");
                } else if ("K".equals(atomOfferingClass)) {
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass("OneTraffic独立服务");
                } else if ("X".equals(atomOfferingClass)) {
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass("(卡+X类)硬件");
                } else if ("A".equals(atomOfferingClass)) {
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass("自营软件服务");
                }else{
                    inventoryExportVO.setPartnerName(supplierName);
                    inventoryExportVO.setAtomOfferingClass(AtomOfferingClassEnum.getDescribe(atomOfferingClass));
                }
                // 这里对库存状态进行判断。
                Integer inventory = inventoryExportDO.getInventory();
                Integer reserveInventory = inventoryExportDO.getReserveInventory();
                Integer inventoryThreshold = inventoryExportDO.getInventoryThreshold();
                if (inventory != null) {
                    if ("X".equals(atomOfferingClass)) {
                        // 卡+x
                        /*
                         * if (StringUtils.isNotEmpty(inventoryExportDO.getInventoryId())) {
                         * List<DkcardxInventoryInfo> dkcardxInventoryInfos =
                         * dkcardxInventoryInfoMapper.selectByExample(new
                         * DkcardxInventoryInfoExample().createCriteria().andInventoryIdEqualTo(
                         * inventoryExportDO.getInventoryId()).example());
                         * if (CollectionUtils.isNotEmpty(dkcardxInventoryInfos)) {
                         * List<DkcardxInventoryInfo> collect =
                         * dkcardxInventoryInfos.stream().filter(dkcardxInventoryInfo ->
                         * dkcardxInventoryInfo.getInventoryStatus() == 0).collect(Collectors.toList());
                         * inventoryExportVO.setInventoryStatusName(CollectionUtils.isNotEmpty(collect)
                         * ? "充足" : "短缺");
                         * inventoryExportVO.setInventory(String.valueOf(dkcardxInventoryInfos.stream().
                         * mapToInt(DkcardxInventoryInfo::getCurrentInventory).sum()));
                         * inventoryExportVO.setInventoryTotal(String.valueOf(dkcardxInventoryInfos.
                         * stream().mapToInt(DkcardxInventoryInfo::getTotalInventory).sum()));
                         * inventoryExportVO.setReserveInventory(dkcardxInventoryInfos.stream().mapToInt
                         * (DkcardxInventoryInfo::getReserveQuatity).sum());
                         * }
                         * }
                         */
                        String inventoryMainId = inventoryExportDO.getInventoryMainId();
                        if (StringUtils.isNotEmpty(inventoryMainId)) {
                            DkcardxInventoryDetailInfoParam detailInfoParam = new DkcardxInventoryDetailInfoParam();
                            detailInfoParam.setInventoryMainId(inventoryMainId);
                            if (hasCityCode) {
                                detailInfoParam.setLocation(cityCode);
                            }
                            List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfoList = dkcardxInventoryDetailInfoService
                                    .listDkcardxInventoryDetailInfoExport(detailInfoParam, loginIfo4Redis);
                            if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfoList)) {
                                int currentInventory = dkcardxInventoryDetailInfoList.stream()
                                        .mapToInt(DkcardxInventoryDetailInfo::getCurrentInventory).sum();
                                inventoryExportVO
                                        .setInventoryStatusName(currentInventory > inventoryThreshold ? "充足" : "短缺");
                                inventoryExportVO.setInventory(String.valueOf(currentInventory));
                                inventoryExportVO.setInventoryTotal(String.valueOf(dkcardxInventoryDetailInfoList
                                        .stream().mapToInt(DkcardxInventoryDetailInfo::getTotalInventory).sum()));
                                inventoryExportVO.setReserveInventory(dkcardxInventoryDetailInfoList.stream()
                                        .mapToInt(DkcardxInventoryDetailInfo::getReserveQuatity).sum());
                            }
                        }
                    } else if (StringUtils.isNotEmpty(inventoryExportDO.getInventoryType())
                            && inventoryExportDO.getInventoryType().equals("1")) {
                        inventoryExportVO.setInventoryStatusName("充足");
                        inventoryExportVO.setInventory("♾️");
                        inventoryExportVO.setInventoryTotal("♾️");
                    } else {
                        // 设置库存状态
                        inventoryExportVO.setInventoryStatusName(
                                (inventory + reserveInventory > inventoryThreshold) ? "充足" : "短缺");
                        inventoryExportVO.setInventory(String.valueOf(inventory));
                        // 计算库存
                        inventoryExportVO.setInventoryTotal(String.valueOf(inventory + reserveInventory));
                    }
                }
                inventoryExportVO
                        .setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(inventoryExportDO.getSpuOfferingClass()));
                inventoryExportVO.setReserveInventory(reserveInventory);

                inventoryExportVOList.add(inventoryExportVO);
            } else {
                provCityMap = (HashMap) inventoryValue;
                Object cityObj = provCityMap.get(provinceCode);
                // 判断该省份省份已经存在，不存在新增，存在则更新
                if (cityObj == null) {
                    Set citySet = new HashSet();
                    if (hasCityCode) {
                        citySet.add(cityCode);
                        provCityMap.put(provinceCode, citySet);
                    }
                } else {
                    Set citySet = (Set) cityObj;
                    if (hasCityCode) {
                        citySet.add(cityCode);
                    }
                    provCityMap.put(provinceCode, citySet);
                }
            }
        });

        inventoryExportVOList.stream().forEach(inventoryExportVO -> {
            String spuOfferingCode = inventoryExportVO.getSpuOfferingCode();
            if (StringUtils.isEmpty(spuOfferingCode)) {
                spuOfferingCode = "";
            }
            String skuOfferingCode = inventoryExportVO.getSkuOfferingCode();
            if (StringUtils.isEmpty(skuOfferingCode)) {
                skuOfferingCode = "";
            }
            String offeringCode = inventoryExportVO.getOfferingCode();
            if (StringUtils.isEmpty(offeringCode)) {
                offeringCode = "";
            }
            String key = spuOfferingCode
                    .concat("_").concat(skuOfferingCode)
                    .concat("_").concat(offeringCode);
            Object inventoryValue = inventoryMap.get(key);
            if (inventoryValue != null) {
                List proList = new ArrayList();
                List cityList = new ArrayList();
                // 省市转换
                ((HashMap) inventoryValue).forEach((pro, citySet) -> {
                    String proName = provinceCityConfig.getProvinceCodeNameMap().get(pro);
                    String cityNameStr = "{";
                    if (citySet != null) {
                        Iterator cityIterator = ((Set) citySet).iterator();
                        while (cityIterator.hasNext()) {
                            String city = cityIterator.next() + "";
                            String cityName = provinceCityConfig.getcityCodeNameMap().get(city);
                            if (StringUtils.isNotEmpty(cityName)) {
                                cityNameStr = cityNameStr.concat(cityName).concat(",");
                            }
                        }
                    }
                    if (cityNameStr.length() > 1) {
                        cityNameStr = cityNameStr.substring(0, cityNameStr.length() - 1).concat("}");
                    } else {
                        cityNameStr = cityNameStr.concat("}");
                    }
                    if (StringUtils.isNotEmpty(proName)) {
                        proList.add(proName);
                    }
                    if (citySet != null && !"{}".equals(cityNameStr)) {
                        cityList.add(cityNameStr);
                    }
                });
                if (CollectionUtil.isNotEmpty(proList)) {
                    inventoryExportVO.setProvince(proList.toString());
                }
                if (CollectionUtil.isNotEmpty(cityList)) {
                    inventoryExportVO.setCity(cityList.toString());
                }
            }
        });

        log.info("库存导出准备构造excel");
        // 使用sxssfwrokbook,速度很快，并解决内存溢出的问题
        String sheetName = "库存列表";
        Workbook workbook = ExcelUtils.exportInventorySimpleExcelFast(sheetName, InventoryExportVO.class,
                inventoryExportVOList, true);
        log.info("库存导出接口构造excel完毕");
        SXSSFSheet sheet = (SXSSFSheet) workbook.getSheet(sheetName);
        // 冻结第二行表头
        sheet.createFreezePane(0, 2, 0, 2);
        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            workbook.write(outputStream);

        } catch (IOException e) {
            log.error("库存导出异常:{}", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("库存导出异常:{}", e);
                }
            }
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("库存导出异常:{}", e);
            }
        }
    }

    @Override
    public String inventoryManagementModeConfigKx(String atomId, String inventoryManagementModeKx) {
        String inventoryId = BaseServiceUtils.getId();
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoMapper.selectByPrimaryKey(atomId);
        if (StringUtils.isNotEmpty(atomOfferingInfo.getInventoryManagementModeKx())) {
            throw new BusinessException(StatusConstant.INVENTORY_MODE_CONFING_KX);
        }
        if (!"X".equals(atomOfferingInfo.getOfferingClass())) {
            throw new BusinessException(StatusConstant.INVENTORY_MODE_CONFING_KX, "不是卡+X的不能配置库存模式");
        }
        String spuCode = atomOfferingInfo.getSpuCode();
        String skuCode = atomOfferingInfo.getSkuCode();
        Optional<SkuOfferingInfo> optional = skuOfferingInfoMapper
                .selectByExample(new SkuOfferingInfoExample().createCriteria().andSpuCodeEqualTo(spuCode)
                        .andOfferingCodeEqualTo(skuCode).andDeleteTimeIsNull().example())
                .stream().findFirst();
        if (optional.isPresent()) {
            SkuOfferingInfo skuOfferingInfo = optional.get();
            String productType = skuOfferingInfo.getProductType();
            if ("1".equals(productType) || "2".equals(productType) || "3".equals(productType)) {
                throw new BusinessException(StatusConstant.INVENTORY_MODE_CONFING_KX,
                        "x产品类型是1:5G CPE,2:5G 快线,3:千里眼不能配置");
            }
        } else {
            throw new BusinessException(StatusConstant.INVENTORY_MODE_CONFING_KX, "规格商品不存在");
        }

        atomOfferingInfo.setInventoryManagementModeKx(inventoryManagementModeKx);
        atomOfferingInfo.setInventoryId(inventoryId);
        atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfo);

        List<DkcardxInventoryConfig> dkcardxInventoryConfigs = dkcardxInventoryConfigMapper
                .selectByExample(new DkcardxInventoryConfigExample().createCriteria()
                        .andInventoryIdEqualTo(inventoryId)
                        .example());
        String deviceVersion = null;
        String content = "【配置库存】\n商品组/销售组编码"
                .concat(atomOfferingInfo.getSpuCode()).concat("\n")
                .concat("商品规格编码").concat(atomOfferingInfo.getSkuCode()).concat("\n")
                .concat("原子商品编码").concat(atomOfferingInfo.getOfferingCode()).concat("\n")
                .concat("省侧库存模式为").concat(InventoryManagementModeKxEnum.getDesc(inventoryManagementModeKx));
        if (!(CollectionUtils.isEmpty(dkcardxInventoryConfigs) || dkcardxInventoryConfigs.size() == 0)) {
            deviceVersion = dkcardxInventoryConfigs.get(0).getDeviceVersion();
            content = content.concat("\n设备型号为").concat(deviceVersion);
        }

        // 记录日志
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code, GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                content, LogResultEnum.LOG_SUCESS.code, null);

        return inventoryId;
    }

    @Override
    public List<DkCardxInventoryInfoDTO> getKxInventoryList(String inventoryId) {
        List<DkcardxInventoryInfo> dkcardxInventoryInfos = dkcardxInventoryInfoMapper
                .selectByExample(new DkcardxInventoryInfoExample().createCriteria()
                        .andInventoryIdEqualTo(inventoryId).example());
        List<DkCardxInventoryInfoDTO> collect = null;
        if (CollectionUtils.isNotEmpty(dkcardxInventoryInfos)) {
            collect = dkcardxInventoryInfos.stream().map(dkcardxInventoryInfo -> {
                DkCardxInventoryInfoDTO dto = new DkCardxInventoryInfoDTO();
                BeanUtils.copyProperties(dkcardxInventoryInfo, dto);
                String provinceName = dkcardxInventoryInfo.getProvinceName();
                String cityName = dkcardxInventoryInfo.getCityName();
                String location = dkcardxInventoryInfo.getLocation();
                if (StringUtils.isNotEmpty(provinceName) && StringUtils.isEmpty(location)) {
                    dto.setProvinceCityName(provinceName);
                } else if (StringUtils.isNotEmpty(provinceName) && StringUtils.isNotEmpty(location)) {
                    dto.setProvinceCityName(cityName);
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return collect;
    }

    @Override
    public List<InventoryConfigKxDetailsDTO> getInventoryConfigKxList(String inventoryId, String saleStatus,
                                                                      String cityCode) {
        DkcardxInventoryConfigExample example = new DkcardxInventoryConfigExample();
        DkcardxInventoryConfigExample.Criteria criteria = example.createCriteria();
        criteria.andInventoryIdEqualTo(inventoryId);
        if (StringUtils.isNotEmpty(saleStatus)) {
            criteria.andSaleStatusEqualTo(saleStatus);
        }
        if (StringUtils.isNotEmpty(cityCode)) {
            // 空地市查询判断
            if ("1111".equals(cityCode)) {
                criteria.andLocationIsNull();
            } else {
                criteria.andLocationEqualTo(cityCode);
            }
        }
        List<DkcardxInventoryConfig> dkcardxInventoryConfigs = dkcardxInventoryConfigMapper
                .selectByExample(criteria.example());
        List<InventoryConfigKxDetailsDTO> collect = null;
        if (CollectionUtils.isNotEmpty(dkcardxInventoryConfigs)) {
            collect = dkcardxInventoryConfigs.stream().map(dkcardxInventoryConfig -> {
                InventoryConfigKxDetailsDTO dto = new InventoryConfigKxDetailsDTO();
                BeanUtils.copyProperties(dkcardxInventoryConfig, dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return collect;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteKxInventoryConfig(String id, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        boolean adminRole = BaseConstant.ADMIN_ROLE.equals(roleType);
        boolean isPartnerLordRole = PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (adminRole || isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            DkcardxInventoryConfig dkcardxInventoryConfig = dkcardxInventoryConfigMapper.selectByPrimaryKey(id);
            String saleStatus = dkcardxInventoryConfig.getSaleStatus();
            String beId = dkcardxInventoryConfig.getBeId();
            String location = dkcardxInventoryConfig.getLocation();
            String inventoryId = dkcardxInventoryConfig.getInventoryId();
            if (!SellStatusEnum.NOT_SELL.getType().equals(saleStatus)) {
                throw new BusinessException(StatusConstant.INVENTORY_MODE_CONFING_KX, "状态不是未销售状态不能剔除");
            }
            dkcardxInventoryConfigMapper.deleteByPrimaryKey(id);
            List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(
                    new AtomOfferingInfoExample().createCriteria().andInventoryIdEqualTo(inventoryId).example());
            AtomOfferingInfo atomOfferingInfo = atomOfferingInfos.get(0);

            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();

            String cooperatorId = atomOfferingInfo.getCooperatorId();
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
            Data4User cooperator = data4UserBaseAnswer.getData();

            String content = "【配置库存-剔除x终端】\n商品组/销售组编码"
                    .concat(atomOfferingInfo.getSpuCode()).concat("\n")
                    .concat("商品规格编码").concat(atomOfferingInfo.getSkuCode()).concat("\n")
                    .concat("原子商品编码").concat(atomOfferingInfo.getOfferingCode()).concat("\n")
                    .concat("剔除x终端1条,");

            String inventoryManagementModeKx = atomOfferingInfo.getInventoryManagementModeKx();
            // 删除后判断库存的配置是否是空，空就直接删除库存信息表信息
            DkcardxInventoryInfoExample dkcardxInventoryInfoExample = new DkcardxInventoryInfoExample().createCriteria()
                    .andInventoryIdEqualTo(inventoryId).example();
            List<DkcardxInventoryInfo> dkcardxInventoryInfos = dkcardxInventoryInfoMapper
                    .selectByExample(dkcardxInventoryInfoExample);
            if (InventoryManagementModeKxEnum.PROVINCE.getType().equals(inventoryManagementModeKx)) {
                content = content.concat(setProvinceNameContent(beId, provinceCodeNameMap));
                List<DkcardxInventoryConfig> dkcardxInventoryConfigs = dkcardxInventoryConfigMapper
                        .selectByExample(new DkcardxInventoryConfigExample().createCriteria()
                                .andInventoryIdEqualTo(inventoryId).example());
                if (CollectionUtils.isEmpty(dkcardxInventoryConfigs) || dkcardxInventoryConfigs.size() == 0) {
                    dkcardxInventoryInfoMapper.deleteByExample(dkcardxInventoryInfoExample);
                    content = content.concat(setTotalZeroContent());
                } else {
                    Optional<DkcardxInventoryInfo> dkcardxInventoryInfoOptional = dkcardxInventoryInfos.stream()
                            .filter(dkcardxInventoryInfo -> StringUtils.isNotEmpty(dkcardxInventoryInfo.getBeId()))
                            .findFirst();
                    inventoryDeductionDispose(dkcardxInventoryInfoOptional, atomOfferingInfo, cooperatorId, cooperator);
                    content = content.concat(setTotalReduceContent(dkcardxInventoryInfoOptional.get()));
                }
            } else {
                // 地市管理
                // 删除的是地市为空的，查询地市为空的配置信息
                if (StringUtils.isEmpty(location)) {
                    content = content.concat(setProvinceNameContent(beId, provinceCodeNameMap));

                    List<DkcardxInventoryConfig> dkcardxInventoryConfigs = dkcardxInventoryConfigMapper.selectByExample(
                            new DkcardxInventoryConfigExample().createCriteria().andInventoryIdEqualTo(inventoryId)
                                    .andLocationIsNull().example());
                    if (CollectionUtils.isEmpty(dkcardxInventoryConfigs) || dkcardxInventoryConfigs.size() == 0) {
                        dkcardxInventoryInfoMapper.deleteByExample(new DkcardxInventoryInfoExample().createCriteria()
                                .andInventoryIdEqualTo(inventoryId).andBeIdIsNotNull().andLocationIsNull().example());
                        content = content.concat(setTotalZeroContent());
                    } else {
                        Optional<DkcardxInventoryInfo> dkcardxInventoryInfoOptional = dkcardxInventoryInfos.stream()
                                .filter(dkcardxInventoryInfo -> StringUtils.isNotEmpty(dkcardxInventoryInfo.getBeId())
                                        && StringUtils.isEmpty(dkcardxInventoryInfo.getLocation()))
                                .findFirst();
                        inventoryDeductionDispose(dkcardxInventoryInfoOptional, atomOfferingInfo, cooperatorId,
                                cooperator);
                        content = content.concat(setTotalReduceContent(dkcardxInventoryInfoOptional.get()));
                    }
                } else {
                    Object cityName = locationCodeNameMap.get(location);
                    if (cityName != null) {
                        content = content.concat(cityName + "");
                    } else {
                        content = content.concat("未知地市");
                    }
                    List<DkcardxInventoryConfig> dkcardxInventoryConfigs = dkcardxInventoryConfigMapper.selectByExample(
                            new DkcardxInventoryConfigExample().createCriteria().andInventoryIdEqualTo(inventoryId)
                                    .andLocationEqualTo(location).example());
                    if (CollectionUtils.isEmpty(dkcardxInventoryConfigs) || dkcardxInventoryConfigs.size() == 0) {
                        dkcardxInventoryInfoMapper.deleteByExample(
                                new DkcardxInventoryInfoExample().createCriteria().andInventoryIdEqualTo(inventoryId)
                                        .andBeIdIsNotNull().andLocationEqualTo(location).example());
                        content = content.concat(setTotalZeroContent());
                    } else {
                        // 地市扣减对应地市库存信息
                        Optional<DkcardxInventoryInfo> optionalDkcardxInventoryInfo = dkcardxInventoryInfos.stream()
                                .filter(dkcardxInventoryInfo -> dkcardxInventoryInfo.getLocation() != null
                                        && dkcardxInventoryInfo.getLocation().equals(location))
                                .findFirst();
                        inventoryDeductionDispose(optionalDkcardxInventoryInfo, atomOfferingInfo, cooperatorId,
                                cooperator);
                        content = content.concat(setTotalReduceContent(optionalDkcardxInventoryInfo.get()));
                    }
                }
            }

            // 记录日志
            logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code, GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                    content, LogResultEnum.LOG_SUCESS.code, null);
        } else {
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }

    }

    private String setProvinceNameContent(String beId,
                                          Map<Object, Object> provinceCodeNameMap) {
        String content = "";
        Object provinceName = provinceCodeNameMap.get(beId);
        if (provinceName != null) {
            content = content.concat(provinceName + "");
        } else {
            content = content.concat("未知省份");
        }
        return content;
    }

    private String setTotalZeroContent() {
        return "减少1个库存,总库存数由1更新为0;";
    }

    private String setTotalReduceContent(DkcardxInventoryInfo dkcardxInventoryInfo) {
        Integer totalInventory = dkcardxInventoryInfo.getTotalInventory();
        String content = "减少1个库存,总库存数由".concat((totalInventory + 1) + "")
                .concat("更新为").concat((totalInventory) + ";");
        return content;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setInventoryWarnValue(InstallInventoryWarnParam params, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        boolean adminRole = BaseConstant.ADMIN_ROLE.equals(roleType);
        boolean isPartnerLordRole = PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = PARTNER_ROLE.equals(roleType);

        if (adminRole || isPartnerLordRole || isPartnerRole) {
            Boolean isNotice = params.getIsNotice();
            String atomId = params.getAtomId();
            AtomOfferingInfo atomOfferingInfo = atomOfferingInfoMapper.selectByPrimaryKey(atomId);
            Boolean isNoticeOld = atomOfferingInfo.getIsNotice();
            List<InstallInventoryWarnValue> inventoryWarnValues = params.getInventoryWarnValues();
            String content = "【配置库存-设置预警值】\n商品组/销售组编码"
                    .concat(atomOfferingInfo.getSpuCode()).concat("\n")
                    .concat("商品规格编码").concat(atomOfferingInfo.getSkuCode()).concat("\n")
                    .concat("原子商品编码").concat(atomOfferingInfo.getOfferingCode()).concat("\n");
            if (CollectionUtils.isNotEmpty(inventoryWarnValues)) {
                for (int i = 0; i < inventoryWarnValues.size(); i++) {
                    String id = inventoryWarnValues.get(i).getId();
                    Integer inventoryWarn = inventoryWarnValues.get(i).getInventoryWarn();
                    if (inventoryWarn < 0 || inventoryWarn > 9999) {
                        throw new BusinessException(StatusConstant.INVENTORY_WARN_SIZE_KX);
                    }
                    DkcardxInventoryInfo dkcardxInventoryInfo = dkcardxInventoryInfoMapper.selectByPrimaryKey(id);
                    Integer totalInventory = dkcardxInventoryInfo.getTotalInventory();
                    String inventoryManagementModeKx = atomOfferingInfo.getInventoryManagementModeKx();
                    String provinceType = InventoryManagementModeKxEnum.PROVINCE.getType();
                    Integer inventoryWarnOld = dkcardxInventoryInfo.getInventoryWarn();

                    if (totalInventory < inventoryWarn) {
                        dkcardxInventoryInfo.setInventoryStatus(0);
                    } else {
                        dkcardxInventoryInfo.setInventoryStatus(1);
                    }
                    dkcardxInventoryInfo.setUpdateTime(new Date());
                    dkcardxInventoryInfo.setInventoryWarn(inventoryWarn);
                    dkcardxInventoryInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryInfo);
                    if (inventoryWarn != null) {
                        if (!inventoryWarn.equals(inventoryWarnOld)) {
                            // 省管
                            if (provinceType.equals(inventoryManagementModeKx)) {
                                String provinceName = dkcardxInventoryInfo.getProvinceName();
                                content = content.concat(provinceName).concat("的库存预警值由")
                                        .concat(String.valueOf(inventoryWarnOld))
                                        .concat("调整为").concat(String.valueOf(inventoryWarn)).concat(";").concat("\n");

                            } else {
                                String cityName = dkcardxInventoryInfo.getCityName();
                                content = content.concat(cityName).concat("的库存预警值由")
                                        .concat(String.valueOf(inventoryWarnOld))
                                        .concat("调整为").concat(String.valueOf(inventoryWarn)).concat(";").concat("\n");

                            }
                        }
                    }
                    if (totalInventory < inventoryWarn) {
                        // 判断是否发送短信预警
                        if (isNotice) {
                            String cooperatorId = atomOfferingInfo.getCooperatorId();
                            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
                            Data4User cooperator = data4UserBaseAnswer.getData();
                            // 判断总库存和预警库存值
                            // TODO 短信模板修改
                            String phone = cooperator.getPhone();
                            List<String> phones = new ArrayList<>();
                            // 查询主合作伙伴信息
                            /*BaseAnswer<Data4User> userBaseAnswer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
                            if (userBaseAnswer == null
                                    || !userBaseAnswer.getStateCode()
                                    .equals(ExcepStatus.getSuccInstance().getStateCode())
                                    || userBaseAnswer.getData() == null) {
                                throw new RuntimeException("调用获取主用户信息失败。从合作伙伴ID:" + cooperatorId);
                            }
                            if (userBaseAnswer.getData().getIsSend()) {
                                phones.add(userBaseAnswer.getData().getPhone());
                            }*/

                            // 获取从合作伙伴电话
                            addCooperatorPhone(atomId,phones);

                            Boolean isSend = cooperator.getIsSend();
                            if (isSend != null && isSend){
                                phones.add(phone);
                            }
                            Map<String, String> msgMap = new HashMap<>();
                            phones = phones.stream().distinct().collect(Collectors.toList());
                            Msg4Request msg4Request = new Msg4Request();
                            msg4Request.setMobiles(phones);
                            msgMap.put("atomOfferingName", atomOfferingInfo.getOfferingName());
                            msgMap.put("offeringCode", atomOfferingInfo.getOfferingCode());

                            if (provinceType.equals(inventoryManagementModeKx)) {
                                String provinceName = dkcardxInventoryInfo.getProvinceName();
                                msgMap.put("warehouse", provinceName);
                            } else {
                                String cityName = dkcardxInventoryInfo.getCityName();
                                String provinceName = dkcardxInventoryInfo.getProvinceName();
                                if (StringUtils.isEmpty(cityName)) {
                                    msgMap.put("warehouse", provinceName);
                                } else {
                                    msgMap.put("warehouse", cityName);
                                }

                            }

                            msg4Request.setMessage(msgMap);
                            msg4Request.setTemplateId(smsInventoryKxDeficiencyTemplateId);
                            smsFeignClient.asySendMessage(msg4Request);
                        }
                    }
                }

            } else {
                log.info("setWarningValue 要设置的库存预警值数据为空inventoryWarnValues:{}", inventoryWarnValues);
            }
            if (isNotice && !isNoticeOld.equals(isNotice)) {
                content = content.concat("勾选短信提醒。");
            } else if (!isNotice && !isNoticeOld.equals(isNotice)) {
                content = content.concat("取消勾选短信提醒。");
            }
            atomOfferingInfo.setIsNotice(isNotice);
            atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfo);
            // 记录日志
            logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code, GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                    content, LogResultEnum.LOG_SUCESS.code, null);

        } else {
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }
    }

    /**
     * 发送卡+x库存不足短信
     *
     * @param atomOfferingInfo
     * @param dkInventoryInfo
     */
    @Override
    public void sendInventoryKxDeficiencyNote(AtomOfferingInfo atomOfferingInfo, DkcardxInventoryInfo dkInventoryInfo) {
        String cooperatorId = atomOfferingInfo.getCooperatorId();
        BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
        Data4User cooperator = data4UserBaseAnswer.getData();
        // 判断总库存和预警库存值
        // TODO 短信模板修改
        String phone = cooperator.getPhone();
        List<String> phones = new ArrayList<>();
        /*// 查询主合作伙伴信息
        BaseAnswer<Data4User> userBaseAnswer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
        if (userBaseAnswer == null
                || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                || userBaseAnswer.getData() == null) {
            throw new RuntimeException("调用获取主用户信息失败。从合作伙伴ID:" + cooperatorId);
        }
        if (userBaseAnswer.getData().getIsSend()) {
            phones.add(userBaseAnswer.getData().getPhone());
        }*/
        Boolean isSend = cooperator.getIsSend();
        if (isSend != null && isSend){
            phones.add(phone);
        }
        // 获取从合作伙伴电话
        addCooperatorPhone(atomOfferingInfo.getId(),phones);

        Map<String, String> msgMap = new HashMap<>();
        phones = phones.stream().distinct().collect(Collectors.toList());
        Msg4Request msg4Request = new Msg4Request();
        msg4Request.setMobiles(phones);
        msgMap.put("atomOfferingName", atomOfferingInfo.getOfferingName());
        msgMap.put("offeringCode", atomOfferingInfo.getOfferingCode());
        if (InventoryManagementModeKxEnum.PROVINCE.getType().equals(atomOfferingInfo.getInventoryManagementModeKx())) {
            String provinceName = dkInventoryInfo.getProvinceName();
            msgMap.put("warehouse", provinceName);
        } else if (InventoryManagementModeKxEnum.CITY.getType()
                .equals(atomOfferingInfo.getInventoryManagementModeKx())) {
            String cityName = dkInventoryInfo.getCityName();
            String provinceName = dkInventoryInfo.getProvinceName();
            if (StringUtils.isEmpty(cityName)) {
                msgMap.put("warehouse", provinceName);
            } else {
                msgMap.put("warehouse", cityName);
            }
        } else {
            log.info("Error 未配置库存管理模式");
            throw new BusinessException(StatusConstant.INVOICE_RECORD_NOT_EXIST, "未配置库存管理模式");
        }
        msg4Request.setMessage(msgMap);
        // 库存不足
        msg4Request.setTemplateId(smsInventoryKxDeficiencyTemplateId);

        BaseAnswer<Void> voidBaseAnswer = smsFeignClient.asySendMessage(msg4Request);
        log.info("总库存不足，低于预警值 库存警报短信通知合作伙伴的短信发送结果:{}", JSON.toJSONString(voidBaseAnswer));
    }

    /**
     * 处理删除终端库存扣减逻辑
     *
     * @param dkcardxInventoryInfoOptional
     * @param atomOfferingInfo
     * @param cooperatorId
     * @param cooperator
     */
    public void inventoryDeductionDispose(Optional<DkcardxInventoryInfo> dkcardxInventoryInfoOptional,
                                          AtomOfferingInfo atomOfferingInfo, String cooperatorId, Data4User cooperator) {
        // Optional<DkcardxInventoryInfo> dkcardxInventoryInfoOptional =
        // dkcardxInventoryInfos.stream().filter(dkcardxInventoryInfo ->
        // StringUtils.isNotEmpty(dkcardxInventoryInfo.getBeId())).findFirst();
        if (dkcardxInventoryInfoOptional.isPresent()) {
            DkcardxInventoryInfo dkcardxInventoryInfo = dkcardxInventoryInfoOptional.get();
            Integer currentInventory = dkcardxInventoryInfo.getCurrentInventory();
            Integer totalInventory = dkcardxInventoryInfo.getTotalInventory();
            Integer inventoryWarn = dkcardxInventoryInfo.getInventoryWarn();
            Boolean isNotice = atomOfferingInfo.getIsNotice();
            String inventoryManagementModeKx = atomOfferingInfo.getInventoryManagementModeKx();
            int currentInventoryNew = currentInventory - 1;
            int totalInventoryNew = totalInventory - 1;
            // 更新库存信息
            String provinceType = InventoryManagementModeKxEnum.PROVINCE.getType();
            dkcardxInventoryInfo.setCurrentInventory(currentInventoryNew);
            dkcardxInventoryInfo.setTotalInventory(totalInventoryNew);
            dkcardxInventoryInfo.setUpdateTime(new Date());
            if (totalInventoryNew < inventoryWarn) {
                dkcardxInventoryInfo.setInventoryStatus(0);
            }
            dkcardxInventoryInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryInfo);
            // 判断是否发送短信预警
            if (isNotice) {
                // 判断总库存和预警库存值
                if (totalInventoryNew < inventoryWarn) {
                    // TODO 短信模板修改
                    String phone = cooperator.getPhone();
                    List<String> phones = new ArrayList<>();
                    // 查询主合作伙伴信息
                    /*BaseAnswer<Data4User> userBaseAnswer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
                    if (userBaseAnswer == null
                            || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                            || userBaseAnswer.getData() == null) {
                        throw new RuntimeException("调用获取主用户信息失败。从合作伙伴ID:" + cooperatorId);
                    }
                    if (userBaseAnswer.getData().getIsSend()) {
                        phones.add(userBaseAnswer.getData().getPhone());
                    }*/
                    Boolean isSend = cooperator.getIsSend();
                    if (isSend != null && isSend){
                        phones.add(phone);
                    }

                    // 查询从合作合并信息
                    addCooperatorPhone(atomOfferingInfo.getId(),phones);

                    Map<String, String> msgMap = new HashMap<>();
                    phones = phones.stream().distinct().collect(Collectors.toList());
                    Msg4Request msg4Request = new Msg4Request();
                    msg4Request.setMobiles(phones);
                    msgMap.put("atomOfferingName", atomOfferingInfo.getOfferingName());
                    msgMap.put("offeringCode", atomOfferingInfo.getOfferingCode());
                    if (provinceType.equals(inventoryManagementModeKx)) {
                        String provinceName = dkcardxInventoryInfo.getProvinceName();
                        msgMap.put("warehouse", provinceName);
                    } else {
                        String cityName = dkcardxInventoryInfo.getCityName();
                        String provinceName = dkcardxInventoryInfo.getProvinceName();
                        if (StringUtils.isEmpty(cityName)) {
                            msgMap.put("warehouse", provinceName);
                        } else {
                            msgMap.put("warehouse", cityName);
                        }

                    }

                    msg4Request.setMessage(msgMap);
                    msg4Request.setTemplateId(smsInventoryKxDeficiencyTemplateId);
                    smsFeignClient.asySendMessage(msg4Request);
                }
            }
        }
    }

    /**
     * 添加从合作伙伴电话
     * @param atomOfferingId
     * @param phones
     */
    private void addCooperatorPhone(String atomOfferingId,
                                    List<String> phones){
        // 查询从合作合并信息
        AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample = new AtomOfferingCooperatorRelationExample();
        atomOfferingCooperatorRelationExample.createCriteria()
                .andAtomOfferingIdEqualTo(atomOfferingId);
        List<AtomOfferingCooperatorRelation> atomOfferingCooperatorRelationList
                = atomOfferingCooperatorRelationService.listAtomOfferingCooperatorRelationByExample(atomOfferingCooperatorRelationExample);
        if (CollectionUtil.isNotEmpty(atomOfferingCooperatorRelationList)){
            List<String> cooperatorIdList = atomOfferingCooperatorRelationList.stream().map(AtomOfferingCooperatorRelation::getCooperatorId)
                    .collect(Collectors.toList());
            BaseAnswer<List<Data4User>> cooperatorListBaseAnswer = userFeignClient.partnerInfoByIds(cooperatorIdList);
            if (cooperatorListBaseAnswer == null
                    || !cooperatorListBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                    || cooperatorListBaseAnswer.getData() == null) {
                log.error("调用获取从合作伙伴信息失败。从合作伙伴ID:{}" + String.join(",",cooperatorIdList));
                throw new RuntimeException("调用获取从合作伙伴信息失败" );
            }
            List<String> cooperatorPhoneList = cooperatorListBaseAnswer.getData().stream()
                    .map(Data4User::getPhone)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cooperatorPhoneList)){
                phones.addAll(cooperatorPhoneList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> configInventory(InventoryConfigRequest request, String userId,
                                            LoginIfo4Redis loginIfo4Redis, String ip) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoMapper.selectByPrimaryKey(request.getId());
        List<CategoryInfo> categoryInfoList = categoryInfoMapper.selectByExample(new CategoryInfoExample()
                .createCriteria()
                .andSpuIdEqualTo(atomOfferingInfo.getSpuId())
                .example());
        String spuOfferingClass = categoryInfoList.get(0).getOfferingClass();

        List<String> phones = new ArrayList<>();
        // 查询是否有该原子商品
        if (atomOfferingInfo == null) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                        "【配置库存】", userId, ip, LogResultEnum.LOG_FAIL.code,
                        StatusConstant.PRODUCT_NOT_EXIST.getMessage());
            });
            throw new BusinessException(StatusConstant.PRODUCT_NOT_EXIST);
        }
        BaseAnswer<Data4User> userBaseAnswer = null;
        List<AtomOfferingCooperatorRelation> atomOfferingCooperatorRelationList = null;
        if (SPUOfferingClassEnum.A06.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClass)) {
            String cooperatorId = atomOfferingInfo.getCooperatorId();
            // 未配置合作伙伴不允许配置库存
            if (cooperatorId == null) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                            GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                            IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), userId, ip,
                            LogResultEnum.LOG_FAIL.code, StatusConstant.NO_COOPERATOR.getMessage());
                });
                throw new BusinessException(StatusConstant.NO_COOPERATOR);
            }

            userBaseAnswer = userFeignClient.partnerInfoById(cooperatorId);
            if (userBaseAnswer == null
                    || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                    || userBaseAnswer.getData() == null) {
                log.error("配置库存时调用获取主用户信息失败。主合作伙伴ID:{}", cooperatorId);
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                            GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                            IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), userId, ip,
                            LogResultEnum.LOG_FAIL.code, "调用获取主用户信息失败。主合作伙伴ID:" + cooperatorId);
                });
                throw new RuntimeException("调用获取主用户信息失败。");
            }
            Data4User cooperator = userBaseAnswer.getData();

            /*BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
            Data4User cooperator = data4UserBaseAnswer.getData();
            if (cooperator.getIsPrimary()) {
                // 只有配置了合作伙伴从账号才能配置库存
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                            GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                            IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), userId, ip,
                            LogResultEnum.LOG_FAIL.code, "配置合作伙伴从账号之后才能配置库存");
                });
                throw new BusinessException(StatusConstant.AUTH_ERROR, "配置合作伙伴从账号之后才能配置库存");
            }*/

            // 获取从账号信息
            AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample = new AtomOfferingCooperatorRelationExample();
            atomOfferingCooperatorRelationExample.createCriteria()
                    .andAtomOfferingIdEqualTo(atomOfferingInfo.getId());
            atomOfferingCooperatorRelationList
                    = atomOfferingCooperatorRelationService.listAtomOfferingCooperatorRelationByExample(atomOfferingCooperatorRelationExample);
            if (CollectionUtil.isEmpty(atomOfferingCooperatorRelationList)){
                // 只有配置了合作伙伴从账号才能配置库存
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                            GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                            IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), userId, ip,
                            LogResultEnum.LOG_FAIL.code, "配置合作伙伴从账号之后才能配置库存");
                });
                throw new BusinessException(StatusConstant.AUTH_ERROR, "配置合作伙伴从账号之后才能配置库存");
            }

            // boolean lordPartner = false;
            // if (loginIfo4Redis.getRoleType().equals(PARTNER_LORD_ROLE)) {
            // //主账号可以为下面的从账号商品配置库存
            // BaseAnswer<List<String>> answer = userFeignClient.getDownUserIds(userId);
            // List<String> downUserIds = answer.getData();
            // if (downUserIds != null && downUserIds.contains(cooperatorId)) {
            // lordPartner = true;
            // }
            // }
            String phone = cooperator.getPhone();
            // 查询主合作伙伴信息
            /*userBaseAnswer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
            if (userBaseAnswer == null
                    || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                    || userBaseAnswer.getData() == null) {
                log.error("配置库存时调用获取主用户信息失败。从合作伙伴ID:{}", cooperatorId);
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                            GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                            IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), userId, ip,
                            LogResultEnum.LOG_FAIL.code, "调用获取主用户信息失败。从合作伙伴ID:" + cooperatorId);
                });
                throw new RuntimeException("调用获取主用户信息失败。从合作伙伴ID:" + cooperatorId);
            }
            if (userBaseAnswer.getData().getIsSend()) {
                phones.add(userBaseAnswer.getData().getPhone());
            }*/
            Boolean isSend = cooperator.getIsSend();
            if (isSend != null && isSend){
                phones.add(phone);
            }

            List<String> cooperatorIdList = atomOfferingCooperatorRelationList.stream().map(AtomOfferingCooperatorRelation::getCooperatorId)
                    .collect(Collectors.toList());
            BaseAnswer<List<Data4User>> cooperatorListBaseAnswer = userFeignClient.partnerInfoByIds(cooperatorIdList);
            if (cooperatorListBaseAnswer == null
                    || !cooperatorListBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
                    || cooperatorListBaseAnswer.getData() == null) {
                log.error("配置库存时调用获取从合作伙伴信息失败。从合作伙伴ID:{}", String.join(",",cooperatorIdList));
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                            GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                            IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), userId, ip,
                            LogResultEnum.LOG_FAIL.code, "调用获取从合作伙伴信息失败。从合作伙伴ID:" + String.join(",",cooperatorIdList));
                });
                throw new RuntimeException("调用获取从合作伙伴信息失败。");
            }
            List<String> cooperatorPhoneList = cooperatorListBaseAnswer.getData().stream()
                    .map(Data4User::getPhone)
                    .collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cooperatorIdList)){
                phones.addAll(cooperatorPhoneList);
            }
        } else if (SPUOfferingClassEnum.A04.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A08.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A09.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A12.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A14.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A15.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A16.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A17.getSpuOfferingClass().equals(spuOfferingClass)) {
            List<UserRefundKxVO> userRefundKxVOList = userRefundKxService
                    .listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_SERVICE_INSUFFICIENT_INVENTORY_WARNING);
            if (CollectionUtils.isNotEmpty(userRefundKxVOList)) {
                userRefundKxVOList.stream().forEach(item -> phones.add(item.getPhone()));
            }
        }
        // 配置库存的不是超管或者从合作伙伴本人或者从合作伙伴的主伙伴 则无权限 这里可能合作伙伴为空的情况
        // if (!userId.equals(cooperatorId) &&
        // !loginIfo4Redis.getRoleType().equals(BaseConstant.ADMIN_ROLE) && !lordPartner
        // ) {
        // throw new BusinessException(StatusConstant.AUTH_ERROR);
        // }
        // 判断当前合作伙伴是否是当前商品配置的合作伙伴
        // 获取从账号信息
        if (CollectionUtil.isEmpty(atomOfferingCooperatorRelationList)){
            AtomOfferingCooperatorRelationExample atomOfferingCooperatorRelationExample = new AtomOfferingCooperatorRelationExample();
            atomOfferingCooperatorRelationExample.createCriteria()
                    .andAtomOfferingIdEqualTo(atomOfferingInfo.getId());
            atomOfferingCooperatorRelationList
                    = atomOfferingCooperatorRelationService.listAtomOfferingCooperatorRelationByExample(atomOfferingCooperatorRelationExample);
        }
        String cooperatorId = atomOfferingInfo.getCooperatorId();
        String roleType = loginIfo4Redis.getRoleType();
        if (PARTNER_ROLE.equals(roleType)) {
            /*if (!userId.equals(cooperatorId)) {
                throw new BusinessException(StatusConstant.AUTH_ERROR);
            }*/
            if (CollectionUtil.isNotEmpty(atomOfferingCooperatorRelationList)){
                List<String> cooperatorIdList = atomOfferingCooperatorRelationList.stream()
                        .map(AtomOfferingCooperatorRelation::getCooperatorId)
                        .collect(Collectors.toList());
                if (!cooperatorIdList.contains(userId)){
                    throw new BusinessException(StatusConstant.AUTH_ERROR);
                }
            }
        }
        if (PARTNER_LORD_ROLE.equals(roleType)) {
            // 主合作伙伴不为空v
            if(StringUtils.isNotEmpty(cooperatorId) && userBaseAnswer != null){
                if (!userId.equals(userBaseAnswer.getData().getUserId())) {
                    throw new BusinessException(StatusConstant.AUTH_ERROR);
                }
            }
        }
        // 对于未配置过库存的需要修改配置了库存
        if (!atomOfferingInfo.getIsInventory()) {
            atomOfferingInfo.setIsInventory(true);
            atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfo);
        }
        // 判断添加的数量 减少用负数表示
        Long addAmount;
        if (request.getOperType() == 0) {
            addAmount = request.getAmount();
        } else {
            addAmount = -request.getAmount();
        }

        if (!(SPUOfferingClassEnum.A04.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A08.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A09.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A12.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A14.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A15.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A16.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A17.getSpuOfferingClass().equals(spuOfferingClass))
                && !((SPUOfferingClassEnum.A06.getSpuOfferingClass().equals(spuOfferingClass) ||
                SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClass))
                && atomOfferingInfo.getOfferingClass().equals("H"))

        ) {
            throw new BusinessException(StatusConstant.PRODUCT_NOT_EXIST, "此原子商品不能配置库存");
        }
        // 判断总库存是否小于0，超卖变量增加可以随便增加，减少不行
        Long inventory = atomOfferingInfo.getInventory();
        Long reserveInventory = atomOfferingInfo.getReserveInventory();
        long totalInventory = inventory + reserveInventory;
        int affectNum = 0;
        if (totalInventory < 0) {
            if (request.getOperType() == 1) {
                throw new BusinessException(StatusConstant.PRODUCT_NOT_EXIST, "此原子商品总库存小于0不能减少库存");
            }
            affectNum = inventoryHandlerMapper.updateInventoryConfigOversoldByPrimaryKey(request.getId(), addAmount,
                    request.getThreshold(), request.getIsNotice());
        } else {
            affectNum = inventoryHandlerMapper.updateInventoryConfigByPrimaryKey(request.getId(), addAmount,
                    request.getThreshold(), request.getIsNotice());
        }
        if (affectNum == 0) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                        IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), userId, ip,
                        LogResultEnum.LOG_FAIL.code, StatusConstant.NO_INVENTORY.getMessage());
            });
            throw new BusinessException(StatusConstant.NO_INVENTORY);
        }
        // 这里对短信通知状态重置 规则也可以修改为订单库存量大于预警数才修改。都可
        if (atomOfferingInfo.getNotified() != null && atomOfferingInfo.getNotified() && request.getOperType() == 0
                && request.getAmount() > 0) {
            AtomOfferingInfo noticeAtom = new AtomOfferingInfo();
            noticeAtom.setId(atomOfferingInfo.getId());
            noticeAtom.setNotified(false);
            atomOfferingInfoMapper.updateByPrimaryKeySelective(noticeAtom);
        }

        if ((atomOfferingInfo.getInventory() + atomOfferingInfo.getReserveInventory() <= atomOfferingInfo
                .getInventoryThreshold()) && request.getIsNotice()) {
            log.info("配置库存发送短信：configInventory inventory={}; reserveInventory={}; threshold={}; isNotice={}",
                    atomOfferingInfo.getInventory(), atomOfferingInfo.getReserveInventory(),
                    atomOfferingInfo.getInventoryThreshold(), request.getIsNotice());

            Map<String, String> msgMap = new HashMap<>();
            List<String> phoneList = phones.stream().distinct().collect(Collectors.toList());
            Msg4Request msg4Request = new Msg4Request();
            msg4Request.setMobiles(phoneList);
            msgMap.put("atomOfferingName", atomOfferingInfo.getOfferingName());
            msgMap.put("offeringCode", atomOfferingInfo.getOfferingCode());
            msg4Request.setMessage(msgMap);
            msg4Request.setTemplateId(smsInventoryTemplateId);
            smsFeignClient.asySendMessage(msg4Request);
        }

        // 记录日志
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code, GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                IotLogUtil.configInventoryContentFromRequest(request, atomOfferingInfo), LogResultEnum.LOG_SUCESS.code,
                null);

        return baseAnswer;
    }

    /**
     * 查询库存
     *
     * @param baseRequest
     * @return
     */
    @Override
    public IOTAnswer<InventoryInfoResponse> qryInventory(IOTRequest baseRequest) {
        log.info("库存查询请求内容:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<InventoryInfoResponse> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        InventoryInfoRequest inventoryInfoRequest = JSON.parseObject(baseRequest.getContent(),
                InventoryInfoRequest.class);

        return getInventoryInfoResponse(baseRequest, inventoryInfoRequest, iotAnswer,false);
    }

    //为了小程序复用，所以把库存查询抽取出来
    @Override
    public IOTAnswer<InventoryInfoResponse> getInventoryInfoResponse(IOTRequest baseRequest, InventoryInfoRequest inventoryInfoRequest, IOTAnswer<InventoryInfoResponse> iotAnswer, Boolean isMini) {
        List<InventoryInfoRequest.InventoryInfo> inventoryInfos = inventoryInfoRequest.getInventoryInfo();
        // InventoryInfoRequest.InventoryInfo inventoryInfo =
        // inventoryInfoRequest.getInventoryInfo();
        // 封装返回
        InventoryInfoResponse inventoryInfoResponse = new InventoryInfoResponse();
        iotAnswer.setContent(inventoryInfoResponse);
        InventoryInfoResponse.InventoryInfo inventoryInfoRes = new InventoryInfoResponse.InventoryInfo();
        List<InventoryInfoResponse.SpuOfferingInfo> spuOfferingInfosRes = new ArrayList<>();
        inventoryInfoRes.setSpuOfferingInfo(spuOfferingInfosRes);
        inventoryInfoResponse.setInventoryInfo(inventoryInfoRes);
        inventoryInfos.forEach(inventoryInfo -> {
            List<InventoryInfoRequest.SpuOfferingInfo> spuOfferingInfoReqs = inventoryInfo.getSpuOfferingInfo();

            List<QueryInventoryDTO.SpuInfo> thirdSpuInfoList = new ArrayList<>();
            String bookRegionId = inventoryInfoRequest.getRegionId();
            // 判断dict类额度是否短缺
            AtomicBoolean isShort = new AtomicBoolean(false);
            spuOfferingInfoReqs.forEach(spu -> {
                String spuOfferingClass = spu.getOfferingClass();

                // 返回spuOfferingInfo
                InventoryInfoResponse.SpuOfferingInfo spuOfferingInfoRes = new InventoryInfoResponse.SpuOfferingInfo();
                spuOfferingInfosRes.add(spuOfferingInfoRes);
                List<InventoryInfoResponse.SkuOfferingInfo> skuOfferingInfosRes = new ArrayList<>();

                String spuOfferingCode = spu.getSpuOfferingCode();
                spuOfferingInfoRes.setSpuOfferingCode(spuOfferingCode);
                spuOfferingInfoRes.setSkuOfferingInfo(skuOfferingInfosRes);
                List<InventoryInfoRequest.SkuOfferingInfo> skuOfferingInfoReqs = spu.getSkuOfferingInfo();
                if (SPUOfferingClassEnum.A06.getSpuOfferingClass().equals(spuOfferingClass)
                        || SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClass)) {
                    List<QueryInventoryDTO.SkuInfo> thirdSkuInfoList = new ArrayList<>();
                    // 这里只查看硬件库存
                    if (ObjectUtils.isNotEmpty(skuOfferingInfoReqs)) {
                        skuOfferingInfoReqs.forEach(sku -> {
                            String skuOfferingCode = sku.getSkuOfferingCode();
                            SkuOfferingInfoExample skuOfferingInfoExample = new SkuOfferingInfoExample();
                            skuOfferingInfoExample.createCriteria()
                                    .andOfferingCodeEqualTo(skuOfferingCode)
                                    .andDeleteTimeIsNull();
                            List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper
                                    .selectByExample(skuOfferingInfoExample);
                            if (skuOfferingInfos.size() == 0) {
                                throw new IOTException(iotAnswer, "-11", "规格商品不存在，请确认已完成同步");
                            }
                            AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                            AtomOfferingInfoExample.Criteria criteria = atomOfferingInfoExample.createCriteria();
                            criteria.andOfferingClassEqualTo("H");
                            criteria.andSkuCodeEqualTo(skuOfferingCode);
                            criteria.andDeleteTimeIsNull();
                            // 查询相关的原子商品
                            List<AtomOfferingInfo> result = atomOfferingInfoMapper
                                    .selectByExample(atomOfferingInfoExample);
                            // new added 增加判断，是计算自有库存还是调用第三方接口
                            List<AtomOfferingInfo> localResult = new ArrayList<>();

                            List<String> materialNumList = new ArrayList<>();
                            for (AtomOfferingInfo atominfo : result) {
                                String saleRegion = atominfo.getOfferingsaleregion();
                                log.info("查询库存 qryInventory saleRegion = {}", saleRegion);
                                if ("Yunnan".equals(saleRegion)) {
                                    materialNumList.add(atominfo.getExtHardOfferingCode());
                                }
                                // else if("Qinghai".equals(saleRegion)){
                                // province = "Qinghai";
                                // }
                                else {
                                    localResult.add(atominfo);
                                }
                            }
                            log.info("qryInventory materialNumList size = {}", materialNumList.size());
                            if (materialNumList.size() != 0) {
                                QueryInventoryDTO.SkuInfo skuinfo = new QueryInventoryDTO.SkuInfo();
                                skuinfo.setSkuCode(skuOfferingCode);
                                skuinfo.setMaterialNum(materialNumList);
                                thirdSkuInfoList.add(skuinfo);
                            }

                            // 查出所有的原子商品信息，然后根据库存去计算规格数量剩余
                            // Long minQuantity = getMinQuantity(result);
                            Long minQuantity = getMinQuantity(inventoryInfo, localResult, iotAnswer);
                            InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new InventoryInfoResponse.SkuOfferingInfo();
                            skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                            skuOfferingInfoRes.setQuantity(minQuantity);
                            skuOfferingInfosRes.add(skuOfferingInfoRes);
                        });
                        log.info("qryInventory thirdSkuInfoList size = {}", thirdSkuInfoList.size());
                        if (thirdSkuInfoList.size() != 0) {
                            QueryInventoryDTO.SpuInfo spuInfo = new QueryInventoryDTO.SpuInfo();
                            spuInfo.setSpuCode(spuOfferingCode);
                            spuInfo.setSkuInfo(thirdSkuInfoList);
                            thirdSpuInfoList.add(spuInfo);
                        }

                    } else { // 查询spu下所有sku库存
                        // 查询spu下sku集合
                        /*
                         * SkuOfferingInfoExample skuOfferingInfoExample = new SkuOfferingInfoExample();
                         * SkuOfferingInfoExample.Criteria criteria1 =
                         * skuOfferingInfoExample.createCriteria();
                         * criteria1.andSpuCodeEqualTo(spuOfferingCode);
                         * List<SkuOfferingInfo> skuOfferingInfos =
                         * skuOfferingInfoMapper.selectByExample(skuOfferingInfoExample);
                         * skuOfferingInfos.forEach( sku ->{
                         * String skuOfferingCode = sku.getOfferingCode();
                         * AtomOfferingInfoExample atomOfferingInfoExample = new
                         * AtomOfferingInfoExample();
                         * AtomOfferingInfoExample.Criteria criteria =
                         * atomOfferingInfoExample.createCriteria();
                         * criteria.andOfferingClassEqualTo("H");
                         * criteria.andSkuCodeEqualTo(skuOfferingCode);
                         * //查询相关的原子商品
                         * List<AtomOfferingInfo> result =
                         * atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
                         * //查出所有的原子商品信息，然后根据库存去计算规格数量剩余
                         * Long minQuantity = getMinQuantity(result);
                         * InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new
                         * InventoryInfoResponse.SkuOfferingInfo();
                         * skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                         * skuOfferingInfoRes.setQuantity(minQuantity);
                         * skuOfferingInfosRes.add(skuOfferingInfoRes);
                         * });
                         */
                        // 查询spu下所有原子商品
                        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                        AtomOfferingInfoExample.Criteria criteria = atomOfferingInfoExample.createCriteria();
                        criteria.andSpuCodeEqualTo(spuOfferingCode);
                        criteria.andOfferingClassEqualTo("H");
                        criteria.andDeleteTimeIsNull();
                        List<AtomOfferingInfo> result = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
                        // 根据skuCode分类
                        Map<String, List<AtomOfferingInfo>> skuCode2InfoMap = groupBySkuCode(result);
                        if (ObjectUtils.isNotEmpty(skuCode2InfoMap)) {
                            Iterator<Map.Entry<String, List<AtomOfferingInfo>>> it = skuCode2InfoMap.entrySet()
                                    .iterator();
                            while (it.hasNext()) {
                                Map.Entry<String, List<AtomOfferingInfo>> next = it.next();
                                String skuOfferingCode = next.getKey();
                                List<AtomOfferingInfo> aoiList = next.getValue();
                                // 查出所有的原子商品信息，然后根据库存去计算规格数量剩余
                                Long minQuantity = getMinQuantity(inventoryInfo, aoiList, iotAnswer);
                                InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new InventoryInfoResponse.SkuOfferingInfo();
                                skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                                skuOfferingInfoRes.setQuantity(minQuantity);
                                skuOfferingInfosRes.add(skuOfferingInfoRes);
                            }
                        }
                    }
                } else if (SPUOfferingClassEnum.A04.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A08.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A09.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A12.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A14.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A15.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A16.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A17.getSpuOfferingClass().equals(spuOfferingClass)) {
                    // 判断spu的库存模式 运营统管同SKU下被查询原子商品的库存均≧1，返回充足，否则返回不足 非运营默认充足
                    List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper
                            .selectByExample(new SpuOfferingInfoExample().createCriteria()
                                    .andOfferingCodeEqualTo(spuOfferingCode).example());
                    SpuOfferingInfo spuOfferingInfo = spuOfferingInfos.get(0);
                    String inventoryType = spuOfferingInfo.getInventoryType();
                    String bookId = inventoryInfo.getBookId();
                    if (StringUtils.isEmpty(inventoryType)) {
                        throw new IOTException(iotAnswer, "当前服务类商品(商品组/销售商品)未配置库存模式，请联系运营配置！");
                    }
                    if (OPERATION_ADMIN_STATISTICS.equals(inventoryType)) {
                        // TODO 最新要改造的服务类商品查询库存模式
                        String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
                        log.info("test reserve cardx pattern = {}", nowInventoryPattern);
                        for (InventoryInfoRequest.SkuOfferingInfo skuOfferingInfoReq : skuOfferingInfoReqs) {
                            String skuOfferingCode = skuOfferingInfoReq.getSkuOfferingCode();
                            InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new InventoryInfoResponse.SkuOfferingInfo();
                            List<InventoryInfoResponse.AtomOfferingInfo> atomOfferingInfosShops = new ArrayList<>();
                            skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                            List<InventoryInfoRequest.AtomOfferingInfo> atomOfferingInfoList = skuOfferingInfoReq
                                    .getAtomOfferingInfo();
                            if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                                log.info("qryInventory 原子商品信息未传");
                                throw new IOTException(iotAnswer, "-1", "原子商品信息未传");
                            }
                            Long quantity = 1L;
                            A:
                            for (InventoryInfoRequest.AtomOfferingInfo atomOfferingInfo : atomOfferingInfoList) {
                                String atomOfferingCode = atomOfferingInfo.getAtomOfferingCode();
                                InventoryInfoResponse.AtomOfferingInfo atomOfferingInfoShop = new InventoryInfoResponse.AtomOfferingInfo();
                                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper
                                        .selectByExample(new AtomOfferingInfoExample().createCriteria()
                                                .andSpuCodeEqualTo(spuOfferingCode)
                                                .andSkuCodeEqualTo(skuOfferingCode)
                                                .andOfferingCodeEqualTo(atomOfferingCode).example());
                                if (CollectionUtils.isEmpty(atomOfferingInfos)) {
                                    log.info("qryAtomInfo 原子商品信息未查询到");
                                    throw new IOTException(iotAnswer, "-1", "原子商品信息未查询到");
                                }
                                AtomOfferingInfo atomOfferingInfoEntity = atomOfferingInfos.get(0);
                                Long inventory = atomOfferingInfoEntity.getInventory();
                                Long reserveInventory = atomOfferingInfoEntity.getReserveInventory();
                                if (StringUtils.isNotEmpty(nowInventoryPattern)) {
                                    if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                                        if (StringUtils.isNotEmpty(bookId)) {
                                            // 流水号不为空拍下模式返回总库存
                                            log.info("运营模式拍下减库存模式下预占流水号bookIdInventory：{}", bookId);
                                            atomOfferingInfoShop
                                                    .setAtomOfferingCode(atomOfferingInfoEntity.getOfferingCode());
                                            atomOfferingInfoShop.setQuantity(inventory + reserveInventory);
                                        } else {
                                            // 流水号为空拍下减库存返回当前库存
                                            log.info("运营模式拍下减库存模式下预占流水号bookIdInventory：{}", "是空的");
                                            atomOfferingInfoShop
                                                    .setAtomOfferingCode(atomOfferingInfoEntity.getOfferingCode());
                                            atomOfferingInfoShop.setQuantity(inventory);
                                        }

                                    } else {
                                        // 付款减库存返回总库存 无论流水号是否为空都返回总数
                                        if (StringUtils.isNotEmpty(bookId)) {
                                            log.info("运营模式付款减库存模式下预占流水号bookIdInventory：{}", bookId);
                                        } else {
                                            log.info("运营模式付款减库存模式下预占流水号bookIdInventory：{}", "是空的");
                                        }
                                        atomOfferingInfoShop
                                                .setAtomOfferingCode(atomOfferingInfoEntity.getOfferingCode());
                                        atomOfferingInfoShop.setQuantity(inventory + reserveInventory);
                                    }
                                }
                                // 判断省采额度是否充足，省采额度只会同时订购一个sku和1个原子数量。所以和原子结算单价做比较即可.且都为拍下减库存
                                // A04 DICT范式产品线条额度预占，额度预占只有下单减库存

                                List<String> dictProductLines = new ArrayList<>();
                                dictProductLines.add("00");
                                dictProductLines.add("01");
                                CategoryInfo categoryInfo = categoryInfoMapper.selectByExample(new CategoryInfoExample()
                                        .createCriteria().andSpuIdEqualTo(spuOfferingInfo.getId()).example()).get(0);

                                if (Objects.equals(categoryInfo.getOfferingClass(), "A04")
                                        && dictProductLines.contains(spuOfferingInfo.getDictProductlines())
                                        && isNumeric(atomOfferingInfoEntity.getSettleservicename())) {
                                    // 获取现存额度
                                    // 获取提单人省代码
                                    ContractCityInfoExample contractCityInfoExample = new ContractCityInfoExample();
                                    ContractCityInfoExample.Criteria criteria1 = contractCityInfoExample.or();
                                    criteria1.andMallCodeEqualTo(inventoryInfoRequest.getRegionId());
                                    ContractCityInfoExample.Criteria criteria2 = contractCityInfoExample.or();
                                    criteria2.andProvinceMallCodeEqualTo(inventoryInfoRequest.getRegionId());
                                    List<ContractCityInfo> contractCityInfos = contractCityInfoMapper
                                            .selectByExample(contractCityInfoExample);
                                    if (CollectionUtils.isEmpty(contractCityInfos)) {
                                        log.info("提单人区域必传，不能为空");
                                        throw new IOTException(iotAnswer, "-1", "提单人区域必传，不能为空");
                                    }
                                    ContractCityInfo contractCityInfo = contractCityInfos.get(0);
                                    ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                                    ServicePackLimitAmountExample.Criteria criteria = servicePackLimitAmountExample
                                            .createCriteria();
                                    if (contractCityInfo.getProvinceMallCode().equals("000")) {
                                        // 全国，只需要判断服务包是否存在额度
                                        criteria.andServiceCodeEqualTo(atomOfferingInfoEntity.getSettleservicename())
                                                .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                                        List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                                                .selectByExample(servicePackLimitAmountExample);
                                        if (CollectionUtils.isEmpty(servicePackLimitAmounts)) {
                                            log.info("DICT类商品:额度短缺");
                                            isShort.set(true);
                                            atomOfferingInfoShop.setQuantity(0L);
                                            // throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                        }
                                        int validCount = 0;
                                        if (CollectionUtils.isNotEmpty(servicePackLimitAmounts)) {
                                            for (ServicePackLimitAmount servicePackLimitAmount : servicePackLimitAmounts) {
                                                Date now = new Date();
                                                String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss")
                                                        .format(now);
                                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                                                Date currentDateTime = null;
                                                try {
                                                    currentDateTime = dateFormat.parse(yearMonthDay);
                                                    Date expTime = dateFormat
                                                            .parse(servicePackLimitAmount.getExptime());
                                                    if (currentDateTime.after(expTime)) {

                                                        log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                                                        // 冻结额度
                                                        executorService
                                                                .execute(() -> expireLimit(servicePackLimitAmount));
                                                        throw new IOTException(iotAnswer, "DICT类商品:额度短缺");
                                                    }
                                                } catch (ParseException e) {
                                                    // throw new IOTException(iotAnswer, "DICT类商品:额度短缺");
                                                    validCount += 1;
                                                }
                                                if (servicePackLimitAmount.getCurrentInventory()
                                                        * 1000 < atomOfferingInfoEntity.getSettlePrice()) {
                                                    log.error("{}DICT类商品:额度短缺", baseRequest.getMessageSeq());
                                                    // throw new IOTException(iotAnswer, "DICT类商品:额度短缺");
                                                    validCount += 1;
                                                }
                                            }
                                        }

                                        if (validCount != 0) {
                                            isShort.set(true);
                                            atomOfferingInfoShop.setQuantity(0L);
                                            // throw new IOTException(iotAnswer,"-1", "DICT类商品:额度短缺");
                                        }

                                    } else {
                                        // 省份
                                        criteria.andServiceCodeEqualTo(atomOfferingInfoEntity.getSettleservicename())
                                                .andCompanyIdEqualTo(contractCityInfo.getProvinceMallCode())
                                                .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                                        List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                                                .selectByExample(servicePackLimitAmountExample);
                                        if (CollectionUtils.isEmpty(servicePackLimitAmounts)) {
                                            log.info("DICT类商品:额度短缺");
                                            isShort.set(true);
                                            atomOfferingInfoShop.setQuantity(0L);
                                            // throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                        }
                                        if (CollectionUtils.isNotEmpty(servicePackLimitAmounts)) {
                                            ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts
                                                    .get(0);
                                            Date now = new Date();
                                            String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
                                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                                            Date currentDateTime = null;
                                            try {
                                                currentDateTime = dateFormat.parse(yearMonthDay);
                                                Date expTime = dateFormat.parse(servicePackLimitAmount.getExptime());
                                                if (currentDateTime.after(expTime)) {

                                                    log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                                                    // 冻结额度
                                                    executorService.execute(() -> expireLimit(servicePackLimitAmount));
                                                    throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                                }
                                            } catch (ParseException e) {
                                                isShort.set(true);
                                                atomOfferingInfoShop.setQuantity(0L);
                                                // throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                            }
                                            if (servicePackLimitAmount.getCurrentInventory()
                                                    * 1000 < atomOfferingInfoEntity.getSettlePrice()) {
                                                log.error("{}DICT类商品:额度短缺", baseRequest.getMessageSeq());
                                                // throw new IOTException(iotAnswer,"-1", "DICT类商品:额度短缺");
                                                isShort.set(true);
                                                atomOfferingInfoShop.setQuantity(0L);
                                            }
                                        }

                                    }

                                }

                                atomOfferingInfosShops.add(atomOfferingInfoShop);
                            }
                            // skuOfferingInfoRes.setQuantity(quantity);
                            skuOfferingInfoRes.setAtomOfferingInfo(atomOfferingInfosShops);
                            skuOfferingInfosRes.add(skuOfferingInfoRes);
                        }

                        // 查询库存模式
                        /*
                         * String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
                         * log.info("test reserve cardx pattern = {}", nowInventoryPattern);
                         * for (InventoryInfoRequest.SkuOfferingInfo skuOfferingInfoReq :
                         * skuOfferingInfoReqs) {
                         * String skuOfferingCode = skuOfferingInfoReq.getSkuOfferingCode();
                         * InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new
                         * InventoryInfoResponse.SkuOfferingInfo();
                         * skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                         * List<InventoryInfoRequest.AtomOfferingInfo> atomOfferingInfoList =
                         * skuOfferingInfoReq.getAtomOfferingInfo();
                         * if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                         * log.info("qryInventory 原子商品信息未传");
                         * throw new IOTException(iotAnswer, "-1", "原子商品信息未传");
                         * }
                         * Long quantity = 1L;
                         * A:
                         * for (InventoryInfoRequest.AtomOfferingInfo atomOfferingInfo :
                         * atomOfferingInfoList) {
                         * String atomOfferingCode = atomOfferingInfo.getAtomOfferingCode();
                         * List<AtomOfferingInfo> atomOfferingInfos =
                         * atomOfferingInfoMapper.selectByExample(new
                         * AtomOfferingInfoExample().createCriteria().andSpuCodeEqualTo(spuOfferingCode)
                         * .andSkuCodeEqualTo(skuOfferingCode).andOfferingCodeEqualTo(atomOfferingCode).
                         * example());
                         * if (CollectionUtils.isEmpty(atomOfferingInfos)){
                         * log.info("qryAtomInfo 原子商品信息未查询到");
                         * throw new IOTException(iotAnswer, "-1", "原子商品信息未查询到");
                         * }
                         * AtomOfferingInfo atomOfferingInfoEntity = atomOfferingInfos.get(0);
                         * Long inventory = atomOfferingInfoEntity.getInventory();
                         * Long reserveInventory = atomOfferingInfoEntity.getReserveInventory();
                         * if (StringUtils.isNotEmpty(nowInventoryPattern)) {
                         * if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                         * //拍下减库存判断当前库存
                         * if (inventory<1L){
                         * quantity = 0L;
                         * break A;
                         * }
                         * }else {
                         * //付款减库存判断总库存
                         * if (inventory+reserveInventory <1L){
                         * quantity = 0L;
                         * break A;
                         * }
                         * }
                         * }
                         *
                         * }
                         * skuOfferingInfoRes.setQuantity(quantity);
                         * skuOfferingInfosRes.add(skuOfferingInfoRes);
                         * }
                         */
                    } else {
                        // TODO 最新要改造的服务类商品查询库存模式
                        // 非运营默认充足 不管预占流水号是不是空都返回9999
                        if (StringUtils.isNotEmpty(bookId)) {
                            log.info("非运营模式下预占流水号bookIdInventory：{}", bookId);
                        } else {
                            log.info("非运营模式下预占流水号bookIdInventory：{}", "是空的");
                        }

                        for (InventoryInfoRequest.SkuOfferingInfo skuOfferingInfoReq : skuOfferingInfoReqs) {
                            String skuOfferingCode = skuOfferingInfoReq.getSkuOfferingCode();
                            InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new InventoryInfoResponse.SkuOfferingInfo();
                            List<InventoryInfoResponse.AtomOfferingInfo> atomOfferingInfosShops = new ArrayList<>();
                            skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                            List<InventoryInfoRequest.AtomOfferingInfo> atomOfferingInfoList = skuOfferingInfoReq
                                    .getAtomOfferingInfo();
                            if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                                log.info("qryInventory 原子商品信息未传");
                                throw new IOTException(iotAnswer, "-1", "原子商品信息未传");
                            }
                            for (InventoryInfoRequest.AtomOfferingInfo atomOfferingInfo : atomOfferingInfoList) {
                                String atomOfferingCode = atomOfferingInfo.getAtomOfferingCode();
                                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper
                                        .selectByExample(new AtomOfferingInfoExample().createCriteria()
                                                .andSpuCodeEqualTo(spuOfferingCode)
                                                .andSkuCodeEqualTo(skuOfferingCode)
                                                .andOfferingCodeEqualTo(atomOfferingCode).example());
                                if (CollectionUtils.isEmpty(atomOfferingInfos)) {
                                    log.info("qryAtomInfo 原子商品信息未查询到");
                                    throw new IOTException(iotAnswer, "-1", "原子商品信息未查询到");
                                }
                                InventoryInfoResponse.AtomOfferingInfo atomOfferingInfoShop = new InventoryInfoResponse.AtomOfferingInfo();
                                // TODO 非运营模式是无限库存呀 先默认吧
                                atomOfferingInfoShop.setAtomOfferingCode(atomOfferingCode);
                                atomOfferingInfoShop.setQuantity(9999L);
                                atomOfferingInfosShops.add(atomOfferingInfoShop);
                                // 判断省采额度是否充足，省采额度只会同时订购一个sku和1个原子数量。所以和原子结算单价做比较即可.且都为拍下减库存
                                // A04 DICT范式产品线条额度预占，额度预占只有下单减库存
                                List<String> dictProductLines = new ArrayList<>();
                                dictProductLines.add("00");
                                dictProductLines.add("01");
                                CategoryInfo categoryInfo = categoryInfoMapper.selectByExample(new CategoryInfoExample()
                                        .createCriteria().andSpuIdEqualTo(spuOfferingInfo.getId()).example()).get(0);
                                if (Objects.equals(categoryInfo.getOfferingClass(), "A04")
                                        && dictProductLines.contains(spuOfferingInfo.getDictProductlines())
                                        && isNumeric(atomOfferingInfos.get(0).getSettleservicename())) {
                                    // 获取现存额度
                                    // 获取提单人省代码
                                    ContractCityInfoExample contractCityInfoExample = new ContractCityInfoExample();
                                    ContractCityInfoExample.Criteria criteria1 = contractCityInfoExample.or();
                                    criteria1.andMallCodeEqualTo(inventoryInfoRequest.getRegionId());
                                    ContractCityInfoExample.Criteria criteria2 = contractCityInfoExample.or();
                                    criteria2.andProvinceMallCodeEqualTo(inventoryInfoRequest.getRegionId());
                                    List<ContractCityInfo> contractCityInfos = contractCityInfoMapper
                                            .selectByExample(contractCityInfoExample);
                                    if (CollectionUtils.isEmpty(contractCityInfos)) {
                                        log.info("提单人区域必传，不能为空");
                                        throw new IOTException(iotAnswer, "-1", "提单人区域必传，不能为空");
                                    }
                                    ContractCityInfo contractCityInfo = contractCityInfos.get(0);
                                    ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                                    ServicePackLimitAmountExample.Criteria criteria = servicePackLimitAmountExample
                                            .createCriteria();
                                    if (contractCityInfo.getProvinceMallCode().equals("000")) {
                                        // 全国，只需要判断服务包是否存在额度
                                        criteria.andServiceCodeEqualTo(atomOfferingInfos.get(0).getSettleservicename())
                                                .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                                        List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                                                .selectByExample(servicePackLimitAmountExample);
                                        if (CollectionUtils.isEmpty(servicePackLimitAmounts)) {
                                            log.info("DICT类商品:额度短缺");
                                            isShort.set(true);
                                            atomOfferingInfoShop.setQuantity(0L);
                                            // throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                        } else {

                                            for (ServicePackLimitAmount servicePackLimitAmount : servicePackLimitAmounts) {
                                                Date now = new Date();
                                                String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss")
                                                        .format(now);
                                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                                                Date currentDateTime = null;
                                                try {
                                                    currentDateTime = dateFormat.parse(yearMonthDay);
                                                    Date expTime = dateFormat
                                                            .parse(servicePackLimitAmount.getExptime());
                                                    if (currentDateTime.after(expTime)) {

                                                        log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                                                        // 冻结额度
                                                        executorService
                                                                .execute(() -> expireLimit(servicePackLimitAmount));
                                                        throw new IOTException(iotAnswer, "DICT类商品:额度短缺");
                                                    }
                                                } catch (ParseException e) {
                                                    isShort.set(true);
                                                    atomOfferingInfoShop.setQuantity(0L);
                                                    log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                                                    // throw new IOTException(iotAnswer, "DICT类商品:额度短缺");

                                                }
                                                if (servicePackLimitAmount.getCurrentInventory()
                                                        * 1000 > atomOfferingInfos.get(0).getSettlePrice()) {
                                                    log.error("{}DICT类商品:额度短缺", baseRequest.getMessageSeq());
                                                    isShort.set(true);
                                                    atomOfferingInfoShop.setQuantity(0L);
                                                    // throw new IOTException(iotAnswer, "DICT类商品:额度短缺");

                                                }
                                            }

                                        }

                                    } else {
                                        // 省份
                                        criteria.andServiceCodeEqualTo(atomOfferingInfos.get(0).getSettleservicename())
                                                .andCompanyIdEqualTo(contractCityInfo.getProvinceMallCode())
                                                .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                                        List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                                                .selectByExample(servicePackLimitAmountExample);
                                        if (CollectionUtils.isEmpty(servicePackLimitAmounts)) {
                                            log.info("DICT类商品:额度短缺");
                                            isShort.set(true);
                                            atomOfferingInfoShop.setQuantity(0L);
                                            // throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                        }
                                        if (CollectionUtils.isNotEmpty(servicePackLimitAmounts)) {
                                            ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts
                                                    .get(0);
                                            Date now = new Date();
                                            String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
                                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                                            Date currentDateTime = null;
                                            try {
                                                currentDateTime = dateFormat.parse(yearMonthDay);
                                                Date expTime = dateFormat.parse(servicePackLimitAmount.getExptime());
                                                if (currentDateTime.after(expTime)) {

                                                    log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                                                    // 冻结额度
                                                    executorService.execute(() -> expireLimit(servicePackLimitAmount));
                                                    throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                                }
                                            } catch (ParseException e) {
                                                isShort.set(true);
                                                atomOfferingInfoShop.setQuantity(0L);
                                                // throw new IOTException(iotAnswer, "-1", "DICT类商品:额度短缺");
                                            }
                                            if (servicePackLimitAmount.getCurrentInventory() * 1000 < atomOfferingInfos
                                                    .get(0).getSettlePrice()) {
                                                log.error("{}DICT类商品:额度短缺", baseRequest.getMessageSeq());
                                                isShort.set(true);
                                                atomOfferingInfoShop.setQuantity(0L);
                                                // throw new IOTException(iotAnswer,"-1", "DICT类商品:额度短缺");
                                            }

                                        }

                                    }

                                }
                            }
                            skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                            skuOfferingInfoRes.setAtomOfferingInfo(atomOfferingInfosShops);
                            skuOfferingInfosRes.add(skuOfferingInfoRes);
                        }

                        // 非运营默认充足
                        /*
                         * for (InventoryInfoRequest.SkuOfferingInfo skuOfferingInfoReq :
                         * skuOfferingInfoReqs) {
                         * String skuOfferingCode = skuOfferingInfoReq.getSkuOfferingCode();
                         * InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new
                         * InventoryInfoResponse.SkuOfferingInfo();
                         * skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                         * skuOfferingInfoRes.setQuantity(1L);
                         * skuOfferingInfosRes.add(skuOfferingInfoRes);
                         * }
                         */
                    }

                } else if (!isMini && SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(spuOfferingClass)) {
                    // 只查看x库存
                    if (ObjectUtils.isNotEmpty(skuOfferingInfoReqs)) {
                        skuOfferingInfoReqs.forEach(sku -> {
                            String skuOfferingCode = sku.getSkuOfferingCode();
                            SkuOfferingInfoExample skuOfferingInfoExample = new SkuOfferingInfoExample();
                            skuOfferingInfoExample.createCriteria()
                                    .andOfferingCodeEqualTo(skuOfferingCode)
                                    .andDeleteTimeIsNull();
                            List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper
                                    .selectByExample(skuOfferingInfoExample);
                            if (skuOfferingInfos.size() == 0) {
                                log.info("库存查询qryInventory：cannot find skuCode = {}", sku.getSkuOfferingCode());
                                throw new IOTException(iotAnswer, "-11", "规格商品不存在，请确认已完成同步");
                            }
                            SkuOfferingInfo skuOfferingInfo = skuOfferingInfos.get(0);
                            String productType = skuOfferingInfo.getProductType();
                            if (KX_PRODUCT_TYPE_ONE.equals(productType) || KX_PRODUCT_TYPE_TWO.equals(productType)
                                    || KX_PRODUCT_TYPE_THREE.equals(productType)) {
                                throw new IOTException(iotAnswer, "-11", "卡+x产品类型1,2,3暂不支持查询");
                            }
                            AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                            AtomOfferingInfoExample.Criteria criteria = atomOfferingInfoExample.createCriteria();
                            criteria.andOfferingClassEqualTo("X");
                            criteria.andSkuCodeEqualTo(skuOfferingCode);
                            criteria.andDeleteTimeIsNull();

                            // 查询相关的原子商品
                            List<AtomOfferingInfo> result = atomOfferingInfoMapper
                                    .selectByExample(atomOfferingInfoExample);
                            // 查询sku商品的发布省份 地市、
                            List<CityInfoDTO> cityInfoList = queryAtomIssueProvinceAndCity(skuOfferingCode);
                            // 查出所有的原子商品信息，然后根据库存去计算规格数量剩余
                            Long minQuantity = getCardXMinQuantity(inventoryInfo, bookRegionId, result, cityInfoList,
                                    iotAnswer);
                            InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new InventoryInfoResponse.SkuOfferingInfo();
                            skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                            skuOfferingInfoRes.setQuantity(minQuantity);
                            skuOfferingInfosRes.add(skuOfferingInfoRes);
                        });

                    } else {
                        // 查询spu下所有sku库存
                        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                        AtomOfferingInfoExample.Criteria criteria = atomOfferingInfoExample.createCriteria();
                        criteria.andSpuCodeEqualTo(spuOfferingCode);
                        criteria.andOfferingClassEqualTo("X");
                        criteria.andDeleteTimeIsNull();
                        List<AtomOfferingInfo> result = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
                        // 根据skuCode分类
                        Map<String, List<AtomOfferingInfo>> skuCode2InfoMap = groupBySkuCode(result);
                        if (ObjectUtils.isNotEmpty(skuCode2InfoMap)) {
                            Iterator<Map.Entry<String, List<AtomOfferingInfo>>> it = skuCode2InfoMap.entrySet()
                                    .iterator();
                            while (it.hasNext()) {
                                Map.Entry<String, List<AtomOfferingInfo>> next = it.next();
                                String skuOfferingCode = next.getKey();
                                List<AtomOfferingInfo> aoiList = next.getValue();
                                // 查询sku商品的发布省份 地市、
                                List<CityInfoDTO> cityInfoList = queryAtomIssueProvinceAndCity(skuOfferingCode);
                                // 查出所有的原子商品信息，然后根据库存去计算规格数量剩余
                                Long minQuantity = getCardXMinQuantity(inventoryInfo, bookRegionId, aoiList,
                                        cityInfoList, iotAnswer);
                                InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new InventoryInfoResponse.SkuOfferingInfo();
                                skuOfferingInfoRes.setSkuOfferingCode(skuOfferingCode);
                                skuOfferingInfoRes.setQuantity(minQuantity);
                                skuOfferingInfosRes.add(skuOfferingInfoRes);
                            }
                        }
                    }
                } else if(isMini){
                    throw new IOTException(iotAnswer, "-1", "小程序查询库存时商品范式错误");
                }else {
                    throw new IOTException(iotAnswer, "-1", "商品范式错误");
                }

            });
            log.info("qryInventory thirdSpuInfoList size = {}", thirdSpuInfoList.size());
            if (thirdSpuInfoList.size() != 0) {
                QueryInventoryDTO queryInventoryDTO = new QueryInventoryDTO();
                QueryInventoryDTO.QInventory inventory = new QueryInventoryDTO.QInventory();
                inventory.setSpuInfo(thirdSpuInfoList);
                queryInventoryDTO.setInventoryInfo(inventory);
                BaseAnswer<Qry3rdInventoryResp> answer = b2BFeignClient.queryInventoryInternal(queryInventoryDTO, "");
                Qry3rdInventoryResp thirdResp = answer.getData();
                log.info("qryInventory QUERY INTERNAL resp = {}", answer);
                List<InventoryInfoResponse.SpuOfferingInfo> thirdSpuOfferingInfosRes = new ArrayList<>();

                thirdResp.getInventoryInfo().getSpuOfferingInfo().forEach(spu -> {
                    InventoryInfoResponse.SpuOfferingInfo thirdSpuOfferingInfoRes = new InventoryInfoResponse.SpuOfferingInfo();
                    thirdSpuOfferingInfosRes.add(thirdSpuOfferingInfoRes);
                    List<InventoryInfoResponse.SkuOfferingInfo> thirdskuOfferingInfosRes = new ArrayList<>();

                    String spuOfferingCode = spu.getSpuCode();
                    thirdSpuOfferingInfoRes.setSpuOfferingCode(spuOfferingCode);
                    thirdSpuOfferingInfoRes.setSkuOfferingInfo(thirdskuOfferingInfosRes);

                    spu.getSkuInfo().forEach(sku -> {
                        List<Qry3rdInventoryResp.InventoryRet> countList = sku.getInventoryCount();
                        long minCount = getThirdMinCount(countList);
                        InventoryInfoResponse.SkuOfferingInfo skuOfferingInfoRes = new InventoryInfoResponse.SkuOfferingInfo();
                        skuOfferingInfoRes.setSkuOfferingCode(sku.getSkuCode());
                        skuOfferingInfoRes.setQuantity(minCount);
                        thirdskuOfferingInfosRes.add(skuOfferingInfoRes);

                    });
                });
                log.info("qryInventory thirdSpuOfferingInfosRes = {}", thirdSpuOfferingInfosRes);
                inventoryInfoResponse.getInventoryInfo().getSpuOfferingInfo().addAll(thirdSpuOfferingInfosRes);

            }

        });
        log.info("qryInventory 库存查询接口返回: {}", iotAnswer);
        // if(isShort.get()){
        // iotAnswer.setResultCode("-1");
        // iotAnswer.setResultDesc("DICT类商品:额度短缺");
        // }
        return iotAnswer;
        /*
         * String skuOfferingCode =
         * inventoryInfoRequest.getInventoryInfo().getOfferingCode();
         * //先查询对应的sku表
         * SkuOfferingInfoExample skuOfferingInfoExample = new SkuOfferingInfoExample();
         * skuOfferingInfoExample.createCriteria().andOfferingCodeEqualTo(
         * skuOfferingCode);
         * List<SkuOfferingInfo> skuOfferingInfos =
         * skuOfferingInfoMapper.selectByExample(skuOfferingInfoExample);
         * if (skuOfferingInfos.size() == 0) {
         * throw new IOTException(iotAnswer, "-11", "规格商品不存在，请确认已完成同步");
         * }
         * //封装返回体
         * InventoryInfoResponse inventoryInfoResponse = new InventoryInfoResponse();
         * InventoryInfoResponse.InventoryInfo inventoryInfo = new
         * InventoryInfoResponse.InventoryInfo();
         * inventoryInfoResponse.setInventoryInfo(inventoryInfo);
         * inventoryInfo.setOfferingCode(skuOfferingCode);
         *
         * AtomOfferingInfoExample atomOfferingInfoExample = new
         * AtomOfferingInfoExample();
         * //这里只查看软件库存。不考虑硬件库存
         * atomOfferingInfoExample.createCriteria().andSkuCodeEqualTo(skuOfferingCode).
         * andOfferingClassEqualTo("H");
         * //查询相关的原子商品
         * List<AtomOfferingInfo> result =
         * atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
         * //查出所有的原子商品信息，然后根据库存去计算规格数量剩余
         * AtomicReference<Long> minQuantity = new AtomicReference<>(null);
         * for (AtomOfferingInfo x : result) {
         * //这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值
         * if (x.getInventory() == null || x.getReserveInventory() == null) {
         * inventoryInfo.setQuantity(0L);
         * iotAnswer.setContent(inventoryInfoResponse);
         * return iotAnswer;
         * }
         * long quantity = x.getInventory() / x.getQuantity();
         * if (minQuantity.get() == null) {
         * minQuantity.set(quantity);
         * }
         * minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
         * }
         * inventoryInfoResponse.setInventoryInfo(inventoryInfo);
         * inventoryInfo.setQuantity(minQuantity.get());
         * iotAnswer.setContent(inventoryInfoResponse);
         * return iotAnswer;
         */
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IOTAnswer<Void> LimitSynchronizationInfo(IOTRequest baseRequest) {
        String limitJson = JSON.toJSONString(baseRequest);
        log.info("授权额度/额度释放信息同步:{}", limitJson);
        Date now = new Date();
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        LimitInfoRequest limitInfoRequest;
        // LimitSyncInfoResponse limitSyncInfoResponse = new LimitSyncInfoResponse();
        try {
            limitInfoRequest = JSON.parseObject(baseRequest.getContent(), LimitInfoRequest.class);

        } catch (Exception e) {
            log.error("授权额度/额度释放信息请求解析异常:{}", e);
            throw new IOTException(iotAnswer, "数据解析异常");
        }
        // limitSyncInfoResponse.setTransID(limitInfoRequest.getTransID());
        try {
            String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            Date currentDateTime = dateFormat.parse(yearMonthDay);
            Date effTime = dateFormat.parse(limitInfoRequest.getEfftime());
            Date expTime = dateFormat.parse(limitInfoRequest.getExptime());
            if ((currentDateTime.before(effTime) || currentDateTime.after(expTime))
                    && limitInfoRequest.getStatus().equals("1")) {
                throw new IOTException(iotAnswer, "授权额度/额度释放操作需要在生效期内");
            }
            if (limitInfoRequest.getOperType().equals("1")) {
                // 授权额度
                // 查询现有授权额度
                ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                servicePackLimitAmountExample.createCriteria().andServiceCodeEqualTo(limitInfoRequest.getServiceCode())
                        .andCompanyIdEqualTo(limitInfoRequest.getCompanyID());
                List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                        .selectByExample(servicePackLimitAmountExample);
                if (servicePackLimitAmounts.size() == 0) {
                    ServicePackLimitAmount servicePackLimitAmount = new ServicePackLimitAmount();
                    servicePackLimitAmount.setId(BaseServiceUtils.getId());
                    servicePackLimitAmount.setCompanyId(limitInfoRequest.getCompanyID());
                    servicePackLimitAmount.setCompanyName(
                            provinceCityConfig.getProvinceCodeNameMap().get(limitInfoRequest.getCompanyID()));
                    servicePackLimitAmount.setServiceCode(limitInfoRequest.getServiceCode());
                    servicePackLimitAmount.setServiceName(limitInfoRequest.getServiceName());
                    servicePackLimitAmount.setIotLimit(Double.valueOf(limitInfoRequest.getIot_limit()));
                    servicePackLimitAmount.setReserveQuatity(0.00);
                    servicePackLimitAmount.setCurrentInventory(Double.valueOf(limitInfoRequest.getIot_limit()));
                    servicePackLimitAmount.setUseInventory(0.00);
                    servicePackLimitAmount.setStatus(DictLimitStatusEnum.SUFFICIENT.getType());
                    servicePackLimitAmount.setProductName(limitInfoRequest.getProductName());
                    servicePackLimitAmount.setEfftime(limitInfoRequest.getEfftime());
                    servicePackLimitAmount.setExptime(limitInfoRequest.getExptime());
                    servicePackLimitAmount.setCreateTime(now);
                    servicePackLimitAmount.setUpdateTime(now);
                    servicePackLimitAmountMapper.insert(servicePackLimitAmount);
                } else {
                    ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts.get(0);
                    if (servicePackLimitAmount.getStatus().equals(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType())) {
                        throw new IOTException(iotAnswer, "授权额度/额度释放操作需要在生效期内");
                    }
                    servicePackLimitAmount.setIotLimit(
                            servicePackLimitAmount.getIotLimit() + new BigDecimal(limitInfoRequest.getIot_limit())
                                    .setScale(2, RoundingMode.HALF_UP).longValue());
                    servicePackLimitAmount.setCurrentInventory(servicePackLimitAmount.getCurrentInventory()
                            + new BigDecimal(limitInfoRequest.getIot_limit())
                            .setScale(2, RoundingMode.HALF_UP).longValue());
                    if (servicePackLimitAmount.getStatus().equals(DictLimitStatusEnum.SHORT.getType())) {
                        servicePackLimitAmount.setStatus("1");
                    }
                    servicePackLimitAmount.setUpdateTime(now);
                    servicePackLimitAmountMapper.updateByPrimaryKeySelective(servicePackLimitAmount);
                }

            } else if (limitInfoRequest.getOperType().equals("2")) {
                // 额度释放
                // 查询现有授权额度
                ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                servicePackLimitAmountExample.createCriteria().andServiceCodeEqualTo(limitInfoRequest.getServiceCode())
                        .andCompanyIdEqualTo(limitInfoRequest.getCompanyID())
                        .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                ;
                List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                        .selectByExample(servicePackLimitAmountExample);
                if (servicePackLimitAmounts.size() == 0) {
                    throw new IOTException(iotAnswer, "授权额度被冻结或不存在");
                } else {
                    if (servicePackLimitAmounts.get(0)
                            .getCurrentInventory() < new BigDecimal(limitInfoRequest.getIot_limit())
                            .setScale(2, RoundingMode.HALF_UP).longValue()) {
                        throw new IOTException(iotAnswer, "释放额度大于现有额度");
                    } else {
                        ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts.get(0);
                        servicePackLimitAmount.setCurrentInventory(servicePackLimitAmount.getCurrentInventory()
                                - new BigDecimal(limitInfoRequest.getIot_limit())
                                .setScale(2, RoundingMode.HALF_UP).longValue());
                        servicePackLimitAmount.setIotLimit(
                                servicePackLimitAmount.getIotLimit() - new BigDecimal(limitInfoRequest.getIot_limit())
                                        .setScale(2, RoundingMode.HALF_UP).longValue());
                        if (servicePackLimitAmount.getIotLimit() == 0) {
                            servicePackLimitAmount.setStatus(DictLimitStatusEnum.SHORT.getType());
                        }
                        servicePackLimitAmount.setUpdateTime(now);
                        servicePackLimitAmountMapper.updateByPrimaryKeySelective(servicePackLimitAmount);
                    }

                }

            } else {
                throw new IOTException(iotAnswer, "请求操作类型错误");
            }
            ServicePackLimitSync servicePackLimitSync = new ServicePackLimitSync();
            servicePackLimitSync.setId(BaseServiceUtils.getId());
            servicePackLimitSync.setTransId(limitInfoRequest.getTransID());
            servicePackLimitSync.setCompanyId(limitInfoRequest.getCompanyID());
            servicePackLimitSync.setProductName(limitInfoRequest.getProductName());
            servicePackLimitSync.setServiceCode(limitInfoRequest.getServiceCode());
            servicePackLimitSync.setServiceName(limitInfoRequest.getServiceName());
            servicePackLimitSync.setStatus(limitInfoRequest.getStatus());
            servicePackLimitSync.setEfftime(limitInfoRequest.getEfftime());
            servicePackLimitSync.setExptime(limitInfoRequest.getExptime());
            servicePackLimitSync.setOperType(limitInfoRequest.getOperType());
            servicePackLimitSync.setIotLimit(Double.valueOf(limitInfoRequest.getIot_limit()));
            servicePackLimitSync.setCreateTime(now);
            servicePackLimitSync.setUpdateTime(now);
            servicePackLimitSyncMapper.insert(servicePackLimitSync);
            // limitSyncInfoResponse.setRspCode("00");
            log.info("授权额度/额度释放信息同步完成:{}", servicePackLimitSync);
        } catch (Exception e) {
            log.error("授权额度/额度释放信息请求失败:{}", e);
            throw new IOTException(iotAnswer, "授权额度/额度释放信息请求失败");
            // limitSyncInfoResponse.setRspCode("99");
            // limitSyncInfoResponse.setRspDesc(e.getMessage());
        }
        // iotAnswer.setContent(limitSyncInfoResponse);
        return iotAnswer;
    }

    @Override
    public IOTAnswer<LimitSyncContent> UsedLimitInquiry(IOTRequest baseRequest) {
        String limitJson = JSON.toJSONString(baseRequest);
        log.info("授权额度查询:{}", limitJson);
        Date now = new Date();
        IOTAnswer<LimitSyncContent> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        LimitGetRequest limitInfoRequest;

        try {
            limitInfoRequest = JSON.parseObject(baseRequest.getContent(), LimitGetRequest.class);

        } catch (Exception e) {
            log.error("授权额度查询请求解析异常:{}", e);
            throw new IOTException(iotAnswer, "数据解析异常");
        }

        ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
        ServicePackLimitAmountExample.Criteria createCriteria = servicePackLimitAmountExample.createCriteria();
        if (limitInfoRequest.getServiceCode() != null) {
            createCriteria.andServiceCodeEqualTo(limitInfoRequest.getServiceCode());
        }
        if (limitInfoRequest.getCompanyID() != null) {
            createCriteria.andCompanyIdEqualTo(limitInfoRequest.getCompanyID());
        }
        createCriteria.andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
        List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                .selectByExample(servicePackLimitAmountExample);
        List<LimitSyncInfoResponse.UsedLimit> usedLimits = new ArrayList<>();
        if (servicePackLimitAmounts.size() > 0) {
            for (ServicePackLimitAmount servicePackLimitAmount : servicePackLimitAmounts) {
                LimitSyncInfoResponse.UsedLimit usedLimit = new LimitSyncInfoResponse.UsedLimit();
                usedLimit.setCompanyID(servicePackLimitAmount.getCompanyId());
                usedLimit.setServiceCode(servicePackLimitAmount.getServiceCode());
                usedLimit.setLimit(String.valueOf(new BigDecimal(servicePackLimitAmount.getUseInventory())
                        .setScale(2, RoundingMode.HALF_UP)));
                usedLimits.add(usedLimit);
            }
        }
        LimitSyncContent contentWrapper = new LimitSyncContent();
        contentWrapper.setUsedLimit(usedLimits);
        iotAnswer.setContent(contentWrapper);
        return iotAnswer;
    }

    /**
     * 额度过期处理
     *
     * @param servicePackLimitAmount
     * @return
     */
    private void expireLimit(ServicePackLimitAmount servicePackLimitAmount) {
        servicePackLimitAmount.setStatus(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
        servicePackLimitAmount.setUpdateTime(new Date());
        servicePackLimitAmountMapper.updateByPrimaryKeySelective(servicePackLimitAmount);

    }

    /**
     * 根据skuCode分类
     *
     * @param result
     * @return
     */
    private Map<String, List<AtomOfferingInfo>> groupBySkuCode(List<AtomOfferingInfo> result) {
        ConcurrentMap<String, List<AtomOfferingInfo>> infoBySkuMap = Maps.newConcurrentMap();
        if (ObjectUtils.isNotEmpty(result)) {
            result.forEach(info -> {
                String skuCode = info.getSkuCode();
                List<AtomOfferingInfo> atomOfferingInfos = infoBySkuMap.get(skuCode);
                if (ObjectUtils.isEmpty(atomOfferingInfos)) {
                    List<AtomOfferingInfo> infos = new ArrayList<>();
                    infos.add(info);
                    infoBySkuMap.put(skuCode, infos);
                } else {
                    atomOfferingInfos.add(info);
                }
            });
        }
        return infoBySkuMap;
    }

    /**
     * 卡+X库存查询
     *
     * @param result
     * @param iotAnswer
     * @return
     */
    private Long getCardXMinQuantity(InventoryInfoRequest.InventoryInfo inventoryInfo, String bookRegion,
                                     List<AtomOfferingInfo> result, List<CityInfoDTO> cityInfoList, IOTAnswer<InventoryInfoResponse> iotAnswer) {
        log.info("getCardXMinQuantity input bookRegion = {}, result = {}", bookRegion, result);
        // 提单人区域
        if (StringUtils.isEmpty(bookRegion)) {
            throw new IOTException(iotAnswer, "提单人区域信息不能为空");
        }
        if (bookRegion.length() != 3 && bookRegion.length() != 4) {
            throw new IOTException(iotAnswer, "提单人区域信息错误bookRegionId：{}", bookRegion);
        }
        int regionType = bookRegion.length() == 3 ? BOOK_RPOVINCE : BOOK_CITY;
        String bookId = inventoryInfo.getBookId();
        // sku商品发布省 市 按照目前来说sku的发布省只有一个 后续需求以提单人区域判断，不sku
        /*
         * CityInfoDTO cityInfoDTO = cityInfoList.get(0);
         * String provinceName = cityInfoDTO.getProvince();
         * List<String> cityName = cityInfoDTO.getCity();
         * String provinceCode =
         * provinceCityConfig.getProvinceNameCodeMap().get(provinceName);
         * List<String> cityCodeList = new ArrayList<>();
         * if (CollectionUtils.isNotEmpty(cityName)){
         * cityName.forEach(x->{
         * if (!"-1".equals(x)){
         * String cityCode = provinceCityConfig.getcityCodeNameMap().get(x);
         * cityCodeList.add(cityCode);
         * }
         * });
         * }
         */
        // 查出所有的原子商品信息，然后根据库存去计算规格数量剩余
        AtomicReference<Long> minQuantity = new AtomicReference<>(null);
        // 查询当前库存模式，拍下减库存模式复用原逻辑返回库存数，付款减库存返回 总库存数=库存数+预占库存数
        String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
        log.info("getCardXMinQuantity InventoryPattern 卡+x库存扣减方式为：{}", nowInventoryPattern);
        // 判断sku商品是全省，还是不是全省 地市名称为空全省范围 新需求以提单人区域判断
        if (regionType == BOOK_RPOVINCE) {
            if (StringUtils.isNotEmpty(nowInventoryPattern)) {
                if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                    if (StringUtils.isNotEmpty(bookId)) {
                        log.info("拍下减库存模式下预占流水号bookIdInventory：{}", bookId);
                        for (AtomOfferingInfo x : result) {
                            // 这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值 省范围查询商品配置的x终端省级库存数（当前库存）
                            String inventoryMainId = x.getInventoryMainId();
                            if (StringUtils.isEmpty(inventoryMainId)) {
                                log.info("getCardXMinQuantity PATTERN_DOWN 当前商品x终端库存未配置：atomId{},skuCode:{}", x.getId(),
                                        x.getSkuCode());
                                minQuantity.set(0L);
                                break;
                            }
                            /*
                             * if (StringUtils.isEmpty(inventoryMainId)){
                             * throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", x.getId());
                             * }
                             */
                            List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper
                                    .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                            .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                            .andLocationIsNull().example());
                            log.info("getCardXMinQuantity PATTERN_DOWN： dkcardxInventoryDetailInfos = {}",
                                    dkcardxInventoryDetailInfos);
                            if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfos)) {
                                log.info("getCardXMinQuantity PATTERN_DOWN 没找到对应的省级库存记录");
                                minQuantity.set(0L);
                                break;
                            }
                            // 一个x终端库存省级，地市库存只有一个
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
                            Integer currentInventory = dkcardxInventoryDetailInfo.getCurrentInventory();
                            Integer reserveQuatity = dkcardxInventoryDetailInfo.getReserveQuatity();
                            Integer totalInventory = dkcardxInventoryDetailInfo.getTotalInventory();
                            if (currentInventory + reserveQuatity == 0) {
                                minQuantity.set(0L);
                                break;
                            }
                            // 下单减库存只返回库存总数量
                            long totalQuantity = currentInventory + reserveQuatity;
                            if (totalQuantity != totalInventory) {
                                log.info("getCardXMinQuantity Error!!! totalInventory is not calculated immediately, " +
                                        "totalquantity = {}, totalInventory = {}", totalQuantity, totalInventory);
                            }
                            // 付款减库存模式计算总库存
                            long quantity = totalInventory / x.getQuantity();
                            if (minQuantity.get() == null) {
                                minQuantity.set(quantity);
                            }
                            minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                            log.info(
                                    "getCardXMinQuantity atomCode = {}, quantity = {}, currentInventory = {}, atomQuantity = {}",
                                    x.getOfferingCode(), quantity, currentInventory, x.getQuantity());
                        }
                    } else {
                        log.info("拍下减库存模式下预占流水号bookIdInventory：{}", "是空的");
                        for (AtomOfferingInfo x : result) {
                            // 这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值 省范围查询商品配置的x终端省级库存数（当前库存）
                            String inventoryMainId = x.getInventoryMainId();
                            // 按产品意思如果原子商品没有配置库存默认返回库存数为0
                            if (StringUtils.isEmpty(inventoryMainId)) {
                                log.info("getCardXMinQuantity PATTERN_DOWN 当前商品x终端库存未配置：atomId{},skuCode:{}", x.getId(),
                                        x.getSkuCode());
                                minQuantity.set(0L);
                                break;
                            }
                            /*
                             * if (StringUtils.isEmpty(inventoryMainId)){
                             * throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", x.getId());
                             * }
                             */
                            List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper
                                    .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                            .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                            .andLocationIsNull().example());
                            log.info("getCardXMinQuantity PATTERN_DOWN： dkcardxInventoryDetailInfos = {}",
                                    dkcardxInventoryDetailInfos);
                            if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfos)) {
                                log.info("getCardXMinQuantity PATTERN_DOWN 没找到对应的省级库存记录");
                                minQuantity.set(0L);
                                break;
                            }
                            // 一个x终端库存省级，地市库存只有一个
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
                            Integer currentInventory = dkcardxInventoryDetailInfo.getCurrentInventory();
                            Integer reserveQuatity = dkcardxInventoryDetailInfo.getReserveQuatity();
                            if (currentInventory == null) {
                                minQuantity.set(0L);
                                break;
                            }
                            // 拍下减库存只返回当前库存数量
                            long quantity = currentInventory / x.getQuantity();
                            if (minQuantity.get() == null) {
                                minQuantity.set(quantity);
                            }
                            minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                            log.info(
                                    "getCardXMinQuantity atomCode = {}, quantity = {}, currentInventory = {}, atomQuantity = {}",
                                    x.getOfferingCode(), quantity, currentInventory, x.getQuantity());
                        }
                    }
                } else {
                    // 付款减库存 总库存
                    if (StringUtils.isNotEmpty(bookId)) {
                        log.info("付款减库存模式下预占流水号bookIdInventory：{}", bookId);
                    } else {
                        log.info("付款减库存模式下预占流水号bookIdInventory：{}", "是空的");
                    }
                    for (AtomOfferingInfo x : result) {
                        // 这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值 省范围查询商品配置的x终端省级库存数（当前库存）
                        String inventoryMainId = x.getInventoryMainId();
                        if (StringUtils.isEmpty(inventoryMainId)) {
                            log.info("getCardXMinQuantity PATTERN_DOWN 当前商品x终端库存未配置：atomId{},skuCode:{}", x.getId(),
                                    x.getSkuCode());
                            minQuantity.set(0L);
                            break;
                        }
                        /*
                         * if (StringUtils.isEmpty(inventoryMainId)){
                         * throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", x.getId());
                         * }
                         */
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                        .andLocationIsNull().example());
                        log.info("getCardXMinQuantity PATTERN_DOWN： dkcardxInventoryDetailInfos = {}",
                                dkcardxInventoryDetailInfos);
                        if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfos)) {
                            log.info("getCardXMinQuantity PATTERN_DOWN 没找到对应的省级库存记录");
                            minQuantity.set(0L);
                            break;
                        }
                        // 一个x终端库存省级，地市库存只有一个
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
                        Integer currentInventory = dkcardxInventoryDetailInfo.getCurrentInventory();
                        Integer reserveQuatity = dkcardxInventoryDetailInfo.getReserveQuatity();
                        Integer totalInventory = dkcardxInventoryDetailInfo.getTotalInventory();
                        if (currentInventory + reserveQuatity == 0) {
                            minQuantity.set(0L);
                            break;
                        }
                        // 下单减库存只返回库存总数量
                        long totalQuantity = currentInventory + reserveQuatity;
                        if (totalQuantity != totalInventory) {
                            log.info("getCardXMinQuantity Error!!! totalInventory is not calculated immediately, " +
                                    "totalquantity = {}, totalInventory = {}", totalQuantity, totalInventory);
                        }
                        // 付款减库存模式计算总库存
                        long quantity = totalInventory / x.getQuantity();
                        if (minQuantity.get() == null) {
                            minQuantity.set(quantity);
                        }
                        minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                        log.info(
                                "getCardXMinQuantity atomCode = {}, quantity = {}, currentInventory = {}, atomQuantity = {}",
                                x.getOfferingCode(), quantity, currentInventory, x.getQuantity());
                    }
                }
            }
        } else {
            // 不为空，表示多个地市发布 根据提单人的区域进行查询，对应地市+省级的x终端库存数。
            if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                if (StringUtils.isNotEmpty(bookId)) {
                    log.info("拍下减库存模式下预占流水号bookIdInventory：{}", bookId);
                    if (regionType == BOOK_RPOVINCE) {
                        throw new IOTException(iotAnswer, "省级提单人无法订购发布区域为地市的商品,regionId:{}", bookRegion);
                    }
                    for (AtomOfferingInfo x : result) {
                        // 这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值 省范围查询商品配置的x终端省级库存数（当前库存）
                        // 根据提单人区域查询x终端库存数据 地市+省
                        String inventoryMainId = x.getInventoryMainId();
                        if (StringUtils.isEmpty(inventoryMainId)) {
                            log.info("getCardXMinQuantity PATTERN_DOWN 当前商品x终端库存未配置：atomId{},skuCode:{}", x.getId(),
                                    x.getSkuCode());
                            minQuantity.set(0L);
                            break;
                        }
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosProvince = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                        .andLocationIsNull().example());
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosCity = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andLocationEqualTo(bookRegion)
                                        .andProvinceAliasNameIsNull().example());
                        log.info(
                                "getCardXMinQuantity PATTERN_DOWN： dkcardxInventoryDetailInfosProvince = {},dkcardxInventoryDetailInfosCity ={}",
                                dkcardxInventoryDetailInfosProvince, dkcardxInventoryDetailInfosCity);
                        if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfosProvince)
                                && CollectionUtils.isEmpty(dkcardxInventoryDetailInfosCity)) {
                            log.info("getCardXMinQuantity PATTERN_DOWN 没找到对应的地市级,省级库存记录");
                            minQuantity.set(0L);
                            break;
                        }
                        Integer currentInventoryProvince = 0;
                        Integer reserveQuatityProvince = 0;
                        Integer currentInventoryCity = 0;
                        Integer reserveQuatityCity = 0;
                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosProvince)) {
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoProvince = dkcardxInventoryDetailInfosProvince
                                    .get(0);
                            currentInventoryProvince = dkcardxInventoryDetailInfoProvince.getCurrentInventory();
                            reserveQuatityProvince = dkcardxInventoryDetailInfoProvince.getReserveQuatity();
                        }

                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosCity)) {
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoCity = dkcardxInventoryDetailInfosCity
                                    .get(0);
                            currentInventoryCity = dkcardxInventoryDetailInfoCity.getCurrentInventory();
                            reserveQuatityCity = dkcardxInventoryDetailInfoCity.getReserveQuatity();
                        }
                        // 拍下减库存只返回当前库存数量
                        int totalProvince = currentInventoryProvince + reserveQuatityProvince;
                        int totalCity = currentInventoryCity + reserveQuatityCity;
                        long quantity = (totalProvince + totalCity) / x.getQuantity();
                        if (minQuantity.get() == null) {
                            minQuantity.set(quantity);
                        }
                        minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                        log.info(
                                "getCardXMinQuantity atomCode = {}, quantity = {}, TotalInventory = {}, atomQuantity = {}",
                                x.getOfferingCode(), quantity, totalProvince + totalCity, x.getQuantity());
                    }
                } else {
                    // 如果提单人是省级,商品是地市，省级提单人无法订购发布区域为地市的商品
                    log.info("拍下减库存模式下预占流水号bookIdInventory：{}", "是空的");
                    if (regionType == BOOK_RPOVINCE) {
                        throw new IOTException(iotAnswer, "省级提单人无法订购发布区域为地市的商品,regionId:{}", bookRegion);
                    }
                    for (AtomOfferingInfo x : result) {
                        // 这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值 省范围查询商品配置的x终端省级库存数（当前库存）
                        // 根据提单人区域查询x终端库存数据 地市+省
                        String inventoryMainId = x.getInventoryMainId();
                        if (StringUtils.isEmpty(inventoryMainId)) {
                            log.info("getCardXMinQuantity PATTERN_DOWN 当前商品x终端库存未配置：atomId{},skuCode:{}", x.getId(),
                                    x.getSkuCode());
                            minQuantity.set(0L);
                            break;
                        }
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosProvince = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                        .andLocationIsNull().example());
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosCity = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andLocationEqualTo(bookRegion)
                                        .andProvinceAliasNameIsNull().example());
                        log.info(
                                "getCardXMinQuantity PATTERN_DOWN： dkcardxInventoryDetailInfosProvince = {},dkcardxInventoryDetailInfosCity ={}",
                                dkcardxInventoryDetailInfosProvince, dkcardxInventoryDetailInfosCity);
                        if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfosProvince)
                                && CollectionUtils.isEmpty(dkcardxInventoryDetailInfosCity)) {
                            log.info("getCardXMinQuantity PATTERN_DOWN 没找到对应的地市级,省级库存记录");
                            minQuantity.set(0L);
                            break;
                        }
                        Integer currentInventoryProvince = 0;
                        Integer currentInventoryCity = 0;
                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosProvince)) {
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoProvince = dkcardxInventoryDetailInfosProvince
                                    .get(0);
                            currentInventoryProvince = dkcardxInventoryDetailInfoProvince.getCurrentInventory();
                        }

                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosCity)) {
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoCity = dkcardxInventoryDetailInfosCity
                                    .get(0);
                            currentInventoryCity = dkcardxInventoryDetailInfoCity.getCurrentInventory();
                        }
                        // 拍下减库存只返回当前库存数量
                        long quantity = (currentInventoryProvince + currentInventoryCity) / x.getQuantity();
                        if (minQuantity.get() == null) {
                            minQuantity.set(quantity);
                        }
                        minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                        log.info(
                                "getCardXMinQuantity atomCode = {}, quantity = {}, TotalInventory = {}, atomQuantity = {}",
                                x.getOfferingCode(), quantity, currentInventoryProvince + currentInventoryCity,
                                x.getQuantity());
                    }
                }
            } else {
                if (StringUtils.isNotEmpty(bookId)) {
                    log.info("付款减库存模式下预占流水号bookIdInventory：{}", bookId);
                } else {
                    log.info("付款减库存模式下预占流水号bookIdInventory：{}", "是空的");
                }
                // 付款减库存 总库存
                if (regionType == BOOK_RPOVINCE) {
                    throw new IOTException(iotAnswer, "省级提单人无法订购发布区域为地市的商品,regionId:{}", bookRegion);
                }
                for (AtomOfferingInfo x : result) {
                    // 这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值 省范围查询商品配置的x终端省级库存数（当前库存）
                    // 根据提单人区域查询x终端库存数据 地市+省
                    String inventoryMainId = x.getInventoryMainId();
                    if (StringUtils.isEmpty(inventoryMainId)) {
                        log.info("getCardXMinQuantity PATTERN_DOWN 当前商品x终端库存未配置：atomId{},skuCode:{}", x.getId(),
                                x.getSkuCode());
                        minQuantity.set(0L);
                        break;
                    }
                    List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosProvince = dkcardxInventoryDetailInfoMapper
                            .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                    .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                    .andLocationIsNull().example());
                    List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosCity = dkcardxInventoryDetailInfoMapper
                            .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                    .andInventoryMainIdEqualTo(inventoryMainId).andLocationEqualTo(bookRegion)
                                    .andProvinceAliasNameIsNull().example());
                    log.info(
                            "getCardXMinQuantity PATTERN_DOWN： dkcardxInventoryDetailInfosProvince = {},dkcardxInventoryDetailInfosCity ={}",
                            dkcardxInventoryDetailInfosProvince, dkcardxInventoryDetailInfosCity);
                    if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfosProvince)
                            && CollectionUtils.isEmpty(dkcardxInventoryDetailInfosCity)) {
                        log.info("getCardXMinQuantity PATTERN_DOWN 没找到对应的地市级,省级库存记录");
                        minQuantity.set(0L);
                        break;
                    }
                    Integer currentInventoryProvince = 0;
                    Integer reserveQuatityProvince = 0;
                    Integer currentInventoryCity = 0;
                    Integer reserveQuatityCity = 0;
                    if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosProvince)) {
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoProvince = dkcardxInventoryDetailInfosProvince
                                .get(0);
                        currentInventoryProvince = dkcardxInventoryDetailInfoProvince.getCurrentInventory();
                        reserveQuatityProvince = dkcardxInventoryDetailInfoProvince.getReserveQuatity();
                    }

                    if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosCity)) {
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoCity = dkcardxInventoryDetailInfosCity
                                .get(0);
                        currentInventoryCity = dkcardxInventoryDetailInfoCity.getCurrentInventory();
                        reserveQuatityCity = dkcardxInventoryDetailInfoCity.getReserveQuatity();
                    }
                    // 拍下减库存只返回当前库存数量
                    int totalProvince = currentInventoryProvince + reserveQuatityProvince;
                    int totalCity = currentInventoryCity + reserveQuatityCity;
                    long quantity = (totalProvince + totalCity) / x.getQuantity();
                    if (minQuantity.get() == null) {
                        minQuantity.set(quantity);
                    }
                    minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                    log.info("getCardXMinQuantity atomCode = {}, quantity = {}, TotalInventory = {}, atomQuantity = {}",
                            x.getOfferingCode(), quantity, totalProvince + totalCity, x.getQuantity());
                }
            }
        }
        return minQuantity.get();
    }

    /**
     * 根据库存去计算规格数量剩余
     *
     * @param result
     * @return
     */
    private Long getMinQuantity(InventoryInfoRequest.InventoryInfo inventoryInfo, List<AtomOfferingInfo> result,
                                IOTAnswer<InventoryInfoResponse> iotAnswer) {
        // 查出所有的原子商品信息，然后根据库存去计算规格数量剩余
        AtomicReference<Long> minQuantity = new AtomicReference<>(null);
        // 查询当前库存模式，拍下减库存模式复用原逻辑返回库存数，付款减库存返回 总库存数=库存数+预占库存数
        String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
        String bookId = inventoryInfo.getBookId();
        if (StringUtils.isNotEmpty(nowInventoryPattern)) {
            if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                // 预占流水号不为空，拍下模式返回总数，为空返回当前库存
                if (StringUtils.isNotEmpty(bookId)) {
                    log.info("拍下减库存模式下预占流水号bookIdInventory：{}", bookId);
                    for (AtomOfferingInfo x : result) {
                        // 这里获取（总库存）/规格下原子商品数量 比较最小值
                        if (x.getInventory() == null || x.getReserveInventory() == null) {
                            minQuantity.set(0L);
                            break;
                        }
                        long inventorySum = x.getInventory() + x.getReserveInventory();
                        long quantity = inventorySum / x.getQuantity();
                        if (minQuantity.get() == null) {
                            minQuantity.set(quantity);
                        }
                        minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                    }
                } else {
                    log.info("拍下减库存模式下预占流水号bookIdInventory：{}", "是空的");
                    for (AtomOfferingInfo x : result) {
                        // 这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值
                        if (x.getInventory() == null || x.getReserveInventory() == null) {
                            minQuantity.set(0L);
                            break;
                        }
                        long quantity = x.getInventory() / x.getQuantity();
                        if (minQuantity.get() == null) {
                            minQuantity.set(quantity);
                        }
                        minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                    }
                }
            } else {
                // 付款减库存模式 无论有问预占流水都返回总库存
                if (StringUtils.isNotEmpty(bookId)) {
                    log.info("付款减库存模式下预占流水号bookIdInventory：{}", bookId);
                } else {
                    log.info("付款减库存模式下预占流水号bookIdInventory：{}", "是空的");
                }
                for (AtomOfferingInfo x : result) {
                    // 这里获取（总库存）/规格下原子商品数量 比较最小值
                    if (x.getInventory() == null || x.getReserveInventory() == null) {
                        minQuantity.set(0L);
                        break;
                    }
                    long inventorySum = x.getInventory() + x.getReserveInventory();
                    long quantity = inventorySum / x.getQuantity();
                    if (minQuantity.get() == null) {
                        minQuantity.set(quantity);
                    }
                    minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                }
            }
        } else {
            throw new IOTException(iotAnswer, "库存切换模式未配置，查询库存失败");
        }
        return minQuantity.get();
    }

    /**
     * 释放库存
     *
     * @param baseRequest
     * @return
     */
    @Override
    public IOTAnswer<Void> releaseInventory(IOTRequest baseRequest) {
        log.info("接受到库存释放请求:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        ReleaseInventoryRequest request = JSON.parseObject(baseRequest.getContent(), ReleaseInventoryRequest.class);
        List<ReleaseInventoryRequest.BookInfo> bookInfo = request.getBookInfo();
        if (CollectionUtils.isEmpty(bookInfo)) {
            throw new IOTException(iotAnswer, "-1", "未传递流水号信息");
        }
        List<String> lockKeys = bookInfo.stream().map(b -> {
            return "releaseInventory" + b.getBookId();
        }).collect(Collectors.toList());

        return redisUtil.smartLock(lockKeys, () -> {
            for (ReleaseInventoryRequest.BookInfo info : bookInfo) {
                String bookId = info.getBookId();
                // 任何一个预占释放失败，就会抛出异常，阻断流程。只有全部释放成功才会返回成功
                getReleaseInventoryIOTAnswer(baseRequest, iotAnswer, bookId);
            }
            return iotAnswer;
        });
    }

    /**
     * 获得锁后内层使用事务，可以避免并发情况下预占过多的库存,并且减小事务隔离的范围，避免事务互相隔离导致更新的判断条件失效
     */
    @Transactional(rollbackFor = Exception.class)
    public IOTAnswer<Void> getReleaseInventoryIOTAnswer(IOTRequest baseRequest, IOTAnswer<Void> iotAnswer,
                                                        String bookId) {
        try {
            String content = stringRedisTemplate.opsForValue().get(REDIS_RESERVE_INVENTORY_PREFIX + bookId);
            List<String> inventoryInContractClass = Arrays.asList(SPUOfferingClassEnum.A04.getSpuOfferingClass(),
                    SPUOfferingClassEnum.A08.getSpuOfferingClass(), SPUOfferingClassEnum.A09.getSpuOfferingClass(),
                    SPUOfferingClassEnum.A12.getSpuOfferingClass(), SPUOfferingClassEnum.A14.getSpuOfferingClass(),
                    SPUOfferingClassEnum.A15.getSpuOfferingClass(), SPUOfferingClassEnum.A16.getSpuOfferingClass(),
                    SPUOfferingClassEnum.A17.getSpuOfferingClass());
            if (content == null) {
                //临时处理dict请求库存恢复
                Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(bookId);
                String spuOfferingClass = order2cInfo.getSpuOfferingClass();
                if (inventoryInContractClass.contains(spuOfferingClass)){
                    //针对DICT类的仅退款已经同步的订单默认成功释放 方便商城走流程  真的库存恢复在订单同步退款完成
                    log.info("针对DICT类(服务类)的仅退款已经同步的订单默认成功释放bookId:{}", bookId);
                    log.info("releaseInventory,result:{}", JSON.toJSONString(iotAnswer));
                    return  iotAnswer;
                }
                log.info("未查询到Redis中该订单流水号:{}", bookId);
                throw new IOTException(iotAnswer, "11", "该笔订单:" + bookId + "已完成释放，请勿重复发起");
            }
            List<String> inventoryInAtomClass = Arrays.asList(SPUOfferingClassEnum.A06.getSpuOfferingClass(),
                    SPUOfferingClassEnum.A07.getSpuOfferingClass());
            List<String> inventoryInKxClass = Arrays.asList(SPUOfferingClassEnum.A11.getSpuOfferingClass());
            List<ReserveInventoryRequest.SpuOfferingInfo> inventoryInAtomSpu = new ArrayList<>();
            List<ReserveInventoryRequest.SpuOfferingInfo> inventoryInContractSpu = new ArrayList<>();
            List<ReserveInventoryRequest.SpuOfferingInfo> inventoryInKxSpu = new ArrayList<>();

            if (content != null) {
                JSONObject jsonBookInfo = JSON.parseObject(content);
                ReserveInventoryRequest.InventoryInfo inventoryInfo = null;
                if (ObjectUtils.isEmpty(jsonBookInfo.getJSONArray("spuOfferingInfo"))) {
                    // 老格式预占信息
                    inventoryInfo = new ReserveInventoryRequest.InventoryInfo();
                    ReserveInventoryRequest.SpuOfferingInfo spuOfferingInfo = new ReserveInventoryRequest.SpuOfferingInfo();
                    spuOfferingInfo.setOfferingClass(SPUOfferingClassEnum.A06.getSpuOfferingClass());
                    ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = new ReserveInventoryRequest.SkuOfferingInfo();
                    skuOfferingInfo.setOfferingCode(jsonBookInfo.getString("offeringCode"));
                    skuOfferingInfo.setQuantity(Long.valueOf(jsonBookInfo.getString("quantity")));
                    spuOfferingInfo.setSkuOfferingInfo(Collections.singletonList(skuOfferingInfo));
                    inventoryInfo.setSpuOfferingInfo(Collections.singletonList(spuOfferingInfo));

                } else {
                    inventoryInfo = JSON.parseObject(content, ReserveInventoryRequest.InventoryInfo.class);
                }
                inventoryInAtomSpu = inventoryInfo.getSpuOfferingInfo().stream()
                        .filter(x -> inventoryInAtomClass.contains(x.getOfferingClass())).collect(Collectors.toList());
                inventoryInContractSpu = inventoryInfo.getSpuOfferingInfo().stream()
                        .filter(x -> inventoryInContractClass.contains(x.getOfferingClass()))
                        .collect(Collectors.toList());
                inventoryInKxSpu = inventoryInfo.getSpuOfferingInfo().stream()
                        .filter(x -> inventoryInKxClass.contains(x.getOfferingClass())).collect(Collectors.toList());
            }

            // 预占时已经校验，释放库存不再校验范式冲突
            if (CollectionUtils.isNotEmpty(inventoryInAtomSpu)) {
                String skuOfferingCode = inventoryInAtomSpu.get(0).getSkuOfferingInfo().get(0).getOfferingCode();
                Long skuQuantity = inventoryInAtomSpu.get(0).getSkuOfferingInfo().get(0).getQuantity();
                AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                // 这里只查询硬件的，软件不考虑 offeringClass 为 H
                atomOfferingInfoExample.createCriteria()
                        .andSkuCodeEqualTo(skuOfferingCode)
                        .andOfferingClassEqualTo("H")
                        .andDeleteTimeIsNull();
                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper
                        .selectByExample(atomOfferingInfoExample);

                List<String> materialNums = new ArrayList<>();

                atomOfferingInfos.forEach(x -> {
                    log.info("releaseInventory 释放库存 third region = {}", x.getOfferingsaleregion());
                    if ("Yunnan".equals(x.getOfferingsaleregion())) {
                        // 目前只有一个的情况
                        materialNums.add(x.getId());
                    } else {
                        // 这里对每个库存进行修改
                        // 按照道理是不会减失败，因为预占成功了肯定是有库存量的
                        int influence = inventoryHandlerMapper.releaseInventoryByPrimaryKey(x.getId(), skuQuantity);
                        if (influence == 0) {
                            log.error("{}库存预占数不足,bookId:{}，释放失败", baseRequest.getMessageSeq(), bookId);
                            throw new IOTException(iotAnswer, "库存预占数不足,bookId:" + bookId + "，释放失败");
                        }
                    }
                });
                log.info("releaseInventory third materialNums = {}", materialNums.size());
                if (materialNums.size() != 0) {
                    BaseAnswer answer = b2BFeignClient.releaseInventoryInternal(bookId);
                    if ("1".equals(answer.getStateCode())) {
                        // 成功
                        // donothing
                    } else {
                        // 失败
                        log.error("第三方接口释放库存失败:{},bookId:{}", answer.getMessage(), bookId);
                        throw new IOTException(iotAnswer, "13", "第三方接口释放库存失败,bookId:" + bookId);
                    }
                }
            } else if (CollectionUtils.isNotEmpty(inventoryInContractSpu)) {
                // AO4,A08,A09,A12,A14,A15,A16,A17释放库存
                // 是否A14 产品线条
                AtomicBoolean flag = new AtomicBoolean(false);
                inventoryInContractSpu.forEach(spu -> {
                    List<Order2cAtomInfo> order2cAtomInfos = new ArrayList<>();
                    spu.getSkuOfferingInfo().forEach(sku -> sku.getAtomOfferingInfo().forEach(atom -> {
                        Order2cAtomInfo atomInfo = new Order2cAtomInfo();
                        atomInfo.setSpuOfferingCode(spu.getSpuOfferingCode());
                        atomInfo.setSkuOfferingCode(sku.getOfferingCode());
                        atomInfo.setSkuQuantity(sku.getQuantity());
                        atomInfo.setAtomOfferingCode(atom.getOfferingCode());
                        atomInfo.setAtomQuantity(atom.getQuantity());
                        order2cAtomInfos.add(atomInfo);
                    }));
                    List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper
                            .selectByExample(new SpuOfferingInfoExample().createCriteria()
                                    .andOfferingCodeEqualTo(spu.getSpuOfferingCode()).example());
                    SpuOfferingInfo spuOfferingInfo = spuOfferingInfos.get(0);
                    String inventoryType = spuOfferingInfo.getInventoryType();
                    if (StringUtils.isEmpty(inventoryType)) {
                        throw new IOTException(iotAnswer, "当前服务类商品未配置库存模式");
                    }
                    // 判断spu库存模式
                    if (OPERATION_ADMIN_STATISTICS.equals(inventoryType)) {
                        for (Order2cAtomInfo x : order2cAtomInfos) {
                            log.info("releaseInventory, {}", JSON.toJSONString(x));
                            String spuOfferingCode = x.getSpuOfferingCode();
                            String skuOfferingCode = x.getSkuOfferingCode();
                            String atomOfferingCode = x.getAtomOfferingCode();
                            List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper
                                    .selectByExample(new AtomOfferingInfoExample().createCriteria()
                                            .andSpuCodeEqualTo(spuOfferingCode).andSkuCodeEqualTo(skuOfferingCode)
                                            .andOfferingCodeEqualTo(atomOfferingCode).example());
                            if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                                log.error("{}服务类商品不存在,bookId:{}，释放失败", baseRequest.getMessageSeq(), bookId);
                                throw new IOTException(iotAnswer, "服务类商品不存在,bookId:" + bookId + "，释放失败");
                            }
                            AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
                            // 这里对每个库存进行修改
                            // 按照道理是不会减失败，因为预占成功了肯定是有库存量的
                            int influence = inventoryHandlerMapper.releaseInventoryServiceByPrimaryKey(
                                    atomOfferingInfo.getId(), x.getAtomQuantity() * x.getSkuQuantity());
                            if (influence == 0) {
                                log.error("{}服务类商品库存预占数不足,bookId:{}，释放失败", baseRequest.getMessageSeq(), bookId);
                                throw new IOTException(iotAnswer, "服务类商品库存预占数不足,bookId:" + bookId + "，释放失败");
                            }
                            // A04 DICT范式产品线条额度恢复

                            List<String> dictProductLines = new ArrayList<>();
                            dictProductLines.add("00");
                            dictProductLines.add("01");

                            CategoryInfo categoryInfo = categoryInfoMapper.selectByExample(new CategoryInfoExample()
                                    .createCriteria().andSpuIdEqualTo(spuOfferingInfo.getId()).example()).get(0);
                            if (categoryInfo != null && Objects.equals(categoryInfo.getOfferingClass(), "A04")
                                    && dictProductLines.contains(spuOfferingInfo.getDictProductlines())
                                    && isNumeric(atomOfferingInfo.getSettleservicename())) {
                                flag.set(true);
                                // 获取提单人区域
                                String contentX = stringRedisTemplate.opsForValue().get(
                                        REDIS_LIMIT_COMMIT_PERSON_PREFIX + bookId + "_" + atomOfferingInfo.getId());
                                if (contentX == null) {
                                    log.info("未查询到Redis中该订单流水号:{}", bookId);
                                    throw new IOTException(iotAnswer, "11", "该笔订单:" + bookId + "已完成释放，请勿重复发起");
                                }
                                DictLimitRedisDTO inventoryArea = JSON.parseObject(contentX, DictLimitRedisDTO.class);
                                // 获取现存额度
                                ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                                servicePackLimitAmountExample.createCriteria()
                                        .andServiceCodeEqualTo(atomOfferingInfo.getSettleservicename())
                                        .andCompanyIdEqualTo(inventoryArea.getRegionId())
                                        .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                                List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                                        .selectByExample(servicePackLimitAmountExample);
                                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
                                        .isEmpty(servicePackLimitAmounts)) {
                                    log.info("DICT类商品:运营统管额度未找到,原子信息:{}", JSON.toJSONString(x));
                                    throw new IOTException(iotAnswer, "未找到该商品的省采额度");
                                }
                                ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts.get(0);

                                int influence2 = 0;
                                influence2 = inventoryHandlerMapper.releaseLimitInventoryService(
                                        servicePackLimitAmount.getId(), inventoryArea.getLimit());
                                if (influence2 == 0) {
                                    // 这里不会执行，因为前面已经预占了。但是保险
                                    log.info("DICT类商品:运营统管额度恢复失败,原子信息:{}", JSON.toJSONString(x));
                                    throw new IOTException(iotAnswer, "DICT省采额度恢复失败");
                                }
                            }
                        }
                    } else {
                        // 非运管 默认成功 逻辑上为应对切换模式，对原子预占数进行释放，但是不操作当前库存
                        for (Order2cAtomInfo x : order2cAtomInfos) {
                            log.info("releaseInventory, {}", JSON.toJSONString(x));
                            String spuOfferingCode = x.getSpuOfferingCode();
                            String skuOfferingCode = x.getSkuOfferingCode();
                            String atomOfferingCode = x.getAtomOfferingCode();
                            List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper
                                    .selectByExample(new AtomOfferingInfoExample().createCriteria()
                                            .andSpuCodeEqualTo(spuOfferingCode).andSkuCodeEqualTo(skuOfferingCode)
                                            .andOfferingCodeEqualTo(atomOfferingCode).example());
                            if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                                log.error("{}服务类商品不存在,bookId:{}，释放失败", baseRequest.getMessageSeq(), bookId);
                                throw new IOTException(iotAnswer, "服务类商品不存在,bookId:" + bookId + "，释放失败");
                            }
                            AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
                            // 按照道理是不会减失败，因为预占成功了肯定是有库存量的
                            int influence = inventoryHandlerMapper.releaseNoInventoryByPrimaryKey(
                                    atomOfferingInfo.getId(), x.getAtomQuantity() * x.getSkuQuantity());
                            if (influence == 0) {
                                log.error("{}服务类商品库存预占数不足,bookId:{}，释放失败", baseRequest.getMessageSeq(), bookId);
                                throw new IOTException(iotAnswer, "服务类商品库存预占数不足,bookId:" + bookId + "，释放失败");
                            }
                            // A04 DICT范式产品线条额度恢复

                            List<String> dictProductLines = new ArrayList<>();
                            dictProductLines.add("00");
                            dictProductLines.add("01");

                            CategoryInfo categoryInfo = categoryInfoMapper.selectByExample(new CategoryInfoExample()
                                    .createCriteria().andSpuIdEqualTo(spuOfferingInfo.getId()).example()).get(0);
                            if (categoryInfo != null && Objects.equals(categoryInfo.getOfferingClass(), "A04")
                                    && dictProductLines.contains(spuOfferingInfo.getDictProductlines())
                                    && isNumeric(atomOfferingInfo.getSettleservicename())) {
                                flag.set(true);
                                // 获取提单人区域
                                String contentX = stringRedisTemplate.opsForValue().get(
                                        REDIS_LIMIT_COMMIT_PERSON_PREFIX + bookId + "_" + atomOfferingInfo.getId());
                                if (contentX == null) {
                                    log.info("未查询到Redis中该订单流水号:{}", bookId);
                                    throw new IOTException(iotAnswer, "11", "该笔订单:" + bookId + "已完成释放，请勿重复发起");
                                }
                                DictLimitRedisDTO inventoryArea = JSON.parseObject(contentX, DictLimitRedisDTO.class);
                                // 获取现存额度
                                ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                                servicePackLimitAmountExample.createCriteria()
                                        .andServiceCodeEqualTo(atomOfferingInfo.getSettleservicename())
                                        .andCompanyIdEqualTo(inventoryArea.getRegionId())
                                        .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                                List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                                        .selectByExample(servicePackLimitAmountExample);
                                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
                                        .isEmpty(servicePackLimitAmounts)) {
                                    log.info("DICT类商品:运营统管额度未找到,原子信息:{}", JSON.toJSONString(x));
                                    throw new IOTException(iotAnswer, "未找到该商品的省采额度");
                                }
                                ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts.get(0);

                                int influence2 = 0;
                                influence2 = inventoryHandlerMapper.releaseLimitInventoryService(
                                        servicePackLimitAmount.getId(), inventoryArea.getLimit());
                                if (influence2 == 0) {
                                    // 这里不会执行，因为前面已经预占了。但是保险
                                    log.info("DICT类商品:运营统管额度恢复失败,原子信息:{}", JSON.toJSONString(x));
                                    throw new IOTException(iotAnswer, "DICT省采额度恢复失败");
                                }
                            }
                        }
                    }
                });
                if (flag.get()) {
                    String pattern = REDIS_LIMIT_COMMIT_PERSON_PREFIX + bookId + "_*";
                    Set<String> keys = stringRedisTemplate.keys(pattern);
                    if (keys != null) {
                        stringRedisTemplate.delete(keys);
                    }
                }

            } else if (CollectionUtils.isNotEmpty(inventoryInKxSpu)) {
                // 卡+X 释放库存 或者5类X产品类型支持个人客户预付费订购，包含：“合同履约”、
                // “One NET独立服务”、 “标准产品(One NET）”、“One Park独立服务”和“标准产品（One Park）”
                String skuOfferingCode = inventoryInKxSpu.get(0).getSkuOfferingInfo().get(0).getOfferingCode();
                Long skuQuantity = inventoryInKxSpu.get(0).getSkuOfferingInfo().get(0).getQuantity();
                ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = inventoryInKxSpu.get(0).getSkuOfferingInfo()
                        .get(0);
                List<ReserveInventoryRequest.AtomOfferingInfo> atomOfferingInfoList = skuOfferingInfo
                        .getAtomOfferingInfo();
                List<String> atomCodeList = atomOfferingInfoList.stream()
                        .map(ReserveInventoryRequest.AtomOfferingInfo::getOfferingCode).collect(Collectors.toList());
                AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                // 这里只查询硬件的，软件不考虑 offeringClass 为 H
                atomOfferingInfoExample.createCriteria()
                        .andSkuCodeEqualTo(skuOfferingCode)
                        .andOfferingClassEqualTo("X")
                        .andDeleteTimeIsNull();
                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper
                        .selectByExample(atomOfferingInfoExample);
                // 要释放的码号库存
                String cardInventory = stringRedisTemplate.opsForValue()
                        .get(REDIS_CARD_INVENTORY_CONSTANT + bookId + "_" + skuOfferingInfo.getOfferingCode());
                if (cardInventory == null) {
                    log.info("未查询到Redis中该订单流水号,码号库存库存数:{}", bookId);
                    throw new IOTException(iotAnswer, "11", "该笔订单:" + bookId + "已完成释放，请勿重复发起");
                }
                atomOfferingInfos.forEach(x -> {
                    log.info("releaseKxInventory 释放库存 third region = {}", x.getOfferingsaleregion());
                    // 库存释放 原子商品上总的预占数要更新，x终端详情上对应省是库存数。原子x终端详情表本商品对应省市库存数
                    String offeringCode = x.getOfferingCode();
                    if (!atomCodeList.contains(offeringCode)) {
                        throw new IOTException(iotAnswer, "释放的原子商品编码信息为查询到，释放失败");
                    }
                    // 要释放的原子信息
                    ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.stream()
                            .filter(atom -> atom.getOfferingCode().equals(offeringCode)).findFirst().get();
                    String contentX = stringRedisTemplate.opsForValue()
                            .get(REDIS_COMMIT_INVENTORY_AREA + bookId + "_" + x.getId());
                    if (contentX == null) {
                        log.info("未查询到Redis中该订单流水号,x终端库存数:{}", bookId);
                        throw new IOTException(iotAnswer, "11", "该笔订单:" + bookId + "已完成释放，请勿重复发起");
                    }
                    String inventoryMainId = x.getInventoryMainId();
                    // 获取redis保存的x终端省份地市信息
                    List<InventoryAreaDTO> inventoryAreaList = JSON.parseArray(contentX, InventoryAreaDTO.class);
                    // 要释放的原子总预占数
                    long releaseQuantity = atomOfferingInfo.getQuantity() * skuQuantity;
                    long sum = inventoryAreaList.stream().mapToLong(InventoryAreaDTO::getReserveQuantity).sum();
                    if (releaseQuantity > sum) {
                        throw new IOTException(iotAnswer, "要释放的数大于预占数", "该笔订单:" + bookId + "要释放的数大于预占数");
                    }
                    // 遍历进行对应地市或者省份符合库存
                    int influence = 0;
                    for (InventoryAreaDTO inventoryAreaDTO : inventoryAreaList) {
                        String areaCode = inventoryAreaDTO.getAreaCode();
                        Long reserveQuantity = inventoryAreaDTO.getReserveQuantity();
                        int areaCodeType = areaCode.length() == 3 ? PROVINCE_INVENTORY : CITY_INVENTORY;
                        if (areaCodeType == PROVINCE_INVENTORY) {
                            // 释放对应省级库存
                            List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper
                                    .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                            .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                            .andLocationIsNull().example());
                            if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfos)) {
                                throw new IOTException(iotAnswer, "释放的对应省级库存未查询到", inventoryMainId);
                            }
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
                            // 释放原子商品的本商品的总预占数及对应的x终端库存数
                            influence = inventoryHandlerMapper.releaseKxInventoryByPrimaryKey(inventoryMainId,
                                    x.getId(), areaCode, "", reserveQuantity);
                            // 释放原子商品x终端详情表对应省市本商品数
                            List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfos = dkcardxInventoryAtomInfoMapper
                                    .selectByExample(new DkcardxInventoryAtomInfoExample().createCriteria()
                                            .andAtomIdEqualTo(x.getId())
                                            .andInventoryDetailIdEqualTo(dkcardxInventoryDetailInfo.getId()).example());
                            // 原子商品id和x终端详情主键id确定唯一、
                            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = dkcardxInventoryAtomInfos.get(0);
                            Long atomInventory = dkcardxInventoryAtomInfo.getAtomInventory();
                            dkcardxInventoryAtomInfo.setAtomInventory(atomInventory - reserveQuantity);
                            dkcardxInventoryAtomInfo.setUpdateTime(new Date());
                            dkcardxInventoryAtomInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryAtomInfo);
                            if (influence == 0) {
                                log.error("{} 卡+X 库存预占数省级库存不足,bookId:{}，释放失败", baseRequest.getMessageSeq(), bookId);
                                throw new IOTException(iotAnswer,
                                        "卡+X库存预占数省级库存不足,bookId:" + bookId + "_" + areaCode + "，释放失败");
                            }
                        } else {
                            // 释放对应的地市库存
                            List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper
                                    .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                            .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNull()
                                            .andLocationEqualTo(areaCode).example());
                            if (CollectionUtils.isEmpty(dkcardxInventoryDetailInfos)) {
                                throw new IOTException(iotAnswer, "释放的对应省级库存未查询到", inventoryMainId);
                            }
                            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
                            // 释放原子商品的本商品的总预占数及对应的x终端库存数
                            influence = inventoryHandlerMapper.releaseKxInventoryByPrimaryKey(inventoryMainId,
                                    x.getId(), "", areaCode, reserveQuantity);
                            // 释放原子商品x终端详情表对应省市本商品数
                            List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfos = dkcardxInventoryAtomInfoMapper
                                    .selectByExample(new DkcardxInventoryAtomInfoExample().createCriteria()
                                            .andAtomIdEqualTo(x.getId())
                                            .andInventoryDetailIdEqualTo(dkcardxInventoryDetailInfo.getId()).example());
                            // 原子商品id和x终端详情主键id确定唯一、
                            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = dkcardxInventoryAtomInfos.get(0);
                            Long atomInventory = dkcardxInventoryAtomInfo.getAtomInventory();
                            dkcardxInventoryAtomInfo.setAtomInventory(atomInventory - reserveQuantity);
                            dkcardxInventoryAtomInfo.setUpdateTime(new Date());
                            dkcardxInventoryAtomInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryAtomInfo);
                            if (influence == 0) {
                                log.error("{} 卡+X 库存预占数地市库存不足,bookId:{}，释放失败", baseRequest.getMessageSeq(), bookId);
                                throw new IOTException(iotAnswer,
                                        "卡+X库存预占数省级地市库存不足,bookId:" + bookId + "_" + areaCode + "，释放失败");
                            }
                        }
                    }

                });
                // 码号库存释放
                List<CardInventoryInfoDTO> cardInventoryInfoDTOS = JSON.parseArray(cardInventory,
                        CardInventoryInfoDTO.class);
                int reserveQuatity = cardInventoryInfoDTOS.stream().mapToInt(CardInventoryInfoDTO::getReserveQuatity)
                        .sum();
                for (CardInventoryInfoDTO cardInventoryInfoDTO : cardInventoryInfoDTOS) {
                    // 处理预占商品本预占数
                    String id = cardInventoryInfoDTO.getId();
                    Integer reserveQuatityAtom = cardInventoryInfoDTO.getReserveQuatity();
                    String atomId = cardInventoryInfoDTO.getAtomId();
                    List<CardInventoryAtomInfo> cardInventoryAtomInfos = cardInventoryAtomInfoMapper
                            .selectByExample(new CardInventoryAtomInfoExample().createCriteria()
                                    .andCardInventoryMainIdEqualTo(id).andAtomIdEqualTo(atomId).example());
                    CardInventoryAtomInfo cardInventoryAtomInfoOld = cardInventoryAtomInfos.get(0);
                    CardInventoryAtomInfo cardInventoryAtomInfo = new CardInventoryAtomInfo();
                    cardInventoryAtomInfo.setId(cardInventoryAtomInfoOld.getId());
                    cardInventoryAtomInfo
                            .setAtomInventory(cardInventoryAtomInfoOld.getAtomInventory() - reserveQuatityAtom);
                    cardInventoryAtomInfo.setUpdateTime(new Date());
                    cardInventoryAtomInfoMapper.updateByPrimaryKeySelective(cardInventoryAtomInfo);
                }
                // 处理码号库存预占信息 同一个sku下码号库存是唯一的
                CardInventoryMainInfo cardInventoryMainInfo = cardInventoryMainInfoMapper
                        .selectByPrimaryKey(cardInventoryInfoDTOS.get(0).getId());
                CardInventoryMainInfo cardInventoryMainInfoEn = new CardInventoryMainInfo();
                cardInventoryMainInfoEn.setId(cardInventoryMainInfo.getId());
                cardInventoryMainInfoEn.setReserveQuatity(cardInventoryMainInfo.getReserveQuatity() - reserveQuatity);
                cardInventoryMainInfoEn
                        .setCurrentInventory(cardInventoryMainInfo.getCurrentInventory() + reserveQuatity);

                cardInventoryMainInfoEn.setUpdateTime(new Date());
                cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);
                stringRedisTemplate
                        .delete(REDIS_CARD_INVENTORY_CONSTANT + bookId + "_" + skuOfferingInfo.getOfferingCode());

                atomOfferingInfos.forEach(x -> {
                    // 所有库存恢复后删除x终端保存数据redisKey
                    stringRedisTemplate.delete(REDIS_COMMIT_INVENTORY_AREA + bookId + "_" + x.getId());
                });
            } else {
                log.info("getReleaseInventoryIOTAnswer Error 释放库错误：未匹配的产品类型！");
            }
            // 删除redisKey
            stringRedisTemplate.delete(REDIS_RESERVE_INVENTORY_PREFIX + bookId);
            log.info("库存释放完毕,订单流水号:{}", bookId);
        } catch (IOTException iotException) {
            // 对于已知IOT异常进行直接输出
            throw iotException;
        } catch (Exception e) {
            // 对于未知异常封装为IOT异常输出
            log.error("未知错误，错误异常描述:", e);
            throw new IOTException(iotAnswer, "12", "库存释放失败,bookId:" + bookId);
        }
        log.info("releaseInventory,result:{}", JSON.toJSONString(iotAnswer));
        return iotAnswer;
    }

    /**
     * 库存预占
     * preemptStatus 0 预占成功 1 预占失败
     *
     * @param baseRequest
     * @return
     */
    @Override
    public IOTAnswer<ReserveInventoryResponse> reserveInventory(IOTRequest baseRequest) {
        log.info("接受到库存预占请求:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<ReserveInventoryResponse> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        ReserveInventoryRequest request = JSON.parseObject(baseRequest.getContent(), ReserveInventoryRequest.class);
        List<ReserveInventoryRequest.InventoryInfo> inventoryInfos = request.getInventoryInfo();
        if (CollectionUtils.isEmpty(inventoryInfos)) {
            log.error("预定库存信息为空");
            throw new IOTException(iotAnswer, "预定库存信息为空");
        }
        // 按目前场景bookId和spu,sku 一对一的，商城这边拆单处理按主商品、软件服务分别拆开，分别进行预占的
        // A06-软件功能费+（代销类）硬件
        // A07-软件功能费+（合同履约类）硬件
        // A04：（DICT）产品增值服务包
        // A08：OneNET独立服务
        // A09：OnePark独立服务
        // A11: 卡+X
        // A12：行车卫士标准产品
        // A14:OneCyber标准产品
        // A15：千里眼独立服务
        // A16：和对讲独立服务
        // A17：云视讯独立服务

        List<String> inventoryInAtomClass = Arrays.asList(SPUOfferingClassEnum.A06.getSpuOfferingClass(),
                SPUOfferingClassEnum.A07.getSpuOfferingClass());

        List<String> inventoryInAtomKXClass = Arrays.asList(SPUOfferingClassEnum.A11.getSpuOfferingClass());

        List<String> inventoryInContractClass = Arrays.asList(SPUOfferingClassEnum.A04.getSpuOfferingClass(),
                SPUOfferingClassEnum.A08.getSpuOfferingClass(), SPUOfferingClassEnum.A09.getSpuOfferingClass(),
                SPUOfferingClassEnum.A12.getSpuOfferingClass(), SPUOfferingClassEnum.A14.getSpuOfferingClass(),
                SPUOfferingClassEnum.A15.getSpuOfferingClass(), SPUOfferingClassEnum.A16.getSpuOfferingClass(),
                SPUOfferingClassEnum.A17.getSpuOfferingClass());
        List<ReserveInventoryRequest.InventoryInfo> inventoryInAtomSpu = new ArrayList<>();

        List<ReserveInventoryRequest.InventoryInfo> inventoryInAtomKXSpu = new ArrayList<>();

        List<ReserveInventoryRequest.InventoryInfo> inventoryInContractSpu = new ArrayList<>();
        // 用于DICT类预占，根据skuCode分组暂存spu,sku,atom信息
        // Map<String, List<Order2cAtomInfo>> skuAndSpuSkuAtomMap = new HashMap<>();
        // 运营模式下的map
        Map<String, List<Order2cAtomInfo>> skuAndSpuSkuAtomMapOperation = new HashMap<>();
        // 非运营模式下的map
        Map<String, List<Order2cAtomInfo>> skuAndSpuSkuAtomMapNoOperation = new HashMap<>();
        // 用于DICT类预占,暂存skuCode和inventoryInfo的对应关系
        Map<String, ReserveInventoryRequest.InventoryInfo> skuAndInventoryMap = new HashMap<>();

        // 用于K+X类预占，根据skuCode分组暂存spu,sku,atom信息
        Map<String, List<Order2cAtomInfo>> skuAndSpuSkuAtomKXMap = new HashMap<>();
        // 用于K+X类预占,暂存skuCode和inventoryInfo的对应关系
        Map<String, ReserveInventoryRequest.InventoryInfo> skuAndInventoryKXMap = new HashMap<>();
        // 涉及到多个商品的预占，需要使用多key的锁
        List<String> lockKeyAtomClass = new ArrayList<>();
        List<String> lockKeyContractClass = new ArrayList<>();
        List<String> lockKeyKXClass = new ArrayList<>();
        int i = 0;
        for (ReserveInventoryRequest.InventoryInfo inventoryInfo : inventoryInfos) {
            i++;
            String bookId = inventoryInfo.getBookId();
            if (inventoryInfo.getSpuOfferingInfo().size() > 1) {
                throw new IOTException(iotAnswer, "当前只支持一个bookId对应一个spu预占");
            }
            ReserveInventoryRequest.SpuOfferingInfo spuOfferingInfo = inventoryInfo.getSpuOfferingInfo().get(0);
            String offeringClass = spuOfferingInfo.getOfferingClass();

            List<ReserveInventoryRequest.SkuOfferingInfo> skuOfferingInfoList = spuOfferingInfo.getSkuOfferingInfo();
            if (skuOfferingInfoList.size() > 1) {
                throw new IOTException(iotAnswer, "当前只支持一个spu对应一个sku预占");
            }
            ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = skuOfferingInfoList.get(0);

            if (inventoryInAtomClass.contains(offeringClass)) {
                if (skuOfferingInfo.getQuantity() < 0) {
                    // 这里防止输入负数导致库存变相增加
                    throw new IOTException(iotAnswer, "规格商品:[" + skuOfferingInfo.getOfferingCode() + "]预占数量:["
                            + skuOfferingInfo.getQuantity() + "]错误");
                }
                inventoryInAtomSpu.add(inventoryInfo);
                String lockKey = "reserveInventory_" + skuOfferingInfo.getOfferingCode();
                lockKeyAtomClass.add(lockKey);
            } else if (inventoryInContractClass.contains(offeringClass)) {
                List<ReserveInventoryRequest.AtomOfferingInfo> atomOfferingInfos = skuOfferingInfo
                        .getAtomOfferingInfo();
                // 这里防止输入负数导致库存变相增加
                if (atomOfferingInfos.stream().anyMatch(x -> x.getQuantity() < 0)) {
                    log.info("规格商品:[" + skuOfferingInfo.getOfferingCode() + "]的原子商品预占数量错误:"
                            + JSON.toJSONString(skuOfferingInfo.getAtomOfferingInfo()));
                    throw new IOTException(iotAnswer, "规格商品:[" + skuOfferingInfo.getOfferingCode() + "]的原子商品预占数量错误:"
                            + JSON.toJSONString(skuOfferingInfo.getAtomOfferingInfo()));
                }
                inventoryInContractSpu.add(inventoryInfo);
                List<Order2cAtomInfo> order2cAtomInfos = new ArrayList<>();
                List<Order2cAtomInfo> order2cAtomInfosOperation = new ArrayList<>();
                List<Order2cAtomInfo> order2cAtomInfosNoOperation = new ArrayList<>();
                List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper
                        .selectByExample(new SpuOfferingInfoExample()
                                .createCriteria().andOfferingCodeEqualTo(spuOfferingInfo.getSpuOfferingCode())
                                .example());
                SpuOfferingInfo spuOfferingInfoEntity = spuOfferingInfos.get(0);
                String inventoryType = spuOfferingInfoEntity.getInventoryType();
                if (StringUtils.isEmpty(inventoryType)) {
                    throw new IOTException(iotAnswer, "当前服务类商品未配置库存模式");
                }
                String key = bookId + '_' + skuOfferingInfo.getOfferingCode() + "_" + i;
                for (ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfo : skuOfferingInfo
                        .getAtomOfferingInfo()) {
                    String lockKey = StringUtils
                            .join(Arrays.asList("reserveInventory", spuOfferingInfo.getSpuOfferingCode(),
                                    skuOfferingInfo.getOfferingCode(), atomOfferingInfo.getOfferingCode()), "_");
                    lockKeyContractClass.add(lockKey);
                    Order2cAtomInfo atomInfo = new Order2cAtomInfo();
                    atomInfo.setSpuOfferingCode(spuOfferingInfo.getSpuOfferingCode());
                    atomInfo.setSkuOfferingCode(skuOfferingInfo.getOfferingCode());
                    atomInfo.setSkuQuantity(skuOfferingInfo.getQuantity());
                    atomInfo.setAtomOfferingCode(atomOfferingInfo.getOfferingCode());
                    // 服务类商品的预占数是以原子信息的预占数计算的
                    atomInfo.setAtomQuantity(atomOfferingInfo.getQuantity());

                    order2cAtomInfos.add(atomInfo);
                    if (OPERATION_ADMIN_STATISTICS.equals(inventoryType)) {
                        order2cAtomInfosOperation.add(atomInfo);
                        skuAndSpuSkuAtomMapOperation.put(key, order2cAtomInfosOperation);
                    } else {
                        order2cAtomInfosNoOperation.add(atomInfo);
                        skuAndSpuSkuAtomMapNoOperation.put(key, order2cAtomInfosNoOperation);
                    }
                }

                // skuAndSpuSkuAtomMap.put(key, order2cAtomInfos);
                /*
                 * skuAndSpuSkuAtomMapOperation.put(key,order2cAtomInfosOperation);
                 * skuAndSpuSkuAtomMapNoOperation.put(key,order2cAtomInfosNoOperation);
                 */
                skuAndInventoryMap.put(key, inventoryInfo);
            } else if (inventoryInAtomKXClass.contains(offeringClass)) {
                // 代客下单卡+X类型 或者5类X产品类型支持个人客户预付费订购，包含：“合同履约”、
                // “One NET独立服务”、 “标准产品(One NET）”、“One Park独立服务”和“标准产品（One Park）”
                if (skuOfferingInfo.getQuantity() < 0) {
                    // 这里防止输入负数导致库存变相增加
                    throw new IOTException(iotAnswer, "规格商品:[" + skuOfferingInfo.getOfferingCode() + "]预占数量:["
                            + skuOfferingInfo.getQuantity() + "]错误");
                }
                inventoryInAtomKXSpu.add(inventoryInfo);
                String lockKey = "reserveInventory_" + skuOfferingInfo.getOfferingCode();
                lockKeyKXClass.add(lockKey);
                // List<ReserveInventoryRequest.AtomOfferingInfo> atomOfferingInfos =
                // skuOfferingInfo.getAtomOfferingInfo();
                // //这里防止输入负数导致库存变相增加
                // if (atomOfferingInfos.stream().anyMatch(x -> x.getQuantity() <0)) {
                // log.info("规格商品:["+ skuOfferingInfo.getOfferingCode()
                // +"]的原子商品预占数量错误:"+JSON.toJSONString(skuOfferingInfo.getAtomOfferingInfo()));
                // throw new IOTException(iotAnswer, "规格商品:["+ skuOfferingInfo.getOfferingCode()
                // +"]的原子商品预占数量错误:"+JSON.toJSONString(skuOfferingInfo.getAtomOfferingInfo()));
                // }
                // inventoryInAtomKXSpu.add(inventoryInfo);
                //
                // List<Order2cAtomInfo> order2cAtomInfos = new ArrayList<>();
                // for (ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfo :
                // skuOfferingInfo.getAtomOfferingInfo()) {
                // String lockKey = StringUtils.join(Arrays.asList("reserveInventory",
                // spuOfferingInfo.getSpuOfferingCode(),
                // skuOfferingInfo.getOfferingCode(), atomOfferingInfo.getOfferingCode()), "_");
                // lockKeyList.add(lockKey);
                // Order2cAtomInfo atomInfo = new Order2cAtomInfo();
                // atomInfo.setSpuOfferingCode(spuOfferingInfo.getSpuOfferingCode());
                // atomInfo.setSkuOfferingCode(skuOfferingInfo.getOfferingCode());
                // atomInfo.setSkuQuantity(skuOfferingInfo.getQuantity());
                // atomInfo.setAtomOfferingCode(atomOfferingInfo.getOfferingCode());
                // atomInfo.setAtomQuantity(atomOfferingInfo.getQuantity());
                // order2cAtomInfos.add(atomInfo);
                // }
                // skuAndSpuSkuAtomKXMap.put(skuOfferingInfo.getOfferingCode()+"_"+i,order2cAtomInfos);
                // skuAndInventoryKXMap.put(skuOfferingInfo.getOfferingCode()+"_"+i,inventoryInfo);
            } else {
                throw new IOTException(iotAnswer, "存在不支持预占库存的商品范式:" + offeringClass);
            }
        }

        /*
         * if (CollectionUtil.isNotEmpty(inventoryInAtomSpu) &&
         * CollectionUtil.isNotEmpty(inventoryInContractSpu)) {
         * log.info("不支持同时预占代销类与DICT类库存");
         * throw new IOTException(iotAnswer, "不支持同时预占代销类与DICT类库存");
         * }
         */
        ReserveInventoryResponse response = new ReserveInventoryResponse();
        List<ReserveInventoryResponse.InventoryInfo> inventoryInfoList = new ArrayList<>();
        response.setInventoryInfo(inventoryInfoList);

        iotAnswer.setContent(response);
        if (CollectionUtil.isNotEmpty(inventoryInAtomSpu)) {
            // AO6,A07预占库存
            redisUtil.smartLock(lockKeyAtomClass, () -> {
                for (ReserveInventoryRequest.InventoryInfo inventoryInfo : inventoryInAtomSpu) {
                    List<ReserveInventoryRequest.SpuOfferingInfo> spuOfferingInfo = inventoryInfo.getSpuOfferingInfo();
                    ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = spuOfferingInfo.get(0)
                            .getSkuOfferingInfo().get(0);
                    ReserveInventoryResponse.InventoryInfo responseInventoryInfo = new ReserveInventoryResponse.InventoryInfo();
                    responseInventoryInfo.setOfferingCode(skuOfferingInfo.getOfferingCode());
                    inventoryInfoList.add(responseInventoryInfo);
                    try {
                        // 使用内部独立事务，自己调用自己
                        inventoryService.getReserveInventoryResponse(baseRequest, iotAnswer, inventoryInfo,
                                skuOfferingInfo.getQuantity(), responseInventoryInfo,
                                skuOfferingInfo.getOfferingCode());
                    } catch (IOTException e) {
                        // 发生IOT异常，此部分预占失败且已经设置状态
                    } catch (Exception e) {
                        // 发生其他异常，此部分预占失败但未设置状态
                        log.error("代销类预占失败,其他错误", e);
                        responseInventoryInfo.setPreemptStatus("1");
                    }
                }
                // 总预占状态设置
                /*
                 * boolean fail = inventoryInfoList.stream().anyMatch(inventoryInfo -> {
                 * return "1".equals(inventoryInfo.getPreemptStatus());
                 * });
                 * response.setAllPreemptStatus(fail ? "1" : "0");
                 * setAllPreemptStatus(iotAnswer, response, inventoryInfoList);
                 */
                return null;
            });

        }
        // 服务类库存预占
        if (CollectionUtil.isNotEmpty(inventoryInContractSpu)) {
            // AO4,A08,A09,A12,A14,A15,A16,A17预占库存
            redisUtil.smartLock(lockKeyContractClass, () -> {
                // 查询spu库存模式,运营模式预占
                if (MapUtils.isNotEmpty(skuAndSpuSkuAtomMapOperation)) {
                    log.info("运营模式下要预占的信息skuAndSpuSkuAtomMapOperation：{}", skuAndSpuSkuAtomMapOperation);
                    ReserveInventoryResponse.InventoryInfo responseInventoryInfo = null;
                    ReserveInventoryRequest.InventoryInfo inventoryInfo = null;
                    for (Map.Entry<String, List<Order2cAtomInfo>> entry : skuAndSpuSkuAtomMapOperation.entrySet()) {
                        String key = entry.getKey();
                        // String skuOfferingCode = key.substring(0, key.lastIndexOf("_"));
                        String[] splitSkuOfferingCode = key.split("_");
                        String skuOfferingCode = splitSkuOfferingCode[1];
                        List<Order2cAtomInfo> list = entry.getValue();
                        inventoryInfo = skuAndInventoryMap.get(key);
                        ReserveInventoryRequest.SpuOfferingInfo spuOfferingInfo = inventoryInfo.getSpuOfferingInfo()
                                .get(0);
                        ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = spuOfferingInfo.getSkuOfferingInfo()
                                .get(0);
                        responseInventoryInfo = new ReserveInventoryResponse.InventoryInfo();
                        responseInventoryInfo.setOfferingCode(skuOfferingCode);
                        inventoryInfoList.add(responseInventoryInfo);
                        try {
                            inventoryService.getReserveInventoryResponse(baseRequest, iotAnswer,
                                    skuAndInventoryMap.get(key), skuOfferingInfo, responseInventoryInfo, list);
                            // 预占完成后存入redis中缓存起来。 hashKey 为 inventory:bookId value
                            // {"skuOfferingCode":"","quantity":xxx}
                            // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                            // 服务类商品会传多个sku，所以这里循环处理 key多加
                            String bookId = inventoryInfo.getBookId();
                            Boolean setIfAbsent = stringRedisTemplate.opsForValue()
                                    .setIfAbsent(REDIS_RESERVE_INVENTORY_PREFIX + bookId, JSON.toJSONString(inventoryInfo));
                            if (setIfAbsent == null || !setIfAbsent) {
                                responseInventoryInfo.setPreemptStatus("1");
                                log.info("该笔订单已完成预占，请勿重复发起,bookId:{}", bookId);
                                throw new IOTException(iotAnswer, "11", "该笔订单已完成预占，请勿重复发起");
                            }
                        } catch (IOTException e) {
                            // 发生IOT异常，表示此部分预占失败且已经设置状态
                        } catch (Exception e) {
                            // 发生其他异常，表示此部分预占失败但未设置状态
                            log.error("DICT类预占失败,其他错误", e);
                            responseInventoryInfo.setPreemptStatus("1");
                        }
                    }
                }
                // 非运营模式预占
                if (MapUtils.isNotEmpty(skuAndSpuSkuAtomMapNoOperation)) {
                    // 非运营默认充足，但是还是要修改预占数
                    log.info("非运营模式下要预占的信息skuAndSpuSkuAtomMapNoOperation：{}", skuAndSpuSkuAtomMapNoOperation);
                    ReserveInventoryResponse.InventoryInfo responseInventoryInfo = null;
                    ReserveInventoryRequest.InventoryInfo inventoryInfo = null;
                    for (Map.Entry<String, List<Order2cAtomInfo>> entry : skuAndSpuSkuAtomMapNoOperation.entrySet()) {
                        String key = entry.getKey();
                        // String skuOfferingCode = key.substring(0, key.lastIndexOf("_"));
                        String[] splitSkuOfferingCode = key.split("_");
                        String skuOfferingCode = splitSkuOfferingCode[1];
                        List<Order2cAtomInfo> list = entry.getValue();
                        inventoryInfo = skuAndInventoryMap.get(key);
                        ReserveInventoryRequest.SpuOfferingInfo spuOfferingInfo = inventoryInfo.getSpuOfferingInfo()
                                .get(0);
                        ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = spuOfferingInfo.getSkuOfferingInfo()
                                .get(0);
                        responseInventoryInfo = new ReserveInventoryResponse.InventoryInfo();
                        // 默认为成功
                        responseInventoryInfo.setOfferingCode(skuOfferingCode);
                        responseInventoryInfo.setPreemptStatus("0");
                        inventoryInfoList.add(responseInventoryInfo);
                        try {
                            // 非运营统筹 为了后续操作 进行库存预占数入库
                            inventoryService.getNoReserveInventoryResponse(baseRequest, iotAnswer,
                                    skuAndInventoryMap.get(key), skuOfferingInfo, responseInventoryInfo, list);
                            // 预占完成后存入redis中缓存起来。 hashKey 为 inventory:bookId value
                            // {"skuOfferingCode":"","quantity":xxx}
                            // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                            String bookId = inventoryInfo.getBookId();
                            Boolean setIfAbsent = stringRedisTemplate.opsForValue()
                                    .setIfAbsent(REDIS_RESERVE_INVENTORY_PREFIX + bookId, JSON.toJSONString(inventoryInfo));
                            if (setIfAbsent == null || !setIfAbsent) {
                                responseInventoryInfo.setPreemptStatus("1");
                                log.info("该笔订单已完成预占，请勿重复发起,bookId:{}", bookId);
                                throw new IOTException(iotAnswer, "11", "该笔订单已完成预占，请勿重复发起");
                            }
                        } catch (IOTException e) {
                            // 发生IOT异常，表示此部分预占失败且已经设置状态
                        } catch (Exception e) {
                            // 发生其他异常，表示此部分预占失败但未设置状态
                            log.error("DICT类预占失败,其他错误", e);
                            responseInventoryInfo.setPreemptStatus("1");
                        }
                    }
                }
                // 总预占状态设置
                /*
                 * setAllPreemptStatus(iotAnswer, response, inventoryInfoList);
                 * return iotAnswer;
                 */
                return null;
            });
        }

        if (CollectionUtil.isNotEmpty(inventoryInAtomKXSpu)) {
            // A11代客下单卡+X预占
            redisUtil.smartLock(lockKeyKXClass, () -> {
                for (ReserveInventoryRequest.InventoryInfo inventoryInfo : inventoryInAtomKXSpu) {
                    List<ReserveInventoryRequest.SpuOfferingInfo> spuOfferingInfo = inventoryInfo.getSpuOfferingInfo();
                    ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = spuOfferingInfo.get(0)
                            .getSkuOfferingInfo().get(0);
                    ReserveInventoryResponse.InventoryInfo responseInventoryInfo = new ReserveInventoryResponse.InventoryInfo();
                    responseInventoryInfo.setOfferingCode(skuOfferingInfo.getOfferingCode());
                    inventoryInfoList.add(responseInventoryInfo);
                    try {
                        // 使用内部独立事务，自己调用自己
                        inventoryService.getReserveInventoryKXResponse(baseRequest, iotAnswer, inventoryInfo,
                                responseInventoryInfo, skuOfferingInfo.getQuantity(),
                                skuOfferingInfo.getOfferingCode());
                    } catch (IOTException e) {
                        // 发生IOT异常，此部分预占失败且已经设置状态
                    } catch (Exception e) {
                        // 发生其他异常，此部分预占失败但未设置状态
                        log.error("卡+X类预占失败,其他错误", e);
                        responseInventoryInfo.setPreemptStatus("1");
                    }

                }
                // 总预占状态设置
                // setAllPreemptStatus(iotAnswer, response, inventoryInfoList);
                return null;
            });
        } /*
         * else {
         * throw new IOTException(iotAnswer, "无有效预占请求");
         * }
         */
        if (CollectionUtil.isEmpty(inventoryInAtomSpu) && CollectionUtil.isEmpty(inventoryInContractSpu)
                && CollectionUtil.isEmpty(inventoryInAtomKXSpu)) {
            throw new IOTException(iotAnswer, "无有效预占请求");
        }
        // 总预占状态设置
        boolean fail = inventoryInfoList.stream().anyMatch(inventoryInfo -> {
            return "1".equals(inventoryInfo.getPreemptStatus());
        });
        response.setAllPreemptStatus(fail ? "1" : "0");
        iotAnswer.setContent(response);
        return iotAnswer;
    }

    /**
     * 根据各部分预占状态获取总预占状态，全部成功才算成功
     */
    private void setAllPreemptStatus(IOTAnswer<ReserveInventoryResponse> iotAnswer, ReserveInventoryResponse response,
                                     List<ReserveInventoryResponse.InventoryInfo> inventoryInfoList) {
        boolean fail = inventoryInfoList.stream().anyMatch(inventoryInfo -> {
            return "1".equals(inventoryInfo.getPreemptStatus());
        });
        response.setAllPreemptStatus(fail ? "1" : "0");
        iotAnswer.setContent(response);
    }

    /**
     * 获得锁后内层使用事务，可以避免并发情况下预占过多的库存,并且减小事务隔离的范围，避免事务互相隔离导致更新的判断条件失效
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void getReserveInventoryResponse(IOTRequest baseRequest, IOTAnswer<ReserveInventoryResponse> iotAnswer,
                                            ReserveInventoryRequest.InventoryInfo inventoryInfo, Long skuQuantity,
                                            ReserveInventoryResponse.InventoryInfo responseInventoryInfo, String skuOfferingCode) {
        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
        // 这里只查询硬件的，软件不考虑 offeringClass 为 H
        atomOfferingInfoExample.createCriteria()
                .andSkuCodeEqualTo(skuOfferingCode)
                .andOfferingClassEqualTo("H")
                .andDeleteTimeIsNull();

        List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
        List<String> materialNumList = new ArrayList<>();

        for (AtomOfferingInfo x : atomOfferingInfos) {
            log.info("reserveInventory, saleRegion = {}", x.getOfferingsaleregion());
            // 新增判断是省侧库还是本地库
            if ("Yunnan".equals(x.getOfferingsaleregion())) {
                materialNumList.add(x.getExtHardOfferingCode());
            } else {
                // 这里对每个库存进行修改, 如果是拍下减库存按照原来逻辑，如果是付款减库存 就不进行判断（库存是否大于预占）
                String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
                long reserveNum = x.getQuantity() * skuQuantity;
                if (StringUtils.isNotEmpty(nowInventoryPattern)) {
                    if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                        // 校验预占数和当前库存数
                        long inventory = x.getInventory();
                        if (reserveNum > inventory) {
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "拍下减库存库模式下库存不足，预占数大于当前库存，预占失败");
                        }
                        int influence = inventoryHandlerMapper.updateInventoryByPrimaryKey(x.getId(), skuQuantity);
                        // 这里对其进行库存修改如果影响行数为0 则说明修改预占失败，需要回滚，且返回失败
                        if (influence == 0) {
                            log.error("{}拍下减库存模式下库存不足，预占失败", baseRequest.getMessageSeq());
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "拍下减库存库模式下库存不足，预占失败");
                        }
                    } else {
                        // 校验预占数和总库存数
                        long inventoryTotal = x.getInventory() + x.getReserveInventory();
                        if (reserveNum > inventoryTotal) {
                            log.error("{}付款减库存模式下库存不足，预占失败", baseRequest.getMessageSeq());
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "付款减库存模式下库存不足，预占数大于总库存数，预占失败");
                        }
                        int influence = inventoryHandlerMapper.updateInventoryPaymentByPrimaryKey(x.getId(),
                                skuQuantity);
                        // 这里对其进行库存修改如果影响行数为0 则说明修改预占失败，需要回滚，且返回失败
                        if (influence == 0) {
                            log.error("{}付款减库存模式下，预占失败", baseRequest.getMessageSeq());
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "付款减库存模式下，预占失败");
                        }
                    }

                } else {
                    throw new IOTException(iotAnswer, "库存切换模式未配置，预占失败");
                }
            }
        }

        if (materialNumList.size() != 0) {
            log.info("reserveInventory, materialNumList size = {}", materialNumList.size());
            // 目前只有单个产品
            // for(Material mt:materialNumList){
            List<ReserveInventoryDTO.Material> mtList = new ArrayList<>();
            ReserveInventoryDTO reserveDto = new ReserveInventoryDTO();
            ReserveInventoryDTO.Material material = new ReserveInventoryDTO.Material();
            material.setNumber(materialNumList.get(0));
            material.setQuatity(skuQuantity);
            reserveDto.setMaterials(mtList);
            reserveDto.setBookId(inventoryInfo.getBookId());
            BaseAnswer answer = b2BFeignClient.reserveInventoryInternal(reserveDto, "");
            log.info("reserveInventory, 三房接口预占库存 answer = {}", answer);
            if (answer.getStateCode().equals("1")) {
                log.info("reserveInventory, 三房接口预占库存 成功");
                // 接口预占成功
                // 第三方预占也要自己维护redis信息，不然释放库存找不到具体商品，无法判断是调用省侧接口还是自己处理
                Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(
                        REDIS_RESERVE_INVENTORY_PREFIX + inventoryInfo.getBookId(), JSON.toJSONString(inventoryInfo));
                log.info("reserveInventory, 三房接口预占库存 setIfAbsent = {}", setIfAbsent);
                if (setIfAbsent == null || !setIfAbsent) {
                    responseInventoryInfo.setPreemptStatus("1");

                    throw new IOTException(iotAnswer, "11", "第三方库存，该笔订单已完成预占，请勿重复发起");
                }
                responseInventoryInfo.setPreemptStatus("0");

            } else {
                log.info("reserveInventory, 三房接口预占库存 失败");
                responseInventoryInfo.setPreemptStatus("1");
                throw new IOTException(iotAnswer, "第三方预占失败");
            }
            // }
        }

        // 预占完成后存入redis中缓存起来。 hashKey 为 inventory:bookId value
        // {"skuOfferingCode":"","quantity":xxx}
        // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
        Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(
                REDIS_RESERVE_INVENTORY_PREFIX + inventoryInfo.getBookId(), JSON.toJSONString(inventoryInfo));
        if (setIfAbsent == null || !setIfAbsent) {
            responseInventoryInfo.setPreemptStatus("1");

            throw new IOTException(iotAnswer, "11", "该笔订单已完成预占，请勿重复发起");
        }
        responseInventoryInfo.setPreemptStatus("0");
        log.info("代销类getReserveInventoryResponse结果:{}", JSON.toJSONString(responseInventoryInfo));
    }

    /**
     * 获得锁后内层使用事务，可以避免并发情况下预占过多的库存,并且减小事务隔离的范围，避免事务互相隔离导致更新的判断条件失效 运营统管
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void getReserveInventoryResponse(IOTRequest baseRequest, IOTAnswer<ReserveInventoryResponse> iotAnswer,
                                            ReserveInventoryRequest.InventoryInfo inventoryInfo,
                                            ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo,
                                            ReserveInventoryResponse.InventoryInfo responseInventoryInfo,
                                            List<Order2cAtomInfo> order2cAtomInfos) {
        String bookId = inventoryInfo.getBookId();
        for (Order2cAtomInfo x : order2cAtomInfos) {
            log.info("reserveInventory, {}", JSON.toJSONString(x));
            String spuOfferingCode = x.getSpuOfferingCode();
            String skuOfferingCode = x.getSkuOfferingCode();
            String atomOfferingCode = x.getAtomOfferingCode();
            List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper
                    .selectByExample(new AtomOfferingInfoExample().createCriteria()
                            .andSpuCodeEqualTo(spuOfferingCode).andSkuCodeEqualTo(skuOfferingCode)
                            .andOfferingCodeEqualTo(atomOfferingCode).example());
            if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                String msg = String.format("%s-%s-%s，未查询到服务类商品，预占失败,bookId:%s",
                        x.getSpuOfferingCode(), x.getSkuOfferingCode(), x.getAtomOfferingCode(), bookId);
                log.error(msg);
                responseInventoryInfo.setPreemptStatus("1");
                throw new IOTException(iotAnswer, msg);
            }
            AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);

            // 这里对每个库存进行修改, 如果是拍下减库存按照原来逻辑，如果是付款减库存 就不进行判断（库存是否大于预占）
            String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
            // long reserveNum = atomOfferingInfo.getQuantity() *
            // skuOfferingInfo.getQuantity();
            long reserveNum = x.getAtomQuantity() * skuOfferingInfo.getQuantity();
            if (StringUtils.isNotEmpty(nowInventoryPattern)) {
                if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                    // 校验预占数和当前库存数
                    long inventory = atomOfferingInfo.getInventory();
                    if (reserveNum > inventory) {
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "服务类商品：拍下减库存库模式下库存不足，预占数大于当前库存，预占失败");
                    }
                    int influence = inventoryHandlerMapper.updateInventoryServiceByPrimaryKey(atomOfferingInfo.getId(),
                            reserveNum);
                    // 这里对其进行库存修改如果影响行数为0 则说明修改预占失败，需要回滚，且返回失败
                    if (influence == 0) {
                        log.error("{}服务类商品：拍下减库存模式下库存不足，预占失败", baseRequest.getMessageSeq());
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "服务类商品：拍下减库存库模式下库存不足，预占失败");
                    }
                } else {
                    // 校验预占数和总库存数
                    long inventoryTotal = atomOfferingInfo.getInventory() + atomOfferingInfo.getReserveInventory();
                    if (reserveNum > inventoryTotal) {
                        log.error("{}服务类商品：付款减库存模式下库存不足，预占失败", baseRequest.getMessageSeq());
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "服务类商品：付款减库存模式下库存不足，预占数大于总库存数，预占失败");
                    }
                    int influence = inventoryHandlerMapper.updateInventoryServiceByPrimaryKey(atomOfferingInfo.getId(),
                            reserveNum);
                    // 这里对其进行库存修改如果影响行数为0 则说明修改预占失败，需要回滚，且返回失败
                    if (influence == 0) {
                        log.error("{}付款减库存模式下，预占失败", baseRequest.getMessageSeq());
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "付款减库存模式下，预占失败");
                    }
                }
                // A04 DICT范式产品线条额度预占，额度预占只有下单减库存

                // 根据spuCode获取spu信息
                ReserveInventoryRequest.SpuOfferingInfo curentSpu = new ReserveInventoryRequest.SpuOfferingInfo();
                for (ReserveInventoryRequest.SpuOfferingInfo spuOfferingInfo : inventoryInfo.getSpuOfferingInfo()) {
                    if (spuOfferingInfo.getSpuOfferingCode().equals(spuOfferingInfo.getSpuOfferingCode())) {
                        BeanUtils.copyProperties(spuOfferingInfo, curentSpu);
                    }
                }
                int influence2 = 0;
                List<String> dictProductLines = new ArrayList<>();
                dictProductLines.add("00");
                dictProductLines.add("01");
                List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper
                        .selectByExample(new SpuOfferingInfoExample().createCriteria()
                                .andOfferingCodeEqualTo(curentSpu.getSpuOfferingCode()).example());
                SpuOfferingInfo spuOfferingInfo = spuOfferingInfos.get(0);
                CategoryInfo categoryInfo = categoryInfoMapper.selectByExample(
                                new CategoryInfoExample().createCriteria().andSpuIdEqualTo(spuOfferingInfo.getId()).example())
                        .get(0);
                ReserveInventoryRequest request = JSON.parseObject(baseRequest.getContent(),
                        ReserveInventoryRequest.class);
                if (Objects.equals(categoryInfo.getOfferingClass(), "A04")
                        && dictProductLines.contains(curentSpu.getDictProductLines())
                        && isNumeric(atomOfferingInfo.getSettleservicename())) {
                    // 获取现存额度
                    // 获取额度省份
                    // 获取提单人省代码
                    ContractCityInfoExample contractCityInfoExample = new ContractCityInfoExample();
                    ContractCityInfoExample.Criteria criteria1 = contractCityInfoExample.or();
                    criteria1.andMallCodeEqualTo(request.getRegionId());
                    ContractCityInfoExample.Criteria criteria2 = contractCityInfoExample.or();
                    criteria2.andProvinceMallCodeEqualTo(request.getRegionId());
                    List<ContractCityInfo> contractCityInfos = contractCityInfoMapper
                            .selectByExample(contractCityInfoExample);
                    if (CollectionUtils.isEmpty(contractCityInfos)) {
                        log.info("提单人区域必传，不能为空");
                        throw new IOTException(iotAnswer, "-1", "提单人区域必传，不能为空");
                    }
                    ContractCityInfo contractCityInfo = contractCityInfos.get(0);

                    ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                    servicePackLimitAmountExample.createCriteria()
                            .andServiceCodeEqualTo(atomOfferingInfo.getSettleservicename())
                            .andCompanyIdEqualTo(contractCityInfo.getProvinceMallCode())
                            .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                    List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                            .selectByExample(servicePackLimitAmountExample);
                    if (CollectionUtils.isEmpty(servicePackLimitAmounts)) {
                        log.error("{}DICT类商品:额度不足，预占失败", baseRequest.getMessageSeq());
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败");
                    }
                    ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts.get(0);
                    Date now = new Date();
                    String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                    Date currentDateTime = null;
                    try {
                        currentDateTime = dateFormat.parse(yearMonthDay);
                        Date expTime = dateFormat.parse(servicePackLimitAmount.getExptime());
                        if (currentDateTime.after(expTime)) {

                            log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                            responseInventoryInfo.setPreemptStatus("1");
                            // 冻结额度
                            executorService.execute(() -> expireLimit(servicePackLimitAmount));
                            throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败");

                        }
                    } catch (ParseException e) {
                        throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败");
                    }
                    // 获取limit额度
                    ReserveInventoryRequest.AtomOfferingInfo selectedAtomOfferingInfo = null;
                    for (ReserveInventoryRequest.AtomOfferingInfo atomItem : skuOfferingInfo.getAtomOfferingInfo()) {
                        if (Objects.equals(atomItem.getOfferingCode(), atomOfferingInfo.getOfferingCode())) {
                            selectedAtomOfferingInfo = atomItem;
                        }
                    }
                    if (selectedAtomOfferingInfo == null) {
                        log.error("{}DICT类商品:额度不足，预占失败,原子编码不匹配", baseRequest.getMessageSeq());
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败,原子编码不匹配");
                    }
                    if (servicePackLimitAmounts.get(0).getCurrentInventory() < Double
                            .valueOf(selectedAtomOfferingInfo.getLimit()).longValue()) {
                        log.error("{}DICT类商品:额度不足，预占失败", baseRequest.getMessageSeq());
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败");
                    }

                    influence2 = inventoryHandlerMapper.updateDICTLimitByPrimaryKey(servicePackLimitAmount.getId(),
                            Double.valueOf(selectedAtomOfferingInfo.getLimit()));
                    if (influence2 == 0) {
                        log.error("{}DICT类商品:额度不足，预占失败", baseRequest.getMessageSeq());
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败");
                    }
                    DictLimitRedisDTO dictLimitRedisDTO = new DictLimitRedisDTO();
                    dictLimitRedisDTO.setRegionId(contractCityInfo.getProvinceMallCode());
                    dictLimitRedisDTO.setLimit(Double.valueOf(selectedAtomOfferingInfo.getLimit()));
                    Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(REDIS_LIMIT_COMMIT_PERSON_PREFIX
                                    + inventoryInfo.getBookId() + "_" + atomOfferingInfo.getId(),
                            JSON.toJSONString(dictLimitRedisDTO), 365, TimeUnit.DAYS);
                    if (setIfAbsent == null || !setIfAbsent) {
                        responseInventoryInfo.setPreemptStatus("1");
                        throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                    }
                }
            } else {
                throw new IOTException(iotAnswer, "库存切换模式未配置，预占失败");
            }
        }
        // 这里对库存剩余进行判断是否发送短信
        responseInventoryInfo.setPreemptStatus("0");
        log.info("服务类商品类getReserveInventoryResponse结果:{}", JSON.toJSONString(responseInventoryInfo));
    }

    /**
     * 判断字符串是否为纯数字（排除正负号）
     *
     * @param str 输入字符串
     * @return true-是纯数字，false-不是纯数字
     */
    private boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c != '-' && (c < '0' || c > '9')) { // 添加了对负号的检查
                return false;
            }
        }
        return true;
    }

    ;

    /**
     * 获得锁后内层使用事务，可以避免并发情况下预占过多的库存,并且减小事务隔离的范围，避免事务互相隔离导致更新的判断条件失效 非运营统管
     *
     * @param baseRequest
     * @param iotAnswer
     * @param inventoryInfo
     * @param skuOfferingInfo
     * @param responseInventoryInfo
     * @param order2cAtomInfos
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void getNoReserveInventoryResponse(IOTRequest baseRequest, IOTAnswer<ReserveInventoryResponse> iotAnswer,
                                              ReserveInventoryRequest.InventoryInfo inventoryInfo,
                                              ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo,
                                              ReserveInventoryResponse.InventoryInfo responseInventoryInfo,
                                              List<Order2cAtomInfo> order2cAtomInfos) {
        String bookId = inventoryInfo.getBookId();
        for (Order2cAtomInfo x : order2cAtomInfos) {
            log.info("reserveInventory, {}", JSON.toJSONString(x));
            String spuOfferingCode = x.getSpuOfferingCode();
            String skuOfferingCode = x.getSkuOfferingCode();
            String atomOfferingCode = x.getAtomOfferingCode();
            SpuOfferingInfo spuOfferingInfo = spuOfferingInfoMapper
                    .selectByExample(new SpuOfferingInfoExample().createCriteria()
                            .andOfferingCodeEqualTo(spuOfferingCode).example())
                    .get(0);
            List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper
                    .selectByExample(new AtomOfferingInfoExample().createCriteria()
                            .andSpuCodeEqualTo(spuOfferingCode).andSkuCodeEqualTo(skuOfferingCode)
                            .andOfferingCodeEqualTo(atomOfferingCode).example());
            if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                String msg = String.format("%s-%s-%s，未查询到服务类商品，预占失败,bookId:%s",
                        x.getSpuOfferingCode(), x.getSkuOfferingCode(), x.getAtomOfferingCode(), bookId);
                log.error(msg);
                responseInventoryInfo.setPreemptStatus("1");
                throw new IOTException(iotAnswer, msg);
            }
            long reserveNum = x.getAtomQuantity() * skuOfferingInfo.getQuantity();
            AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
            // 非运 不做校验 预占数和当前库存数
            int influence = inventoryHandlerMapper.updateNoInventoryByPrimaryKey(atomOfferingInfo.getId(), reserveNum);
            // 这里对其进行库存修改如果影响行数为0 则说明修改预占失败，需要回滚，且返回失败，一般不会失败。 虽然是非运但是为了后续操作，要预占数入库判断校验一下
            if (influence == 0) {
                log.error("{}服务类商品：非运营统筹预占数入库，预占失败", baseRequest.getMessageSeq());
                responseInventoryInfo.setPreemptStatus("1");
                throw new IOTException(iotAnswer, "服务类商品：非运营统筹预占数入库，预占失败");
            }
            // A04 DICT范式产品线条额度预占
            String spuOfferingClass = inventoryInfo.getSpuOfferingInfo().get(0).getOfferingClass();

            // 根据spuCode获取spu信息

            int influence2 = 0;
            List<String> dictProductLines = new ArrayList<>();
            dictProductLines.add("00");
            dictProductLines.add("01");
            ReserveInventoryRequest request = JSON.parseObject(baseRequest.getContent(), ReserveInventoryRequest.class);
            if (Objects.equals(spuOfferingClass, "A04")
                    && dictProductLines.contains(spuOfferingInfo.getDictProductlines())
                    && isNumeric(atomOfferingInfo.getSettleservicename())) {
                // 获取现存额度
                // 获取提单人省代码
                ContractCityInfoExample contractCityInfoExample = new ContractCityInfoExample();
                ContractCityInfoExample.Criteria criteria1 = contractCityInfoExample.or();
                criteria1.andMallCodeEqualTo(request.getRegionId());
                ContractCityInfoExample.Criteria criteria2 = contractCityInfoExample.or();
                criteria2.andProvinceMallCodeEqualTo(request.getRegionId());
                List<ContractCityInfo> contractCityInfos = contractCityInfoMapper
                        .selectByExample(contractCityInfoExample);
                if (CollectionUtils.isEmpty(contractCityInfos)) {
                    log.info("提单人区域必传，不能为空");
                    throw new IOTException(iotAnswer, "-1", "提单人区域必传，不能为空");
                }
                ContractCityInfo contractCityInfo = contractCityInfos.get(0);
                // 获取limit额度
                ReserveInventoryRequest.AtomOfferingInfo selectedAtomOfferingInfo = null;
                for (ReserveInventoryRequest.AtomOfferingInfo atomItem : skuOfferingInfo.getAtomOfferingInfo()) {
                    if (Objects.equals(atomItem.getOfferingCode(), atomOfferingInfo.getOfferingCode())) {
                        selectedAtomOfferingInfo = atomItem;
                    }
                }
                if (selectedAtomOfferingInfo == null) {
                    log.error("{}DICT类商品:额度不足，预占失败,原子编码不匹配", baseRequest.getMessageSeq());
                    responseInventoryInfo.setPreemptStatus("1");
                    throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败,原子编码不匹配");
                }
                ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
                servicePackLimitAmountExample.createCriteria()
                        .andServiceCodeEqualTo(atomOfferingInfo.getSettleservicename())
                        .andCompanyIdEqualTo(contractCityInfo.getProvinceMallCode())
                        .andStatusNotEqualTo(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
                List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                        .selectByExample(servicePackLimitAmountExample);
                if (CollectionUtils.isEmpty(servicePackLimitAmounts) || servicePackLimitAmounts.get(0)
                        .getCurrentInventory() < Double.valueOf(selectedAtomOfferingInfo.getLimit()).longValue()) {
                    log.error("{}DICT类商品:非运营统筹模式下额度不足，预占失败", baseRequest.getMessageSeq());
                    responseInventoryInfo.setPreemptStatus("1");
                    throw new IOTException(iotAnswer, "DICT类商品:非运营统筹模式下额度不足，预占失败");
                }
                ServicePackLimitAmount servicePackLimitAmount = servicePackLimitAmounts.get(0);
                // 冻结额度
                Date now = new Date();
                String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
                Date currentDateTime = null;
                try {
                    currentDateTime = dateFormat.parse(yearMonthDay);
                    Date expTime = dateFormat.parse(servicePackLimitAmount.getExptime());
                    if (currentDateTime.after(expTime)) {

                        log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                        responseInventoryInfo.setPreemptStatus("1");
                        // 冻结额度
                        executorService.execute(() -> expireLimit(servicePackLimitAmount));
                        throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败");

                    }
                } catch (ParseException e) {
                    throw new IOTException(iotAnswer, "DICT类商品:额度不足，预占失败");
                }
                influence2 = inventoryHandlerMapper.updateDICTLimitByPrimaryKey(servicePackLimitAmount.getId(),
                        Double.valueOf(selectedAtomOfferingInfo.getLimit()));
                if (influence2 == 0) {
                    log.error("{}DICT类商品:非运营统筹模式下额度不足，预占失败", baseRequest.getMessageSeq());
                    responseInventoryInfo.setPreemptStatus("1");
                    throw new IOTException(iotAnswer, "DICT类商品:非运营统筹模式下额度不足，预占失败");
                }
                DictLimitRedisDTO dictLimitRedisDTO = new DictLimitRedisDTO();
                dictLimitRedisDTO.setRegionId(contractCityInfo.getProvinceMallCode());
                dictLimitRedisDTO.setLimit(Double.valueOf(selectedAtomOfferingInfo.getLimit()));
                Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(REDIS_LIMIT_COMMIT_PERSON_PREFIX
                                + inventoryInfo.getBookId() + "_" + atomOfferingInfo.getId(),
                        JSON.toJSONString(dictLimitRedisDTO),365, TimeUnit.DAYS);
                if (setIfAbsent == null || !setIfAbsent) {
                    responseInventoryInfo.setPreemptStatus("1");
                    throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                }
            }

        }

        // 这里对库存剩余进行判断是否发送短信
        // responseInventoryInfo.setPreemptStatus("0");
        log.info("服务类商品类getReserveInventoryResponse结果:{}", JSON.toJSONString(responseInventoryInfo));
    }

    /**
     * K+X 获得锁后内层使用事务，可以避免并发情况下预占过多的库存,并且减小事务隔离的范围，避免事务互相隔离导致更新的判断条件失效
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void getReserveInventoryKXResponse(IOTRequest baseRequest, IOTAnswer<ReserveInventoryResponse> iotAnswer,
                                              ReserveInventoryRequest.InventoryInfo inventoryInfo,
                                              ReserveInventoryResponse.InventoryInfo responseInventoryInfo,
                                              Long skuQuantity,
                                              String skuOfferingCode) {
        ReserveInventoryRequest request = JSON.parseObject(baseRequest.getContent(), ReserveInventoryRequest.class);

        // 保存提单人区域信息到redis
        stringRedisTemplate.opsForValue().setIfAbsent(REDIS_COMMIT_PERSON_PREFIX + inventoryInfo.getBookId(),
                request.getRegionId());

        // 查询sku发布省份信息 一个sku目前只对应一个省份
        List<CityInfoDTO> cityInfoList = queryAtomIssueProvinceAndCity(skuOfferingCode);
        CityInfoDTO cityInfoDTO = cityInfoList.get(0);
        String provinceName = cityInfoDTO.getProvince();
        List<String> cityName = cityInfoDTO.getCity();
        String provinceCode = provinceCityConfig.getProvinceNameCodeMap().get(provinceName);
        List<String> cityCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cityName)) {
            cityName.forEach(x -> {
                if (!"-1".equals(x)) {
                    String cityCode = provinceCityConfig.getcityCodeNameMap().get(x);
                    cityCodeList.add(cityCode);
                }
            });
        }
        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
        // 这里只查询硬件的，软件不考虑 offeringClass 为 X
        atomOfferingInfoExample.createCriteria()
                .andSkuCodeEqualTo(skuOfferingCode)
                .andOfferingClassEqualTo("X")
                .andDeleteTimeIsNull();

        List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
        // 提单人
        String bookRegionId = request.getRegionId();
        if (bookRegionId.length() != 3 && bookRegionId.length() != 4) {
            throw new IOTException(iotAnswer, "提单人区域信息错误bookRegionId：{}", bookRegionId);
        }
        int bookRegionType = bookRegionId.length() == 3 ? BOOK_RPOVINCE : BOOK_CITY;
        // 判断提单人区域类型 和sku发布省市 如果提单人区域是省，sku发布不实全省，这种是不能下单预占的
       /* if (bookRegionType == BOOK_RPOVINCE && CollectionUtils.isNotEmpty(cityCodeList)) {
            throw new IOTException(iotAnswer, "提单人区域是省份，sku发布省不是全省无法预占，预占失败");
        }*/
        List<ReserveInventoryRequest.SpuOfferingInfo> spuOfferingInfo = inventoryInfo.getSpuOfferingInfo();
        ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = spuOfferingInfo.get(0).getSkuOfferingInfo().get(0);
        List<ReserveInventoryRequest.AtomOfferingInfo> atomOfferingInfoList = skuOfferingInfo.getAtomOfferingInfo();
        List<String> atomCodeList = atomOfferingInfoList.stream()
                .map(ReserveInventoryRequest.AtomOfferingInfo::getOfferingCode).collect(Collectors.toList());
        // 通过sku查询模板名称，及服务编码确定码号库存
        Optional<SkuOfferingInfo> skuOfferingInfoOptional = skuOfferingInfoMapper
                .selectByExample(
                        new SkuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(skuOfferingCode).example())
                .stream().findFirst();
        if (!skuOfferingInfoOptional.isPresent()) {
            throw new IOTException(iotAnswer, "规格商品不存在:skuOfferingCode{}", skuOfferingCode);
        }
        SkuOfferingInfo skuOfferingInfoEn = skuOfferingInfoOptional.get();
        // TODO 后续要割接开卡模板编码 以编码确定唯一性查询 判断码号库存够不够
        String custCode = skuOfferingInfoEn.getCustCode();
        String templateName = skuOfferingInfoEn.getTemplateName();
        String templateId = skuOfferingInfoEn.getTemplateId();
        Optional<CardInventoryMainInfo> inventoryMainInfoOptional = cardInventoryMainInfoMapper
                .selectByExample(new CardInventoryMainInfoExample().createCriteria().andCustCodeEqualTo(custCode)
                        .andTemplateIdEqualTo(templateId).example())
                .stream().findFirst();
        if (!inventoryMainInfoOptional.isPresent()) {
            throw new IOTException(iotAnswer, "码号库存不存在:custCode{}，templateId：{}", custCode + "_" + templateId);
        }
        long totalCardReserve = 0L;
        CardInventoryMainInfo cardInventoryMainInfo = inventoryMainInfoOptional.get();
        List<CardInventoryInfoDTO> cardInventoryInfoDTOS = new ArrayList<>();
        // 筛选出多原子商品预占数（不含卡的筛选掉）
        for (int i = 0; i < atomOfferingInfos.size(); i++) {
            String inventoryMainId = atomOfferingInfos.get(i).getInventoryMainId();
            String offeringCode = atomOfferingInfos.get(i).getOfferingCode();
            if (StringUtils.isEmpty(inventoryMainId)) {
                throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", atomOfferingInfos.get(i).getId());
            }
            DkcardxInventoryMainInfo dkcardxInventoryMainInfo = dkcardxInventoryMainInfoMapper
                    .selectByPrimaryKey(inventoryMainId);
            String terminalType = dkcardxInventoryMainInfo.getTerminalType();
            if (!NO_INCLUDED_CARD.getType().equals(terminalType)) {
                ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfoReserve = atomOfferingInfoList.stream()
                        .filter(atom -> atom.getOfferingCode().equals(offeringCode)).findFirst().get();
                long reserveNum = atomOfferingInfoReserve.getQuantity() * skuQuantity;
                totalCardReserve = totalCardReserve + reserveNum;
                CardInventoryInfoDTO cardInventoryInfoDTO = new CardInventoryInfoDTO();
                cardInventoryInfoDTO.setId(cardInventoryMainInfo.getId());
                cardInventoryInfoDTO.setCustCode(custCode);
                cardInventoryInfoDTO.setAtomId(atomOfferingInfos.get(i).getId());
                cardInventoryInfoDTO.setTemplateId(templateId);
                cardInventoryInfoDTO.setReserveQuatity(Integer.valueOf(String.valueOf(reserveNum)));
                cardInventoryInfoDTOS.add(cardInventoryInfoDTO);
                // 存储码号预占信息，关联原子商品信息与码号库存信息。并分支redis缓存预占
                handlerCardInventoryAtomInfo(inventoryMainInfoOptional, atomOfferingInfos.get(i), reserveNum);

            }
        }
        // 码号库存redis缓存信息 码号库存更新
        Integer reserveSum = Integer.valueOf(String.valueOf(totalCardReserve));
        Integer currentInventoryCard = cardInventoryMainInfo.getCurrentInventory();
        if (currentInventoryCard - reserveSum < 0) {
            throw new IOTException(iotAnswer, "当前码号库存库存数不足:cardInventoryMainInfo{}", cardInventoryMainInfo.getId());
        }
        CardInventoryMainInfo cardInventoryMainInfoEn = new CardInventoryMainInfo();
        cardInventoryMainInfoEn.setId(cardInventoryMainInfo.getId());
        cardInventoryMainInfoEn.setCurrentInventory(cardInventoryMainInfo.getCurrentInventory() - reserveSum);
        cardInventoryMainInfoEn.setReserveQuatity(cardInventoryMainInfo.getReserveQuatity() + reserveSum);
        cardInventoryMainInfoEn.setUpdateTime(new Date());
        cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);

        for (AtomOfferingInfo x : atomOfferingInfos) {
            // 这里对每个库存进行修改, 如果是拍下减库存按照原来逻辑，如果是付款减库存 就不进行判断（库存是否大于预占）
            String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
            log.info("test reserve cardx pattern = {}", nowInventoryPattern);
            String offeringCode = x.getOfferingCode();
            if (StringUtils.isNotEmpty(nowInventoryPattern)) {
                if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                    // 拍下减库存模式
                    if (bookRegionType == BOOK_RPOVINCE) {
                        // sku发布全省的时候都是预占省级库存，不用判断提单人的区域类型 直接扣减省级预占库存数（新需求改成判断提单人区域）
                        String inventoryMainId = x.getInventoryMainId();
                        if (StringUtils.isEmpty(inventoryMainId)) {
                            throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", x.getId());
                        }
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                        .andLocationIsNull().example());
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
                        Integer currentInventory = dkcardxInventoryDetailInfo.getCurrentInventory();
                        if (!atomCodeList.contains(offeringCode)) {
                            throw new IOTException(iotAnswer, "预占的原子商品编码信息未查询到，预占失败");
                        }
                        // 要预占的原子信息
                        ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.stream()
                                .filter(atom -> atom.getOfferingCode().equals(offeringCode)).findFirst().get();
                        long reserveNum = atomOfferingInfo.getQuantity() * skuQuantity;
                        if (reserveNum > currentInventory) {
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "拍下减库存库模式下K+x库存不足，预占数大于当前库存，预占失败");
                        }
                        int influence = inventoryHandlerMapper.reserveCardXInventoryByInventoryMainId(inventoryMainId,
                                x.getId(), dkcardxInventoryDetailInfo.getBeId(), "", reserveNum);
                        if (influence == 0) {
                            log.info("cardx update inventory：{}拍下减库存模式下库存不足，预占失败", baseRequest.getMessageSeq());
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "卡+X 拍下减库存库模式下存不足，预占失败");
                        }
                        // 存X终端库存原子详情表信息
                        capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfo, inventoryMainId, x,
                                inventoryInfo, reserveNum);
                        // 操作完成，保存预占信息到redis,库存释放，扣减 订单交付时使用
                        // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                        List<InventoryAreaDTO> inventoryAreaList = new ArrayList<>();
                        InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                        inventoryAreaDTO.setAreaCode(dkcardxInventoryDetailInfo.getBeId());
                        inventoryAreaDTO.setReserveQuantity(reserveNum);
                        inventoryAreaList.add(inventoryAreaDTO);
                        Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(REDIS_COMMIT_INVENTORY_AREA
                                + inventoryInfo.getBookId() + "_" + x.getId(), JSON.toJSONString(inventoryAreaList));
                        if (setIfAbsent == null || !setIfAbsent) {
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                        }
                    } else {
                        // 不是全省,
                        String inventoryMainId = x.getInventoryMainId();
                        if (StringUtils.isEmpty(inventoryMainId)) {
                            throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", x.getId());
                        }
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosProvince = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                        .andLocationIsNull().example());
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosCity = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNull()
                                        .andLocationEqualTo(bookRegionId).example());
                        Integer currentInventoryProvince = 0;
                        Integer currentInventoryCity = 0;

                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoProvince = new DkcardxInventoryDetailInfo();
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoCity = new DkcardxInventoryDetailInfo();
                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosProvince)) {
                            dkcardxInventoryDetailInfoProvince = dkcardxInventoryDetailInfosProvince.get(0);
                            currentInventoryProvince = dkcardxInventoryDetailInfoProvince.getCurrentInventory();
                        }
                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosCity)) {
                            dkcardxInventoryDetailInfoCity = dkcardxInventoryDetailInfosCity.get(0);
                            currentInventoryCity = dkcardxInventoryDetailInfoCity.getCurrentInventory();
                        }
                        if (!atomCodeList.contains(offeringCode)) {
                            throw new IOTException(iotAnswer, "预占的原子商品编码信息为查询到，预占失败");
                        }
                        // 要预占的原子信息
                        ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.stream()
                                .filter(atom -> atom.getOfferingCode().equals(offeringCode)).findFirst().get();
                        long reserveNum = atomOfferingInfo.getQuantity() * skuQuantity;
                        Integer currentInventory = currentInventoryProvince + currentInventoryCity;
                        // 这里判断地市和省级库存是否充足
                        if (reserveNum > currentInventory) {
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "拍下减库存库模式下K+x库存不足，预占数大于当前库存，预占失败");
                        }
                        // 判断地市级库存够不够扣除预占数，够就不用扣除省级的了 ，不够就扣除省级的库存
                        int influenceProvince = 0;
                        int influenceCity = 0;
                        if (reserveNum <= currentInventoryCity) {
                            if (currentInventoryCity != 0) {
                                influenceCity = inventoryHandlerMapper.reserveCardXInventoryByInventoryMainId(
                                        inventoryMainId, x.getId(), "", bookRegionId, reserveNum);
                            }
                            // 已经判断了 一般不会出现这种情况
                            if (influenceCity == 0) {
                                log.info("cardx update inventory：{}拍下减库存模式下库存不足，预占失败", baseRequest.getMessageSeq());
                                responseInventoryInfo.setPreemptStatus("1");
                                throw new IOTException(iotAnswer, "卡+X 拍下减库存库模式下存不足，预占失败");
                            }

                            // 存X终端库存原子详情表信息
                            capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfoCity, inventoryMainId, x,
                                    inventoryInfo, reserveNum);
                            // 操作完成，保存预占信息到redis,库存释放，扣减 订单交付时使用
                            // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                            List<InventoryAreaDTO> inventoryAreaList = new ArrayList<>();
                            InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                            inventoryAreaDTO.setAreaCode(bookRegionId);
                            inventoryAreaDTO.setReserveQuantity(reserveNum);
                            inventoryAreaList.add(inventoryAreaDTO);
                            Boolean setIfAbsent = stringRedisTemplate.opsForValue()
                                    .setIfAbsent(REDIS_COMMIT_INVENTORY_AREA
                                                    + inventoryInfo.getBookId() + "_" + x.getId(),
                                            JSON.toJSONString(inventoryAreaList));
                            if (setIfAbsent == null || !setIfAbsent) {
                                responseInventoryInfo.setPreemptStatus("1");
                                throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                            }
                        } else {
                            // 先扣除地市级库存，不够 扣除省级库存
                            List<InventoryAreaDTO> inventoryAreaList = new ArrayList<>();
                            long residueMinusInventory = 0L;
                            if (currentInventoryCity != 0 && currentInventoryCity > 0) {
                                influenceCity = inventoryHandlerMapper.reserveCardXInventoryByInventoryMainId(
                                        inventoryMainId, x.getId(), "", bookRegionId,
                                        Long.parseLong(String.valueOf(currentInventoryCity)));
                                if (influenceCity == 0) {
                                    log.info("cardx update inventory：{}拍下减库存模式下地市级库存不足，预占失败",
                                            baseRequest.getMessageSeq());
                                    responseInventoryInfo.setPreemptStatus("1");
                                    throw new IOTException(iotAnswer, "卡+X 拍下减库存库模式下地市级库存不足，预占失败");
                                }
                                // 剩余要扣减的库存
                                residueMinusInventory = reserveNum - currentInventoryCity;
                                // 存X终端库存原子详情表信息 地市
                                capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfoCity, inventoryMainId, x,
                                        inventoryInfo, Long.parseLong(String.valueOf(currentInventoryCity)));
                                InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                                inventoryAreaDTO.setAreaCode(bookRegionId);
                                inventoryAreaDTO
                                        .setReserveQuantity(Long.parseLong(String.valueOf(currentInventoryCity)));
                                inventoryAreaList.add(inventoryAreaDTO);
                            } else {
                                residueMinusInventory = reserveNum;
                            }

                            if (currentInventoryProvince != 0) {
                                influenceProvince = inventoryHandlerMapper.reserveCardXInventoryByInventoryMainId(
                                        inventoryMainId, x.getId(), dkcardxInventoryDetailInfoProvince.getBeId(), "",
                                        residueMinusInventory);
                                if (influenceProvince == 0) {
                                    log.info("cardx update inventory：{}拍下减库存模式下省级库存不足，预占失败",
                                            baseRequest.getMessageSeq());
                                    responseInventoryInfo.setPreemptStatus("1");
                                    throw new IOTException(iotAnswer, "卡+X 拍下减库存库模式下省级库存不足，预占失败");
                                }
                                // 存X终端库存原子详情表信息 省
                                capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfoProvince, inventoryMainId,
                                        x, inventoryInfo, residueMinusInventory);
                                InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                                inventoryAreaDTO.setAreaCode(dkcardxInventoryDetailInfoProvince.getBeId());
                                inventoryAreaDTO.setReserveQuantity(residueMinusInventory);
                                inventoryAreaList.add(inventoryAreaDTO);
                            }
                            // 操作完成，保存预占信息到redis,库存释放，扣减 订单交付时使用
                            // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                            Boolean setIfAbsent = stringRedisTemplate.opsForValue()
                                    .setIfAbsent(REDIS_COMMIT_INVENTORY_AREA
                                                    + inventoryInfo.getBookId() + "_" + x.getId(),
                                            JSON.toJSONString(inventoryAreaList));
                            if (setIfAbsent == null || !setIfAbsent) {
                                responseInventoryInfo.setPreemptStatus("1");
                                throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                            }
                        }
                    }
                } else {
                    // 付款减库存模式
                    if (bookRegionType == BOOK_RPOVINCE) {
                        // sku发布全省的时候都是预占省级库存，不用判断提单人的区域类型 直接扣减省级预占库存数（新的判断提单人区域）
                        String inventoryMainId = x.getInventoryMainId();
                        if (StringUtils.isEmpty(inventoryMainId)) {
                            throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", x.getId());
                        }
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                        .andLocationIsNull().example());
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
                        Integer currentInventory = dkcardxInventoryDetailInfo.getCurrentInventory();
                        Integer reserveQuatity = dkcardxInventoryDetailInfo.getReserveQuatity();
                        int totalInventory = currentInventory + reserveQuatity;
                        if (!atomCodeList.contains(offeringCode)) {
                            throw new IOTException(iotAnswer, "预占的原子商品编码信息为查询到，预占失败");
                        }
                        // 要预占的原子信息
                        ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.stream()
                                .filter(atom -> atom.getOfferingCode().equals(offeringCode)).findFirst().get();
                        long reserveNum = atomOfferingInfo.getQuantity() * skuQuantity;
                        if (reserveNum > totalInventory) {
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "付款减库存模式下K+x库存不足，预占数大于总库存库存，预占失败");
                        }
                        int influence = inventoryHandlerMapper.reserveCardXPayInventoryByInventoryMainId(
                                inventoryMainId, x.getId(), dkcardxInventoryDetailInfo.getBeId(), "", reserveNum);
                        if (influence == 0) {
                            log.info("cardx update inventory：{}付款减库存模式库存不足，预占失败", baseRequest.getMessageSeq());
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "卡+X 付款减库存模式下存不足，预占失败");
                        }
                        // 存X终端库存原子详情表信息
                        capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfo, inventoryMainId, x,
                                inventoryInfo, reserveNum);
                        // 操作完成，保存预占信息到redis,库存释放，扣减 订单交付时使用
                        // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                        List<InventoryAreaDTO> inventoryAreaList = new ArrayList<>();
                        InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                        inventoryAreaDTO.setAreaCode(dkcardxInventoryDetailInfo.getBeId());
                        inventoryAreaDTO.setReserveQuantity(reserveNum);
                        inventoryAreaList.add(inventoryAreaDTO);
                        Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(REDIS_COMMIT_INVENTORY_AREA
                                + inventoryInfo.getBookId() + "_" + x.getId(), JSON.toJSONString(inventoryAreaList));
                        if (setIfAbsent == null || !setIfAbsent) {
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                        }
                    } else {
                        // 不是全省,
                        String inventoryMainId = x.getInventoryMainId();
                        if (StringUtils.isEmpty(inventoryMainId)) {
                            throw new IOTException(iotAnswer, "请先配置商品x终端库存:atomId{}", x.getId());
                        }
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosProvince = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull()
                                        .andLocationIsNull().example());
                        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfosCity = dkcardxInventoryDetailInfoMapper
                                .selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                                        .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNull()
                                        .andLocationEqualTo(bookRegionId).example());
                        Integer currentInventoryProvince = 0;
                        Integer currentInventoryCity = 0;
                        Integer reserveQuatityProvince = 0;
                        Integer reserveQuatityCity = 0;
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoProvince = new DkcardxInventoryDetailInfo();
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfoCity = new DkcardxInventoryDetailInfo();
                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosProvince)) {
                            dkcardxInventoryDetailInfoProvince = dkcardxInventoryDetailInfosProvince.get(0);
                            currentInventoryProvince = dkcardxInventoryDetailInfoProvince.getCurrentInventory();
                            reserveQuatityProvince = dkcardxInventoryDetailInfoProvince.getReserveQuatity();
                        }
                        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfosCity)) {
                            dkcardxInventoryDetailInfoCity = dkcardxInventoryDetailInfosCity.get(0);
                            currentInventoryCity = dkcardxInventoryDetailInfoCity.getCurrentInventory();
                            reserveQuatityCity = dkcardxInventoryDetailInfoCity.getReserveQuatity();
                        }
                        if (!atomCodeList.contains(offeringCode)) {
                            throw new IOTException(iotAnswer, "预占的原子商品编码信息为查询到，预占失败");
                        }
                        // 要预占的原子信息
                        ReserveInventoryRequest.AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.stream()
                                .filter(atom -> atom.getOfferingCode().equals(offeringCode)).findFirst().get();
                        long reserveNum = atomOfferingInfo.getQuantity() * skuQuantity;
                        int currentInventory = currentInventoryProvince + currentInventoryCity;
                        int reserveQuatity = reserveQuatityProvince + reserveQuatityCity;

                        // 这里判断地市和省级库存是否充足
                        if (reserveNum > (currentInventory + reserveQuatity)) {
                            responseInventoryInfo.setPreemptStatus("1");
                            throw new IOTException(iotAnswer, "付款减库存模式下K+x库存不足，预占数大于总库存，预占失败");
                        }
                        // 判断地市级库存够不够扣除预占数，够就不用扣除省级的了 ，不够就扣除省级的库存
                        int influenceProvince = 0;
                        int influenceCity = 0;
                        if (reserveNum <= (currentInventoryCity + reserveQuatityCity)) {
                            if ((currentInventoryCity + reserveQuatityCity) != 0) {
                                influenceCity = inventoryHandlerMapper.reserveCardXPayInventoryByInventoryMainId(
                                        inventoryMainId, x.getId(), "", bookRegionId, reserveNum);
                            }
                            // 已经判断了 一般不会出现这种情况
                            if (influenceCity == 0) {
                                log.info("cardx update inventory：{}付款减库存模式下库存不足，预占失败", baseRequest.getMessageSeq());
                                responseInventoryInfo.setPreemptStatus("1");
                                throw new IOTException(iotAnswer, "卡+X 付款减库存模式下存不足，预占失败");
                            }

                            // 存X终端库存原子详情表信息
                            capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfoCity, inventoryMainId, x,
                                    inventoryInfo, reserveNum);
                            // 操作完成，保存预占信息到redis,库存释放，扣减 订单交付时使用
                            // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                            List<InventoryAreaDTO> inventoryAreaList = new ArrayList<>();
                            InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                            inventoryAreaDTO.setAreaCode(bookRegionId);
                            inventoryAreaDTO.setReserveQuantity(reserveNum);
                            inventoryAreaList.add(inventoryAreaDTO);
                            Boolean setIfAbsent = stringRedisTemplate.opsForValue()
                                    .setIfAbsent(REDIS_COMMIT_INVENTORY_AREA
                                                    + inventoryInfo.getBookId() + "_" + x.getId(),
                                            JSON.toJSONString(inventoryAreaList));
                            if (setIfAbsent == null || !setIfAbsent) {
                                responseInventoryInfo.setPreemptStatus("1");
                                throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                            }
                        } else {
                            // 先扣除地市级库存，不够 扣除省级库存 前面已经判断预占数和总库存数的校验
                            long residueMinusInventory = 0L;
                            List<InventoryAreaDTO> inventoryAreaList = new ArrayList<>();
                            if ((currentInventoryCity + reserveQuatityCity) != 0
                                    && (currentInventoryCity + reserveQuatityCity) > 0) {
                                influenceCity = inventoryHandlerMapper.reserveCardXPayInventoryByInventoryMainId(
                                        inventoryMainId, x.getId(),
                                        "", bookRegionId,
                                        Long.parseLong(String.valueOf((currentInventoryCity + reserveQuatityCity))));
                                if (influenceCity == 0) {
                                    log.info("cardx update inventory：{}付款减库存模式下地市级库存不足，预占失败",
                                            baseRequest.getMessageSeq());
                                    responseInventoryInfo.setPreemptStatus("1");
                                    throw new IOTException(iotAnswer, "卡+X 付款减库存模式下地市级库存不足，预占失败");
                                }
                                // 剩余要扣减的库存
                                residueMinusInventory = reserveNum - (currentInventoryCity + reserveQuatityCity);
                                // 存X终端库存原子详情表信息 地市
                                capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfoCity, inventoryMainId, x,
                                        inventoryInfo,
                                        Long.parseLong(String.valueOf((currentInventoryCity + reserveQuatityCity))));
                                InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                                inventoryAreaDTO.setAreaCode(bookRegionId);
                                inventoryAreaDTO.setReserveQuantity(
                                        Long.parseLong(String.valueOf((currentInventoryCity + reserveQuatityCity))));
                                inventoryAreaList.add(inventoryAreaDTO);

                            } else {
                                residueMinusInventory = reserveNum;
                            }
                            if ((currentInventoryProvince + reserveQuatityProvince) != 0) {
                                influenceProvince = inventoryHandlerMapper.reserveCardXPayInventoryByInventoryMainId(
                                        inventoryMainId, x.getId(), dkcardxInventoryDetailInfoProvince.getBeId(), "",
                                        residueMinusInventory);
                                if (influenceProvince == 0) {
                                    log.info("cardx update inventory：{}付款减库存模式下省级库存不足，预占失败",
                                            baseRequest.getMessageSeq());
                                    responseInventoryInfo.setPreemptStatus("1");
                                    throw new IOTException(iotAnswer, "卡+X 付款减库存模式下省级库存不足，预占失败");
                                }
                                // 存X终端库存原子详情表信息 省
                                capsulationDkcardxInventoryAtomInfo(dkcardxInventoryDetailInfoProvince, inventoryMainId,
                                        x, inventoryInfo, residueMinusInventory);
                                InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
                                inventoryAreaDTO.setAreaCode(dkcardxInventoryDetailInfoProvince.getBeId());
                                inventoryAreaDTO.setReserveQuantity(residueMinusInventory);
                                inventoryAreaList.add(inventoryAreaDTO);
                            }
                            // 操作完成，保存预占信息到redis,库存释放，扣减 订单交付时使用
                            // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
                            Boolean setIfAbsent = stringRedisTemplate.opsForValue()
                                    .setIfAbsent(REDIS_COMMIT_INVENTORY_AREA
                                                    + inventoryInfo.getBookId() + "_" + x.getId(),
                                            JSON.toJSONString(inventoryAreaList));
                            if (setIfAbsent == null || !setIfAbsent) {
                                responseInventoryInfo.setPreemptStatus("1");
                                throw new IOTException(iotAnswer, "11", "该笔订单原子商品已完成预占，请勿重复发起");
                            }
                        }
                    }
                }
            } else {
                throw new IOTException(iotAnswer, "库存切换模式未配置，预占失败");
            }
        }
        // 终端库存预占完成，在执行码号库存预占
        stringRedisTemplate.opsForValue().set(
                REDIS_CARD_INVENTORY_CONSTANT + inventoryInfo.getBookId() + "_" + skuOfferingCode,
                JSON.toJSONString(cardInventoryInfoDTOS));

        // 预占完成后存入redis中缓存起来。 hashKey 为 inventory:bookId value
        // {"skuOfferingCode":"","quantity":xxx}
        // 这里存在同时执行上面操作 这个时候谁插入了redis记录，则保留，另外一条抛出异常执行回滚操作
        Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(
                REDIS_RESERVE_INVENTORY_PREFIX + inventoryInfo.getBookId(), JSON.toJSONString(inventoryInfo));
        if (setIfAbsent == null || !setIfAbsent) {
            responseInventoryInfo.setPreemptStatus("1");

            throw new IOTException(iotAnswer, "11", "该笔订单已完成预占，请勿重复发起");
        }
        responseInventoryInfo.setPreemptStatus("0");
        log.info("卡+X预占 getReserveInventoryResponse结果:{}", JSON.toJSONString(responseInventoryInfo));

    }

    /**
     * 处理一个sku里有多个硬件产品的情况，取最小值
     *
     * @return
     */
    private long getThirdMinCount(List<Qry3rdInventoryResp.InventoryRet> countList) {
        long minCount = -1;
        for (Qry3rdInventoryResp.InventoryRet item : countList) {
            // String materialNum = item.getMaterialNum();
            long quatity = item.getQuatity();
            // 暂时取第一个
            minCount = quatity;
        }
        return minCount;
    }

    private boolean isContractOrder() {

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> setInventoryType(InventoryTypeRequest param) {
        List<SpuOfferingInfo> localSpuInfos = spuOfferingInfoMapper.selectByExample(
                new SpuOfferingInfoExample()
                        .createCriteria()
                        .andOfferingCodeEqualTo(param.getSpuCode())
                        .andDeleteTimeIsNull()
                        .example());
        SpuOfferingInfo localSpuInfo;
        if (CollectionUtil.isEmpty(localSpuInfos)) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "spu不存在");
        } else {
            localSpuInfo = localSpuInfos.get(0);
            List<CategoryInfo> categoryInfos = categoryInfoMapper.selectByExample(
                    new CategoryInfoExample().createCriteria()
                            .andSpuIdEqualTo(localSpuInfo.getId())
                            .example());
            if (CollectionUtils.isNotEmpty(categoryInfos)) {
                CategoryInfo categoryInfo = categoryInfos.get(0);
                String spuOfferingClass = categoryInfo.getOfferingClass();
                if (SPUOfferingClassEnum.A04.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A08.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A09.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A12.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A14.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A15.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A16.getSpuOfferingClass().equals(spuOfferingClass) ||
                        SPUOfferingClassEnum.A17.getSpuOfferingClass().equals(spuOfferingClass)) {
                    if (StringUtils.isEmpty(localSpuInfo.getInventoryType())) {
                        {
                            String content = "【配置库存模式】\n商品组/销售组编码"
                                    .concat(param.getSpuCode()).concat("\n")
                                    .concat("库存模式配置为")
                                    .concat(param.getType().equals("0") ? "运营统管" : "非运营统管");
                            // 记录日志
                            logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                                    GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                                    content, LogResultEnum.LOG_SUCESS.code, null);
                        }
                    } else if (!localSpuInfo.getInventoryType().equals(param.getType())) {
                        String content = "【重新配置库存模式】\n商品组/销售组编码"
                                .concat(param.getSpuCode()).concat("\n")
                                .concat("库存模式由")
                                .concat(localSpuInfo.getInventoryType().equals("0") ? "运营统管" : "非运营统管")
                                .concat("改为")
                                .concat(param.getType().equals("0") ? "运营统管" : "非运营统管");
                        // 记录日志
                        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                                GoodsManageOperateEnum.INVENTORY_CONFIG.code,
                                content, LogResultEnum.LOG_SUCESS.code, null);
                    }
                } else {
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "库存模式与原有模式相同");
                }
                // 如果是非运管切到运管，拍下库存模式当前库存设置成0.付款减库存模式下 当前库存设置成负数的预占数
                if (StringUtils.isNotEmpty(localSpuInfo.getInventoryType())
                        && localSpuInfo.getInventoryType().equals("1") && param.getType().equals("0")) {
                    AtomOfferingInfo offeringInfo = new AtomOfferingInfo();
                    String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
                    log.info("当前模式:{}", nowInventoryPattern);
                    if (StringUtils.isNotEmpty(nowInventoryPattern)) {
                        if (INVENTORY_PATTERN_DOWN.equals(nowInventoryPattern)) {
                            // 拍下
                            offeringInfo.setInventory(0L);
                            atomOfferingInfoMapper.updateByExampleSelective(offeringInfo,
                                    new AtomOfferingInfoExample().createCriteria()
                                            .andSpuCodeEqualTo(localSpuInfo.getOfferingCode())
                                            .andDeleteTimeIsNull()
                                            .example());
                        } else if (INVENTORY_PATTERN_PAYMENT.equals(nowInventoryPattern)) {
                            // 付款
                            inventoryHandlerMapper.setInventoryPaymentBySpu(localSpuInfo.getOfferingCode());
                        }
                    }
                }
                // 如果是运营统筹切换到非运营统筹，库存预警值变为0
                if (StringUtils.isNotEmpty(localSpuInfo.getInventoryType())
                        && localSpuInfo.getInventoryType().equals("0") && param.getType().equals("1")) {
                    AtomOfferingInfo atomOfferingInfo = new AtomOfferingInfo();
                    atomOfferingInfo.setInventoryThreshold(0L);
                    atomOfferingInfoMapper.updateByExampleSelective(atomOfferingInfo,
                            new AtomOfferingInfoExample().createCriteria()
                                    .andSpuCodeEqualTo(param.getSpuCode())
                                    .example());
                }

                localSpuInfo.setInventoryType(param.getType());
                spuOfferingInfoMapper.updateByExampleSelective(localSpuInfo,
                        new SpuOfferingInfoExample().createCriteria()
                                .andOfferingCodeEqualTo(param.getSpuCode())
                                .andDeleteTimeIsNull()
                                .example());

            } else {
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "spu非服务类商品");
            }
        }

        log.info("spu配置库存模式,spuCode:{},type:{}", param.getSpuCode(), param.getType());
        return BaseAnswer.success(null);
    }

    /**
     * @param spuOfferingName
     * @param skuOfferingName
     * @param atomOfferingName
     * @param spuOfferingClass
     * @param partnerName
     * @param cooperatorName   原子商品名
     * @param inventoryStatus  0 库存充足 1 库存不足
     * @param page
     * @param num
     * @param loginIfo4Redis
     * @return
     */
    // 数据权限-库存配置
    @Override
    public BaseAnswer<PageData<InventoryInfoDTO>> getInventoryNewList(String spuOfferingName, String skuOfferingName,
                                                                      String spuOfferingCode, String skuOfferingCode,
                                                                      String atomOfferingName, List<String> spuOfferingClass,
                                                                      String partnerName, String cooperatorName,
                                                                      Integer inventoryStatus, List<String> spuOfferingStatus, List<String> skuOfferingStatus, String h5Key,
                                                                      List<String> h5SpuOfferingClasses,
                                                                      String inventoryType,
                                                                      LoginIfo4Redis loginIfo4Redis,
                                                                      Integer page, Integer num) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue()
                .get(REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes)
                || (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL))) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        // 商品名称搜索支持反斜杠适配
        if (spuOfferingName != null) {
            spuOfferingName = spuOfferingName.replaceAll("\\\\", "\\\\\\\\");
        }
        if (skuOfferingName != null) {
            skuOfferingName = skuOfferingName.replaceAll("\\\\", "\\\\\\\\");
        }
        if (atomOfferingName != null) {
            atomOfferingName = atomOfferingName.replaceAll("\\\\", "\\\\\\\\");
        }
        String userId = loginIfo4Redis.getUserId();
        String roleType = loginIfo4Redis.getRoleType();
        BaseAnswer<PageData<InventoryInfoDTO>> baseAnswer = new BaseAnswer<>();
        PageData<InventoryInfoDTO> pageData = new PageData<>();
        baseAnswer.setData(pageData);
        pageData.setPage(page);
        if (CollectionUtil.isNotEmpty(spuOfferingClass) && !SPUOfferingClassEnum.containsList(spuOfferingClass)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品类型错误");
        }
        // 这里会对人员角色进行判断。如果是超管和运营管理员 客服管理员看到的应该是全部的库存信息，如果是合作伙伴看到的应该只有属于合作伙伴的信息
        List<String> userIdList = new ArrayList<>();
        Long count = null;

        String beId = "";
        String location = "";
        // 从合作
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL)) {
            userIdList.add(userId);
            count = inventoryHandlerMapper.countInventoryNewByHandleQuery(spuOfferingName, skuOfferingName,
                    spuOfferingCode, skuOfferingCode,
                    atomOfferingName, spuOfferingClass,
                    partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus,
                    userIdList, h5Key, h5SpuOfferingClasses,
                    inventoryType, beId, location);
            // 主合作
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)) {
            if (roleType.equals(PARTNER_LORD_ROLE)) {
                BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                    userIdList = downUserIds.getData();
                }
                userIdList.add(userId);
            } else if (roleType.equals(BaseConstant.PARTNER_PROVINCE)) {
                log.info("合作伙伴省管查询库存信息：partnerProvince：{}", roleType);
                // 省管配置权限为主合作伙伴权限
                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
                if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                    throw new BusinessException("10004", "合作伙伴省管账号错误");
                }

                Data4User data4User = data4UserBaseAnswer.getData();
                String companyType = data4User.getCompanyType();
                boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType)
                        && "2".equals(companyType);
                String userLocation = data4User.getLocationIdPartner();
                log.info("登录用户是否是合作伙伴省管：partnerIsProvinceUser:{}", isProvinceUser);
                if (isProvinceUser) {
                    BaseAnswer<Data4User> userPartner = userFeignClient
                            .getUserPartnerPrimaryByPartnerName(data4User.getPartnerName());
                    if (userPartner == null || !SUCCESS.getStateCode().equals(userPartner.getStateCode())) {
                        throw new BusinessException("10004", "合作伙伴省管获取主合作伙伴账号错误");
                    }
                    Data4User userPartnerData = userPartner.getData();
                    if (Optional.ofNullable(userPartnerData).isPresent()) {
                        BaseAnswer<List<String>> downUserIds = userFeignClient
                                .getDownUserIds(userPartnerData.getUserId());
                        if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                            userIdList = downUserIds.getData();
                        }
                        userIdList.add(userPartnerData.getUserId());
                        userIdList.add(userId);
                    }
                    if ("all".equals(userLocation)) {
                        beId = data4User.getBeIdPartner();
                    } else {
                        location = userLocation;
                    }
                } else {
                    throw new BusinessException(StatusConstant.AUTH_ERROR, "账号不属于省公司");
                }
            } else {
                throw new BusinessException(StatusConstant.AUTH_ERROR);
            }
            count = inventoryHandlerMapper.countInventoryNewByHandleQuery(spuOfferingName, skuOfferingName,
                    spuOfferingCode, skuOfferingCode,
                    atomOfferingName, spuOfferingClass,
                    partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, userIdList,
                    h5Key, h5SpuOfferingClasses, inventoryType,
                    beId, location);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)) {
            count = inventoryHandlerMapper.countInventoryNewByHandleQuery(spuOfferingName, skuOfferingName,
                    spuOfferingCode, skuOfferingCode,
                    atomOfferingName, spuOfferingClass, partnerName,
                    cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, null,
                    h5Key, h5SpuOfferingClasses, inventoryType,
                    beId, location);
        } else {
            // 这里考虑后面可能有多种角色。应该也是没有权限的
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }
        pageData.setCount(count);
        if (count == 0) {
            return baseAnswer;
        }
        List<InventoryInfoHandle> inventoryInfoHandles;
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL)) {
            inventoryInfoHandles = inventoryHandlerMapper.selectInventoryNewByHandleQuery((page - 1) * num, num,
                    spuOfferingName, skuOfferingName, spuOfferingCode,
                    skuOfferingCode, atomOfferingName, spuOfferingClass,
                    partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, userIdList,
                    h5Key, h5SpuOfferingClasses, inventoryType,
                    beId, location);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)) {
            inventoryInfoHandles = inventoryHandlerMapper.selectInventoryNewByHandleQuery((page - 1) * num, num,
                    spuOfferingName, skuOfferingName, spuOfferingCode,
                    skuOfferingCode, atomOfferingName, spuOfferingClass,
                    partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, userIdList,
                    h5Key, h5SpuOfferingClasses, inventoryType,
                    beId, location);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)) {
            inventoryInfoHandles = inventoryHandlerMapper.selectInventoryNewByHandleQuery((page - 1) * num, num,
                    spuOfferingName, skuOfferingName, spuOfferingCode,
                    skuOfferingCode, atomOfferingName, spuOfferingClass,
                    partnerName, cooperatorName, inventoryStatus,
                    spuOfferingStatus, skuOfferingStatus, null,
                    h5Key, h5SpuOfferingClasses, inventoryType,
                    beId, location);
        } else {
            throw new BusinessException(StatusConstant.AUTH_ERROR);
        }
        List<InventoryInfoDTO> inventoryInfoDTOS = new ArrayList<>();
        pageData.setData(inventoryInfoDTOS);
        inventoryInfoHandles.forEach(x -> {
            InventoryInfoDTO inventoryInfoDTO = new InventoryInfoDTO();
            BeanUtils.copyProperties(x, inventoryInfoDTO);
            if (x.getIsPrimary() != null && !x.getIsPrimary()) {
                inventoryInfoDTO.setHasPartner(true);
            } else {
                inventoryInfoDTO.setHasPartner(false);
            }
            inventoryInfoDTO.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(x.getSpuOfferingClass()));
            // 查询商品信息发布省地市信息
            List<CityInfoDO> cityInfoList = productHandlerMapper.selectCityInfo(x.getSkuOfferingCode());
            List<CityInfoDTO> cityInfo = new ArrayList<>();
            Map<String, Set<String>> cityMap = new HashMap<>();
            // provinceCityConfig.getcityCodeNameMap().get(city);
            for (CityInfoDO city : cityInfoList) {
                String province = city.getProvince();
                String provinceName = provinceCityConfig.getProvinceCodeNameMap().get(province);
                String cityName = provinceCityConfig.getcityCodeNameMap().get(city.getCity());
                if (StringUtils.isNotEmpty(provinceName)) {
                    // 如果是河南的商品，则默认省内接单，否则默认OS接单
                    /*
                     * if ("371".equals(province)) {
                     * productConfigDTO.setOrdertakeType(2);
                     * } else {
                     * productConfigDTO.setOrdertakeType(1);
                     * }
                     */
                    if (!cityMap.containsKey(provinceName)) {
                        Set<String> citySet = new HashSet<>();
                        citySet.add(cityName);
                        cityMap.put(provinceName, citySet);
                    } else {
                        Set<String> cityExistSet = cityMap.get(provinceName);
                        cityExistSet.add(cityName);
                    }
                }
            }
            for (String province : cityMap.keySet()) {
                CityInfoDTO cityInfoDTO = new CityInfoDTO();
                cityInfoDTO.setProvince(province);
                Set<String> citySetList = cityMap.get(province);
                List<String> provinceCity = new ArrayList<>();
                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(citySetList)) {
                    provinceCity = new ArrayList<>(citySetList);
                }
                cityInfoDTO.setCity(provinceCity);
                cityInfo.add(cityInfoDTO);
            }

            inventoryInfoDTO.setCityInfo(cityInfo);

            inventoryInfoDTOS.add(inventoryInfoDTO);
        });
        return baseAnswer;
    }

    /**
     * 额度列表查询
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @Override
    public BaseAnswer<PageData<LimitInfoDTO>> getLimitList(LimitListRequest param,
                                                           LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPage() != null ? param.getPage() : 1;
        Integer pageSize = param.getNum() != null ? param.getNum() : 10;
        PageData<LimitInfoDTO> pageData = new PageData<>();
        List<LimitInfoDTO> limitInfoDTOS = new ArrayList<>();

        ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
        ServicePackLimitAmountExample.Criteria criteria = servicePackLimitAmountExample.createCriteria();
        if (param.getProductName() != null) {
            criteria.andProductNameLike("%" + param.getProductName() + "%");
        }
        if (param.getServiceName() != null) {
            criteria.andServiceNameLike("%" + param.getServiceName() + "%");
        }
        if (param.getStatus() != null) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (param.getCompanyId() != null) {
            criteria.andCompanyIdEqualTo(param.getCompanyId());
        }
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                .selectByExample(servicePackLimitAmountExample);
        servicePackLimitAmounts.forEach(x -> {
            if (limitExpired(x)) {
                x.setStatus(DictLimitStatusEnum.LOSE_EFFECTIVENESS.getType());
            }
        });
        PageInfo<ServicePackLimitAmount> pageInfo = new PageInfo<>(servicePackLimitAmounts);
        pageData.setCount(pageInfo.getTotal());

        // List<LimitInfoDTO> servicePackLimitAmounts =
        // inventoryHandlerMapper.getDICTLimitList(param);
        limitInfoDTOS = servicePackLimitAmounts.stream().map(x -> {
            LimitInfoDTO limitInfoDTO = new LimitInfoDTO();
            BeanUtils.copyProperties(x, limitInfoDTO);
            limitInfoDTO.setCompanyName(provinceCityConfig.getProvinceCodeNameMap().get(x.getCompanyId()));
            // limitInfoDTO.setResidualQuatity(x.getCurrentInventory());
            limitInfoDTO.setChargeId(DictLimitTypeEnum.getChargeId(x.getProductName()));
            return limitInfoDTO;
        }).collect(Collectors.toList());

        pageData.setData(limitInfoDTOS);
        return BaseAnswer.success(pageData);

    }

    @Override
    public BaseAnswer<List<SimpleItemDTO>> getLimitStatus() {
        List<SimpleItemDTO> list = new ArrayList<>();
        DictLimitStatusEnum[] values = DictLimitStatusEnum.values();
        for (DictLimitStatusEnum value : values) {
            String type = value.getType();
            SimpleItemDTO dto = new SimpleItemDTO();
            dto.setCode(type);
            dto.setName(value.getDesc());
            list.add(dto);
        }
        return BaseAnswer.success(list);
    }

    /**
     * 额度列表导出
     *
     * @param param
     * @param loginIfo4Redis
     * @return
     */

    @Override
    public void getLimitExport(LimitListRequest param,
                               LoginIfo4Redis loginIfo4Redis) throws IOException {
        String exportPhone = param.getExportPhone();
        String exportMask = param.getExportMask() == null ? "" : param.getExportMask() + "";
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 验证当前用户是否超管，及验证码是否正确
        SmsValidUtil.checkSmsValid(isAdmin, exportMask, exportPhone, redisTemplate);
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<DictLimitExportVO> limitInfoDTOS = new ArrayList<>();
        ServicePackLimitAmountExample servicePackLimitAmountExample = new ServicePackLimitAmountExample();
        ServicePackLimitAmountExample.Criteria criteria = servicePackLimitAmountExample.createCriteria();
        if (param.getProductName() != null) {
            criteria.andProductNameLike("%" + param.getProductName() + "%");
        }
        if (param.getServiceName() != null) {
            criteria.andServiceNameLike("%" + param.getServiceName() + "%");
        }
        if (param.getStatus() != null) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (param.getCompanyId() != null) {
            criteria.andCompanyIdEqualTo(param.getCompanyId());
        }

        List<ServicePackLimitAmount> servicePackLimitAmounts = servicePackLimitAmountMapper
                .selectByExample(servicePackLimitAmountExample);
        limitInfoDTOS = servicePackLimitAmounts.stream().map(x -> {
            DictLimitExportVO limitInfoDTO = new DictLimitExportVO();
            BeanUtils.copyProperties(x, limitInfoDTO);
            limitInfoDTO.setCompanyName(provinceCityConfig.getProvinceCodeNameMap().get(x.getCompanyId()));
            limitInfoDTO.setStatusDesc(DictLimitStatusEnum.getDesc(x.getStatus()));
            limitInfoDTO.setChargeId(DictLimitTypeEnum.getChargeId(x.getProductName()));
            return limitInfoDTO;
        }).collect(Collectors.toList());

        String fileName = "额度库存导出" + DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);

        CommonResponseUtils commonResponseUtils = ExcelUtils.exportExcelFileInput(limitInfoDTOS, fileName, fileName,
                DictLimitExportVO.class, fileName, response);
        if (!commonResponseUtils.get("stateCode").equals(ResponseCode.SUCCESS)) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }

    }

    /**
     * @param spuOfferingName
     * @param spuOfferingCode
     * @param spuOfferingClass
     * @param page
     * @param num
     * @param loginIfo4Redis
     * @return
     */
    // 数据权限-库存配置
    @Override
    public BaseAnswer<PageData<InventoryInfoDTO>> getInventoryTypeList(String spuOfferingName,
                                                                       String spuOfferingCode,
                                                                       String spuOfferingClass,
                                                                       List<String> h5SpuOfferingClasses,
                                                                       String inventoryType,
                                                                       LoginIfo4Redis loginIfo4Redis,
                                                                       Integer page, Integer num) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue()
                .get(REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes)
                || (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_PERSONAL))) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        // 商品名称搜索支持反斜杠适配
        if (spuOfferingName != null) {
            spuOfferingName = spuOfferingName.replaceAll("\\\\", "\\\\\\\\");
        }
        BaseAnswer<PageData<InventoryInfoDTO>> baseAnswer = new BaseAnswer<>();
        PageData<InventoryInfoDTO> pageData = new PageData<>();
        baseAnswer.setData(pageData);
        pageData.setPage(page);
        if (StringUtils.isNotEmpty(spuOfferingClass) && !SPUOfferingClassEnum.contains(spuOfferingClass)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品类型错误");
        }
        Long count = inventoryHandlerMapper.countInventoryTypeByHandleQuery(spuOfferingName, spuOfferingCode,
                spuOfferingClass, h5SpuOfferingClasses, inventoryType);
        pageData.setCount(count);
        if (count == 0) {
            return baseAnswer;
        }
        List<InventoryInfoHandle> inventoryInfoHandles = inventoryHandlerMapper.selectInventoryTypeByHandleQuery(
                (page - 1) * num, num,
                spuOfferingName, spuOfferingCode, spuOfferingClass, h5SpuOfferingClasses, inventoryType);
        List<InventoryInfoDTO> inventoryInfoDTOS = new ArrayList<>();
        pageData.setData(inventoryInfoDTOS);
        inventoryInfoHandles.forEach(x -> {
            InventoryInfoDTO inventoryInfoDTO = new InventoryInfoDTO();
            BeanUtils.copyProperties(x, inventoryInfoDTO);
            inventoryInfoDTO.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(x.getSpuOfferingClass()));

            inventoryInfoDTOS.add(inventoryInfoDTO);
        });
        return baseAnswer;
    }

    /**
     * 查询sku商品发布省份地市信息
     *
     * @param skuCode
     * @return
     */
    private List<CityInfoDTO> queryAtomIssueProvinceAndCity(String skuCode) {
        // 查询商品信息发布省地市信息
        List<CityInfoDO> cityInfoList = productHandlerMapper.selectCityInfo(skuCode);
        List<CityInfoDTO> cityInfo = new ArrayList<>();
        Map<String, Set<String>> cityMap = new HashMap<>();
        for (CityInfoDO city : cityInfoList) {
            String province = city.getProvince();
            String provinceName = provinceCityConfig.getProvinceCodeNameMap().get(province);
            String cityName = provinceCityConfig.getcityCodeNameMap().get(city.getCity());
            if (StringUtils.isNotEmpty(provinceName)) {
                // 如果是河南的商品，则默认省内接单，否则默认OS接单
                /*
                 * if ("371".equals(province)) {
                 * productConfigDTO.setOrdertakeType(2);
                 * } else {
                 * productConfigDTO.setOrdertakeType(1);
                 * }
                 */
                if (!cityMap.containsKey(provinceName)) {
                    Set<String> citySet = new HashSet<>();
                    if (StringUtils.isEmpty(cityName)) {
                        citySet.add("-1");
                    } else {
                        citySet.add(cityName);
                    }
                    cityMap.put(provinceName, citySet);
                } else {
                    Set<String> cityExistSet = cityMap.get(provinceName);
                    cityExistSet.add(cityName);
                }
            }
        }
        for (String province : cityMap.keySet()) {
            CityInfoDTO cityInfoDTO = new CityInfoDTO();
            cityInfoDTO.setProvince(province);
            Set<String> citySetList = cityMap.get(province);
            List<String> provinceCity = new ArrayList<>();
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(citySetList)) {
                provinceCity = new ArrayList<>(citySetList);
            }
            cityInfoDTO.setCity(provinceCity);
            cityInfo.add(cityInfoDTO);
        }
        return cityInfo;
    }

    /**
     * 封装卡+X终端库存原子详情表数据入库
     *
     * @param dkcardxInventoryDetailInfo
     * @param inventoryMainId
     * @param atomOfferingInfo
     * @param inventoryInfo
     * @param atomInventory
     */
    private void capsulationDkcardxInventoryAtomInfo(DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo,
                                                     String inventoryMainId, AtomOfferingInfo atomOfferingInfo,
                                                     ReserveInventoryRequest.InventoryInfo inventoryInfo, Long atomInventory) {
        Date date = new Date();
        // 通过详情表id 省份地市信息 查询x终端信息 在通过原子商品id查询x终端原子信息就行更新
        String id = dkcardxInventoryDetailInfo.getId();
        String atomId = atomOfferingInfo.getId();
        List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfos = dkcardxInventoryAtomInfoMapper
                .selectByExample(new DkcardxInventoryAtomInfoExample().createCriteria()
                        .andAtomIdEqualTo(atomId).andInventoryDetailIdEqualTo(id).example());
        if (CollectionUtils.isEmpty(dkcardxInventoryAtomInfos)) {
            ReserveInventoryRequest.SpuOfferingInfo spuOfferingInfo = inventoryInfo.getSpuOfferingInfo().get(0);
            String spuOfferingCode = spuOfferingInfo.getSpuOfferingCode();
            ReserveInventoryRequest.SkuOfferingInfo skuOfferingInfo = spuOfferingInfo.getSkuOfferingInfo().get(0);
            String skuOfferingCode = skuOfferingInfo.getOfferingCode();
            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = new DkcardxInventoryAtomInfo();
            dkcardxInventoryAtomInfo.setId(BaseServiceUtils.getId());
            dkcardxInventoryAtomInfo.setInventoryDetailId(dkcardxInventoryDetailInfo.getId());
            dkcardxInventoryAtomInfo.setInventoryMainId(inventoryMainId);
            dkcardxInventoryAtomInfo.setAtomId(atomOfferingInfo.getId());
            dkcardxInventoryAtomInfo.setAtomInventory(atomInventory);
            dkcardxInventoryAtomInfo.setSpuCode(spuOfferingCode);
            dkcardxInventoryAtomInfo.setSkuCode(skuOfferingCode);
            dkcardxInventoryAtomInfo.setOfferingCode(atomOfferingInfo.getOfferingCode());
            dkcardxInventoryAtomInfo.setCreateTime(date);
            dkcardxInventoryAtomInfo.setUpdateTime(date);
            dkcardxInventoryAtomInfoMapper.insert(dkcardxInventoryAtomInfo);
        } else {
            // 原子商品id +x终端详情主键id确定唯一性
            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = dkcardxInventoryAtomInfos.get(0);
            Long atomInventoryOld = dkcardxInventoryAtomInfo.getAtomInventory();
            dkcardxInventoryAtomInfo.setAtomInventory(atomInventory + atomInventoryOld);
            dkcardxInventoryAtomInfo.setUpdateTime(date);
            dkcardxInventoryAtomInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryAtomInfo);
        }
    }

    /**
     * 封装码号库存原子商品信息关联
     *
     * @param inventoryMainInfoOptional
     * @param atomOfferingInfo
     */
    private void handlerCardInventoryAtomInfo(Optional<CardInventoryMainInfo> inventoryMainInfoOptional,
                                              AtomOfferingInfo atomOfferingInfo, long reserveNum) {

        CardInventoryMainInfo cardInventoryMainInfo = inventoryMainInfoOptional.get();
        String id = cardInventoryMainInfo.getId();
        Date date = new Date();
        List<CardInventoryAtomInfo> cardInventoryAtomInfos = cardInventoryAtomInfoMapper
                .selectByExample(new CardInventoryAtomInfoExample().createCriteria().andCardInventoryMainIdEqualTo(id)
                        .andAtomIdEqualTo(atomOfferingInfo.getId()).example());
        if (CollectionUtil.isEmpty(cardInventoryAtomInfos)) {
            CardInventoryAtomInfo cardInventoryAtomInfo = new CardInventoryAtomInfo();
            String cardId = BaseServiceUtils.getId();
            cardInventoryAtomInfo.setId(cardId);
            cardInventoryAtomInfo.setCardInventoryMainId(id);
            cardInventoryAtomInfo.setAtomInventory(Integer.valueOf(String.valueOf(reserveNum)));
            cardInventoryAtomInfo.setAtomId(atomOfferingInfo.getId());
            cardInventoryAtomInfo.setSpuCode(atomOfferingInfo.getSpuCode());
            cardInventoryAtomInfo.setSkuCode(atomOfferingInfo.getSkuCode());
            cardInventoryAtomInfo.setOfferingCode(atomOfferingInfo.getOfferingCode());
            cardInventoryAtomInfo.setCreateTime(date);
            cardInventoryAtomInfo.setUpdateTime(date);
            cardInventoryAtomInfoMapper.insert(cardInventoryAtomInfo);
            AtomOfferingInfo atomOfferingInfoEn = new AtomOfferingInfo();
            atomOfferingInfoEn.setId(atomOfferingInfo.getId());
            atomOfferingInfoEn.setCardInfoInventoryMainId(cardId);
            atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfoEn);
        } else {
            log.info("原子商品与码号库存关系已关联cardInventoryMainInfoId:{}", id);
            CardInventoryAtomInfo cardInventoryAtomInfo = new CardInventoryAtomInfo();
            CardInventoryAtomInfo cardInventoryAtomInfoOld = cardInventoryAtomInfos.get(0);
            cardInventoryAtomInfo.setId(cardInventoryAtomInfoOld.getId());
            cardInventoryAtomInfo.setAtomInventory(
                    cardInventoryAtomInfoOld.getAtomInventory() + Integer.valueOf(String.valueOf(reserveNum)));
            cardInventoryAtomInfo.setUpdateTime(date);
            cardInventoryAtomInfoMapper.updateByPrimaryKeySelective(cardInventoryAtomInfo);
        }

    }

    @Override
    public List<DkcardxInventoryMainInfo> getInventoryKxDeviceList(ConfigCardXInventoryDeviceParam param) {
        DkcardxInventoryMainInfoExample example = new DkcardxInventoryMainInfoExample();
        DkcardxInventoryMainInfoExample.Criteria criteria = example.createCriteria();
        criteria.andBeIdEqualTo(param.getBeId());
        criteria.andTerminalTypeEqualTo(param.getTerminalType());
        if (StringUtils.isNotEmpty(param.getCustCode())) {
            criteria.andCustCodeEqualTo(param.getCustCode());
        } else {
            criteria.andCustCodeIsNull();
        }
        if (StringUtils.isNotEmpty(param.getTemplateId())) {
            criteria.andTemplateIdEqualTo(param.getTemplateId());
        } else {
            criteria.andTemplateIdIsNull();
        }
        List<DkcardxInventoryMainInfo> dkcardxInventoryMainInfoList = dkcardxInventoryMainInfoMapper.selectByExample(
                example);
        return dkcardxInventoryMainInfoList;
    }

    @Override
    public PageData<KXInventoryDetailImeiDTO> listKXInventoryDetailImei(
            KXInventoryDetailImeiParam kxInventoryDetailImeiParam) {
        PageData<KXInventoryDetailImeiDTO> pageData = new PageData<>();
        Integer pageNum = kxInventoryDetailImeiParam.getPageNum();
        Integer pageSize = kxInventoryDetailImeiParam.getPageSize();

        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        String locationParam = kxInventoryDetailImeiParam.getLocation();
        if ("省级".equals(locationParam)) {
            kxInventoryDetailImeiParam.setLocation("-1");
        }

        Page<CardRelationImportInfoVO> page = new Page<>(pageNum, pageSize);
        List<KXInventoryDetailImeiDTO> detailImeiDTOList = inventoryHandlerMapper.listKXInventoryDetailImei(page,
                kxInventoryDetailImeiParam);
        if (CollectionUtils.isNotEmpty(detailImeiDTOList)) {
            detailImeiDTOList.stream().forEach(detailImeiDTO -> {
                detailImeiDTO.setSellStatusName(SellStatusEnum.getDescByType(detailImeiDTO.getSellStatus()));
                String location = detailImeiDTO.getLocation();
                if (StringUtils.isEmpty(location)) {
                    detailImeiDTO.setLocationName("省级");
                } else {
                    detailImeiDTO.setLocationName(locationCodeNameMap.get(location) + "");
                }
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(detailImeiDTOList);
        return pageData;
    }

    @Override
    public List<KXInventoryDetailLocationDTO> listKXInventoryDetailLocation(String atomId) {
        List<KXInventoryDetailLocationDTO> locationDTOList = new ArrayList<>();
        KXInventoryDetailLocationDTO locationDTO = new KXInventoryDetailLocationDTO();
        locationDTO.setLocation("全部");
        locationDTO.setLocationName("全部");
        locationDTOList.add(locationDTO);
        List<KXInventoryDetailLocationDTO> detailLocationDTOList = inventoryHandlerMapper
                .listKXInventoryDetailLocation(atomId);
        if (CollectionUtils.isNotEmpty(detailLocationDTOList)) {
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            detailLocationDTOList.stream().forEach(detailLocationDTO -> {
                String location = detailLocationDTO.getLocation();
                if (org.apache.commons.lang3.StringUtils.isEmpty(location)) {
                    detailLocationDTO.setLocation("省级");
                    detailLocationDTO.setLocationName("省级");
                } else {
                    detailLocationDTO.setLocationName(locationCodeNameMap.get(location) + "");
                }
                locationDTOList.add(detailLocationDTO);
            });
        }
        return locationDTOList;
    }

    @Override
    public List<DkCardxInventoryDetailInfoDTO> getInventoryKxDetailList(String atomId) {
        List<DkCardxInventoryDetailInfoDTO> dkCardxInventoryDetailInfoDTOS = new ArrayList<>();
        List<DkCardxInventoryDetailInfoDTO> dkcardxInventoryMainInfoList = inventoryHandlerMapper
                .getInventoryKxDetailList(atomId);
        if (CollectionUtils.isNotEmpty(dkcardxInventoryMainInfoList)) {
            Integer provinceTotalInventory = 0;
            Boolean hasProvince = true;
            Optional<DkCardxInventoryDetailInfoDTO> optional = dkcardxInventoryMainInfoList.stream().filter(
                            item -> StringUtils.isNotEmpty(item.getProvinceCityName())
                                    && item.getProvinceCityName().equals("省级"))
                    .findFirst();
            if (optional.isPresent()) {
                provinceTotalInventory = optional.get().getTotalInventory();
            } else {
                hasProvince = false;
            }

            // 有省级和地市一种处理方式，只有省级或地市一种处理方式
            for (DkCardxInventoryDetailInfoDTO dkCardxInventoryDetailInfoDTO : dkcardxInventoryMainInfoList) {
                DkCardxInventoryDetailInfoDTO dto = new DkCardxInventoryDetailInfoDTO();
                if (StringUtils.isEmpty(dkCardxInventoryDetailInfoDTO.getProvinceCityName())) {
                    continue;
                }
                BeanUtils.copyProperties(dkCardxInventoryDetailInfoDTO, dto);
                if (dkCardxInventoryDetailInfoDTO.getAtomInventory() == null) {
                    dto.setAtomInventory(0);
                }
                if ((dkcardxInventoryMainInfoList.size() == 1
                        && dkCardxInventoryDetailInfoDTO.getProvinceCityName().equals("省级")) || !hasProvince) {
                    dto.setInventoryStatus(dto.getTotalInventory() >= dto.getInventoryThreshold() ? 1 : 0);
                } else {
                    // 省级地市都有的情况，忽略省级
                    if (!dkCardxInventoryDetailInfoDTO.getProvinceCityName().equals("省级")) {
                        dto.setInventoryStatus(
                                (dto.getTotalInventory() + provinceTotalInventory) >= dto.getInventoryThreshold() ? 1
                                        : 0);
                    }
                }
                dkCardxInventoryDetailInfoDTOS.add(dto);
            }
        }
        return dkCardxInventoryDetailInfoDTOS;
    }

    // 判断额度是否过期
    private boolean limitExpired(ServicePackLimitAmount servicePackLimitAmount) {
        Date now = new Date();
        String yearMonthDay = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date currentDateTime = null;
        try {
            currentDateTime = dateFormat.parse(yearMonthDay);
            Date expTime = dateFormat.parse(servicePackLimitAmount.getExptime());
            if (currentDateTime.after(expTime)) {

                log.error("DICT类商品:额度过期，,额度信息:{}", servicePackLimitAmount);
                // 冻结额度
                executorService.execute(() -> expireLimit(servicePackLimitAmount));
                return true;
            }
        } catch (ParseException e) {
            log.error("DICT类商品:额度过期处理失败,额度信息:{}", servicePackLimitAmount);
        }
        return false;
    }

    @Override
    public void disposePassageKXOrderInventoryMessage() {
        // 查询历史代客下单卡+x订单
        List<String> orderTypeList = /* new ArrayList<>() */toCustomerOrderType;
        List<PassageKXOrderDTO> passageKXOrderS = new ArrayList<>();
        /*
         * orderTypeList.add("00");
         * orderTypeList.add("02");
         */
        List<Order2cInfo> order2cInfoList = order2cInfoMapper.selectByExample(new Order2cInfoExample().createCriteria()
                .andOrderTypeIn(orderTypeList).andSpuOfferingClassEqualTo("A11").example());
        // 通过原子订单查询spuCode skuCode atomCode
        List<Order2cAtomInfo> order2cAtomInfos = order2cAtomInfoMapper
                .selectByExample(new Order2cAtomInfoExample().createCriteria()
                        .andOrderIdIn(
                                order2cInfoList.stream().map(Order2cInfo::getOrderId).collect(Collectors.toList()))
                        .example());
        List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfos.stream()
                .filter(x -> x.getAtomOfferingClass().equals("X")).collect(Collectors.toList());
        for (Order2cAtomInfo order2cAtomInfo : order2cAtomInfoList) {
            String orderId = order2cAtomInfo.getOrderId();
            String spuOfferingCode = order2cAtomInfo.getSpuOfferingCode();
            String skuOfferingCode = order2cAtomInfo.getSkuOfferingCode();
            String atomOfferingCode = order2cAtomInfo.getAtomOfferingCode();
            // 查询原子商品信息
            List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper
                    .selectByExample(new AtomOfferingInfoExample().createCriteria().andSpuCodeEqualTo(spuOfferingCode)
                            .andSkuCodeEqualTo(skuOfferingCode).andOfferingCodeEqualTo(atomOfferingCode).example());
            AtomOfferingInfo atomOfferingInfo = atomOfferingInfos.get(0);
            String inventoryId = atomOfferingInfo.getInventoryId();
            String inventoryManagementModeKx = atomOfferingInfo.getInventoryManagementModeKx();
            if (StringUtils.isEmpty(inventoryManagementModeKx)) {
                continue;
            }
            PassageKXOrderDTO passageKXOrderDTO = new PassageKXOrderDTO();
            passageKXOrderDTO.setOrderId(orderId);
            passageKXOrderDTO.setAtomId(atomOfferingInfo.getId());
            passageKXOrderDTO.setInventoryId(inventoryId);
            passageKXOrderDTO.setInventoryManagementModeKx(inventoryManagementModeKx);
            Order2cInfo order2cInfo = order2cInfoMapper
                    .selectByExample(new Order2cInfoExample().createCriteria().andOrderIdEqualTo(orderId).example())
                    .get(0);
            String reserveBeId = order2cInfo.getReserveBeId();
            String reserveLocation = order2cInfo.getReserveLocation();
            log.info("disposePassageKXOrderInventoryMessage 订单 order_id = {} :订单提单人省份 = {}, 订单提单人地市 = {}", orderId,
                    reserveBeId, reserveLocation);
            passageKXOrderDTO.setReserveBeId(reserveBeId);
            passageKXOrderDTO.setReserveLocation(reserveLocation);
            passageKXOrderDTO.setAtomQuantity(atomOfferingInfo.getQuantity());
            passageKXOrderS.add(passageKXOrderDTO);
        }
        List<String> inventoryInKXClass = Arrays.asList(SPUOfferingClassEnum.A11.getSpuOfferingClass());
        List<ReserveInventoryRequest.SpuOfferingInfo> inventoryInKXSpu = new ArrayList<>();
        for (PassageKXOrderDTO passageKXOrder : passageKXOrderS) {
            // 保存提单人区域信息到redis s说是全部统计到省级 所以提单人以省编码存储
            log.info("disposePassageKXOrderInventoryMessage before 写入redis提单人丢失信息：key = {}",
                    passageKXOrder.getOrderId());
            log.info("disposePassageKXOrderInventoryMessage before 写入redis提单人丢失信息：value = {}",
                    passageKXOrder.getReserveBeId());
            stringRedisTemplate.opsForValue().setIfAbsent(REDIS_COMMIT_PERSON_PREFIX + passageKXOrder.getOrderId(),
                    passageKXOrder.getReserveBeId());
            // 获取原来预占的sku预占数
            String content = stringRedisTemplate.opsForValue()
                    .get(REDIS_RESERVE_INVENTORY_PREFIX + passageKXOrder.getOrderId());
            if (content == null) {
                log.info("未查询到Redis中该订单流水号:{}", passageKXOrder.getOrderId());
                continue;
            }
            if (content != null) {
                ReserveInventoryRequest.InventoryInfo inventoryInfo = null;
                inventoryInfo = JSON.parseObject(content, ReserveInventoryRequest.InventoryInfo.class);
                log.info("查询到Redis中该订单预占信息inventoryInfo：{}", inventoryInfo);
                inventoryInKXSpu = inventoryInfo.getSpuOfferingInfo().stream()
                        .filter(x -> inventoryInKXClass.contains(x.getOfferingClass())).collect(Collectors.toList());
            }
            log.info("disposePassageKXOrderInventoryMessage inventoryInKXSpu = {}", inventoryInKXSpu);
            // 预占数
            Long bookQuantity = inventoryInKXSpu.get(0).getSkuOfferingInfo().get(0).getQuantity();
            // 总预占数
            long reserveNum = bookQuantity * passageKXOrder.getAtomQuantity();
            // 把预占信息存到新的卡+x预占redis信息
            List<InventoryAreaDTO> inventoryAreaList = new ArrayList<>();
            InventoryAreaDTO inventoryAreaDTO = new InventoryAreaDTO();
            String inventoryManagementModeKx = passageKXOrder.getInventoryManagementModeKx();
            String reserveBeId = passageKXOrder.getReserveBeId();
            String reserveLocation = passageKXOrder.getReserveLocation();
            // 查看线上的 原子商品都是配置的省级库存模式
            log.info("disposePassageKXOrderInventoryMessage inventoryManagementModeKx = {}", inventoryManagementModeKx);
            if ("1".equals(inventoryManagementModeKx)) {
                inventoryAreaDTO.setAreaCode(reserveBeId);
            } else {
                inventoryAreaDTO.setAreaCode(reserveLocation);
            }
            inventoryAreaDTO.setReserveQuantity(reserveNum);
            log.info("disposePassageKXOrderInventoryMessage inventoryAreaDTO = {}", inventoryAreaDTO);
            inventoryAreaList.add(inventoryAreaDTO);
            log.info("disposePassageKXOrderInventoryMessage before setRedis 写入Redis丢失信息: key = {}",
                    passageKXOrder.getOrderId() + "_" + passageKXOrder.getAtomId());
            log.info("disposePassageKXOrderInventoryMessage before setRedis 写入Redis丢失信息: value = {}",
                    JSON.toJSONString(inventoryAreaList));
            Boolean setIfAbsent = stringRedisTemplate.opsForValue().setIfAbsent(REDIS_COMMIT_INVENTORY_AREA
                            + passageKXOrder.getOrderId() + "_" + passageKXOrder.getAtomId(),
                    JSON.toJSONString(inventoryAreaList));
            if (setIfAbsent == null || !setIfAbsent) {
                log.info("查询到Redis中该订单预占信息已存在预占信息 inventoryAreaList：{}", inventoryAreaList);
                // throw new BusinessException(BaseErrorConstant.PARAM_ERROR,
                // "查询到Redis中该订单预占信息已存在预占信息");
            }
        }

    }

    @Override
    public void updateHistoryInventoryAtomInfo(String id, Long atomInventory) {
        DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = dkcardxInventoryAtomInfoMapper.selectByPrimaryKey(id);
        if (Optional.ofNullable(dkcardxInventoryAtomInfo).isPresent()) {
            dkcardxInventoryAtomInfo.setAtomInventory(atomInventory);
            dkcardxInventoryAtomInfoMapper.updateByPrimaryKeySelective(dkcardxInventoryAtomInfo);
        }
    }

    @Override
    public void updateInventoryAtomInfoFix() {
        List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfoList = inventoryHandlerMapper
                .updateInventoryAtomInfoFix();
        List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfos = new ArrayList<>();
        Date date = new Date();
        for (DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo : dkcardxInventoryAtomInfoList) {
            List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfoList = dkcardxInventoryDetailInfoMapper
                    .selectByExample(
                            new DkcardxInventoryDetailInfoExample().createCriteria()
                                    .andInventoryMainIdEqualTo(dkcardxInventoryAtomInfo.getInventoryMainId())
                                    .andProvinceAliasNameIsNull()
                                    .example());
            if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfoList)) {
                for (DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo : dkcardxInventoryDetailInfoList) {
                    DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo1 = new DkcardxInventoryAtomInfo();
                    BeanUtils.copyProperties(dkcardxInventoryAtomInfo, dkcardxInventoryAtomInfo1);
                    dkcardxInventoryAtomInfo1.setId(BaseServiceUtils.getId());
                    dkcardxInventoryAtomInfo1.setInventoryDetailId(dkcardxInventoryDetailInfo.getId());
                    dkcardxInventoryAtomInfo1.setAtomInventory(0L);
                    dkcardxInventoryAtomInfo1.setCreateTime(date);
                    dkcardxInventoryAtomInfo1.setUpdateTime(date);
                    dkcardxInventoryAtomInfos.add(dkcardxInventoryAtomInfo1);
                }
            }
        }
        dkcardxInventoryAtomInfoMapper.batchInsert(dkcardxInventoryAtomInfos);
    }
}
