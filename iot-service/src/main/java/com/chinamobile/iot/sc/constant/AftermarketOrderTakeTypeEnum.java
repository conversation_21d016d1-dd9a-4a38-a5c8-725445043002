package com.chinamobile.iot.sc.constant;

public enum AftermarketOrderTakeTypeEnum {

    OS(1,"OS接单"),
    PROVINCE(2,"省内接单")
    ;

    public Integer code;
    public String name;


    AftermarketOrderTakeTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }



    public static AftermarketOrderTakeTypeEnum fromCode(Integer code){
        AftermarketOrderTakeTypeEnum[] values = AftermarketOrderTakeTypeEnum.values();
        for (AftermarketOrderTakeTypeEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }
}
