package com.chinamobile.iot.sc.constant.limit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 * @description dict额度类型枚举类
 */
public enum DictLimitStatusEnum {

    SHORT("0","短缺"),
    SUFFICIENT("1","充足"),
    LOSE_EFFECTIVENESS("2","失效");

    /**
     * 卡片类型
     */
    private String type;

    /**
     * 卡片描述
     */
    private String desc;

    DictLimitStatusEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type) {
        for (DictLimitStatusEnum value : DictLimitStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public static Boolean containDesc(String desc) {
        for (DictLimitStatusEnum value : DictLimitStatusEnum.values()) {
            if (value.desc.equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
