package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineConfigReplenish;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架配置补充service接口类
 */
public interface NewProductRequestOnlineOfflineConfigReplenishService {

    /**
     * 新增配置补充信息
     * @param configReplenish
     */
    void saveConfigReplenish(NewProductRequestOnlineOfflineConfigReplenish configReplenish);

    /**
     * 更新配置补充信息
     * @param configReplenish
     */
    void updateConfigReplenishByRequestIdAndComboInfoId(NewProductRequestOnlineOfflineConfigReplenish configReplenish);

    /**
     * 更新配置补充信息
     * @param configReplenish
     */
    void updateConfigReplenishById(NewProductRequestOnlineOfflineConfigReplenish configReplenish);

    /**
     * 根据商品申请id和套餐信息id获取配置补充信息
     * @param newProductRequestId
     * @param comboInfoId
     * @return
     */
    NewProductRequestOnlineOfflineConfigReplenish getConfigReplenishByRequestIdAndComboInfoId(String newProductRequestId,
                                                                                              String comboInfoId);
}
