package com.chinamobile.iot.sc.service.impl;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.*;
import com.chinamobile.iot.sc.service.BaseOssService;
import com.chinamobile.iot.sc.util.qiniu.QiniuServiceUtil;
import com.chinamobile.iot.sc.util.qiniu.Utils;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Base64;

import static com.chinamobile.iot.sc.common.utils.BaseServiceUtils.doSuccess;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

import com.chinamobile.iot.sc.exceptions.BaseErrorConstant.*;


/**
 * <AUTHOR>
 * 七牛云存储
 * 文档: https://developer.qiniu.com/kodo/sdk/1239/java
 * 默认开启 qiniu
 */
@Service
@ConditionalOnProperty(value="storage.platform", havingValue="Qiniu",matchIfMissing = true)
@Slf4j
public class QiniuCloudStorageService extends BaseOssService {

	@Autowired
    private QiniuServiceUtil serviceUtil;

    /**
     * 上传文件
     *
     * @param file 文件
     * @param isCover 指定文件名称为key：覆盖试上传
     * @param expiredDay
     * @return 上传反馈信息
     */
    public BaseAnswer<UpResult> uploadFile(File file, String fname, boolean isCover, int expiredDay) throws Exception{
        try {
            Response response;
            if(isCover){
                //覆盖试操作：token传递相同文件才不会报614错误，否则报错
                response = serviceUtil.getUploadManager().put(file, fname, serviceUtil.getUpToken(fname));
            }else{
                //设置原始文件，在返回结果中带回
                response = serviceUtil.getUploadManager().put(file, Utils.getKey(file), serviceUtil.getUpToken());
            }
            BaseAnswer<UpResult> answer = serviceUtil.doResult(response, fname);
            setExpired(expiredDay, answer);
            return answer;
        } catch (QiniuException ex) {
            log.error("七牛上传文件失败",ex);
            return serviceUtil.doResult(ex.response,fname);
        }
    }

    /**
     * 上传文件
     *
     * @param bytes 文件字节数组
     * @param fname 文件名称
     * @param isCover 指定文件名称为key：覆盖试上传
     */
    public BaseAnswer<UpResult> uploadByte(ByteArrayUpload data) throws Exception{
        String fileName = data.getFileName();
        try {
            Response response = null;
            byte[] bytes = data.getBytes();
            if(data.isCover()){
                //覆盖试操作：token传递相同文件才不会报614错误，否则报错
                response = serviceUtil.getUploadManager().put(bytes, fileName, serviceUtil.getUpToken(fileName));
            }else{
                //设置原始文件，在返回结果中带回
                response = serviceUtil.getUploadManager().put(bytes, Utils.getKey(bytes, fileName), serviceUtil.getUpToken());
            }
            BaseAnswer<UpResult> answer = serviceUtil.doResult(response,fileName);
            setExpired(data.getExpiredDay(), answer);
            return answer;
        } catch (QiniuException ex) {
            log.error("七牛上传文件失败",ex);
            return serviceUtil.doResult(ex.response,fileName);
        }
    }

    private void setExpired(int expiredDay, BaseAnswer<UpResult> answer) throws Exception {
        if(expiredDay>0 && SUCCESS.equals(answer.getStatus())){
            deleteAfterDays(expiredDay,answer.getData().getKey());
        }
    }

    /**
     *  base64 上传
     *
     * @param base64
     */
    public BaseAnswer<UpResult>  uploadBase64(Base64Upload base64) throws Exception{
        try {
            final String[] info = base64.getBase64().split(",");
            String src = info.length == 2 ? info[1] : info[0];
            //src = filter(src);
            byte[] data = Base64.getDecoder().decode(src);
            return uploadByte(new ByteArrayUpload(data,base64.getFileName(),base64.isCover(),base64.getExpiredDay()));
        } catch (QiniuException ex) {
            log.error("七牛上传文件失败",ex);
            return serviceUtil.doResult(ex.response,base64.getFileName());
        }
    }

    /*private String filter(String src) {
        //特殊字符过滤
       return src.replace("n","").replace("\r\n","").replace("\r","")
               .replace("\n","");
    }*/

    /**
     * 删除文件
     *
     * @param keys 文件
     */
    public BaseAnswer<DelResult> delete(String... keys) throws Exception{
        BucketManager.BatchOperations deleteOp = new BucketManager.BatchOperations().addDeleteOp(serviceUtil.getConf().getBucket(), keys);
        return serviceUtil.doDelBatch(deleteOp, keys);
    }

    /**
     * 删除文件:过期删
     */
    public BaseAnswer<DelResult> deleteAfterDays(int days,String... keys) throws Exception{
        BucketManager.BatchOperations afterDaysOps = new BucketManager.BatchOperations().addDeleteAfterDaysOps(serviceUtil.getConf().getBucket(),days, keys);
        return serviceUtil.doDelBatch(afterDaysOps, keys);
    }

    @Override
    public BaseAnswer<QueryResult> getUrl(String key) {
        QueryResult ret = new QueryResult();
        ret.setOuterUrl( serviceUtil.getDownloadPath(key));
        ret.setInnerUrl( serviceUtil.getDownloadPath4Inner(key));
        return doSuccess(ret);
    }

    @Override
    public BaseAnswer<UpResult> copy(CopyUpload copyUpload) throws Exception {
        String toKey = copyUpload.getToKey();
        try {
            String bucket = serviceUtil.getConf().getBucket();
            Response copy = serviceUtil.getBucketManager().copy(bucket, copyUpload.getFromKey(), bucket, toKey,copyUpload.isCover());
            UpResult ans = new UpResult();
            ans.setKey(toKey).setFileName(toKey).setOuterUrl(serviceUtil.getDownloadPath(toKey))
                    .setInnerUrl(serviceUtil.getDownloadPath4Inner(toKey));
            BaseAnswer<UpResult> answer = doSuccess(ans);
            setExpired(copyUpload.getExpiredDay(), answer);
            return answer;
        }  catch (QiniuException ex) {
            log.error("七牛上传文件失败",ex);
            return serviceUtil.doResult(ex.response, toKey);
        }
    }
}
