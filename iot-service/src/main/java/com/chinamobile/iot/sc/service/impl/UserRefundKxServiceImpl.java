package com.chinamobile.iot.sc.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.dao.InventoryCutInfoMapper;
import com.chinamobile.iot.sc.dao.UserRefundKxMapper;
import com.chinamobile.iot.sc.dao.ext.InventoryCutInfoMapperExt;
import com.chinamobile.iot.sc.dao.ext.UserRefundKxMapperExt;
import com.chinamobile.iot.sc.enums.log.*;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.InventoryCutInfo;
import com.chinamobile.iot.sc.pojo.entity.InventoryCutInfoExample;
import com.chinamobile.iot.sc.pojo.entity.UserRefundKx;
import com.chinamobile.iot.sc.pojo.entity.UserRefundKxExample;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.InventoryCutListVO;
import com.chinamobile.iot.sc.pojo.vo.KxCanChooseUserVO;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.UserRefundKxService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.IotLogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

import static com.chinamobile.iot.sc.util.DateUtils.DEFAULT_DATETIME_FORMAT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/14
 * @description 卡+X退货发送短信的相关人员配置service实现类
 */
@Service
public class UserRefundKxServiceImpl implements UserRefundKxService {

    @Value("${supply.des.key}")
    private String encryptKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Resource
    private UserRefundKxMapperExt userRefundKxMapperExt;

    @Resource
    private UserRefundKxMapper userRefundKxMapper;

    @Resource
    private InventoryCutInfoMapper inventoryCutInfoMapper;

    @Resource
    private InventoryCutInfoMapperExt inventoryCutInfoMapperExt;

    @Resource
    private LogService logService;

    @Override
    public List<KxCanChooseUserVO> listKxCanChooseUser(KxCanChooseUserParam kxCanChooseUserParam) {
        if (StringUtils.isNotBlank(kxCanChooseUserParam.getPhone())) {
            kxCanChooseUserParam.setPhone(IOTEncodeUtils.encryptIOTMessage(kxCanChooseUserParam.getPhone(), encryptKey));
        }
        List<KxCanChooseUserVO> kxCanChooseUserVOList
                = userRefundKxMapperExt.listKxCanChooseUser(kxCanChooseUserParam);
        if (CollectionUtils.isNotEmpty(kxCanChooseUserVOList)){
            kxCanChooseUserVOList.stream().forEach(kxCanChooseUserVO -> {
                String phone = kxCanChooseUserVO.getPhone();
                phone = IOTEncodeUtils.decryptIOTMessage(phone, encryptKey);
                kxCanChooseUserVO.setPhone(phone);
            });
        }
        return kxCanChooseUserVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUserRefundKx(AddUserRefundKxParam addUserRefundKxParam,
                                LoginIfo4Redis loginIfo4Redis) {
        List<UserRefundKx> userRefundKxes = userRefundKxMapper.selectByExample(new UserRefundKxExample().createCriteria()
                .andUserIdEqualTo(addUserRefundKxParam.getUserId())
                .andNoticeTypeEqualTo(addUserRefundKxParam.getNoticeType()).example());
        if (CollectionUtils.isNotEmpty(userRefundKxes)) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"用户"+addUserRefundKxParam.getUserName()+"已经配置此种短信通知");
        }
        UserRefundKx userRefundKx = new UserRefundKx();
        userRefundKx.setId(BaseServiceUtils.getId());
        BeanUtils.copyProperties(addUserRefundKxParam,userRefundKx);
        userRefundKx.setCreator(loginIfo4Redis.getUserName());
        userRefundKx.setCreateTime(new Date());
        userRefundKxMapper.insert(userRefundKx);

        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.NOTE_WARNING.code,
                IotLogUtil.addUserRefundKx(addUserRefundKxParam),LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserRefundKxById(DeleteUserRefundKxParam deleteUserRefundKxParam) {
        userRefundKxMapper.deleteByPrimaryKey(deleteUserRefundKxParam.getId());
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.NOTE_WARNING.code,
                IotLogUtil.deleteUserRefundKx(deleteUserRefundKxParam), LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    public List<UserRefundKxVO> listUserRefundKx(QueryNoticeUserParam param) {
        if (StringUtils.isNotBlank(param.getPhone())) {
            param.setPhone(IOTEncodeUtils.encryptIOTMessage(param.getPhone(), encryptKey));
        }
        List<UserRefundKxVO> userRefundKxVOList = userRefundKxMapperExt.listUserRefundKx(param);
        if (CollectionUtils.isNotEmpty(userRefundKxVOList)){
            userRefundKxVOList.forEach(userRefundKxVO -> {
                String phone = userRefundKxVO.getPhone();
                phone = IOTEncodeUtils.decryptIOTMessage(phone, encryptKey);
                userRefundKxVO.setPhone(phone);
            });
        }
        return userRefundKxVOList;

    }

    @Override
    public List<UserRefundKxVO> listUserRefundKx(Integer noticeType) {
        QueryNoticeUserParam param = new QueryNoticeUserParam();
        param.setNoticeType(noticeType);
        return listUserRefundKx(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cutNowInventoryPattern(InventoryPatternCutParam param, LoginIfo4Redis loginIfo4Redis,String ip) {
        String inventoryNowName = param.getInventoryNowName();
        Date date = new Date();
        List<InventoryCutInfo> inventoryCutInfos = inventoryCutInfoMapper.selectByExample(new InventoryCutInfoExample().createCriteria()
                .andIsCancelEqualTo(false).example());
        if (CollectionUtils.isNotEmpty(inventoryCutInfos) && inventoryCutInfos.size()==1){
            InventoryCutInfo inventoryCutInfo = inventoryCutInfos.get(0);
            if (inventoryNowName.equals(inventoryCutInfo.getInventoryNowName())){
                logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.INVENTORY_PATTERN.code,
                        IotLogUtil.inventoryCutLog(param),LogResultEnum.LOG_FAIL.code, "模式切换请求参数错误");
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"模式切换请求参数错误");
            }
            inventoryCutInfo.setIsCancel(true);
            inventoryCutInfo.setUpdateTime(date);
            inventoryCutInfoMapper.updateByPrimaryKeySelective(inventoryCutInfo);
            InventoryCutInfo inventoryCutInfoNow = assembleDateInventoryCutInfo(param, loginIfo4Redis, date);
            inventoryCutInfoMapper.insert(inventoryCutInfoNow);
        }else if (CollectionUtils.isEmpty(inventoryCutInfos)){
            InventoryCutInfo inventoryCutInfoNow = assembleDateInventoryCutInfo(param, loginIfo4Redis, date);
            inventoryCutInfoMapper.insert(inventoryCutInfoNow);
        }else {
            logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.INVENTORY_PATTERN.code,
                    IotLogUtil.inventoryCutLog(param),LogResultEnum.LOG_FAIL.code, "当前模式存在多个");
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"当前模式存在多个");
        }
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code, SystemInstallEnum.INVENTORY_PATTERN.code,
                IotLogUtil.inventoryCutLog(param),LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    public BaseAnswer<PageData<InventoryCutListVO>> getInventoryCutList(InventoryCutListParam param) {
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        Date strToDate = null;
        Date endToDate = null;
        BaseAnswer<PageData<InventoryCutListVO>> baseAnswer = new BaseAnswer<>();
        Page<InventoryCutListVO> page = new Page<>(param.getPageNum(), param.getPageSize());
        PageData<InventoryCutListVO> pageData = new PageData<>();
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)){
            try {
                strToDate = DateUtils.strToDate(startTime, DEFAULT_DATETIME_FORMAT);
                endToDate = DateUtils.strToDate(endTime, DEFAULT_DATETIME_FORMAT);
            } catch (ParseException e) {
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"时间格式转化错误");
            }
        }
        List<InventoryCutListVO> inventoryCutInfoPage = inventoryCutInfoMapperExt.getInventoryCutInfoPage(page, param.getInventoryPatternName(), strToDate, endToDate);
        pageData.setPage( param.getPageNum());
        pageData.setCount(page.getTotal());
        pageData.setData(inventoryCutInfoPage);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    @Override
    public String getNowInventoryPattern() {
        List<InventoryCutInfo> inventoryCutInfos = inventoryCutInfoMapper.selectByExample(new InventoryCutInfoExample().createCriteria()
                .andIsCancelEqualTo(false).example());
        String inventoryNowName;
        if (CollectionUtils.isNotEmpty(inventoryCutInfos) && inventoryCutInfos.size()==1){
            InventoryCutInfo inventoryCutInfo = inventoryCutInfos.get(0);
            inventoryNowName = inventoryCutInfo.getInventoryNowName();
            if ("1".equals(inventoryNowName)){
                inventoryNowName ="拍下减库存";
            }else {
                inventoryNowName ="付款减库存";
            }
        }else{
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"当前模式存在多个或者为空");
        }

        return inventoryNowName;
    }


    /**
     * 组装实体参数
     * @param param
     * @param loginIfo4Redis
     * @param date
     * @return
     */
    private InventoryCutInfo assembleDateInventoryCutInfo(InventoryPatternCutParam param, LoginIfo4Redis loginIfo4Redis,Date date){
        InventoryCutInfo inventoryCutInfoNow = new InventoryCutInfo();
        inventoryCutInfoNow.setId(BaseServiceUtils.getId());
        inventoryCutInfoNow.setInventoryNowName(param.getInventoryNowName());
        inventoryCutInfoNow.setInventoryPatternName(param.getInventoryPatternName());
        inventoryCutInfoNow.setCutTime(date);
        inventoryCutInfoNow.setOperator(loginIfo4Redis.getRoleType());
        inventoryCutInfoNow.setIsCancel(false);
        inventoryCutInfoNow.setCreateTime(date);
        inventoryCutInfoNow.setUpdateTime(date);
        return inventoryCutInfoNow;
    }
}
