package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.CardChooseDeliveryMapper;
import com.chinamobile.iot.sc.pojo.entity.CardChooseDelivery;
import com.chinamobile.iot.sc.pojo.entity.CardChooseDeliveryExample;
import com.chinamobile.iot.sc.pojo.vo.CardChooseDeliveryVO;
import com.chinamobile.iot.sc.service.CardChooseDeliveryService;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/9
 * @description 批量导入交付卡信息service实现类
 */
@Service
public class CardChooseDeliveryServiceImpl implements CardChooseDeliveryService {

    @Resource
    private CardChooseDeliveryMapper cardChooseDeliveryMapper;

    @Override
    public void batchAddCardChooseDelivery(List<CardChooseDelivery> cardChooseDeliveryList) {
        cardChooseDeliveryMapper.batchInsert(cardChooseDeliveryList);
    }

    @Override
    public void deleteCardChooseDeliveryByNeed(CardChooseDeliveryExample cardChooseDeliveryExample) {
        cardChooseDeliveryMapper.deleteByExample(cardChooseDeliveryExample);
    }

    @Override
    public List<CardChooseDelivery> listCardChooseDeliveryByNeed(CardChooseDeliveryExample cardChooseDeliveryExample) {
        return cardChooseDeliveryMapper.selectByExample(cardChooseDeliveryExample);
    }

    @Override
    public List<CardChooseDeliveryVO> listCardChooseDeliveryByAtomOrderId(List<String> atomOrderIdList) {
        List<CardChooseDeliveryVO> cardChooseDeliveryVOList = new ArrayList<>();
        CardChooseDeliveryExample cardChooseDeliveryExample = new CardChooseDeliveryExample();
        cardChooseDeliveryExample.createCriteria()
                .andAtomOrderIdIn(atomOrderIdList);

        List<CardChooseDelivery> cardChooseDeliveryList = listCardChooseDeliveryByNeed(cardChooseDeliveryExample);
        if (CollectionUtils.isNotEmpty(cardChooseDeliveryList)){
            Map<String,CardChooseDeliveryVO> cardChooseDeliveryVOMap = new HashMap();
            cardChooseDeliveryList.forEach(cardChooseDelivery -> {
                String deviceVersion = cardChooseDelivery.getDeviceVersion();
                CardChooseDeliveryVO existCardChooseDeliveryVO = cardChooseDeliveryVOMap.get(deviceVersion);
                if (existCardChooseDeliveryVO == null) {
                    List<CardChooseDelivery> deliveryList = new ArrayList<>();
                    deliveryList.add(cardChooseDelivery);
                    CardChooseDeliveryVO cardChooseDeliveryVO = new CardChooseDeliveryVO();
                    cardChooseDeliveryVO.setDeviceVersion(deviceVersion);
                    cardChooseDeliveryVO.setCardChooseDeliveryList(deliveryList);
                    cardChooseDeliveryVOMap.put(deviceVersion,cardChooseDeliveryVO);
                }else{
                    existCardChooseDeliveryVO.getCardChooseDeliveryList().add(cardChooseDelivery);
                }
            });
            cardChooseDeliveryVOMap.values().forEach(cardChooseDeliveryVO -> {
                cardChooseDeliveryVOList.add(cardChooseDeliveryVO);
            });
        }

        return cardChooseDeliveryVOList;
    }

    @Override
    public List<CardChooseDeliveryVO> listCardChooseDeliveryByOrderId(List<String> orderIdList) {
        List<CardChooseDeliveryVO> cardChooseDeliveryVOList = new ArrayList<>();
        CardChooseDeliveryExample cardChooseDeliveryExample = new CardChooseDeliveryExample();
        cardChooseDeliveryExample.createCriteria()
                .andOrderIdIn(orderIdList);

        List<CardChooseDelivery> cardChooseDeliveryList = listCardChooseDeliveryByNeed(cardChooseDeliveryExample);
        if (CollectionUtils.isNotEmpty(cardChooseDeliveryList)){
            Map<String,CardChooseDeliveryVO> cardChooseDeliveryVOMap = new HashMap();
            cardChooseDeliveryList.forEach(cardChooseDelivery -> {
                String deviceVersion = cardChooseDelivery.getDeviceVersion();
                CardChooseDeliveryVO existCardChooseDeliveryVO = cardChooseDeliveryVOMap.get(deviceVersion);
                if (existCardChooseDeliveryVO == null) {
                    List<CardChooseDelivery> deliveryList = new ArrayList<>();
                    deliveryList.add(cardChooseDelivery);
                    CardChooseDeliveryVO cardChooseDeliveryVO = new CardChooseDeliveryVO();
                    cardChooseDeliveryVO.setDeviceVersion(deviceVersion);
                    cardChooseDeliveryVO.setCardChooseDeliveryList(deliveryList);
                    cardChooseDeliveryVOMap.put(deviceVersion,cardChooseDeliveryVO);
                }else{
                    existCardChooseDeliveryVO.getCardChooseDeliveryList().add(cardChooseDelivery);
                }
            });
            cardChooseDeliveryVOMap.values().forEach(cardChooseDeliveryVO -> {
                cardChooseDeliveryVOList.add(cardChooseDeliveryVO);
            });
        }

        return cardChooseDeliveryVOList;
    }

    @Override
    public void deleteCardChooseDeliveryById(String id) {
        cardChooseDeliveryMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void updateCardChooseDeliveryByNeed(CardChooseDelivery cardChooseDelivery,
                                               CardChooseDeliveryExample cardChooseDeliveryExample) {
        cardChooseDeliveryMapper.updateByExampleSelective(cardChooseDelivery,cardChooseDeliveryExample);
    }
}
