package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfoExample;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryCardDetailParam;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryDetailInfoParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16
 * @description 卡+X终端库存信息详情service接口类
 */
public interface DkcardxInventoryDetailInfoService {

    /**
     * 批量新增卡+X终端库存信息详情
     *
     * @param detailInfoList
     */
    void batchInsertDkcardxInventoryDetailInfo(List<DkcardxInventoryDetailInfo> detailInfoList);

    /**
     * 获取详情相关的卡+X信息
     *
     * @param cardDetailParam
     * @return
     */
    PageData<DkcardxInventoryCardDetailInfoDTO> listDkcardxInventoryCardDetailInfo(DkcardxInventoryCardDetailParam cardDetailParam,
                                                                                   LoginIfo4Redis loginIfo4Redis);

    /**
     * 根据需要获取卡+X终端库存信息详情
     *
     * @param dkcardxInventoryDetailInfoParam
     * @return
     */
    List<DkcardxInventoryDetailInfo> listDkcardxInventoryDetailInfo(DkcardxInventoryDetailInfoParam dkcardxInventoryDetailInfoParam,
                                                                    LoginIfo4Redis loginIfo4Redis);

    /**
     * 作用于商品管理库存导出
     * @param dkcardxInventoryDetailInfoParam
     * @param loginIfo4Redis
     * @return
     */
    List<DkcardxInventoryDetailInfo> listDkcardxInventoryDetailInfoExport(DkcardxInventoryDetailInfoParam dkcardxInventoryDetailInfoParam,
                                                                    LoginIfo4Redis loginIfo4Redis);

    /**
     * 根据需要获取卡+X终端库存信息详情
     * @param detailInfoExample
     * @return
     */
    List<DkcardxInventoryDetailInfo> listDkcardxInventoryDetailInfoByNeed(DkcardxInventoryDetailInfoExample detailInfoExample);

    /**
     * 根据主键id更新卡+X终端库存信息详情
     *
     * @param dkcardxInventoryDetailInfo
     */
    void updateDkcardxInventoryDetailInfoById(DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo);

    /**
     * 根据主键id获取卡+X终端库存信息详情
     *
     * @param id
     * @return
     */
    DkcardxInventoryDetailInfo getDkcardxInventoryDetailInfoById(String id);

    /**
     * 处理历史导入的卡+X库存详情到原子商品上面
     */
    void handleKxImportHistoryToInventoryAtom();

    /**
     * 处理终端库存数据
     * @param id
     * @param reserveQuatity
     * @param currentInventory
     * @param totalInventory
     */
    void handleDkcardxInventoryDetailInfo(String detailId,String inventoryAtomId,Integer reserveQuatity,Integer currentInventory,Integer totalInventory,Long atomInventory);


    void delDkcardInventoryDetail(String id);
}
