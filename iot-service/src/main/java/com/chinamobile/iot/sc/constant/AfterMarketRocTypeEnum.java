package com.chinamobile.iot.sc.constant;

public enum AfterMarketRocTypeEnum {

    ORDER_REFUND(1,"商家处理中","用户申请退单","发起退款申请",0),

    REFUND_AGREE(2,"商城退款中","商家审核", "同意了退款申请",1),
    REFUND_REJECT(3, "退款取消","商家审核","拒绝了退款申请", 1),

    REFUND_SUCCESS(4, "退款成功", "退款结果", "完成退款", 2),
    REFUND_CANCEL(5, "退款取消", "退款结果", "取消了退款申请", 2),

    REFUND_FAILED(6, "退款取消", "退款结果", "退款取消", 3);
    /**
     * 订单状态码
     */
    private final Integer status;

    /**
     * 订单状态信息
     */
    private final String message;

    /**
     * 售后进程
     */
    private final String process;

    /**
     * 订单历史描述
     */
    private final String history;

    /**
     * 售后进程码
     */
    private final Integer processStatus;


    AfterMarketRocTypeEnum(Integer status, String message, String process, String history, Integer processStatus){
        this.status = status;
        this.message = message;
        this.process = process;
        this.history = history;
        this.processStatus = processStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMessage(){
        return message;
    }

    public static String getMessageDescribe(Integer status) {
        for(AfterMarketRocTypeEnum value : AfterMarketRocTypeEnum.values()){
            if(value.status.equals(status)){
                return value.message;
            }
        }
        return null;
    }

    public static String getProcessDescribe(Integer status) {
        for(AfterMarketRocTypeEnum value : AfterMarketRocTypeEnum.values()){
            if(value.status.equals(status)){
                return value.process;
            }
        }
        return null;
    }

    public static String getHistoryDescribe(Integer status) {
        for(AfterMarketRocTypeEnum value : AfterMarketRocTypeEnum.values()){
            if(value.status.equals(status)){
                return value.history;
            }
        }
        return null;
    }

    public static Integer getProcessStatus(Integer status) {
        for(AfterMarketRocTypeEnum value : AfterMarketRocTypeEnum.values()){
            if(value.processStatus.equals(status)){
                return value.processStatus;
            }
        }
        return null;
    }

}
