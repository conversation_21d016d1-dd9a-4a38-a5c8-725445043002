package com.chinamobile.iot.sc.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.RegionAndLocationConstant;
import com.chinamobile.iot.sc.dao.AtomOfferingInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cInfoMapper;
import com.chinamobile.iot.sc.dao.SkuOfferingInfoMapper;
import com.chinamobile.iot.sc.dao.SpuOfferingInfoMapper;
import com.chinamobile.iot.sc.excel.ExcelCardRelationImportHandler;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.Order2cInfoExample;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfo;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.SpuOfferingInfo;
import com.chinamobile.iot.sc.pojo.SpuOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.dto.ImportCardRelationDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductStatusSyncDTO;
import com.chinamobile.iot.sc.pojo.dto.RegionAndLocationDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductSyncDTO;
import com.chinamobile.iot.sc.pojo.dto.UpdateOrderOrgBizInfoDTO;
import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import com.chinamobile.iot.sc.service.CardRelationService;
import com.chinamobile.iot.sc.service.IImportDBService;
import com.chinamobile.iot.sc.service.IImportService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.FileUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentMap;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: ImportServiceImpl
 * @description: 存量订单数据导入接口实现类
 * @author: zyj
 * @create: 2022/3/2 14:35
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class ImportServiceImpl implements IImportService {
    //商品信息同步类型
    private static final String SYNC_OFFERING_INFO_TYPE = "0";
    //订单信息同步类型
    private static final String SYNC_2C_ORDER_INFO_TYPE = "1";
    //存量订单组织机构信息、操作员信息同步类型
    private static final String SYNC_ORDER_ORGBIZ_TYPE = "2";
    // 同步spu
    private static final String SYNC_SPU_TYPE = "SPU";
    // 同步sku
    private static final String SYNC_SKU_TYPE = "SKU";
    // 同步原子商品
    private static final String SYNC_ATOM_TYPE = "原子";

    @Value("${iot.secretKey}")
    private String iotSecretKey;
    @Resource
    private IImportDBService importDBService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;
    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;
    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private ExcelCardRelationImportHandler excelDeviceImportHandler;

    @Resource
    private FileUtils fileUtils;

    @Resource
    private CardRelationService cardRelationService;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Override
    public BaseAnswer<Map<String, String>> importIOTData(MultipartFile[] files, String type, String sign) {
        // 鉴权
        checkSign(sign);

        BaseAnswer<Map<String, String>> answer = new BaseAnswer<>();
        if(ObjectUtils.isNotEmpty(files)){
            ConcurrentMap<String, String> resMap = Maps.newConcurrentMap();
            answer.setData(resMap);
            for(MultipartFile file : files){
                if(SYNC_OFFERING_INFO_TYPE.equals(type)){
                    importDBService.analyseProductInfo(file, resMap);
                    log.info("分析、处理商品信息完成！");
                }else if(SYNC_2C_ORDER_INFO_TYPE.equals(type)){
                    importDBService.analyse2COrderInfo(file, resMap);
                    log.info("分析、处理订单信息完成！");
                }else if(SYNC_ORDER_ORGBIZ_TYPE.equals(type)){
                    importDBService.analyseOrderOrgBizInfo(file, resMap);
                    log.info("分析、处理组织机构信息完成！");
                }else {
                    log.error("type类型错误！,type：{}", type);
                    resMap.put(file.getName(), "type类型错误！,type："+type);
                    continue;
                }
            }
        }
        return answer;
    }

    /**
     * 使用excel将地市和区县信息存入redis
     *
     * @param file
     * @return
     */
    @Override
    public BaseAnswer importRegionAndLocation(MultipartFile file) {
        try {
            stringRedisTemplate.delete(RegionAndLocationConstant.REDIS_REGION_KEY);
            stringRedisTemplate.delete(RegionAndLocationConstant.REDIS_LOCATION_KEY);
            InputStream inputStream = null;
            if(file == null){
                ClassPathResource classPathResource = new ClassPathResource("excel/new_region20240329.xlsx");
                inputStream = classPathResource.getInputStream();
            }else {
                inputStream = file.getInputStream();
            }
            List<Object> list = EasyExcel.read(inputStream, RegionAndLocationDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            list.forEach(o -> {
                RegionAndLocationDTO dto = (RegionAndLocationDTO) o;
                if("区县级".equals(dto.getRegionLevel())){
                    stringRedisTemplate.opsForHash().put(RegionAndLocationConstant.REDIS_REGION_KEY,dto.getRegionID(),dto.getRegionName());
                }
                if("地市级".equals(dto.getRegionLevel())){
                    stringRedisTemplate.opsForHash().put(RegionAndLocationConstant.REDIS_LOCATION_KEY,dto.getRegionID(),dto.getRegionName());
                }
            });
        } catch (Exception e) {
            log.error("导入区县和地市信息出错",e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"导入区县和地市信息出错");
        }
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer importProductData(MultipartFile file, String sign) {
        // 鉴权
        checkSign(sign);
        BaseAnswer<Map<String, String>> answer = new BaseAnswer<>();
        Map<String, String> map = new HashMap<>();
        String originalFilename = file.getOriginalFilename();
        if(originalFilename.contains(SYNC_SPU_TYPE)){
            syncSpu(file,map);
        }else if(originalFilename.contains(SYNC_SKU_TYPE)){
            syncSku(file,map);
        }else if(originalFilename.contains(SYNC_ATOM_TYPE)){
            syncAtom(file,map);
        }else {
            log.error("同步商品文件名错误:{}",originalFilename);
            map.put("同步商品文件名错误！","originalFilename");
        }
        answer.setData(map);
        return answer;
    }

    /**
     * 存量商品数据割接-上架状态修改（Spu、Sku）
     * @param file
     * @return
     */
    @Override
    public BaseAnswer importProductStatus(MultipartFile file) {
        try {
            InputStream inputStream = file.getInputStream();
            List<ProductStatusSyncDTO> list = EasyExcel.read(inputStream, ProductStatusSyncDTO.class, null)
                .sheet(0).headRowNumber(1).doReadSync();
            for (ProductStatusSyncDTO dto : list) {
                List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(
                    new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeEqualTo(dto.getSpuCode())
                        .example()
                );
                if (spuOfferingInfos != null) {
                    for (SpuOfferingInfo spuOfferingInfo : spuOfferingInfos) {
                        spuOfferingInfo.setOfferingStatus("2");
                        spuOfferingInfoMapper.updateByPrimaryKey(spuOfferingInfo);
                    }
                }

                List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(
                    new SkuOfferingInfoExample().createCriteria()
                        .andOfferingCodeEqualTo(dto.getSkuCode())
                        .example()
                );
                if (skuOfferingInfos != null) {
                    for (SkuOfferingInfo skuOfferingInfo : skuOfferingInfos) {
                        skuOfferingInfo.setOfferingStatus("2");
                        skuOfferingInfoMapper.updateByPrimaryKey(skuOfferingInfo);
                    }
                }
            }
        } catch (IOException e) {
            // 该服务内部开发人员自己调用，异常不用理会
            throw new RuntimeException(e);
        }
        return BaseAnswer.success(null);
    }

    @Override
    public void importCardRelation(MultipartFile file,
                                         HttpServletRequest request,
                                         HttpServletResponse response) throws Exception  {
        response.setHeader("content-type", "application/octet-stream");
        if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
            response.setHeader("statecode","99999");
            response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }

        ExcelImportResult<ImportCardRelationDTO> result = ExcelUtils.importExcel(file.getInputStream(), 0, 1, excelDeviceImportHandler, ImportCardRelationDTO.class);
        List<ImportCardRelationDTO> failList = result.getFailList();

        if (CollectionUtils.isNotEmpty(failList)) {
            long millis = System.currentTimeMillis();
            String fileName = "导入卡数据(终端IMEI与临时iccid关系)".concat(millis + "-fail.xls");
            String failFile = System.getProperty("user.dir")
                    .concat(File.separator).concat("execl");
            File fileC = new File(failFile);
            if (!fileC.exists()){
                fileC.mkdirs();
            }
            String failFilePath = failFile.concat(File.separator).concat(fileName);
            FileOutputStream fos = new FileOutputStream(failFilePath);
            result.getFailWorkbook().write(fos);
            fos.close();
            File downErrorFile = new File(failFilePath);
            response.setHeader("statecode","99999");
            response.setHeader("message", "");
            fileUtils.downloadFile(downErrorFile, fileName, request, response);
            /*new Thread(()->{
                // 24小时候删除
                try {
                    Thread.sleep(86400000);
                } catch (InterruptedException e) {
                    log.error("删除错误信息文件休眠被打断");
                }
                downErrorFile.delete();
            }).start();*/
        }else {
            List<ImportCardRelationDTO> cardExcelImportList = result.getList();
            if (CollectionUtils.isEmpty(cardExcelImportList)) {
                response.setHeader("statecode","99999");
                response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            List<CardRelation> cardRelationList = new ArrayList<>();
            Date date = new Date();
            // 用于存储出现重复的数据
            List<String> reCopyList = new ArrayList<>();
            int cardSize = cardExcelImportList.size();
            for (int i = 0; i < cardSize; i++){
                ImportCardRelationDTO importCardRelationDTO = cardExcelImportList.get(i);
                String imei = importCardRelationDTO.getImei();
                String tempIccid = importCardRelationDTO.getTempIccid();
                String sn = importCardRelationDTO.getSn();
                CardRelation cardRelation = new CardRelation();
                BeanUtils.copyProperties(importCardRelationDTO,cardRelation);
                cardRelation.setId(BaseServiceUtils.getId());
                cardRelation.setCreateTime(date);
                cardRelation.setUpdateTime(date);
                cardRelationList.add(cardRelation);

                // 判断当前文档中是否有重复数据
                for (int j = i+1;j < cardSize;j++){
                    ImportCardRelationDTO cardRelationDTO = cardExcelImportList.get(j);
                    String sn1 = cardRelationDTO.getSn();
                    if (imei.equals(cardRelationDTO.getImei())){
                        reCopyList.add("imei:".concat(imei));
                        continue;
                    }
                    if (tempIccid.equals(cardRelationDTO.getTempIccid())) {
                        reCopyList.add("tempIccid:".concat(tempIccid));
                        continue;
                    }
                    if (StringUtils.isNotEmpty(sn) && StringUtils.isNotEmpty(sn1)){
                        if (sn.equals(sn1)){
                            reCopyList.add("sn:".concat(sn));
                            continue;
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(reCopyList)){
                String errMsg = "导入数据有错误数据:".concat(reCopyList.toString());
                response.setHeader("statecode","99999");
                response.setHeader("message", URLEncoder.encode(errMsg, "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            response.setHeader("statecode","00000");
            response.setHeader("message", URLEncoder.encode("导入成功", "UTF-8").replaceAll("\\+", "%20"));
            cardRelationService.batchAddCardRelation(cardRelationList);
        }
    }

    @Override
    public BaseAnswer updateOrderOrgBizInfo(MultipartFile file) {
        log.info("updateOrderOrgBizInfo开始执行");
        if(!ExcelUtils.suffixCheck(file.getOriginalFilename())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"文件格式错误");
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            List<Object> list = EasyExcel.read(inputStream, UpdateOrderOrgBizInfoDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            log.info("updateOrderOrgBizInfo读取数据条数:{}",list.size());
            list.forEach(o -> {
                UpdateOrderOrgBizInfoDTO updateOrderOrgBizInfoDTO = (UpdateOrderOrgBizInfoDTO) o;
                Order2cInfo order2cInfo = new Order2cInfo();
                order2cInfo.setOrderId(updateOrderOrgBizInfoDTO.getOrderId());
                order2cInfo.setOrderOrgBizCode(updateOrderOrgBizInfoDTO.getOrderOrgBizCode());
                order2cInfo.setOrgName(updateOrderOrgBizInfoDTO.getOrgName());
                order2cInfo.setOrgLevel(updateOrderOrgBizInfoDTO.getOrgLevel());
                order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
            });
            log.info("updateOrderOrgBizInfo执行完毕");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return BaseAnswer.success("成功");
    }

    private void syncAtom(MultipartFile file, Map<String, String> map) {
        try {
            InputStream inputStream = file.getInputStream();
            List<ProductSyncDTO> list = EasyExcel.read(inputStream, ProductSyncDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            List<AtomOfferingInfo> atomOfferingInfoList = new ArrayList<>();
            for (ProductSyncDTO dto : list) {
                try {
                    String offeringCode = dto.getOfferingCode();
                    AtomOfferingInfoExample example = new AtomOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(offeringCode).example();
                    List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(example);
                    if(!atomOfferingInfos.isEmpty()){
                        map.put(JSON.toJSONString(dto),"原子商品已存在,不可重复添加");
                        continue;
                    }
                    AtomOfferingInfo atomOfferingInfo = new AtomOfferingInfo();
                    atomOfferingInfo.setId(BaseServiceUtils.getId());
                    //excel缺少的字段设置一个默认值
                    atomOfferingInfo.setSpuId("");
                    atomOfferingInfo.setSpuCode("");
                    atomOfferingInfo.setSkuId("");
                    atomOfferingInfo.setSkuCode("");
                    atomOfferingInfo.setIsInventory(false);
                    atomOfferingInfo.setIsNotice(false);
                    atomOfferingInfo.setOfferingCode(dto.getOfferingCode());
                    atomOfferingInfo.setOfferingName(dto.getOfferingName());
                    atomOfferingInfo.setCreateTime(DateTimeUtil.getFormatDate(dto.getCreateTime(),DateTimeUtil.SLASH_DATE));
                    atomOfferingInfo.setUpdateTime(DateTimeUtil.getFormatDate(dto.getUpdateTime(),DateTimeUtil.SLASH_DATE));
                    //标记为已删除
                    atomOfferingInfo.setDeleteTime(new Date());
                    atomOfferingInfoList.add(atomOfferingInfo);
                } catch (ParseException e) {
                    log.error("单个原子商品解析错误,atom:{},error",JSON.toJSONString(dto),e);
                    map.put(JSON.toJSONString(dto),e.getMessage());
                }
            }
            if(!atomOfferingInfoList.isEmpty()){
                atomOfferingInfoMapper.batchInsert(atomOfferingInfoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            map.put("读取原子商品文件错误",e.getMessage());
        }
    }

    private void syncSku(MultipartFile file, Map<String, String> map) {
        try {
            InputStream inputStream = file.getInputStream();
            List<ProductSyncDTO> list = EasyExcel.read(inputStream, ProductSyncDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            List<SkuOfferingInfo> skuOfferingInfoList = new ArrayList<>();
            for (ProductSyncDTO dto : list) {
                try {
                    String offeringCode = dto.getOfferingCode();
                    SkuOfferingInfoExample example = new SkuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(offeringCode).example();
                    List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(example);
                    if(!skuOfferingInfos.isEmpty()){
                        map.put(JSON.toJSONString(dto),"SKU已存在,不可重复添加");
                        continue;
                    }
                    SkuOfferingInfo skuOfferingInfo = new SkuOfferingInfo();
                    skuOfferingInfo.setId(BaseServiceUtils.getId());
                    //excel缺少的字段设置一个默认值
                    skuOfferingInfo.setSpuId("");
                    skuOfferingInfo.setSpuCode("");
                    skuOfferingInfo.setOfferingCode(dto.getOfferingCode());
                    skuOfferingInfo.setOfferingName(dto.getOfferingName());
                    skuOfferingInfo.setCreateTime(DateTimeUtil.getFormatDate(dto.getCreateTime(),DateTimeUtil.SLASH_DATE));
                    skuOfferingInfo.setUpdateTime(DateTimeUtil.getFormatDate(dto.getUpdateTime(),DateTimeUtil.SLASH_DATE));
                    //标记为已删除
                    skuOfferingInfo.setDeleteTime(new Date());
                    skuOfferingInfoList.add(skuOfferingInfo);
                } catch (ParseException e) {
                    log.error("单个sku解析错误,sku:{},error",JSON.toJSONString(dto),e);
                    map.put(JSON.toJSONString(dto),e.getMessage());
                }
            }
            if(!skuOfferingInfoList.isEmpty()){
                skuOfferingInfoMapper.batchInsert(skuOfferingInfoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            map.put("读取sku文件错误",e.getMessage());
        }
    }

    private void syncSpu(MultipartFile file, Map<String, String> map) {
        try {
            InputStream inputStream = file.getInputStream();
            List<ProductSyncDTO> list = EasyExcel.read(inputStream, ProductSyncDTO.class, null)
                    .sheet(0).headRowNumber(1).doReadSync();
            List<SpuOfferingInfo> spuOfferingInfoList = new ArrayList<>();
            for (ProductSyncDTO dto : list) {
                try {
                    String offeringCode = dto.getOfferingCode();
                    SpuOfferingInfoExample example = new SpuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(offeringCode).example();
                    List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(example);
                    if(!spuOfferingInfos.isEmpty()){
                        map.put(JSON.toJSONString(dto),"SPU已存在,不可重复添加");
                        continue;
                    }
                    SpuOfferingInfo spuOfferingInfo = new SpuOfferingInfo();
                    //excel缺少的字段设置一个默认值
                    spuOfferingInfo.setOperId("-1");
                    spuOfferingInfo.setOfferingStatus("0");
                    spuOfferingInfo.setId(BaseServiceUtils.getId());
                    spuOfferingInfo.setOfferingCode(dto.getOfferingCode());
                    spuOfferingInfo.setOfferingName(dto.getOfferingName());
                    spuOfferingInfo.setCreateTime(DateTimeUtil.getFormatDate(dto.getCreateTime(),DateTimeUtil.SLASH_DATE));
                    spuOfferingInfo.setUpdateTime(DateTimeUtil.getFormatDate(dto.getUpdateTime(),DateTimeUtil.SLASH_DATE));
                    //标记为已删除
                    spuOfferingInfo.setDeleteTime(new Date());
                    spuOfferingInfoList.add(spuOfferingInfo);
                } catch (ParseException e) {
                    log.error("单个spu解析错误,spu:{},error",JSON.toJSONString(dto),e);
                    map.put(JSON.toJSONString(dto),e.getMessage());
                }
            }
            if(!spuOfferingInfoList.isEmpty()){
                spuOfferingInfoMapper.batchInsert(spuOfferingInfoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            map.put("读取spu文件错误",e.getMessage());
        }
    }

//    @Override
//    public BaseAnswer<Map<String, String>> <importIOTData(MultipartFile[] files, String type, String sign) {
//
//    }

    /**
     * IOT商城數據導入鑒權
     * @param sign
     * @return
     */
    private void checkSign(String sign){
        boolean isCheck = false;
        if(ObjectUtils.isNotEmpty(sign)){
            isCheck = DigestUtils.md5Hex(iotSecretKey + Constant.ENCODE_PWD).equals(sign);
        }
        if(!isCheck){
            throw new BusinessException(StatusConstant.DATA_IMPORT_AUTH_ERROR);
        }
    }
    
}
