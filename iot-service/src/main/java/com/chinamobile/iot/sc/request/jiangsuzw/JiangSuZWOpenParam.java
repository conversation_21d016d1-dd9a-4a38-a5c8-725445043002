package com.chinamobile.iot.sc.request.jiangsuzw;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/1/11 10:21
 * 请求江苏生态装维平台开通工单（除非标注了密文，否则传递明文）
 */
@Data
public class JiangSuZWOpenParam {

    @JSONField(name = "ROOT")
    private Root ROOT;

    @Data
    public static class Root{
        @JSONField(name = "HEADER")
        private JiangSuZWHeader HEADER;

        @JSONField(name = "BODY")
        private Body BODY;

    }

    @Data
    public static class Body{

        @JSONField(name = "BUSI_INFO")
        private BusiInfo BUSI_INFO;

    }

    @Data
    public static class BusiInfo{

        @JSONField(name = "CREATE_OPER_CODE")
        private String CREATE_OPER_CODE;

        @JSONField(name = "EMPLOYEE_NUM")
        private String EMPLOYEE_NUM;

        @JSONField(name = "CUSTOMER_MANAGER_NAME")
        private String CUSTOMER_MANAGER_NAME;

        @JSONField(name = "CUST_CODE")
        private String CUST_CODE;

        @JSONField(name = "CUST_NAME")
        private String CUST_NAME;

        @JSONField(name = "REMARKS")
        private String REMARKS;

        @JSONField(name = "ORDER_ID")
        private String ORDER_ID;

        @JSONField(name = "ORDERING_CHANNEL_SOURCE")
        private String ORDERING_CHANNEL_SOURCE;

        @JSONField(name = "ORDERING_CHANNEL_NAME")
        private String ORDERING_CHANNEL_NAME;

        @JSONField(name = "CREATE_TIME")
        private String CREATE_TIME;

        //密文
        @JSONField(name = "CONTACT_PERSON_NAME")
        private String CONTACT_PERSON_NAME;

        //密文
        @JSONField(name = "CONTACT_PHONE")
        private String CONTACT_PHONE;

        @JSONField(name = "PROVINCE_NAME")
        private String PROVINCE_NAME;

        @JSONField(name = "CITY_NAME")
        private String CITY_NAME;

        @JSONField(name = "COUNTY_NAME")
        private String COUNTY_NAME;

        @JSONField(name = "TOWN_NAME")
        private String TOWN_NAME;

        @JSONField(name = "ADDRESS_DETAIL")
        private String ADDRESS_DETAIL;

        @JSONField(name = "OFFERING_CLASS")
        private String OFFERING_CLASS;

        @JSONField(name = "ORDER_TYPE")
        private String ORDER_TYPE;

        @JSONField(name = "TOTAL_PRICE")
        private Long TOTAL_PRICE;

        @JSONField(name = "SPU_OFFERING_INFO")
        private SpuOfferingInfo SPU_OFFERING_INFO;

    }

    @Data
    public static class SpuOfferingInfo{

        @JSONField(name = "OFFERING_CODE")
        private String OFFERING_CODE;

        @JSONField(name = "OFFERING_NAME")
        private String OFFERING_NAME;

        @JSONField(name = "ACTION_TYPE")
        private String ACTION_TYPE = "A";

        @JSONField(name = "SKU_OFFERING_INFOS")
        private List<SkuOfferingInfo> SKU_OFFERING_INFOS;

    }

    @Data
    public static class SkuOfferingInfo{

        @JSONField(name = "OFFERING_CODE")
        private String OFFERING_CODE;

        @JSONField(name = "OFFERING_NAME")
        private String OFFERING_NAME;

        @JSONField(name = "ACTION_TYPE")
        private String ACTION_TYPE = "A";

        @JSONField(name = "QUANTITY")
        private String QUANTITY;

        @JSONField(name = "PRICE")
        private Long PRICE;

        @JSONField(name = "ATOM_OFFERING_INFOS")
        private List<AtomOfferingInfo> ATOM_OFFERING_INFOS;

    }

    @Data
    public static class AtomOfferingInfo{

        @JSONField(name = "OFFERING_CODE")
        private String OFFERING_CODE;

        @JSONField(name = "OFFERING_NAME")
        private String OFFERING_NAME;

        @JSONField(name = "ACTION_TYPE")
        private String ACTION_TYPE = "A";

        @JSONField(name = "QUANTITY")
        private String QUANTITY;

        @JSONField(name = "PRICE")
        private Long PRICE;

        @JSONField(name = "DEDUCT_PRICE")
        private Long DEDUCT_PRICE;

        //代客下单订单才有
        @JSONField(name = "TAX_SETTLE_PRICE")
        private Long TAX_SETTLE_PRICE;

    }


}

