package com.chinamobile.iot.sc.request.product;

import lombok.Data;

import java.util.List;

/**

 */
@Data
public class NavigationInfoDTO {

    /**
     * 导航目录信息
     */
    private List<Level1NavigationDTO> level1Navigation;

    @Data
    public static class Level1NavigationDTO{

        private String level1NavigationCode;

        private List<Level2NavigationDTO> level2Navigation;
    }

    @Data
    public static class Level2NavigationDTO{

        private String level2NavigationCode;

        private List<Level3NavigationDTO> level3Navigation;
    }

    @Data
    public static class Level3NavigationDTO{

        private String level3NavigationCode;
    }
}

