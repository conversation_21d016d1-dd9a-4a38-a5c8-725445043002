package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.dao.AtomOfferingInfoMapper;
import com.chinamobile.iot.sc.dao.SkuOfferingInfoMapper;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfo;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfo;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfoExample;
import com.chinamobile.iot.sc.pojo.handle.PriceCutHandle;
import com.chinamobile.iot.sc.service.DataCutService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/1/9 18:00
 * @description: 数据割接口接实现类
 **/
@Service
@Slf4j
public class DataCutServiceImpl implements DataCutService {

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchPriceCut(MultipartFile file) {

        try {
            InputStream inputStream = file.getInputStream();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String readStr;
            boolean success = true;
            List<PriceCutHandle> priceCutHandles = new ArrayList<>();
            while ((readStr = bufferedReader.readLine()) != null){
                //解析到的数据
                log.info("解析到的割接数据 priceCut:{}",readStr);
                if (StringUtils.isEmpty(readStr)){
                    continue;
                }
                Boolean jsonString = isJsonString(readStr);
                if (!jsonString){
                    log.info("解析的数据不是json字符串 notJson：{}",readStr);
                    success = false;
                    break;
                }
                //json字符串转json对象
                PriceCutHandle priceCutHandle = JSONObject.parseObject(readStr, PriceCutHandle.class);
                priceCutHandles.add(priceCutHandle);
            }
            if (success){
                Date date = new Date();
                priceCutHandles.forEach(priceCutHandle -> {
                    PriceCutHandle.SpuOfferingInfo spuOfferingInfo = priceCutHandle.getSpuOfferingInfo();
                    String spuOfferingCode = spuOfferingInfo.getSpuofferingCode();
                    List<PriceCutHandle.SpuOfferingInfo.SkuOfferingInfo> skuOfferingInfo = spuOfferingInfo.getSkuOfferingInfo();
                    //多个sku商品
                    skuOfferingInfo.forEach(skuOfferingInfoEn -> {
                        Long price = skuOfferingInfoEn.getPrice();
                        String skuOfferingCode = skuOfferingInfoEn.getOfferingCode();
                        //查询sku规格商品信息 修改销售目录价
                        SkuOfferingInfoExample skuOfferingInfoExample = new SkuOfferingInfoExample();
                        SkuOfferingInfoExample.Criteria criteria = skuOfferingInfoExample.createCriteria();
                        criteria.andSpuCodeEqualTo(spuOfferingCode).andOfferingCodeEqualTo(skuOfferingCode).example();
                        List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(skuOfferingInfoExample);
                        if (CollectionUtils.isNotEmpty(skuOfferingInfos) && skuOfferingInfos.size()==1){
                            SkuOfferingInfo skuOfferingInfoEntity = skuOfferingInfos.get(0);
                            skuOfferingInfoEntity.setPrice(price);
                            skuOfferingInfoEntity.setUpdateTime(date);
                            skuOfferingInfoMapper.updateByPrimaryKeySelective(skuOfferingInfoEntity);
                        }else {
                            log.info("规格商品sku为空或不是唯一");
                          throw new BusinessException("10116","规格商品sku为空或不是唯一");
                        }
                        List<PriceCutHandle.SpuOfferingInfo.SkuOfferingInfo.AtomOfferingInfo> atomOfferingInfo = skuOfferingInfoEn.getAtomOfferingInfo();
                        atomOfferingInfo.forEach(atomOfferingInfoEn ->{
                            Long atomSalePrice = atomOfferingInfoEn.getAtomSalePrice();
                            String atomOfferingCode = atomOfferingInfoEn.getOfferingCode();
                           //查询原子商品信息 修改原子商品销售目录价
                            AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                            AtomOfferingInfoExample.Criteria atomOfferingCriteria = atomOfferingInfoExample.createCriteria();
                            atomOfferingCriteria.andSpuCodeEqualTo(spuOfferingCode).andSkuCodeEqualTo(skuOfferingCode)
                                    .andOfferingCodeEqualTo(atomOfferingCode).example();
                            List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
                            if (CollectionUtils.isNotEmpty(atomOfferingInfos) && atomOfferingInfos.size()==1){
                                AtomOfferingInfo atomOfferingInfoEntity = atomOfferingInfos.get(0);
                                atomOfferingInfoEntity.setAtomSalePrice(atomSalePrice);
                                atomOfferingInfoEntity.setUpdateTime(date);
                                atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfoEntity);
                            }else {
                                log.info("原子商品atom为空或不是唯一");
                                throw new BusinessException("10117","原子商品atom为空或不是唯一");
                            }
                        });
                    });
                });
            }
            bufferedReader.close();
        } catch (Exception e) {
            throw new BusinessException(e);
        }
    }




    /**
     * 校验字符串是否是json字符串
     * @param jsonString
     * @return
     */
    private static Boolean isJsonString(String jsonString){
        String jsonRegular = "\\{.*\\}|\\[.*\\]";
        Pattern compile = Pattern.compile(jsonRegular);
        Matcher matcher = compile.matcher(jsonString);
        return matcher.matches();
    }
}
