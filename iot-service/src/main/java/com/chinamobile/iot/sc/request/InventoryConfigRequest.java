package com.chinamobile.iot.sc.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;


/**
 * @Author: YSC
 * @Date: 2021/11/12 16:48
 * @Description: 库存配置
 */
@Data
public class InventoryConfigRequest {
    /**
     * 原子商品ID
     */
    private String id;
    /**
     * 操作类型 0 增加 1 减少
     */
    private Integer operType;
    /**
     * 增减数
     */
    @Min(message = "最小取值0",value = 0)
    @Max(message = "最大取值9999",value = 9999)
    private Long amount;
    /**
     * 预警阈值
     */
    @Min(message = "最小取值0",value = 0)
    @Max(message = "最大取值9999",value = 9999)
    private Long threshold;
    /**
     * 是否发送短信通知
     */
    private Boolean isNotice;
}
