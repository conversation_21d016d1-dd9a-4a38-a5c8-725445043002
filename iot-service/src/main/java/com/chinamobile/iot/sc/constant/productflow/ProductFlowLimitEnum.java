package com.chinamobile.iot.sc.constant.productflow;

/**
 * 限制条件枚举类
 */
public enum ProductFlowLimitEnum {

    OVER_INVENTORY(1,"供应额度不足","下一环节审批角色转为知悉"),
    ;

    public Integer code;
    public String name;
    public String desc;

    ProductFlowLimitEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static boolean contains(Integer code){
        if(code == null){
            return false;
        }
        ProductFlowLimitEnum[] values = ProductFlowLimitEnum.values();
        for (ProductFlowLimitEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return true;
            }
        }
        return false;
    }

    public static ProductFlowLimitEnum fromCode(Integer code){
        if(code == null){
            return null;
        }
        ProductFlowLimitEnum[] values = ProductFlowLimitEnum.values();
        for (ProductFlowLimitEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }
}
