package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/26
 * @description 异常处理信息异常类型枚举类
 */
public enum ExpectTypeEnum {

    INNER_SYSTEM_REASON("0","内部系统原因"),
    OUTER_SYSTEM_REASON("1","外部系统原因"),
    CLIENT_REASON("2","客户原因"),
    PARTNER_REASON("3","合作伙伴原因"),
    OTHER_REASON("4","其他");

    /**
     * 异常类型
     */
    private String type;

    /**
     * 异常类型描述
     */
    private String desc;

    ExpectTypeEnum(String type,String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type) {
        for (ExpectTypeEnum value : ExpectTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }
}
