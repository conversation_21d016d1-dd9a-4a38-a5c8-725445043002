package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.AnnouncementAuditParam;
import com.chinamobile.iot.sc.pojo.param.AnnouncementListParam;
import com.chinamobile.iot.sc.pojo.param.AnnouncementVisibleParam;
import com.chinamobile.iot.sc.pojo.param.SaveAnnouncementParam;
import com.chinamobile.iot.sc.pojo.vo.AnnouncementVO;

import javax.validation.Valid;

public interface AnnouncementService {
    BaseAnswer saveAnnouncement(@Valid SaveAnnouncementParam param, LoginIfo4Redis loginIfo4Redis);


    BaseAnswer updateAnnouncement(@Valid SaveAnnouncementParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<AnnouncementVO>> announcementList(AnnouncementListParam param,LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<AnnouncementVO> announcementDetail(String id,String operate);

    BaseAnswer announcementVisible(@Valid AnnouncementVisibleParam param);

    BaseAnswer announcementAudit(@Valid AnnouncementAuditParam param);

    BaseAnswer<PageData<AnnouncementVO>> announcementPopupList(LoginIfo4Redis loginIfo4Redis);

    BaseAnswer announcementRead(String id, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<AnnouncementVO>> announcementList4Home(AnnouncementListParam param, LoginIfo4Redis loginIfo4Redis);
}
