package com.chinamobile.iot.sc.config;

import com.qiniu.common.Zone;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import static com.qiniu.common.Zone.Builder;


/**
 * 七牛配置信息
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix="qiniu")
@Configuration
public class QiniuConfig {
	private String queryHttpInner;
    private String queryHttpOuter;
    private String accessKey;
    private String secretKey;
    private String bucket;
    private String zoom;
    private Boolean useHttpsDomains = false;
    private MyZoom myzoom;
    private Boolean closed = false;

    @Data
    public static class MyZoom {
        private String upHttp;
        private String upHttps;
        private String rsHttp;
        private String rsfHttp;
        public Zone getMyZone(){
            return new Builder()
                    .upHttp(this.upHttp).upHttps(upHttps)
                    .rsfHttp(this.rsfHttp)
                    .rsHttp(this.rsHttp)
                    .build();
        }
    }
}
