package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.NewProductRequestManage;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.NewProductManageDetailsVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineRequestVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductRequestManageListVO;
import com.chinamobile.iot.sc.request.product.ProvinceCityVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13
 * @description 新产品引入申请管理service接口类
 */
public interface NewProductRequestManageService {

    /**
     * 查询第一次上线新产品
     * @param loginIfo4Redis
     * @return
     */
    List<NewProductOnlineRequestVO> listFirstNewProductOnlineRequest(LoginIfo4Redis loginIfo4Redis);

    /**
     * 根据主键id获取新产品
     * @param id
     * @return
     */
    NewProductRequestManage getNewProductRequestManageById(String id);

    /**
     * 分页查询合作伙伴上下架列表页面
     * @param onlineOfflineParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<NewProductOnlineOfflineVO> pageNewProductOnlineOffline(NewProductOnlineOfflineParam onlineOfflineParam,
                                                                    LoginIfo4Redis loginIfo4Redis);

    /**
     * 更新上下架相关的信息
     * @param id
     * @param onlineStatus
     * @param onlineOfflineRequestStatus
     * @param onlineOfflineCurrentHandlerUserId
     */
    void updateOnlineRelatedInfo(String id,
                                 String onlineStatus,
                                 Integer onlineOfflineRequestStatus,
                                 String onlineOfflineCurrentHandlerUserId);

    /**
     * 根据id更新spu和sku编码
     * @param spuAndSkuCodeParam
     */
    void updateSpuAndSkuOfferingCode(NewProductUpdateSpuAndSkuCodeParam spuAndSkuCodeParam);

    /**
     * 新增产品引入
     * @param addParam
     * @param userId
     * @param loginIfo4Redis
     */
    void insetNewProductRequestManage(NewProductRequestManageAddParam addParam,String userId, LoginIfo4Redis loginIfo4Redis);

    /**
     * 编辑为通过审核产品重新走流程
     * @param updateParam
     * @param loginIfo4Redis
     */
    void updateNewProductRequestManage(NewProductRequestManageUpdateParam updateParam,LoginIfo4Redis loginIfo4Redis);

    /**
     * 导入产品
     * @param file
     * @param userId
     * @param loginIfo4Redis
     */
    void importProductRequest(MultipartFile file,String userId,LoginIfo4Redis loginIfo4Redis);



    /**
     * 查询产品详情信息
     * @param id
     * @return
     */
    NewProductManageDetailsVO getProductManageDetails(String id);

    /**
     * 分页查询产品申请列表
     * @param manageWebParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<NewProductRequestManageListVO>  getPageManageApplyList(NewProductRequestManageWebParam manageWebParam,LoginIfo4Redis loginIfo4Redis);


    /**
     * 审核产品引入
     * @param auditParam
     * @param loginIfo4Redis
     */
    void insertProductAuditDispose(NewProductManageAuditParam auditParam, LoginIfo4Redis loginIfo4Redis,String ip);

    /**
     * 通过产品引入流程用户id或上下线流程用户id查询商品还在流程中的列表
     * @param userId
     * @return
     */
   Integer getProductByRequestUserIdOrOnlineUserId(String userId);

    /**
     * 查询城市及省份详情
     * @return
     */
    List<ProvinceCityVO> getProvinceCityList();
}
