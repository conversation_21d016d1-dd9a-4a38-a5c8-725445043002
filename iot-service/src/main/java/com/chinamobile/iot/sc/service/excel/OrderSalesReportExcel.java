package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/12/26 16:40
 * @description: 商城订单销售报表信息
 **/
@Data
public class OrderSalesReportExcel {


    /**
     * 订单号
     */
    @ExcelProperty(value = "业务订单流水号", index = 0)
    private String orderId;


    /**
     * 业务编码
     */
    @ExcelProperty(value = "业务编码", index = 1)
    private String businessCode;

    /**
     * 分销员电话
     */
    @ExcelProperty(value = "分销员账号（电话）", index = 2)
    private String distributorPhone;


    /**
     * 客户省份
     */
    @ExcelProperty(value = "客户省份", index = 3)
    private String clientProvince;

    /**
     * 客户地市
     */
    @ExcelProperty(value = "客户地市", index = 4)
    private String clientCity;

    /**
     * 客户区县
     */
    @ExcelProperty(value = "客户区县", index = 5)
    private String clientDistricts;


    /**
     * 分销员对应客户经理省工号
     */
    @ExcelProperty(value = "分销员对应客户经理省工号", index = 6)
    private String employeeNum;

    /**
     * 客户经理名称
     */
    @ExcelProperty(value = "客户经理姓名", index = 7)
    private String custMgName;

    /**
     * 客户经理电话
     */
    @ExcelProperty(value = "客户经理电话", index = 8)
    private String custMgPhone;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额", index = 9)
    private Double totalPrice;

    /**
     * 订单创建时间
     */
    @ExcelProperty(value = "订单创建时间", index = 10)
    private String createTime;

    /**
     * 收货人姓名
     */
    @ExcelProperty(value = "收货人姓名", index = 11)
    private String contactPersonName;

    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收货人手机号", index = 12)
    private String contactPhone;

    /**|
     * 收货人地址（包括省、地市、区县、地址）
     */
    @ExcelProperty(value = "收货人地址（包括省、地市、区县、地址）", index = 13)
    private String  consigneeAddress;

    /**
     * spu名称
     */
    @ExcelProperty(value = "商品名称（SPU）", index = 14)
    private String spuOfferingName;

    /**
     * sku名称
     */
    @ExcelProperty(value = "规格名称（SKU）", index = 15)
    private String skuOfferingName;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", index = 16)
    private String orderStatus;

}
