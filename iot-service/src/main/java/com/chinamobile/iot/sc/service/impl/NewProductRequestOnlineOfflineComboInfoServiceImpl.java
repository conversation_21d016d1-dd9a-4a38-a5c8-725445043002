package com.chinamobile.iot.sc.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.utils.GenerateOddNumbersUtil;
import com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineComboInfoMapper;
import com.chinamobile.iot.sc.dao.ext.NewProductRequestOnlineOfflineComboInfoMapperExt;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfo;
import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfoExample;
import com.chinamobile.iot.sc.pojo.param.NewProductOnlineOfflineParam;
import com.chinamobile.iot.sc.pojo.param.OnlineRequestParam;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineComboInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架套餐信息service实现类
 */
@Service
public class NewProductRequestOnlineOfflineComboInfoServiceImpl implements NewProductRequestOnlineOfflineComboInfoService {

    @Resource
    private NewProductRequestOnlineOfflineComboInfoMapper newProductRequestOnlineOfflineComboInfoMapper;

    @Resource
    private NewProductRequestOnlineOfflineComboInfoMapperExt newProductRequestOnlineOfflineComboInfoMapperExt;

    @Override
    public void saveComboInfo(NewProductRequestOnlineOfflineComboInfo comboInfo) {
        newProductRequestOnlineOfflineComboInfoMapper.insert(comboInfo);
    }

    @Override
    public void updateComboInfoByRequestIdAndComboInfoId(NewProductRequestOnlineOfflineComboInfo comboInfo) {
        NewProductRequestOnlineOfflineComboInfoExample example = new NewProductRequestOnlineOfflineComboInfoExample();
        example.createCriteria()
                .andIdEqualTo(comboInfo.getId())
                .andNewProductRequestIdEqualTo(comboInfo.getNewProductRequestId());
        newProductRequestOnlineOfflineComboInfoMapper.updateByExampleSelective(comboInfo,example);
    }

    @Override
    public void updateRequestPassById(int requestPass, String id) {
        NewProductRequestOnlineOfflineComboInfo comboInfo = new NewProductRequestOnlineOfflineComboInfo();
        comboInfo.setId(id);
        comboInfo.setRequestPass(requestPass);
        comboInfo.setUpdateTime(new Date());
        newProductRequestOnlineOfflineComboInfoMapper.updateByPrimaryKeySelective(comboInfo);
    }

    @Override
    public void updateOfflineReasonAndOfflineUser(String id,
                                                  String offlineReason,
                                                  String requestOfflineUserId) {
        NewProductRequestOnlineOfflineComboInfo comboInfo = new NewProductRequestOnlineOfflineComboInfo();
        comboInfo.setId(id);
        comboInfo.setOfflineReason(offlineReason);
        comboInfo.setRequestOfflineUserId(requestOfflineUserId);
        comboInfo.setUpdateTime(new Date());
        newProductRequestOnlineOfflineComboInfoMapper.updateByPrimaryKeySelective(comboInfo);
    }

    @Override
    public NewProductRequestOnlineOfflineComboInfo getComboInfoById(String id) {
        return newProductRequestOnlineOfflineComboInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<NewProductOnlineOfflineVO> listNewProductOnlineOfflineToSuperAdmin(Page page, NewProductOnlineOfflineParam onlineOfflineParam) {
        return newProductRequestOnlineOfflineComboInfoMapperExt.listNewProductOnlineOfflineToSuperAdmin(page,onlineOfflineParam);
    }

    @Override
    public List<NewProductOnlineOfflineVO> listNewProductOnlineOfflineToJudgeUser(Page page, NewProductOnlineOfflineParam onlineOfflineParam) {
        return newProductRequestOnlineOfflineComboInfoMapperExt.listNewProductOnlineOfflineToJudgeUser(page,onlineOfflineParam);
    }
}
