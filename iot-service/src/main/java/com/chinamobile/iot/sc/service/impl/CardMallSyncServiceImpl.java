package com.chinamobile.iot.sc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.constant.CardStatusEnum;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.constant.SellStatusEnum;
import com.chinamobile.iot.sc.constant.TerminalTypeEnum;
import com.chinamobile.iot.sc.dao.CardInventoryMainInfoMapper;
import com.chinamobile.iot.sc.dao.CardMallSyncMapper;
import com.chinamobile.iot.sc.dao.ext.CardMallSyncMapperExt;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.CardInfo;
import com.chinamobile.iot.sc.pojo.dto.NullCardHandleDTO;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.param.UpdateCardMallOrderToNullParam;
import com.chinamobile.iot.sc.pojo.param.UpdateCardOrderToNullParam;
import com.chinamobile.iot.sc.request.QrySubscribersRequest;
import com.chinamobile.iot.sc.response.iot.QrySubscribersResponse;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.service.CardMallSyncService;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9
 * @description 商城同步的卡信息service实现类
 */
@Service
public class CardMallSyncServiceImpl implements CardMallSyncService {

    @Resource
    private CardMallSyncMapper cardMallSyncMapper;

    @Resource
    private CardMallSyncMapperExt cardMallSyncMapperExt;

    @Resource
    private IOrder2CService order2CService;

    @Resource
    private CardInfoService cardInfoService;

    @Resource
    private CardInventoryMainInfoMapper cardInventoryMainInfoMapper;

    @Override
    public void batchInsertCardMallSync(List<CardMallSync> cardMallSyncList) {
        cardMallSyncMapper.batchInsert(cardMallSyncList);
    }

    @Override
    public void updateCardMallOrderToNull(UpdateCardMallOrderToNullParam updateCardMallOrderToNullParam) {
        cardMallSyncMapperExt.updateCardMallOrderToNull(updateCardMallOrderToNullParam);
    }

    @Override
    public void updateByNeed(CardMallSync cardMallSync, CardMallSyncExample cardMallSyncExample) {
        cardMallSyncMapper.updateByExampleSelective(cardMallSync,cardMallSyncExample);
    }

    @Override
    public List<CardMallSync> listCardMallSyncByNeed(CardMallSyncExample cardMallSyncExample) {
        return cardMallSyncMapper.selectByExample(cardMallSyncExample);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleNullCardCutOver() {
        List<NullCardHandleDTO> nullCardHandleDTOList = cardMallSyncMapperExt.listNullCardHandle();
        Date date = new Date();
        if (CollectionUtils.isNotEmpty(nullCardHandleDTOList)){
            nullCardHandleDTOList.forEach(nullCardHandleDTO -> {
                String id = nullCardHandleDTO.getId();
                String msisdn = nullCardHandleDTO.getMsisdn();
                String orderId = nullCardHandleDTO.getOrderId();
                String atomOrderId = nullCardHandleDTO.getAtomOrderId();
                Integer orderStatus = nullCardHandleDTO.getOrderStatus();
                // 订单状态为交易完成，码号状态刷新为已销售
                if (OrderStatusInnerEnum.ORDER_SUCCESS.getStatus() == (int)orderStatus){
                    CardMallSync cardMallSync = new CardMallSync();
                    cardMallSync.setId(id);
                    cardMallSync.setOrderId(orderId);
                    cardMallSync.setAtomOrderId(atomOrderId);
                    cardMallSync.setCardStatus(CardStatusEnum.SELL_SUCCESS.getType());
                    cardMallSync.setUpdateTime(date);
                    cardMallSyncMapper.updateByPrimaryKeySelective(cardMallSync);
                }
                // 订单状态为待发货,码号状态刷新为销售中
                else if (OrderStatusInnerEnum.WAIT_SEND.getStatus() == (int)orderStatus
                || OrderStatusInnerEnum.VALET_WAIT_DELIVER.getStatus() == (int)orderStatus
                || OrderStatusInnerEnum.COMPLETE.getStatus() == (int)orderStatus
                        || OrderStatusInnerEnum.ORDER_REFUND.getStatus() == (int)orderStatus
                        ||OrderStatusInnerEnum.ORDER_RETURN.getStatus() == (int)orderStatus
                        || OrderStatusInnerEnum.WAIT_RECEIVE.getStatus() == (int)orderStatus) {
                    CardMallSync cardMallSync = new CardMallSync();
                    cardMallSync.setId(id);
                    cardMallSync.setOrderId(orderId);
                    cardMallSync.setAtomOrderId(atomOrderId);
                    cardMallSync.setCardStatus(CardStatusEnum.SELLING.getType());
                    cardMallSync.setUpdateTime(date);
                    cardMallSyncMapper.updateByPrimaryKeySelective(cardMallSync);
                }
                // 订单状态为交易失败，OS调用3.1.19号卡信息查询接口，根据号卡的查询结果刷新码号状态。
                // 若查询结果为不可销售，码号状态刷新为不可销售。若查询结果为可销售，码号状态保持为未销售。
                else if (OrderStatusInnerEnum.ORDER_FAIL.getStatus() == (int)orderStatus) {
                    CardMallSync cardMallSync = new CardMallSync();
                    cardMallSync.setId(id);
                    cardMallSync.setUpdateTime(date);

                    QrySubscribersRequest qrySubscribersRequest = new QrySubscribersRequest();
                    qrySubscribersRequest.setOrderId(orderId);
                    List<QrySubscribersRequest.MsisdnInfo> msisdnList = new ArrayList<>();
                    QrySubscribersRequest.MsisdnInfo msisdnInfo = new QrySubscribersRequest.MsisdnInfo();
                    msisdnInfo.setMsisdn(msisdn);
                    msisdnList.add(msisdnInfo);
                    qrySubscribersRequest.setMsisdnInfos(msisdnList);
                    BaseAnswer baseAnswer = order2CService.qrySubscribers(qrySubscribersRequest);
                    QrySubscribersResponse response = (QrySubscribersResponse) baseAnswer.getData();
                    List<QrySubscribersResponse.MsisdnInfo> msisdnDetailInfoList = response.getMsisdnDetailInfos();
                    if (CollectionUtils.isEmpty(msisdnDetailInfoList)){
                        throw new BusinessException("10004","码号为".concat(msisdn).concat("没有码号查询结果返回"));
                    }

                    QrySubscribersResponse.MsisdnInfo msisdnInfoResponse = msisdnDetailInfoList.get(0);
                    String msisdnSstatus = msisdnInfoResponse.getStatus();
                    // 可售
                    if ("0".equals(msisdnSstatus)) {
                        cardMallSync.setCardStatus(CardStatusEnum.NOT_SELL.getType());
                    } else if ("9".equals(msisdnSstatus)) {
                        cardMallSync.setCardStatus(CardStatusEnum.CAN_NOT_SELL.getType());
                    }

                    cardMallSyncMapper.updateByPrimaryKeySelective(cardMallSync);
                }
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void testSelfCreateData(Integer dataCount,
                                   String cardType) {
        Date date = new Date();
        String dateStr = DateUtils.dateToStr(date,DateUtils.DATETIME_FORMAT_NO_SYMBOL);
        CardInfo cardInfo = new CardInfo();
        cardInfo.setId(dateStr);
        cardInfo.setTemplateId(dateStr);
        cardInfo.setTemplateName(dateStr);
        cardInfo.setBeId("200");
        cardInfo.setRegionId("6630");
        cardInfo.setCustCode(dateStr);
        cardInfo.setCustName(dateStr);
        cardInfo.setProjectId(dateStr);
        cardInfo.setReqFileName(dateStr);
        cardInfo.setOpenCardTime(dateStr);
        cardInfo.setCreateTime(date);
        cardInfo.setUpdateTime(date);
        cardInfoService.addCardInfo(cardInfo);

        CardInventoryMainInfo cardInventoryMainInfo = new CardInventoryMainInfo();
        cardInventoryMainInfo.setId(dateStr);
        cardInventoryMainInfo.setCustCode(dateStr);
        cardInventoryMainInfo.setCustName(dateStr);
        cardInventoryMainInfo.setTemplateId(dateStr);
        cardInventoryMainInfo.setTemplateName(dateStr);
        cardInventoryMainInfo.setBeId("200");
        cardInventoryMainInfo.setProviceName("广东");
        cardInventoryMainInfo.setRegionId("6630");
        cardInventoryMainInfo.setRegionName("揭阳市");
        cardInventoryMainInfo.setCardType(cardType);
        cardInventoryMainInfo.setReserveQuatity(0);
        cardInventoryMainInfo.setCurrentInventory(dataCount);
        cardInventoryMainInfo.setTotalInventory(dataCount);
        cardInventoryMainInfo.setInventoryThreshold(0);
        cardInventoryMainInfo.setIsNotice(false);
        cardInventoryMainInfo.setInventoryStatus("1");
        cardInventoryMainInfo.setCreateTime(date);
        cardInventoryMainInfo.setUpdateTime(date);
        cardInventoryMainInfoMapper.insert(cardInventoryMainInfo);


        List<CardMallSync> cardMallSyncList = new ArrayList<>();
        Long index = Long.parseLong(dateStr.concat(0 + ""));

        for (int i = 0;i<dataCount;i++){
            CardMallSync cardMallSync = new CardMallSync();
            cardMallSync.setId((index+i)+"");
            cardMallSync.setCardInfoId(dateStr);
            cardMallSync.setCardInventoryMainId(dateStr);
            cardMallSync.setCardType(cardType);
            cardMallSync.setMsisdn((index+i)+"");
            cardMallSync.setIccid((index+i)+"");
            cardMallSync.setCardStatus("0");
            cardMallSync.setCreateTime(date);
            cardMallSync.setUpdateTime(date);
            cardMallSyncList.add(cardMallSync);
        }

        cardMallSyncMapper.batchInsert(cardMallSyncList);
    }
}
