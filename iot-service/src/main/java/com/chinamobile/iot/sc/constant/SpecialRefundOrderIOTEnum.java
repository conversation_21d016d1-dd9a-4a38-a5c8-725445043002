package com.chinamobile.iot.sc.constant;


import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023/1/10 16:29
 * @description: 特殊退货退款订单商城同步反馈状态
 **/
public enum SpecialRefundOrderIOTEnum {

    /**
     * 待退款
     */
    SPECIAL_ORDER_PENDING_REFUND("1", "待退款"),
    /**
     * 退款中
     */
    SPECIAL_ORDER_UNDER_REFUND("2", "退款中"),
    /**
     * 退款成功
     */
    SPECIAL_ORDER_SUCCESSFUL_REFUND("3", "退款成功"),
    /**
     * 退款取消
     */
    SPECIAL_ORDER_CANCELLATION_REFUND("4", "退款取消"),
    /**
     * 退款取消
     */
    SPECIAL_ORDER_PORTION_CANCELLATION_REFUND("5", "部分退款取消"),
    /**
     * 退款取消
     */
    SPECIAL_ORDER_PORTION_SUCCESSFUL_REFUND("6", "部分退款成功");

    /**
     * 订单状态码
     */
    private final String status;
    /**
     * 订单状态信息
     */
    private final String message;

    SpecialRefundOrderIOTEnum(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 获取状态信息
     * @param status
     * @return
     */
    public static String getSpecialOrderStatusMessage(String status){
        SpecialRefundOrderIOTEnum[] values = SpecialRefundOrderIOTEnum.values();
        for (SpecialRefundOrderIOTEnum value : values) {
            if (value.getStatus().equals(status)){
                return value.getMessage();
            }
        }
        return "";
    }
}
