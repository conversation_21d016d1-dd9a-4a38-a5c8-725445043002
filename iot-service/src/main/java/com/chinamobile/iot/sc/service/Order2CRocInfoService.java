package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.vo.LatestRefundRocVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1
 * @description 订单退货退款换货记录service接口类
 */
public interface Order2CRocInfoService {

    /**
     * 根据订单id获取最新的仅退款信息
     *
     * @param orderId
     * @return
     */
    LatestRefundRocVO getLatestRefundRocByOrderId(String orderId);

    BaseAnswer initRocId();
}
