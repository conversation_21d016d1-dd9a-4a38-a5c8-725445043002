package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo;
import com.chinamobile.iot.sc.pojo.param.NewProductRequestHandlerInfoParam;
import com.chinamobile.iot.sc.pojo.vo.NewProductRequestHandlerInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入相关流程处理信息service接口类
 */
public interface NewProductRequestHandlerInfoService {

    /**
     * 新增流程信息
     * @param handlerInfo
     */
    void saveHandlerInfo(NewProductRequestHandlerInfo handlerInfo);

    /**
     * 获取环节中上一次处理的信息
     * @param handlerInfoParam
     * @return
     */
    NewProductRequestHandlerInfo getLastHandlerInfo(NewProductRequestHandlerInfoParam handlerInfoParam);


    /**
     * 查询处理人的流程信息（来源主id+当前处理人id唯一）
     * @param flowSourceId
     * @param flowType
     * @param nextUserId
     * @return
     */
    NewProductRequestHandlerInfo getReviewDetails(String flowSourceId,String flowType, String nextUserId);

    /**
     * 修改流程处理表信息
     * @param handlerInfo
     */
    void updateHandlerInfo(NewProductRequestHandlerInfo handlerInfo);
    /**
     * 批量新增流程信息
     * @param handlerInfoList
     */
    void batchSaveHandlerInfo(List<NewProductRequestHandlerInfo> handlerInfoList);

    /**
     * 查询流程信息列表
     * @param flowSourceId
     * @param flowType
     * @return
     */
    List<NewProductRequestHandlerInfoVO> listNewProductRequestHandlerInfo(String flowSourceId,
                                                                          String flowType);

    /**
     * 通过产品主键id查询流程处理
     * @param flowSourceId
     * @param flowType
     * @return
     */
    List<NewProductRequestHandlerInfo> getListHandlerInfo(String flowSourceId,String flowType);

}
