package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.BenefitOffering;
import com.chinamobile.iot.sc.pojo.entity.BenefitOfferingExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19
 * @description IoT省内融合包信息关联商品信息service接口类
 */
public interface BenefitOfferingService {

    /**
     * 根据需要获取IoT省内融合包信息关联商品信息
     * @param benefitOfferingExample
     * @return
     */
    List<BenefitOffering> listBenefitOfferingByNeed(BenefitOfferingExample benefitOfferingExample);

    /**
     * 批量新增IoT省内融合包信息关联商品信息
     * @param benefitOfferingList
     */
    void batchAddBenefitOffering(List<BenefitOffering> benefitOfferingList);

    /**
     * 根据需要更新IoT省内融合包信息关联商品信息
     * @param benefitOffering
     * @param benefitOfferingExample
     */
    void updateBenefitOfferingByNeed(BenefitOffering benefitOffering,
                                     BenefitOfferingExample benefitOfferingExample);

    /**
     * 根据id更新IoT省内融合包信息关联商品信息
     * @param benefitOffering
     */
    void updateBenefitOfferingById(BenefitOffering benefitOffering);

}
