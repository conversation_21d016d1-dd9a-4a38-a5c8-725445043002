package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfig;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfigExample;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/10
 * @description 代客下单卡+X库存详细配置表（硬件终端详情）service接口类
 */
public interface DkcardxInventoryConfigService {

    /**
     * 根据需要获取配置列表
     * @param example
     * @return
     */
    List<DkcardxInventoryConfig> getDkcardxInventoryConfigByNeed(DkcardxInventoryConfigExample example);

    /**
     * 批量新增配置列表
     * @param dkcardxInventoryConfigList
     */
    void batchAddDkcardxInventoryConfig(List<DkcardxInventoryConfig> dkcardxInventoryConfigList);

    /**
     * 根据需要更新所需字段
     * @param dkcardxInventoryConfig
     * @param example
     */
    void updateDkcardxInventoryConfigByNeed(DkcardxInventoryConfig dkcardxInventoryConfig,
                                            DkcardxInventoryConfigExample example);
}
