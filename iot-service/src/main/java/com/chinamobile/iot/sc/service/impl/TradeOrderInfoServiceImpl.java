package com.chinamobile.iot.sc.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.constant.BusinessCodeEnum;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.TradeOrderInfoMapper;
import com.chinamobile.iot.sc.dao.ext.TradeOrderInfoMapperExt;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.FinancingOrderStatusEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.TradeOrderInfo;
import com.chinamobile.iot.sc.pojo.TradeOrderInfoExample;
import com.chinamobile.iot.sc.pojo.dto.FinancingBillDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.TradeOrderDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.TradeOrderInfoDTO;
import com.chinamobile.iot.sc.pojo.param.TradeOrderInfoParam;
import com.chinamobile.iot.sc.pojo.vo.TradeOrderInfoVO;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.service.OrderBaoliService;
import com.chinamobile.iot.sc.service.TradeOrderInfoService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.chinamobile.iot.sc.common.BaseConstant.*;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.UN_PERMISSION;
import static com.chinamobile.iot.sc.util.excel.EasyExcelUtils.setEasyExcelDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13
 * @description 贸易订单信息service实现类
 */
@Service
public class TradeOrderInfoServiceImpl implements TradeOrderInfoService {

    @Resource
    private TradeOrderInfoMapper tradeOrderInfoMapper;

    @Resource
    private TradeOrderInfoMapperExt tradeOrderInfoMapperExt;

    @Resource
    private OrderBaoliService orderBaoliService;

    @Resource
    private AreaDataConfig areaDataConfig;


    @Value("${iot.encodeKey}")
    private String encodeKey;
    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Override
    public List<TradeOrderInfo> listTradeOrderInfoByContract(String contractNum) {
        TradeOrderInfoExample tradeOrderInfoExample = new TradeOrderInfoExample();
        tradeOrderInfoExample.createCriteria()
                .andContractNumEqualTo(contractNum);
        return tradeOrderInfoMapper.selectByExample(tradeOrderInfoExample);
    }

    @Override
    public void batchInsert(List<TradeOrderInfo> tradeOrderInfoList) {
        tradeOrderInfoMapper.batchInsert(tradeOrderInfoList);
    }

    @Override
    public PageData<TradeOrderInfoVO> pageTradeOrderInfo(TradeOrderInfoParam tradeOrderInfoParam,
                                                         LoginIfo4Redis loginIfo4Redis) {
        PageData<TradeOrderInfoVO> pageData = new PageData<>();
        Integer pageNum = tradeOrderInfoParam.getPageNum();
        Integer pageSize = tradeOrderInfoParam.getPageSize();

        Page<TradeOrderInfoDTO> page = new Page<>(pageNum, pageSize);
        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 是否保理账号
        if (BaseConstant.PARTNER_BAOLI.equals(roleType)) {
            tradeOrderInfoParam.setUserId(userId);
        }

        Boolean isPrimary = loginIfo4Redis.getIsPrimary();
        // 如果是主合作伙伴
        if (isPrimary != null && isPrimary) {
            List<String> downUserIdList = orderBaoliService.listDownUserId(userId);
            tradeOrderInfoParam.setDownUserIdList(downUserIdList);
        }

        List<TradeOrderInfoDTO> tradeOrderInfoDTOList = tradeOrderInfoMapperExt.listTradeOrderInfo(page, tradeOrderInfoParam);

        List<TradeOrderInfoVO> tradeOrderInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tradeOrderInfoDTOList)) {
            tradeOrderInfoDTOList.stream().forEach(tradeOrderInfoDTO -> {
                TradeOrderInfoVO tradeOrderInfoVO = new TradeOrderInfoVO();
                BeanUtils.copyProperties(tradeOrderInfoDTO, tradeOrderInfoVO);

                Integer baoliStatus = tradeOrderInfoDTO.getBaoliStatus();
                String baoliStatusName = FinancingOrderStatusEnum.getDesc(baoliStatus);
                tradeOrderInfoVO.setBaoliStatusName(baoliStatusName);

                String createTimeStr = DateUtils.dateToStr(tradeOrderInfoDTO.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT);
                tradeOrderInfoVO.setCreateTimeStr(createTimeStr);

                BigDecimal thousand = new BigDecimal(1000);
                Long tradePrice = tradeOrderInfoDTO.getTradePrice();
                BigDecimal tradePriceDec = new BigDecimal(tradePrice).divide(thousand, 2, RoundingMode.HALF_UP);
                tradeOrderInfoVO.setTradePriceDec(tradePriceDec);

                Long invoicePrice = tradeOrderInfoDTO.getInvoicePrice();
                if (invoicePrice != null) {
                    BigDecimal invoicePriceDec = new BigDecimal(invoicePrice).divide(thousand, 2, RoundingMode.HALF_UP);
                    tradeOrderInfoVO.setInvoicePriceDec(invoicePriceDec);
                }

                Long maxFinancingPrice = tradeOrderInfoDTO.getMaxFinancingPrice();
                if (maxFinancingPrice != null) {
                    BigDecimal maxFinancingPriceDec = new BigDecimal(maxFinancingPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                    tradeOrderInfoVO.setMaxFinancingPriceDec(maxFinancingPriceDec);
                }

                Long requestFinancingPrice = tradeOrderInfoDTO.getRequestFinancingPrice();
                if (requestFinancingPrice != null) {
                    BigDecimal requestFinancingPriceDec = new BigDecimal(requestFinancingPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                    tradeOrderInfoVO.setRequestFinancingPriceDec(requestFinancingPriceDec);
                }

                tradeOrderInfoVOList.add(tradeOrderInfoVO);

            });
        }
        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(tradeOrderInfoVOList);

        return pageData;
    }

    @Override
    public void exportTradeOrderDetail(String tradeNo, LoginIfo4Redis loginIfo4Redis) throws Exception {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType)){
            throw new BusinessException(UN_PERMISSION);
        }

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();

        //抽取为单独的方法，在申请保理的时候复用
        List<TradeOrderDetailDTO> tradeOrderDetailDTOList = getTradeOrderDetailDTOS(tradeNo);
        if (CollectionUtils.isEmpty(tradeOrderDetailDTOList)){
            throw new BusinessException(BaseErrorConstant.INVALID_DATA_TO_EXPORT);
        }

        // 贸易订单明细
        EasyExcelDTO easyExcelDTO = setEasyExcelDTO(0, "贸易订单明细", "list",
                tradeOrderDetailDTOList, null);

        easyExcelDTOList.add(easyExcelDTO);

        String excelName = "贸易订单导出明细";
        excelName = URLEncoder.encode(excelName, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource("template/trade_order_detail.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        // 导出贸易订单明细
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());
    }

    @Override
    public BaseAnswer<List<SimpleItemDTO>> getBaoliStatusList() {
        List<SimpleItemDTO> list = new ArrayList<>();
        FinancingOrderStatusEnum[] values = FinancingOrderStatusEnum.values();

        for (FinancingOrderStatusEnum value : values) {
            String spuOfferingClass = value.getCode() + "";
            SimpleItemDTO dto = new SimpleItemDTO();
            dto.setCode(spuOfferingClass);
            dto.setName(value.getDesc());
            list.add(dto);
        }
        return BaseAnswer.success(list);
    }

    @Override
    public void exportFinancingBillDetail(String tradeNo, LoginIfo4Redis loginIfo4Redis) throws Exception {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType)){
            throw new BusinessException(UN_PERMISSION);
        }
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();


        List<FinancingBillDetailDTO> financingBillDetailDTOList = listFinancingBillDetail(tradeNo);

        if (CollectionUtils.isEmpty(financingBillDetailDTOList)){
            throw new BusinessException(BaseErrorConstant.INVALID_DATA_TO_EXPORT);
        }
        // 财务台账详情
        EasyExcelDTO easyExcelDTO = setEasyExcelDTO(0, "财务台账导出明细", "list",
                financingBillDetailDTOList, null);

        easyExcelDTOList.add(easyExcelDTO);

        String excelName = "财务台账导出明细";
        excelName = URLEncoder.encode(excelName, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource("template/trade_financing_detail.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        // 导出财务台账明细
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());
    }

    public List<TradeOrderDetailDTO> getTradeOrderDetailDTOS(String tradeNo) {
        List<TradeOrderDetailDTO> tradeOrderDetailDTOList = tradeOrderInfoMapperExt.listTradeOrderDetail(tradeNo);
        if (CollectionUtils.isNotEmpty(tradeOrderDetailDTOList)) {
            BigDecimal thousand = new BigDecimal(1000);
            tradeOrderDetailDTOList.stream().forEach(tradeOrderDetailDTO -> {
                // 数据库的日期格式是 yyyyMMddHHmmss,转换为 yyyy-MM-dd HH:mm:ss
                Date date = DateUtil.parse(tradeOrderDetailDTO.getCreateTime());
                tradeOrderDetailDTO.setCreateTime(DateUtil.formatDateTime(date));

                Integer orderStatus = tradeOrderDetailDTO.getOrderStatus();
                String orderStatusName = OrderStatusInnerEnum.getDescribe(orderStatus);
                tradeOrderDetailDTO.setOrderStatusName(orderStatusName);

                String atomOfferingClass = tradeOrderDetailDTO.getAtomOfferingClass();
                tradeOrderDetailDTO.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(tradeOrderDetailDTO.getSpuOfferingClass()));
                if (AtomOfferingClassEnum.S.name().equals(atomOfferingClass)) {
                    if(SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(tradeOrderDetailDTO.getSpuOfferingClass())){
                        tradeOrderDetailDTO.setAtomOfferingClass("软件功能费");
                    }else{
                        tradeOrderDetailDTO.setAtomOfferingClass("软件");
                    }

                } else if (AtomOfferingClassEnum.H.name().equals(atomOfferingClass)) {
                    if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(tradeOrderDetailDTO.getSpuOfferingClass())) {
                        tradeOrderDetailDTO.setAtomOfferingClass("合同履约类硬件");
                    } else {
                        tradeOrderDetailDTO.setAtomOfferingClass("代销类硬件");
                    }

                } else if (AtomOfferingClassEnum.O.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.O.getDescribe());
                } else if (AtomOfferingClassEnum.D.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.D.getDescribe());
                } else if (AtomOfferingClassEnum.P.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.P.getDescribe());
                }else if (AtomOfferingClassEnum.F.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.F.getDescribe());
                } else if (AtomOfferingClassEnum.K.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.K.getDescribe());
                } else if (AtomOfferingClassEnum.C.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.C.getDescribe());
                } else if (AtomOfferingClassEnum.X.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.X.getDescribe());
                }else if (AtomOfferingClassEnum.M.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.M.getDescribe());
                }else if (AtomOfferingClassEnum.A.name().equals(atomOfferingClass)) {
                    tradeOrderDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.A.getDescribe());
                }

                //解密并过滤地区
                String encryptedArea = tradeOrderDetailDTO.getDeliveryArea();
                String decryptedArea = IOTEncodeUtils.decryptSM4(encryptedArea, iotSm4Key, iotSm4Iv);
                tradeOrderDetailDTO.setDeliveryArea(decryptedArea);

                //解密客户编码
                tradeOrderDetailDTO.setCustCode(IOTEncodeUtils.decryptSM4(tradeOrderDetailDTO.getCustCode(), iotSm4Key, iotSm4Iv));

                String orderDeductPrice = tradeOrderDetailDTO.getOrderDeductPrice();
                if (StringUtils.isNotEmpty(orderDeductPrice)) {
                    String deductEncode = IOTEncodeUtils.decryptSM4(orderDeductPrice, iotSm4Key, iotSm4Iv);
                    BigDecimal orderDeductPriceDec = new BigDecimal(deductEncode).divide(thousand, 2, RoundingMode.HALF_UP);
                    tradeOrderDetailDTO.setOrderDeductPrice(orderDeductPriceDec + "");
                }

                Date orderFinishTime = tradeOrderDetailDTO.getOrderFinishTime();
                tradeOrderDetailDTO.setOrderFinishTimeStr(DateUtils.dateToStr(orderFinishTime, DateUtils.DEFAULT_DATETIME_FORMAT));

                //个人客户所属省份，去掉“移动”后缀
                String province = tradeOrderDetailDTO.getProvince();
                if (StringUtils.isNotEmpty(province) && province.contains("移动")) {
                    tradeOrderDetailDTO.setProvince(province.substring(0, province.indexOf("移动")));
                }

                String orgName = tradeOrderDetailDTO.getOrgName();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(orgName)) {
                    String[] split = orgName.split("-");
                    int orgNameLength = split.length;
                    if (orgNameLength >= 1 && StringUtils.isNotEmpty(split[0]) && split[0].contains("移动")) {
                        tradeOrderDetailDTO.setOrgProvince(split[0].substring(0, province.indexOf("移动")));
                    }
                }

                Long baoliSettlePrice = tradeOrderDetailDTO.getBaoliSettlePrice();
                BigDecimal baoliSettlePriceDec = new BigDecimal(baoliSettlePrice).divide(thousand, 2, RoundingMode.HALF_UP);
                tradeOrderDetailDTO.setBaoliSettlePriceDec(baoliSettlePriceDec);

                tradeOrderDetailDTO.setBusinessCode(BusinessCodeEnum.getChnName(tradeOrderDetailDTO.getBusinessCode()));
            });
        }
        return tradeOrderDetailDTOList;
    }


    private List<FinancingBillDetailDTO> listFinancingBillDetail(String tradeNo) {
        List<FinancingBillDetailDTO> financingBillDetailDTOList = tradeOrderInfoMapperExt.listFinancingBillDetail(tradeNo);
        if (CollectionUtils.isNotEmpty(financingBillDetailDTOList)) {
            BigDecimal thousand = new BigDecimal(1000);
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            Map<Object, Object> regionCodeNameMap = areaDataConfig.getRegionCodeNameMap();
            Map<Object, Object> regionNameCodeMap = areaDataConfig.getRegionNameCodeMap();
            financingBillDetailDTOList.stream().forEach(financingBillDetailDTO -> {

                String custName = financingBillDetailDTO.getCustName();
                custName = IOTEncodeUtils.decryptSM4(custName, iotSm4Key, iotSm4Iv);
                financingBillDetailDTO.setCustName(custName);
                // 解密客户编码
                String custCode = financingBillDetailDTO.getCustCode();
                custCode = IOTEncodeUtils.decryptSM4(custCode, iotSm4Key, iotSm4Iv);
                financingBillDetailDTO.setCustCode(custCode);

                // 数据库的日期格式是 yyyyMMddHHmmss,转换为 yyyy-MM-dd HH:mm:ss
                Date date = DateUtil.parse(financingBillDetailDTO.getCreateTime());
                financingBillDetailDTO.setCreateTime(DateUtil.formatDateTime(date));

                Integer orderStatus = financingBillDetailDTO.getOrderStatus();
                String orderStatusName = OrderStatusInnerEnum.getDescribe(orderStatus);
                financingBillDetailDTO.setOrderStatusName(orderStatusName);

                String atomOfferingClass = financingBillDetailDTO.getAtomOfferingClass();
                financingBillDetailDTO.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(financingBillDetailDTO.getSpuOfferingClass()));
                if (AtomOfferingClassEnum.S.name().equals(atomOfferingClass)) {
                    if(SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(financingBillDetailDTO.getSpuOfferingClass())){
                        financingBillDetailDTO.setAtomOfferingClass("软件功能费");
                    }else{
                        financingBillDetailDTO.setAtomOfferingClass("软件");
                    }

                } else if (AtomOfferingClassEnum.H.name().equals(atomOfferingClass)) {
                    if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(financingBillDetailDTO.getSpuOfferingClass())) {
                        financingBillDetailDTO.setAtomOfferingClass("合同履约类硬件");
                    } else {
                        financingBillDetailDTO.setAtomOfferingClass("代销类硬件");
                    }

                } else if (AtomOfferingClassEnum.O.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.O.getDescribe());
                } else if (AtomOfferingClassEnum.D.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.D.getDescribe());
                } else if (AtomOfferingClassEnum.P.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.P.getDescribe());
                } else if (AtomOfferingClassEnum.F.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.F.getDescribe());
                } else if (AtomOfferingClassEnum.K.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.K.getDescribe());
                } else if (AtomOfferingClassEnum.C.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.C.getDescribe());
                } else if (AtomOfferingClassEnum.X.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.X.getDescribe());
                }else if (AtomOfferingClassEnum.M.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.M.getDescribe());
                }else if (AtomOfferingClassEnum.A.name().equals(atomOfferingClass)) {
                    financingBillDetailDTO.setAtomOfferingClass(AtomOfferingClassEnum.A.getDescribe());
                }

                String beId = financingBillDetailDTO.getBeId();
                if (StringUtils.isNotEmpty(beId)){
                    Object provinceName =provinceCodeNameMap.get(beId);
                    String provinceNameStr = "";
                    if (provinceName != null){
                        if ("471".equals(beId)){
                            financingBillDetailDTO.setProvince("内蒙古");
                        }else {
                            provinceNameStr = provinceName+"";
                            if ((provinceNameStr).contains("移动")) {
                                provinceNameStr = provinceNameStr.substring(0, provinceNameStr.indexOf("移动"));
                            }
                            financingBillDetailDTO.setProvince(provinceNameStr);
                        }


                        Object locationName = locationCodeNameMap.get(financingBillDetailDTO.getLocation());
                        if (locationName != null){
                            financingBillDetailDTO.setCityName(locationName+"");
                            String regionID = financingBillDetailDTO.getRegionID();
                            if (StringUtils.isNotEmpty(regionID) && "10003329".equals(regionID)) {
                                financingBillDetailDTO.setRegionName("綦江区");
                            } else {
                                financingBillDetailDTO.setRegionName(StringUtils.isNotEmpty(regionID) ? (String) regionCodeNameMap.get(regionID) : null);
                            }
                        }
                    }
                }

                //解密并过滤地区
                String encryptedArea = financingBillDetailDTO.getDeliveryArea();
                String decryptedArea = IOTEncodeUtils.decryptSM4(encryptedArea, iotSm4Key, iotSm4Iv);
                financingBillDetailDTO.setDeliveryArea(decryptedArea);

                String detailAddr = "";
                String addr1 = financingBillDetailDTO.getAddr1();
                if (StringUtils.isNotEmpty(addr1)) {
                    addr1 = IOTEncodeUtils.decryptSM4(addr1, iotSm4Key, iotSm4Iv);
                    detailAddr = detailAddr.concat(addr1);
                }
                String addr2 = financingBillDetailDTO.getAddr2();
                if (StringUtils.isNotEmpty(addr2)){
                    addr2 = IOTEncodeUtils.decryptSM4(addr2, iotSm4Key, iotSm4Iv);
                    detailAddr = detailAddr.concat(addr2);
                }
                String addr3 = financingBillDetailDTO.getAddr3();
                if (StringUtils.isNotEmpty(addr3)){
                    addr3 = IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv);
                    detailAddr = detailAddr.concat(addr3);
                }
                String addr4 = financingBillDetailDTO.getAddr4();
                if (StringUtils.isNotEmpty(addr4)){
                    addr4 = IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv);
                    detailAddr = detailAddr.concat(addr4);
                }

                financingBillDetailDTO.setContactPersonAddr(detailAddr);

                financingBillDetailDTO.setContactPersonName(IOTEncodeUtils.decryptSM4(financingBillDetailDTO.getContactPersonName(), iotSm4Key, iotSm4Iv));

                financingBillDetailDTO.setContactPhone(IOTEncodeUtils.decryptSM4(financingBillDetailDTO.getContactPhone(), iotSm4Key, iotSm4Iv));

                String orderDeductPrice = financingBillDetailDTO.getOrderDeductPrice();
                if (StringUtils.isNotEmpty(orderDeductPrice)) {
                    String deductEncode = IOTEncodeUtils.decryptSM4(orderDeductPrice, iotSm4Key, iotSm4Iv);
                    BigDecimal orderDeductPriceDec = new BigDecimal(deductEncode).divide(thousand, 2, RoundingMode.HALF_UP);
                    financingBillDetailDTO.setOrderDeductPrice(orderDeductPriceDec + "");
                }

                Date orderFinishTime = financingBillDetailDTO.getOrderFinishTime();
                financingBillDetailDTO.setOrderFinishTimeStr(DateUtils.dateToStr(orderFinishTime, DateUtils.DEFAULT_DATETIME_FORMAT));


                String orgName = financingBillDetailDTO.getOrgName();
                if (StringUtils.isNotEmpty(orgName)) {
                    String[] split = orgName.split("-");
                    int orgNameLength = split.length;
                    if (orgNameLength >= 1 && StringUtils.isNotEmpty(split[0]) && split[0].contains("移动")) {
                        financingBillDetailDTO.setOrgProvince(split[0].substring(0, split[0].indexOf("移动")));
                    }
                    if (orgNameLength >= 2 && StringUtils.isNotEmpty(split[1])){
                        financingBillDetailDTO.setOrgCity(split[1]);
                    }
                    if (orgNameLength >= 3 && StringUtils.isNotEmpty(split[2])){
                        financingBillDetailDTO.setOrgRegion(split[2]);
                    }
                }

                String totalPrice = financingBillDetailDTO.getTotalPrice();
                totalPrice = IOTEncodeUtils.decryptSM4(totalPrice, iotSm4Key, iotSm4Iv);
                BigDecimal totalPriceDec = new BigDecimal(totalPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                financingBillDetailDTO.setTotalPrice(totalPriceDec+"");

                Long baoliSettlePrice = financingBillDetailDTO.getBaoliSettlePrice();
                BigDecimal baoliSettlePriceDec = new BigDecimal(baoliSettlePrice).divide(thousand, 2, RoundingMode.HALF_UP);
                financingBillDetailDTO.setBaoliSettlePriceDec(baoliSettlePriceDec);

                Long skuPrice = financingBillDetailDTO.getSkuPrice();
                BigDecimal skuPriceDec = new BigDecimal(skuPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                financingBillDetailDTO.setSkuPriceDec(skuPriceDec);

                Long atomPrice = financingBillDetailDTO.getAtomPrice();
                BigDecimal atomPriceDec = new BigDecimal(atomPrice).divide(thousand, 2, RoundingMode.HALF_UP);
                financingBillDetailDTO.setAtomPriceDec(atomPriceDec);

                Long atomSettlePrice = financingBillDetailDTO.getAtomSettlePrice();
                BigDecimal atomSettlePriceDec = new BigDecimal(atomSettlePrice).divide(thousand, 2, RoundingMode.HALF_UP);
                financingBillDetailDTO.setAtomSettlePriceDec(atomSettlePriceDec);

                Long atomTotalSettlePrice = financingBillDetailDTO.getAtomTotalSettlePrice();
                BigDecimal atomTotalSettlePriceDec = new BigDecimal(atomTotalSettlePrice).divide(thousand, 2, RoundingMode.HALF_UP);
                financingBillDetailDTO.setAtomTotalSettlePriceDec(atomTotalSettlePriceDec);

                String baoliStatusName = FinancingOrderStatusEnum.getDesc(financingBillDetailDTO.getBaoliStatus());
                financingBillDetailDTO.setBaoliStatusName(baoliStatusName);

                financingBillDetailDTO.setBusinessCode(BusinessCodeEnum.getChnName(financingBillDetailDTO.getBusinessCode()));
            });
        }
        return financingBillDetailDTOList;
    }
}
