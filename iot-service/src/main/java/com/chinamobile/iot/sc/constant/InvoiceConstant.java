package com.chinamobile.iot.sc.constant;

/**
 * @package: com.chinamobile.iot.sc.constant
 * @ClassName: InvoiceConstant
 * @description: 发票管理常量
 * @author: zyj
 * @create: 2021/12/1 9:54
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public class InvoiceConstant {
    /**
     * 发票标记，0：专票；1：普票；个人客户只能开具普票，不能开具专票。固定填：1;
     */
    public static final String FRANK_SPECIAL = "0";
    public static final String FRANK_NORMAL = "1";
    /**
     * 申请状态，-1-失败，0-申请开发票，1-待开票，2-开票成功，3-冲红申请，4-待录入冲红信息，5-完成冲红
     */
    public static final Integer STATUS_INVOICE_FAIL = -1;
    public static final Integer STATUS_INVOICE_APPLY = 0;
    public static final Integer STATUS_INVOICE_APPLY_ENTRY = 1;
    public static final Integer STATUS_INVOICE_SUCC = 2;
    public static final Integer STATUS_INVOICE_REVERSE = 3;
    public static final Integer STATUS_INVOICE_REVERSE_ENTRY = 4;
    public static final Integer STATUS_INVOICE_REVERSE_SUCC = 5;
    public static final Integer STATUS_INVOICE_REVERSE_FAIL = 6;
    /**
     * 发票操作类型：0-开发票、1-冲红发票
     */
    public static final Integer VOUCHER_TYPE_APPLY = 0;
    public static final Integer VOUCHER_TYPE_REVERSE = 1;

    /**
     * 开具结果：
     * 1：已开具
     * 0：未开具
     * -1：开具失败
     */
    public static final String IOT_RESULT_INVOICE_SUCC = "1";
    public static final String IOT_RESULT_INVOICE_UNDO = "0";
    public static final String IOT_RESULT_INVOICE_FAIL = "-1";
    /**
     * 冲红结果：
     * 0：成功
     * 1：失败
     */
    public static final String IOT_RESULT_INVOICE_REV_SUCC = "0";
    public static final String IOT_RESULT_INVOICE_REV_FAIL = "1";
    //默认文件存储覆盖
    public static final Boolean IS_COVER =  true;
    //默认文件过期时间，无
    public static final Integer EXPIRED_DAY = -1;

    public static final Integer TEMP_EXPIRED_DAY = 1;
    //月结发票
    public static final String MONTH_INVOICE = "2";

}
