package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.param.CardInfoBYInventoryParam;
import com.chinamobile.iot.sc.pojo.param.CardInfoInventoryErrorParam;
import com.chinamobile.iot.sc.pojo.param.CardInfoParam;
import com.chinamobile.iot.sc.pojo.param.CardInventoryWarningParam;
import com.chinamobile.iot.sc.pojo.vo.CardInfoByInventoryVO;
import com.chinamobile.iot.sc.pojo.vo.CardInfoVO;
import com.chinamobile.iot.sc.pojo.vo.CardInventoryInfoVO;

import java.util.List;

/**
 * <AUTHOR> xie<PERSON>oh<PERSON>
 * @date : 2024/12/9 9:58
 * @description: 码号库存接口类
 **/
public interface CardInventoryInfoService {


    /**
     * 分页查询码号库存列表
     * @param cardInfoParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<CardInventoryInfoVO> pageCardInventoryMainInfo(CardInfoParam cardInfoParam,
                                                            LoginIfo4Redis loginIfo4Redis);

    /**
     * 分页查询码号库存关联的码号信息
     * @param param
     * @return
     */
    PageData<CardInfoByInventoryVO> getCardInfoByInventoryId(CardInfoBYInventoryParam param);

    /**
     *
     * 设置预警值
     * @param id
     */
    void settingsCardInventoryForewarning(CardInventoryWarningParam param);


    /**
     * 原子商品配置处查询码号库存信息
     * @param cardInventoryMainId
     * @param atomId
     * @return
     */
    List<CardInventoryInfoVO> getAtomCardInfoInventoryList(String cardInventoryMainId,String atomId);

    /**
     * 处理历史订单，预占码号库存信息数据
     */
    void handleHistoryOrderCardInventory();

    void handleErrorCardInventoryMainInfo(CardInfoInventoryErrorParam param);

    /**
     * 处理历史卡+x原子商品码号库存主键id
     */
    void handleHistoryAtomCardInventoryId(String atomId);
}
