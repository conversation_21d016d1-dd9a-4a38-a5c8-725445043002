package com.chinamobile.iot.sc.request.order2c;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/23 15:23
 * @description 云视讯手机号检测参数
 */
@Data
public class YunShiXunPhoneCheckParam implements Serializable {

    /**
     * 手机号，多个手机用英文逗号隔开
     * 注：AES加密传输
     */
    private String mobile;
    /**
     * AES加解密KEY
     */
    @JSONField(name = "AES_KEY")
    private String aesKey;

    /**
     * AES加解密IV
     */
    @JSONField(name = "AES_IV")
    private String aesIv;

}
