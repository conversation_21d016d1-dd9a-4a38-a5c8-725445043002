package com.chinamobile.iot.sc.constant.productflow;

public enum ProductFlowAttachmentTypeEnum {
    HEAD_IMAGE(0,"商品头图"),
    SLIDE_IMAGE(1,"商品轮播图"),
    MOVIE(2,"视频"),
    PRODUCT_DETAIL_MATERIAL(3,"商品详情页素材（移动端）"),
    REAL_PRODUCT_IMAGE(4,"实质性产品图片（移动端）"),
    AFTER_MARKET_IMAGE(5,"售后政策图片（移动端）"),
    OTHER(6,"其他"),
    ;


    public Integer code;
    public String name;

    ProductFlowAttachmentTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static boolean contains(Integer type){
        ProductFlowAttachmentTypeEnum[] values = ProductFlowAttachmentTypeEnum.values();
        for (ProductFlowAttachmentTypeEnum value : values) {
            if(value.code.intValue() == type.intValue()){
                return true;
            }
        }
        return false;
    }

    public static ProductFlowAttachmentTypeEnum fromContainName(String name){
        ProductFlowAttachmentTypeEnum[] values = ProductFlowAttachmentTypeEnum.values();
        for (ProductFlowAttachmentTypeEnum value : values) {
            if(name.contains(value.name)){
                return value;
            }
        }
        return null;
    }
}
