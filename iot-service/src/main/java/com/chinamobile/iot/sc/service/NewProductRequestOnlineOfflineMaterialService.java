package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterial;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架商品素材service接口类
 */
public interface NewProductRequestOnlineOfflineMaterialService {

    /**
     * 批量新增上传素材信息
     * @param materialList
     */
    void batchSaveMaterial(List<NewProductRequestOnlineOfflineMaterial> materialList);

    /**
     * 删除素材信息
     * @param comboInfoId
     * @param requestId
     */
    void deleteMaterialByRequestIdAndComboInfoId(String comboInfoId,
                                                 String requestId);

    /**
     * 批量更上传素材信息
     * @param materialList
     */
    void batchUpdateMaterialByRequestIdAndComboInfoIdAndFileType(List<NewProductRequestOnlineOfflineMaterial> materialList);

    /**
     * 根据商品申请id和套餐信息id获取商品素材信息
     * @param newProductRequestId
     * @param comboInfoId
     * @return
     */
    List<NewProductRequestOnlineOfflineMaterial> getMaterialByRequestIdAndComboInfoId(String newProductRequestId,
                                                                                String comboInfoId);
}
