package com.chinamobile.iot.sc.constant.productflow;

import org.apache.commons.lang3.StringUtils;

public enum ProductTypeEnum {

    PROVINCE_RANGE(1,"省框"),
    PROVINCE(2,"省内"),
    DICT(3,"DICT"),
    CONTRACT(4,"合同履约"),
    COOPERATE_SALE(5,"联合销售"),
    ;


    public Integer code;
    public String name;

    ProductTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean contains(Integer productType) {
        if(productType == null){
            return false;
        }
        ProductTypeEnum[] values = ProductTypeEnum.values();
        for (ProductTypeEnum value : values) {
            if(value.code.intValue() == productType.intValue()){
                return true;
            }
        }
        return false;
    }

    public static boolean containsName(String name) {
        if(StringUtils.isEmpty(name)){
            return false;
        }
        ProductTypeEnum[] values = ProductTypeEnum.values();
        for (ProductTypeEnum value : values) {
            if(value.name.equals(name)){
                return true;
            }
        }
        return false;
    }

    public static String getName(Integer code) {
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public static Integer getCode(String name) {
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            if (value.name.equals(name)) {
                return value.code;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
